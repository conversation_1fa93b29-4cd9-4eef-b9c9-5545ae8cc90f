/**
 * API服务工具类
 * 封装HTTP请求，处理认证、错误处理等通用逻辑
 */

// 基础URL
const BASE_URL = 'https://api.example.com'; // 替换为实际的API地址

// 请求超时时间（毫秒）
const TIMEOUT = 30000;

// 请求方法
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE'
};

// 响应状态码
const STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
};

/**
 * 获取认证令牌
 * @returns {string} 认证令牌
 */
function getToken() {
  return wx.getStorageSync('token') || '';
}

/**
 * 设置认证令牌
 * @param {string} token 认证令牌
 */
function setToken(token) {
  wx.setStorageSync('token', token);
}

/**
 * 清除认证令牌
 */
function clearToken() {
  wx.removeStorageSync('token');
}

/**
 * 检查是否需要登录
 * @param {number} statusCode HTTP状态码
 * @returns {boolean} 是否需要登录
 */
function needLogin(statusCode) {
  return statusCode === STATUS_CODES.UNAUTHORIZED;
}

/**
 * 处理API响应
 * @param {Object} response API响应对象
 * @returns {Promise} 处理后的响应数据
 */
function handleResponse(response) {
  const { statusCode, data } = response;
  
  // 检查状态码
  if (statusCode >= 200 && statusCode < 300) {
    // 成功响应
    return Promise.resolve(data);
  } else if (needLogin(statusCode)) {
    // 需要登录
    clearToken();
    // 跳转到登录页面
    wx.navigateTo({
      url: '/pages/login/login'
    });
    return Promise.reject(new Error('请先登录'));
  } else {
    // 其他错误
    const error = new Error(data.message || '请求失败');
    error.statusCode = statusCode;
    error.data = data;
    return Promise.reject(error);
  }
}

/**
 * 发送HTTP请求
 * @param {string} url 请求URL
 * @param {string} method 请求方法
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function request(url, method, data = {}, options = {}) {
  // 完整URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`;
  
  // 请求头
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };
  
  // 添加认证令牌
  const token = getToken();
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // 请求选项
  const requestOptions = {
    url: fullUrl,
    method,
    data,
    header: headers,
    timeout: options.timeout || TIMEOUT
  };
  
  // 发送请求
  return new Promise((resolve, reject) => {
    wx.request({
      ...requestOptions,
      success: (response) => {
        handleResponse(response)
          .then(resolve)
          .catch(reject);
      },
      fail: (error) => {
        // 处理网络错误
        const networkError = new Error(error.errMsg || '网络请求失败');
        networkError.original = error;
        reject(networkError);
      }
    });
  });
}

/**
 * GET请求
 * @param {string} url 请求URL
 * @param {Object} params 请求参数
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function get(url, params = {}, options = {}) {
  // 构建查询字符串
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  // 添加查询字符串到URL
  const fullUrl = queryString ? `${url}?${queryString}` : url;
  
  return request(fullUrl, HTTP_METHODS.GET, {}, options);
}

/**
 * POST请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function post(url, data = {}, options = {}) {
  return request(url, HTTP_METHODS.POST, data, options);
}

/**
 * PUT请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function put(url, data = {}, options = {}) {
  return request(url, HTTP_METHODS.PUT, data, options);
}

/**
 * DELETE请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function del(url, data = {}, options = {}) {
  return request(url, HTTP_METHODS.DELETE, data, options);
}

/**
 * 上传文件
 * @param {string} url 请求URL
 * @param {string} filePath 文件路径
 * @param {string} name 文件对应的key
 * @param {Object} formData 附加的表单数据
 * @param {Object} options 请求选项
 * @returns {Promise} 响应数据
 */
function uploadFile(url, filePath, name, formData = {}, options = {}) {
  // 完整URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`;
  
  // 请求头
  const header = {
    ...options.header
  };
  
  // 添加认证令牌
  const token = getToken();
  if (token) {
    header['Authorization'] = `Bearer ${token}`;
  }
  
  // 发送请求
  return new Promise((resolve, reject) => {
    const uploadTask = wx.uploadFile({
      url: fullUrl,
      filePath,
      name,
      formData,
      header,
      timeout: options.timeout || TIMEOUT,
      success: (response) => {
        // 微信小程序uploadFile的返回数据是字符串，需要手动解析
        try {
          const data = JSON.parse(response.data);
          const parsedResponse = {
            ...response,
            data
          };
          
          handleResponse(parsedResponse)
            .then(resolve)
            .catch(reject);
        } catch (error) {
          reject(new Error('解析响应数据失败'));
        }
      },
      fail: (error) => {
        // 处理网络错误
        const networkError = new Error(error.errMsg || '文件上传失败');
        networkError.original = error;
        reject(networkError);
      }
    });
    
    // 上传进度回调
    if (options.onProgress) {
      uploadTask.onProgressUpdate(options.onProgress);
    }
  });
}

// 导出模块
module.exports = {
  get,
  post,
  put,
  del,
  uploadFile,
  getToken,
  setToken,
  clearToken,
  BASE_URL,
  STATUS_CODES
};
