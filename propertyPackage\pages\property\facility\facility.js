// pages/property/facility/facility.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,

    // 搜索相关
    searchValue: '',

    // 分类标签
    categories: [
      { id: 'all', name: '全部', active: true },
      { id: 'monitor', name: '监控设施', active: false },
      { id: 'door', name: '门禁设施', active: false },
      { id: 'fire', name: '消防设施', active: false },
      { id: 'light', name: '照明设施', active: false },
      { id: 'elevator', name: '电梯设施', active: false },
      { id: 'water', name: '给排水设施', active: false },
      { id: 'hvac', name: '暖通设施', active: false },
      { id: 'other', name: '其他设施', active: false }
    ],

    // 快捷统计
    statistics: {
      pendingRepairs: 3,
      pendingMaintenance: 2,
      todayInspection: 5
    },

    // 设施列表
    facilities: [],

    // 当前选中的分类
    currentCategory: 'all',

    // 是否显示添加设施按钮（根据权限控制）
    canAddFacility: true,

    // 动画数据
    animationData: {},

    // 分页相关
    pageNum: 1,
    pageSize: 10,
    hasMoreData: true,

    // 加载更多状态
    isLoadingMore: false
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '设施管理'
    });

    // 添加页面进入动画
    this.animatePageEnter();

    // 尝试从缓存加载数据
    this.loadDataFromCache();

    // 加载设施数据
    this.loadFacilities();
  },

  // 从缓存加载数据
  loadDataFromCache: function() {
    try {
      // 获取缓存的设施数据
      const cachedFacilities = wx.getStorageSync('facilities');
      const cachedStatistics = wx.getStorageSync('statistics');
      const cacheTime = wx.getStorageSync('facilitiesCacheTime');

      // 检查缓存是否存在且未过期（缓存时间不超过5分钟）
      const now = Date.now();
      const cacheExpired = !cacheTime || (now - cacheTime > 5 * 60 * 1000);

      if (cachedFacilities && cachedStatistics && !cacheExpired) {
        this.setData({
          facilities: cachedFacilities,
          statistics: cachedStatistics,
          isLoading: false
        });

        console.log('从缓存加载数据成功');
      }
    } catch (e) {
      console.error('从缓存加载数据失败:', e);
    }
  },

  // 保存数据到缓存
  saveDataToCache: function(facilities, statistics) {
    try {
      wx.setStorageSync('facilities', facilities);
      wx.setStorageSync('statistics', statistics);
      wx.setStorageSync('facilitiesCacheTime', Date.now());

      console.log('保存数据到缓存成功');
    } catch (e) {
      console.error('保存数据到缓存失败:', e);
    }
  },

  // 页面显示时触发
  onShow: function() {
    // 可以在这里刷新数据
    if (!this.data.isLoading) {
      this.loadFacilities();
    }
  },

  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },

  // 加载设施数据
  loadFacilities: function() {
    this.setData({
      isLoading: true,
      pageNum: 1,
      hasMoreData: true,
      facilities: []
    });

    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const allFacilities = this.getMockFacilities();
      const { pageSize } = this.data;

      // 模拟分页
      const facilities = allFacilities.slice(0, pageSize);

      // 判断是否还有更多数据
      const hasMoreData = allFacilities.length > pageSize;

      // 获取统计数据
      const statistics = {
        pendingRepairs: 3,
        pendingMaintenance: 2,
        todayInspection: 5
      };

      this.setData({
        facilities: facilities,
        statistics: statistics,
        isLoading: false,
        hasMoreData: hasMoreData
      });

      // 保存数据到缓存
      this.saveDataToCache(facilities, statistics);
    }, 500);
  },

  // 加载更多设施数据
  loadMoreFacilities: function() {
    // 如果没有更多数据或正在加载中，则不执行
    if (!this.data.hasMoreData || this.data.isLoadingMore) {
      return;
    }

    this.setData({ isLoadingMore: true });

    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const allFacilities = this.getMockFacilities();
      const { pageNum, pageSize, facilities, currentCategory, searchValue } = this.data;

      // 下一页
      const nextPageNum = pageNum + 1;

      // 模拟分页
      const startIndex = pageSize * (nextPageNum - 1);
      const endIndex = pageSize * nextPageNum;

      // 根据分类和搜索词筛选
      let filteredFacilities = allFacilities;

      // 分类筛选
      if (currentCategory !== 'all') {
        filteredFacilities = filteredFacilities.filter(item => item.category === currentCategory);
      }

      // 搜索词筛选
      if (searchValue) {
        const searchLower = searchValue.toLowerCase();
        filteredFacilities = filteredFacilities.filter(item =>
          item.name.toLowerCase().includes(searchLower) ||
          item.code.toLowerCase().includes(searchLower) ||
          item.location.toLowerCase().includes(searchLower)
        );
      }

      // 获取下一页数据
      const newFacilities = filteredFacilities.slice(startIndex, endIndex);

      // 判断是否还有更多数据
      const hasMoreData = filteredFacilities.length > endIndex;

      this.setData({
        facilities: [...facilities, ...newFacilities],
        pageNum: nextPageNum,
        hasMoreData: hasMoreData,
        isLoadingMore: false
      });
    }, 500);
  },

  // 获取模拟设施数据
  getMockFacilities: function() {
    return [
      {
        id: '1',
        name: '小区正门监控',
        code: 'CAM-001',
        category: 'monitor',
        location: '小区正门',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-10-15',
        nextMaintenance: '2024-01-15'
      },
      {
        id: '2',
        name: '小区后门监控',
        code: 'CAM-002',
        category: 'monitor',
        location: '小区后门',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-10-20',
        nextMaintenance: '2024-01-20'
      },
      {
        id: '3',
        name: '地下车库入口监控',
        code: 'CAM-003',
        category: 'monitor',
        location: '地下车库入口',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-09-10',
        nextMaintenance: '2023-12-10'
      },
      {
        id: '4',
        name: '小区中央花园监控',
        code: 'CAM-004',
        category: 'monitor',
        location: '中央花园',
        status: 'offline',
        statusText: '离线',
        lastMaintenance: '2023-08-05',
        nextMaintenance: '2023-11-05'
      },
      {
        id: '5',
        name: '儿童游乐场监控',
        code: 'CAM-005',
        category: 'monitor',
        location: '儿童游乐场',
        status: 'warning',
        statusText: '信号弱',
        lastMaintenance: '2023-09-25',
        nextMaintenance: '2023-12-25'
      },
      {
        id: '6',
        name: '小区正门门禁',
        code: 'ACC-001',
        category: 'door',
        location: '小区正门',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-10-01',
        nextMaintenance: '2024-01-01'
      },
      {
        id: '7',
        name: '小区后门门禁',
        code: 'ACC-002',
        category: 'door',
        location: '小区后门',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-10-05',
        nextMaintenance: '2024-01-05'
      },
      {
        id: '8',
        name: '1号楼单元门',
        code: 'ACC-003',
        category: 'door',
        location: '1号楼',
        status: 'fault',
        statusText: '故障',
        lastMaintenance: '2023-08-15',
        nextMaintenance: '2023-11-15'
      },
      {
        id: '9',
        name: '中央空调系统',
        code: 'HVAC-001',
        category: 'hvac',
        location: '小区公共区域',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-09-15',
        nextMaintenance: '2023-12-15'
      },
      {
        id: '10',
        name: '消防喷淋系统',
        code: 'FIRE-001',
        category: 'fire',
        location: '小区公共区域',
        status: 'normal',
        statusText: '正常',
        lastMaintenance: '2023-10-10',
        nextMaintenance: '2024-01-10'
      }
    ];
  },

  // 切换分类标签
  switchCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;

    // 更新分类标签状态
    const categories = this.data.categories.map(item => {
      return {
        ...item,
        active: item.id === categoryId
      };
    });

    this.setData({
      categories: categories,
      currentCategory: categoryId
    });

    // 根据分类筛选设施
    this.filterFacilities();
  },

  // 搜索设施
  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });

    // 根据搜索词筛选设施
    this.filterFacilities();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: ''
    });

    // 重置筛选
    this.filterFacilities();
  },

  // 筛选设施
  filterFacilities: function() {
    // 重置分页
    this.setData({
      pageNum: 1,
      hasMoreData: true,
      isLoading: true
    });

    const { currentCategory, searchValue, pageSize } = this.data;
    const allFacilities = this.getMockFacilities();

    // 根据分类和搜索词筛选
    let filteredFacilities = allFacilities;

    // 分类筛选
    if (currentCategory !== 'all') {
      filteredFacilities = filteredFacilities.filter(item => item.category === currentCategory);
    }

    // 搜索词筛选
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      filteredFacilities = filteredFacilities.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.code.toLowerCase().includes(searchLower) ||
        item.location.toLowerCase().includes(searchLower)
      );
    }

    // 获取第一页数据
    const firstPageFacilities = filteredFacilities.slice(0, pageSize);

    // 判断是否还有更多数据
    const hasMoreData = filteredFacilities.length > pageSize;

    this.setData({
      facilities: firstPageFacilities,
      hasMoreData: hasMoreData,
      isLoading: false
    });
  },

  // 导航到设施详情页
  navigateToDetail: function(e) {
    const facilityId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/detail/index?id=${facilityId}`
    });
  },

  // 导航到添加设施页面
  navigateToAdd: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/facility/add/index'
    });
  },

  // 导航到我的任务页面
  navigateToMyTasks: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/facility/tasks/index'
    });
  },

  // 导航到报修页面
  navigateToRepair: function(e) {
    const facilityId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/property/facility/repair/index?id=${facilityId}`
    });
  },

  // 导航到待处理报修列表
  navigateToPendingRepairs: function() {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'worker';

    if (userRole === 'admin') {
      // 管理员导航到所有报修任务列表
      wx.navigateTo({
        url: '/pages/property/facility/task-management/index?type=repair&status=pending'
      });
    } else {
      // 普通工作人员导航到我的任务列表
      wx.navigateTo({
        url: '/pages/property/facility/tasks/index?type=repair&status=pending'
      });
    }
  },

  // 导航到待执行保养任务列表
  navigateToPendingMaintenance: function() {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'worker';

    if (userRole === 'admin') {
      // 管理员导航到所有保养任务列表
      wx.navigateTo({
        url: '/pages/property/facility/task-management/index?type=maintenance&status=pending'
      });
    } else {
      // 普通工作人员导航到我的任务列表
      wx.navigateTo({
        url: '/pages/property/facility/tasks/index?type=maintenance&status=pending'
      });
    }
  },

  // 导航到今日巡检任务列表
  navigateToTodayInspection: function() {
    const today = new Date();
    const todayStr = today.getFullYear() + '-' +
                    String(today.getMonth() + 1).padStart(2, '0') + '-' +
                    String(today.getDate()).padStart(2, '0');

    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'worker';

    if (userRole === 'admin') {
      // 管理员导航到所有巡检任务列表
      wx.navigateTo({
        url: `/pages/property/facility/task-management/index?type=inspection&date=${todayStr}`
      });
    } else {
      // 普通工作人员导航到我的任务列表
      wx.navigateTo({
        url: `/pages/property/facility/tasks/index?type=inspection&date=${todayStr}`
      });
    }
  },

  // 调用扫码功能
  scanCode: function() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res);
        // 处理扫码结果，可能是跳转到设施详情页
        // 假设扫码结果是设施ID
        if (res.result) {
          wx.navigateTo({
            url: `/pages/property/facility/detail/index?id=${res.result}`
          });
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载设施数据
    this.loadFacilities();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 上拉触底加载更多
  onReachBottom: function() {
    // 加载更多设施数据
    this.loadMoreFacilities();
  }
})
