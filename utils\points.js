// points.js
// 积分变动事件名称
const POINTS_CHANGE_EVENT = 'pointsChange';

/**
 * 用户等级配置
 */
const USER_LEVELS = [
  { id: 1, name: '普通会员', minPoints: 0, maxPoints: 999, icon: 'level-1' },
  { id: 2, name: '银卡会员', minPoints: 1000, maxPoints: 4999, icon: 'level-2' },
  { id: 3, name: '金卡会员', minPoints: 5000, maxPoints: 9999, icon: 'level-3' },
  { id: 4, name: '钻石会员', minPoints: 10000, maxPoints: Infinity, icon: 'level-4' }
];

/**
 * 等级特权配置
 */
const LEVEL_PRIVILEGES = {
  1: { // 普通会员
    exchangeRights: '积分商城兑换基础权益',
    pointsRatio: 100, // 100积分=1元
    maxDiscountRatio: 0.2, // 最高可抵扣订单金额的20%
    privileges: []
  },
  2: { // 银卡会员
    exchangeRights: '积分商城兑换全部权益',
    pointsRatio: 100, // 100积分=1元
    maxDiscountRatio: 0.25, // 最高可抵扣订单金额的25%
    privileges: [
      { id: 1, name: '生日特权', description: '生日当月额外赠送100积分' },
      { id: 2, name: '专属优惠', description: '部分商品享受95折优惠' },
      { id: 3, name: '社区活动优先报名权', description: '社区活动优先报名' }
    ]
  },
  3: { // 金卡会员
    exchangeRights: '积分商城兑换全部权益',
    pointsRatio: 90, // 90积分=1元
    maxDiscountRatio: 0.3, // 最高可抵扣订单金额的30%
    privileges: [
      { id: 1, name: '生日特权', description: '生日当月额外赠送200积分' },
      { id: 2, name: '专属优惠', description: '部分商品享受9折优惠' },
      { id: 3, name: '社区活动优先报名权', description: '社区活动优先报名' },
      { id: 4, name: '专属客服', description: '享受优先客服服务' },
      { id: 5, name: '物业服务优先处理', description: '物业服务优先处理' },
      { id: 6, name: '每月额外赠送', description: '每月额外赠送50积分' }
    ]
  },
  4: { // 钻石会员
    exchangeRights: '积分商城兑换全部权益',
    pointsRatio: 80, // 80积分=1元
    maxDiscountRatio: 0.35, // 最高可抵扣订单金额的35%
    privileges: [
      { id: 1, name: '生日特权', description: '生日当月额外赠送500积分' },
      { id: 2, name: '专属优惠', description: '部分商品享受85折优惠' },
      { id: 3, name: '社区活动优先报名权', description: '社区活动优先报名权和VIP席位' },
      { id: 4, name: '专属客服', description: '享受优先客服服务' },
      { id: 5, name: '物业服务优先处理', description: '物业服务优先处理和上门服务' },
      { id: 6, name: '专属商品', description: '可购买钻石会员专属商品' },
      { id: 7, name: '免费停车', description: '每月赠送2小时免费停车' },
      { id: 8, name: '每月额外赠送', description: '每月额外赠送100积分' },
      { id: 9, name: '社区活动专属标识', description: '社区活动专属标识' }
    ]
  }
};

/**
 * 积分工具类
 */
const PointsUtil = {
  /**
   * 获取用户积分
   */
  getUserPoints: function() {
    return new Promise((resolve, reject) => {
      // 实际应用中应该从服务器获取最新积分
      const userInfo = wx.getStorageSync('userInfo') || {};
      resolve(userInfo.points || 0);
    });
  },

  /**
   * 更新用户积分
   * @param {Number} points 新的积分值
   */
  updateUserPoints: function(points) {
    return new Promise((resolve, reject) => {
      // 实际应用中应该调用API更新服务器上的积分
      const userInfo = wx.getStorageSync('userInfo') || {};
      const oldPoints = userInfo.points || 0;
      userInfo.points = points;
      wx.setStorageSync('userInfo', userInfo);

      // 同步更新localStorage中的userPoints（用于HTML页面）
      try {
        wx.setStorageSync('userPoints', points.toString());
      } catch (e) {
        console.error('同步更新localStorage中的userPoints失败:', e);
      }

      // 触发积分变动事件
      try {
        const appInstance = getApp();
        if (appInstance && appInstance.globalData) {
          appInstance.globalData.userPoints = points;

          // 创建自定义事件
          const pointsChangeEvent = {
            name: POINTS_CHANGE_EVENT,
            oldPoints: oldPoints,
            newPoints: points
          };

          // 保存事件到全局数据，以便页面可以获取
          appInstance.globalData.pointsChangeEvent = pointsChangeEvent;

          console.log('积分已更新:', oldPoints, '->', points);
        }
      } catch (e) {
        console.error('更新全局积分数据失败:', e);
      }

      resolve(points);
    });
  },

  /**
   * 增加积分
   * @param {Number} points 增加的积分数量
   * @param {String} source 积分来源
   * @param {String} description 积分描述
   */
  addPoints: function(points, source, description) {
    return new Promise((resolve, reject) => {
      this.getUserPoints().then(currentPoints => {
        const newPoints = currentPoints + points;

        // 更新积分
        this.updateUserPoints(newPoints).then(() => {
          // 记录积分历史
          this.addPointsRecord(points, 'earn', description, source);
          resolve(newPoints);
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 使用积分
   * @param {Number} points 使用的积分数量
   * @param {String} purpose 使用目的
   * @param {String} description 使用描述
   */
  usePoints: function(points, purpose, description) {
    return new Promise((resolve, reject) => {
      this.getUserPoints().then(currentPoints => {
        if (currentPoints < points) {
          reject(new Error('积分不足'));
          return;
        }

        const newPoints = currentPoints - points;

        // 更新积分
        this.updateUserPoints(newPoints).then(() => {
          // 记录积分历史
          this.addPointsRecord(points, 'use', description, purpose);
          resolve(newPoints);
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 添加积分记录
   * @param {Number} points 积分数量
   * @param {String} type 类型：earn(获取) / use(使用)
   * @param {String} description 描述
   * @param {String} category 分类
   */
  addPointsRecord: function(points, type, description, category) {
    const record = {
      points: points,
      type: type,
      description: description,
      category: category,
      date: new Date().toISOString()
    };

    // 保存积分记录
    const records = wx.getStorageSync('pointsRecords') || [];
    records.unshift(record);
    wx.setStorageSync('pointsRecords', records);

    // 实际应用中应该同步到服务器
    return Promise.resolve(record);
  },

  /**
   * 获取积分记录
   * @param {String} type 类型：all / earn / use
   * @param {Number} page 页码
   * @param {Number} pageSize 每页数量
   */
  getPointsRecords: function(type = 'all', page = 1, pageSize = 20) {
    return new Promise((resolve) => {
      // 获取所有记录
      const allRecords = wx.getStorageSync('pointsRecords') || [];

      // 按类型筛选
      let records = allRecords;
      if (type !== 'all') {
        records = allRecords.filter(record => record.type === type);
      }

      // 分页
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const pagedRecords = records.slice(start, end);

      resolve({
        records: pagedRecords,
        total: records.length,
        hasMore: end < records.length
      });
    });
  },

  /**
   * 检查任务是否完成
   * @param {Number} taskId 任务ID
   */
  isTaskCompleted: function(taskId) {
    const completedTasks = wx.getStorageSync('completedTasks') || [];
    return completedTasks.includes(taskId);
  },

  /**
   * 标记任务为已完成
   * @param {Number} taskId 任务ID
   */
  markTaskCompleted: function(taskId) {
    const completedTasks = wx.getStorageSync('completedTasks') || [];
    if (!completedTasks.includes(taskId)) {
      completedTasks.push(taskId);
      wx.setStorageSync('completedTasks', completedTasks);
    }
    return Promise.resolve(true);
  },

  /**
   * 兑换商品
   * @param {Number} productId 商品ID
   */
  exchangeProduct: function(productId) {
    // 实际应用中应该调用API进行兑换
    return new Promise((resolve, reject) => {
      // 模拟获取商品信息
      const products = wx.getStorageSync('mallProducts') || [];
      const product = products.find(p => p.id === productId);

      if (!product) {
        reject(new Error('商品不存在'));
        return;
      }

      if (product.stock <= 0) {
        reject(new Error('商品库存不足'));
        return;
      }

      // 使用积分
      this.usePoints(product.points, 'exchange', `兑换商品：${product.title}`).then(newPoints => {
        // 更新商品库存
        product.stock--;
        wx.setStorageSync('mallProducts', products);

        // 添加兑换记录
        const exchanges = wx.getStorageSync('productExchanges') || [];
        exchanges.unshift({
          productId: product.id,
          productTitle: product.title,
          points: product.points,
          date: new Date().toISOString(),
          status: 'pending' // pending, completed, cancelled
        });
        wx.setStorageSync('productExchanges', exchanges);

        resolve({
          success: true,
          newPoints: newPoints,
          product: product
        });
      }).catch(reject);
    });
  },

  /**
   * 获取用户积分等级信息
   * @returns {Promise} 用户积分等级信息
   */
  getUserLevelInfo: function() {
    return new Promise((resolve, reject) => {
      this.getUserPoints().then(points => {
        // 根据积分判断等级
        const level = this.getUserLevelByPoints(points);

        // 获取等级特权
        const privileges = this.getLevelPrivileges(level.id);

        // 计算距离下一等级所需积分
        const nextLevel = this.getNextLevel(level.id);
        const pointsToNextLevel = nextLevel ? nextLevel.minPoints - points : 0;

        resolve({
          points: points,
          level: level,
          privileges: privileges,
          nextLevel: nextLevel,
          pointsToNextLevel: pointsToNextLevel,
          progress: nextLevel ? (points - level.minPoints) / (nextLevel.minPoints - level.minPoints) * 100 : 100
        });
      }).catch(reject);
    });
  },

  /**
   * 根据积分获取用户等级
   * @param {Number} points 积分数量
   * @returns {Object} 用户等级信息
   */
  getUserLevelByPoints: function(points) {
    for (let i = 0; i < USER_LEVELS.length; i++) {
      if (points >= USER_LEVELS[i].minPoints && points <= USER_LEVELS[i].maxPoints) {
        return USER_LEVELS[i];
      }
    }
    return USER_LEVELS[0]; // 默认返回普通会员
  },

  /**
   * 获取下一等级
   * @param {Number} currentLevelId 当前等级ID
   * @returns {Object} 下一等级信息，如果已是最高等级则返回null
   */
  getNextLevel: function(currentLevelId) {
    const nextLevelId = currentLevelId + 1;
    return nextLevelId <= USER_LEVELS.length ? USER_LEVELS[nextLevelId - 1] : null;
  },

  /**
   * 获取等级特权
   * @param {Number} levelId 等级ID
   * @returns {Object} 等级特权信息
   */
  getLevelPrivileges: function(levelId) {
    return LEVEL_PRIVILEGES[levelId] || LEVEL_PRIVILEGES[1]; // 默认返回普通会员特权
  },

  /**
   * 检查积分是否过期
   * @returns {Promise} 过期的积分数量
   */
  checkPointsExpiration: function() {
    return new Promise((resolve, reject) => {
      // 获取积分记录
      const records = wx.getStorageSync('pointsRecords') || [];

      // 当前时间
      const now = new Date();

      // 过期时间：1年前
      const expirationDate = new Date();
      expirationDate.setFullYear(now.getFullYear() - 1);

      // 筛选出过期的积分记录（获取类型且时间超过1年）
      const expiredRecords = records.filter(record => {
        return record.type === 'earn' && new Date(record.date) < expirationDate;
      });

      // 计算过期积分总数
      const expiredPoints = expiredRecords.reduce((total, record) => total + record.points, 0);

      if (expiredPoints > 0) {
        // 扣除过期积分
        this.usePoints(expiredPoints, 'expire', '积分过期').then(() => {
          resolve(expiredPoints);
        }).catch(reject);
      } else {
        resolve(0);
      }
    });
  },

  /**
   * 检查并处理会员等级变更
   * @returns {Promise} 等级变更信息
   */
  checkLevelChange: function() {
    return new Promise((resolve, reject) => {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo') || {};
      const oldLevelId = userInfo.levelId || 1;

      // 获取当前积分和等级
      this.getUserPoints().then(points => {
        const newLevel = this.getUserLevelByPoints(points);

        // 如果等级发生变化
        if (newLevel.id !== oldLevelId) {
          // 更新用户等级
          userInfo.levelId = newLevel.id;
          wx.setStorageSync('userInfo', userInfo);

          // 如果是升级
          if (newLevel.id > oldLevelId) {
            // 记录升级事件
            this.addPointsRecord(0, 'level', `会员升级：${USER_LEVELS[oldLevelId - 1].name} -> ${newLevel.name}`, 'level');

            resolve({
              changed: true,
              isUpgrade: true,
              oldLevel: USER_LEVELS[oldLevelId - 1],
              newLevel: newLevel
            });
          } else {
            // 记录降级事件
            this.addPointsRecord(0, 'level', `会员降级：${USER_LEVELS[oldLevelId - 1].name} -> ${newLevel.name}`, 'level');

            resolve({
              changed: true,
              isUpgrade: false,
              oldLevel: USER_LEVELS[oldLevelId - 1],
              newLevel: newLevel
            });
          }
        } else {
          resolve({
            changed: false,
            level: newLevel
          });
        }
      }).catch(reject);
    });
  },

  /**
   * 获取积分抵扣比例
   * @returns {Promise} 积分抵扣比例
   */
  getPointsDiscountRatio: function() {
    return new Promise((resolve, reject) => {
      this.getUserLevelInfo().then(levelInfo => {
        const privileges = this.getLevelPrivileges(levelInfo.level.id);
        resolve({
          pointsRatio: privileges.pointsRatio, // 积分兑换比例（多少积分=1元）
          maxDiscountRatio: privileges.maxDiscountRatio // 最高可抵扣订单金额的比例
        });
      }).catch(reject);
    });
  },

  /**
   * 获取所有等级信息
   * @returns {Array} 所有等级信息
   */
  getAllLevels: function() {
    return USER_LEVELS;
  }
};

module.exports = PointsUtil;
