// pages/property/facility/tasks/index.js
const dateUtil = require('../../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,

    // 任务类型标签
    taskTypes: [
      { id: 'all', name: '全部', active: true },
      { id: 'inspection', name: '巡检', active: false },
      { id: 'maintenance', name: '保养', active: false },
      { id: 'repair', name: '报修', active: false }
    ],
    currentTaskType: 'all',

    // 任务状态标签
    taskStatuses: [
      { id: 'all', name: '全部', active: true },
      { id: 'pending', name: '待执行', active: false },
      { id: 'processing', name: '进行中', active: false },
      { id: 'completed', name: '已完成', active: false }
    ],
    currentTaskStatus: 'all',

    // 任务列表
    tasks: [],

    // 搜索相关
    searchValue: '',

    // 动画数据
    animationData: {},

    // 问候语
    greeting: '',

    // 用户信息
    userInfo: {
      name: '张工',
      avatar: 'https://ui-avatars.com/api/?name=张工&background=ff8c00&color=fff&size=100',
      status: 'online',
      employeeId: 'PY-001',
      position: '物业维修员'
    },

    // 统计数据
    statistics: {
      todayTasks: 0,
      pendingTasks: 0,
      completedTasks: 0
    },

    // 日期筛选
    filterDate: '',

    // 空状态提示
    emptyStateTitle: '未找到任务',
    emptyStateDescription: '尝试更换筛选条件或清除搜索'
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '我的任务'
    });

    // 添加问候语
    this.setGreeting();

    // 添加页面进入动画
    this.animatePageEnter();

    // 处理URL参数，自动应用筛选条件
    if (options) {
      // 处理任务类型筛选
      if (options.type) {
        const taskType = options.type;
        // 更新任务类型标签状态
        const taskTypes = this.data.taskTypes.map(item => {
          return {
            ...item,
            active: item.id === taskType
          };
        });

        this.setData({
          taskTypes: taskTypes,
          currentTaskType: taskType
        });
      }

      // 处理任务状态筛选
      if (options.status) {
        const taskStatus = options.status;
        // 更新任务状态标签状态
        const taskStatuses = this.data.taskStatuses.map(item => {
          return {
            ...item,
            active: item.id === taskStatus
          };
        });

        this.setData({
          taskStatuses: taskStatuses,
          currentTaskStatus: taskStatus
        });
      }

      // 处理日期筛选（今日巡检任务）
      if (options.date) {
        // 这里可以添加日期筛选的逻辑
        // 但是当前页面没有日期筛选的UI，所以暂时不处理
        // 可以在loadTasks函数中处理日期筛选
        this.setData({
          filterDate: options.date
        });
      }
    }

    // 加载任务数据
    this.loadTasks();
  },

  // 设置问候语
  setGreeting: function() {
    const hour = new Date().getHours();
    let greeting = '';

    if (hour < 6) {
      greeting = '凌晨好，';
    } else if (hour < 9) {
      greeting = '早上好，';
    } else if (hour < 12) {
      greeting = '上午好，';
    } else if (hour < 14) {
      greeting = '中午好，';
    } else if (hour < 18) {
      greeting = '下午好，';
    } else if (hour < 22) {
      greeting = '晚上好，';
    } else {
      greeting = '夜深了，';
    }

    this.setData({
      greeting: greeting
    });
  },

  // 页面显示时触发
  onShow: function() {
    // 可以在这里刷新数据
    if (!this.data.isLoading) {
      this.loadTasks();
    }
  },

  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },

  // 加载任务数据
  loadTasks: function() {
    this.setData({ isLoading: true });

    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const tasks = this.getMockTasks();

      // 计算统计数据
      const statistics = this.calculateStatistics(tasks);

      this.setData({
        tasks: tasks,
        statistics: statistics,
        isLoading: false
      });
    }, 500);
  },

  // 获取模拟任务数据
  getMockTasks: function() {
    const today = new Date();
    const todayStr = dateUtil.formatDate(today);

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = dateUtil.formatDate(yesterday);

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = dateUtil.formatDate(tomorrow);

    return [
      {
        id: '1',
        title: '小区监控设备巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'pending',
        statusText: '待执行',
        date: todayStr,
        time: '09:00-10:00',
        location: '小区各监控点位',
        facilityCount: 12,
        description: '检查小区各监控设备运行状态，确保画面清晰，存储正常',
        assignee: '张工',
        priority: 'high',
        priorityText: '紧急'
      },
      {
        id: '2',
        title: '消防设备月度巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'processing',
        statusText: '进行中',
        date: todayStr,
        time: '14:00-16:00',
        location: '小区公共区域',
        facilityCount: 25,
        description: '检查小区消防栓、灭火器、烟感等设备状态',
        assignee: '张工',
        progress: 60,
        priority: 'medium',
        priorityText: '普通'
      },
      {
        id: '3',
        title: '电梯季度保养',
        type: 'maintenance',
        typeText: '保养',
        status: 'pending',
        statusText: '待执行',
        date: tomorrowStr,
        time: '10:00-12:00',
        location: '1号楼、2号楼',
        facilityCount: 4,
        description: '对小区电梯进行季度保养，检查各部件运行状态',
        assignee: '张工',
        priority: 'high',
        priorityText: '紧急'
      },
      {
        id: '4',
        title: '门禁系统巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'completed',
        statusText: '已完成',
        date: yesterdayStr,
        time: '15:00-16:00',
        location: '各单元门',
        facilityCount: 8,
        description: '检查各单元门门禁系统运行状态，确保刷卡、密码开门正常',
        assignee: '张工',
        completedTime: '16:05',
        result: '正常',
        priority: 'low',
        priorityText: '低'
      },
      {
        id: '5',
        title: '中央空调系统保养',
        type: 'maintenance',
        typeText: '保养',
        status: 'completed',
        statusText: '已完成',
        date: yesterdayStr,
        time: '09:00-11:00',
        location: '小区公共区域',
        facilityCount: 2,
        description: '对中央空调系统进行保养，清洗过滤网，检查制冷效果',
        assignee: '李工',
        completedTime: '10:45',
        result: '异常',
        abnormalDetail: '温度传感器异常，需要更换',
        priority: 'medium',
        priorityText: '普通'
      }
    ];
  },

  // 计算统计数据
  calculateStatistics: function(tasks) {
    const today = dateUtil.formatDate(new Date());

    // 今日任务数
    const todayTasks = tasks.filter(task => task.date === today).length;

    // 待执行任务数
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;

    // 已完成任务数
    const completedTasks = tasks.filter(task => task.status === 'completed').length;

    return {
      todayTasks: todayTasks,
      pendingTasks: pendingTasks,
      completedTasks: completedTasks
    };
  },

  // 切换任务类型标签
  switchTaskType: function(e) {
    const typeId = e.currentTarget.dataset.id;

    // 更新任务类型标签状态
    const taskTypes = this.data.taskTypes.map(item => {
      return {
        ...item,
        active: item.id === typeId
      };
    });

    this.setData({
      taskTypes: taskTypes,
      currentTaskType: typeId
    });

    // 根据筛选条件过滤任务
    this.filterTasks();
  },

  // 切换任务状态标签
  switchTaskStatus: function(e) {
    const statusId = e.currentTarget.dataset.id;

    // 更新任务状态标签状态
    const taskStatuses = this.data.taskStatuses.map(item => {
      return {
        ...item,
        active: item.id === statusId
      };
    });

    this.setData({
      taskStatuses: taskStatuses,
      currentTaskStatus: statusId
    });

    // 根据筛选条件过滤任务
    this.filterTasks();
  },

  // 搜索任务
  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });

    // 根据搜索词筛选任务
    this.filterTasks();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: ''
    });

    // 重置筛选
    this.filterTasks();
  },

  // 筛选任务
  filterTasks: function() {
    const { currentTaskType, currentTaskStatus, searchValue, filterDate } = this.data;
    const allTasks = this.getMockTasks();

    // 根据类型、状态、日期和搜索词筛选
    let filteredTasks = allTasks;

    // 类型筛选
    if (currentTaskType !== 'all') {
      filteredTasks = filteredTasks.filter(item => item.type === currentTaskType);
    }

    // 状态筛选
    if (currentTaskStatus !== 'all') {
      filteredTasks = filteredTasks.filter(item => item.status === currentTaskStatus);
    }

    // 日期筛选
    if (filterDate) {
      filteredTasks = filteredTasks.filter(item => item.date === filterDate);
    }

    // 搜索词筛选
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      filteredTasks = filteredTasks.filter(item =>
        item.title.toLowerCase().includes(searchLower) ||
        item.location.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower)
      );
    }

    // 更新空状态提示
    this.updateEmptyStateMessage();

    // 更新统计数据
    const statistics = this.calculateStatistics(allTasks);

    this.setData({
      tasks: filteredTasks,
      statistics: statistics
    });
  },

  // 更新空状态提示信息
  updateEmptyStateMessage: function() {
    const { currentTaskType, currentTaskStatus, searchValue } = this.data;

    let title = '未找到任务';
    let description = '尝试更换筛选条件或清除搜索';

    // 根据筛选条件生成提示信息
    if (searchValue) {
      title = '未找到相关任务';
      description = '尝试使用其他关键词搜索';
    } else {
      // 根据任务类型和状态生成提示
      const typeText = currentTaskType === 'all' ? '' :
                      currentTaskType === 'inspection' ? '巡检' :
                      currentTaskType === 'maintenance' ? '保养' : '报修';

      const statusText = currentTaskStatus === 'all' ? '' :
                        currentTaskStatus === 'pending' ? '待执行的' :
                        currentTaskStatus === 'processing' ? '进行中的' : '已完成的';

      if (typeText && statusText) {
        title = `暂无${statusText}${typeText}任务`;
      } else if (typeText) {
        title = `暂无${typeText}任务`;
      } else if (statusText) {
        title = `暂无${statusText}任务`;
      }
    }

    this.setData({
      emptyStateTitle: title,
      emptyStateDescription: description
    });
  },

  // 导航到任务详情页
  navigateToTaskDetail: function(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/property/facility/tasks/detail/index?id=${taskId}`
    });
  },

  // 导航到任务执行页
  navigateToTaskExecution: function(e) {
    const taskId = e.currentTarget.dataset.id;

    // 显示加载中提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 模拟网络请求延迟
    setTimeout(() => {
      wx.hideLoading();

      // 导航到任务执行页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/facility/tasks/execute/index?id=${taskId}`,
        success: () => {
          // 导航成功后显示提示
          wx.showToast({
            title: '任务加载成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          // 导航失败显示错误提示
          wx.showToast({
            title: '加载失败，请重试',
            icon: 'error',
            duration: 2000
          });
          console.error('导航到任务执行页失败:', err);
        }
      });
    }, 500);
  },

  // 查看异常详情
  viewAbnormalDetail: function(e) {
    const taskId = e.currentTarget ? e.currentTarget.dataset.id : e;

    // 安全地阻止事件冒泡
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }

    // 显示异常详情对话框
    wx.showModal({
      title: '异常详情',
      content: '设备ID: SB-2023-001\n异常类型: 信号丢失\n异常描述: 监控设备无法连接到网络，可能是网络线路损坏或设备故障\n处理建议: 检查网络连接线路，必要时更换设备',
      confirmText: '处理异常',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          // 用户点击了处理异常
          wx.navigateTo({
            url: `/pages/property/facility/tasks/abnormal/index?id=${taskId}`
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载任务数据
    this.loadTasks();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 筛选今日任务
  filterTodayTasks: function() {
    const today = dateUtil.formatDate(new Date());

    // 设置日期筛选
    this.setData({
      filterDate: today,
      currentTaskType: 'all',
      currentTaskStatus: 'all'
    });

    // 更新任务类型标签状态
    const taskTypes = this.data.taskTypes.map(item => {
      return {
        ...item,
        active: item.id === 'all'
      };
    });

    // 更新任务状态标签状态
    const taskStatuses = this.data.taskStatuses.map(item => {
      return {
        ...item,
        active: item.id === 'all'
      };
    });

    this.setData({
      taskTypes: taskTypes,
      taskStatuses: taskStatuses
    });

    // 应用筛选
    this.filterTasks();

    // 显示提示
    wx.showToast({
      title: '已筛选今日任务',
      icon: 'success',
      duration: 1500
    });
  },

  // 筛选待执行任务
  filterPendingTasks: function() {
    // 重置日期筛选
    this.setData({
      filterDate: '',
      currentTaskType: 'all',
      currentTaskStatus: 'pending'
    });

    // 更新任务类型标签状态
    const taskTypes = this.data.taskTypes.map(item => {
      return {
        ...item,
        active: item.id === 'all'
      };
    });

    // 更新任务状态标签状态
    const taskStatuses = this.data.taskStatuses.map(item => {
      return {
        ...item,
        active: item.id === 'pending'
      };
    });

    this.setData({
      taskTypes: taskTypes,
      taskStatuses: taskStatuses
    });

    // 应用筛选
    this.filterTasks();

    // 显示提示
    wx.showToast({
      title: '已筛选待执行任务',
      icon: 'success',
      duration: 1500
    });
  },

  // 筛选已完成任务
  filterCompletedTasks: function() {
    // 重置日期筛选
    this.setData({
      filterDate: '',
      currentTaskType: 'all',
      currentTaskStatus: 'completed'
    });

    // 更新任务类型标签状态
    const taskTypes = this.data.taskTypes.map(item => {
      return {
        ...item,
        active: item.id === 'all'
      };
    });

    // 更新任务状态标签状态
    const taskStatuses = this.data.taskStatuses.map(item => {
      return {
        ...item,
        active: item.id === 'completed'
      };
    });

    this.setData({
      taskTypes: taskTypes,
      taskStatuses: taskStatuses
    });

    // 应用筛选
    this.filterTasks();

    // 显示提示
    wx.showToast({
      title: '已筛选已完成任务',
      icon: 'success',
      duration: 1500
    });
  }
})
