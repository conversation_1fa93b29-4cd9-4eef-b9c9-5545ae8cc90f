<!--便民信息黄页页面-->
<view class="container">

  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/icons/search.svg" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索服务或商家" bindinput="onSearchInput" value="{{searchKeyword}}"/>
      <view class="search-clear" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="/images/icons/clear.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 服务分类网格 -->
  <view class="category-grid" wx:if="{{!searchKeyword}}">
    <view class="category-title">服务分类</view>
    <view class="grid-container">
      <view class="grid-item" wx:for="{{categories}}" wx:key="id" bindtap="navigateToCategory" data-id="{{item.id}}" data-name="{{item.name}}">
        <view class="grid-icon-bg" style="background-color: {{item.bgColor}}">
          <image class="grid-icon" src="{{item.icon}}" mode="aspectFit"></image>
        </view>
        <view class="grid-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 搜索结果列表 -->
  <view class="search-results" wx:if="{{searchKeyword}}">
    <view class="result-title">搜索结果</view>
    <view class="result-list">
      <view class="result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="result-content">
          <view class="result-name">{{item.name}}</view>
          <view class="result-desc">{{item.description}}</view>
          <view class="result-info">{{item.address || item.phone}}</view>
        </view>
        <view class="result-arrow">
          <image src="/images/icons/arrow-right.svg" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <empty-state
      wx:if="{{searchResults.length === 0}}"
      image="/images/illustrations/empty-search.svg"
      text="未找到相关结果"
      subtext="请尝试其他关键词"
    />
  </view>

  <!-- 热门服务 -->
  <view class="popular-services" wx:if="{{!searchKeyword}}">
    <view class="popular-title">热门服务</view>
    <view class="popular-list">
      <view class="popular-item" wx:for="{{popularServices}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="popular-content">
          <view class="popular-name">{{item.name}}</view>
          <view class="popular-desc">{{item.description}}</view>
          <view class="popular-info">
            <view class="popular-address" wx:if="{{item.address}}">
              <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
              <text>{{item.address}}</text>
            </view>
            <view class="popular-phone" wx:if="{{item.phone}}">
              <image class="info-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
              <text>{{item.phone}}</text>
            </view>
          </view>
        </view>
        <view class="popular-arrow">
          <image src="/images/icons/arrow-right.svg" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</view>
