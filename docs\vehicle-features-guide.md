# 车辆管理功能完善指南

## 功能概述

本次更新完善了小程序的车辆管理功能，主要包括：

1. **登录token管理机制完善**
2. **车牌颜色判断逻辑优化**
3. **车牌输入弹窗交互优化**

## 1. 登录Token管理机制

### 新增功能

#### 1.1 自动Token刷新
- 当API请求返回401状态码时，自动尝试刷新token
- 使用refresh token调用 `/users-api/v1/auth/refresh-token` 接口
- 刷新成功后自动重新发起原始请求

#### 1.2 请求队列管理
- 在token刷新过程中，暂停所有待发送的API请求
- 将请求加入队列，避免重复刷新token
- 刷新完成后，批量处理队列中的请求

#### 1.3 用户状态检测
- `checkUserLogin()`: 检查用户是否已登录
- `clearUserInfo()`: 清除用户登录信息
- 支持首次使用和重新安装场景

### 使用方法

```javascript
const { checkUserLogin, clearUserInfo, refreshToken } = require('../../utils/request.js');

// 检查用户登录状态
if (!checkUserLogin()) {
  // 引导用户登录
}

// 手动刷新token
refreshToken()
  .then(() => {
    console.log('Token刷新成功');
  })
  .catch(error => {
    console.log('Token刷新失败', error);
    clearUserInfo(); // 清除用户信息
  });
```

## 2. 车牌颜色判断逻辑

### 新增功能

#### 2.1 车牌类型检测
支持检测多种车牌类型：
- **普通车牌**: 7位，蓝底白字
- **小型新能源车牌**: 8位，第3位为D或F，绿底黑字
- **大型新能源车牌**: 8位，后6位包含字母，绿底黑字
- **警用车牌**: 7位，以"警"结尾，白底黑字
- **军用车牌**: 7位，特殊格式，白底黑字

#### 2.2 智能颜色推荐
根据车牌类型自动推荐对应的标准颜色：
- 新能源车牌推荐绿色
- 普通车牌推荐蓝色
- 警用/军用车牌推荐白色

### 使用方法

```javascript
const vehicleApi = require('../../api/vehicleApi.js');

// 检测车牌类型
const plateType = vehicleApi.getPlateType('苏AD12345');
console.log(plateType);
// 输出: { type: 'small_new_energy', isNewEnergy: true, description: '小型新能源车牌' }

// 获取推荐颜色
const colors = vehicleApi.getRecommendedColors('苏AD12345');
console.log(colors);
// 输出包含推荐颜色的数组，推荐颜色有 recommended: true 标记

// 向后兼容的新能源检测
const isNewEnergy = vehicleApi.isNewEnergyPlate('苏AD12345');
console.log(isNewEnergy); // true
```

## 3. 车牌输入交互优化

### 新增功能

#### 3.1 灵活的位置编辑
- 用户可以点击任意车牌框位置进行编辑
- 点击时该框高亮显示，表示当前编辑位置
- 支持跳转编辑，不必按顺序输入

#### 3.2 智能输入流程
- 点击省份框：跳转到省份选择界面
- 点击城市代码框：跳转到城市代码选择界面
- 点击号码框：跳转到数字字母混合键盘
- 修改完成后按逻辑继续输入

#### 3.3 实时颜色推荐
- 输入车牌号码时实时检测车牌类型
- 自动更新颜色推荐列表
- 如果没有选择颜色，自动选择推荐颜色

### 界面改进

#### 3.4 颜色选择器优化
- 分为"推荐颜色"和"所有颜色"两个区域
- 推荐颜色有特殊标识和说明
- 显示车牌类型提示信息

## 测试方法

### 运行测试
```bash
# 在项目根目录下运行
node test/vehicle-features-test.js
```

### 手动测试场景

1. **Token管理测试**
   - 清除本地存储，重新进入小程序
   - 模拟token过期，观察自动刷新
   - 网络异常时的处理

2. **车牌类型测试**
   - 输入不同类型的车牌号码
   - 观察颜色推荐是否正确
   - 检查新能源车牌的特殊样式

3. **交互测试**
   - 点击不同位置的车牌框
   - 测试编辑流程的连贯性
   - 验证高亮显示效果

## 注意事项

1. **兼容性**: 所有新功能都保持向后兼容
2. **性能**: 车牌检测和颜色推荐都是本地计算，不影响性能
3. **用户体验**: 自动推荐颜色，减少用户操作步骤
4. **错误处理**: 完善的错误处理和用户提示

## 后续优化建议

1. 添加更多特殊车牌类型支持
2. 支持车牌号码格式验证提示
3. 添加车牌输入历史记录
4. 优化颜色选择器的视觉效果
