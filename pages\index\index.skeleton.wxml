<!--
此文件为开发者工具生成，生成时间: 2025/5/28上午10:48:18
使用方法：
在 D:\BuWorkSpace\物业侧小程序开发（副本）\miniprogram\pages\index\index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 D:\BuWorkSpace\物业侧小程序开发（副本）\miniprogram\pages\index\index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view class="container page-bottom-safe-area" data-darkmode="false">
      <view class="header-bg sk-image"></view>
      <view class="welcome-section">
        <view class="welcome-header">
          <view class="community-selector">
            <text class="community-name sk-transparent sk-text-16-6667-545 sk-text">选择小区</text>
            <text class="community-arrow sk-transparent sk-opacity">▼</text>
          </view>
          <view class="welcome-user">
            <image class="scan-code-icon sk-image" mode="aspectFit"></image>
          </view>
        </view>
        <view class="weather-mood">
          <view class="weather-display">
            <text class="weather-temp sk-transparent sk-text-16-6667-710 sk-text">2°C</text>
            <view class="weather-icon">
              <view class="icon-weather-snowy"></view>
            </view>
          </view>
          <text class="mood-message sk-transparent sk-text-16-6667-851 sk-text">上午好，雪花纷飞，愿您的生活充满温暖与惊喜</text>
        </view>
      </view>
      <view class="banner-carousel">
        <view class="banner-loading">
          <view class="loading-text sk-transparent sk-text-16-6667-884 sk-text">正在加载轮播图...</view>
        </view>
      </view>
      <view class="card">
        <view class="section-title sk-transparent">
          快捷服务
          <view class="more sk-transparent sk-text-16-6667-733 sk-text">全部</view>
        </view>
        <view class="quick-services">
          <view class="service-item">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-972 sk-text" style="background-position-x: 50%;">物业缴费</view>
          </view>
          <view class="service-item" data-url="/servicePackage/pages/repair/repair">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-738 sk-text" style="background-position-x: 50%;">报事报修</view>
          </view>
          <view class="service-item" data-url="/servicePackage/pages/access/access">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-891 sk-text" style="background-position-x: 50%;">智慧通行</view>
          </view>
          <view class="service-item">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-207 sk-text" style="background-position-x: 50%;">物业管理</view>
          </view>
          <view class="service-item">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-802 sk-text" style="background-position-x: 50%;">访客登记</view>
          </view>
          <view class="service-item" data-url="/servicePackage/pages/recycle/green/index">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-755 sk-text" style="background-position-x: 50%;">绿色循环</view>
          </view>
          <view class="service-item" data-url="/communityPackage/pages/community/service/index">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-747 sk-text" style="background-position-x: 50%;">便民服务</view>
          </view>
          <view class="service-item" data-url="/servicePackage/pages/renovation/index/index">
            <image class="service-icon sk-image" mode="aspectFit"></image>
            <view class="service-name sk-transparent sk-text-8-3333-310 sk-text" style="background-position-x: 50%;">装修报备</view>
          </view>
        </view>
      </view>
      <view class="section-title sk-transparent">
        社区活动
        <view class="more sk-transparent sk-text-16-6667-557 sk-text">更多</view>
      </view>
      <view class="community-events">
        <view class="event-card" data-event="[object Object]">
          <view class="event-image" style="background-image: url('https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=500&amp;q=60');"></view>
        </view>
      </view>
    </view>
    <view class="message-detail-modal" data-darkmode="false">
      <view class="message-detail-content" data-darkmode="false">
        <view class="message-detail-header">
          <view class="message-detail-icon">
            <view class="icon-megaphone"></view>
          </view>
          <view class="message-detail-title-container">
            <view class="message-detail-title"></view>
            <view class="message-detail-time"></view>
          </view>
        </view>
        <view class="message-detail-body">
          <view class="message-detail-text"></view>
        </view>
        <view class="message-detail-footer">
          <button class="message-detail-close-btn sk-transparent sk-button sk-pseudo sk-pseudo-circle">我知道了</button>
        </view>
      </view>
    </view>
    <view class="property-auth-modal">
      <view class="property-auth-content">
        <view class="modal-title sk-transparent sk-text-16-6667-796 sk-text sk-pseudo sk-pseudo-circle">物业管理登录</view>
        <view class="input-group sk-pseudo sk-pseudo-circle">
          <view class="modal-input sk-image" placeholder="请输入账号" value="true"></view>
        </view>
        <view class="input-group sk-pseudo sk-pseudo-circle">
          <view class="modal-input sk-image" password="true" placeholder="请输入密码" value="true"></view>
          <view class="input-action">
            <view class="icon-eye-off"></view>
          </view>
        </view>
        <view class="remember-account">
          <view class="checkbox">
            <view class="checkbox-inner"></view>
          </view>
          <text class="sk-transparent sk-text-16-6667-691 sk-text">记住账号密码</text>
        </view>
        <view class="modal-buttons">
          <button class="modal-button cancel-button sk-transparent sk-button sk-pseudo sk-pseudo-circle">取消</button>
          <button class="modal-button confirm-button sk-transparent sk-pseudo sk-pseudo-circle sk-image">登录</button>
        </view>
      </view>
    </view>
  </view>
</template>