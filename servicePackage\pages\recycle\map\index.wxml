<!--附近回收点地图页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- Banner内容 -->
  <view class="banner-content">
    <view class="banner-desc">查找附近垃圾回收站点</view>
  </view>

  <!-- 地图组件 -->
  <view class="map-container">
    <map id="recycleMap"
         class="map"
         longitude="{{longitude}}"
         latitude="{{latitude}}"
         scale="{{scale}}"
         markers="{{markers}}"
         show-location="true"
         bindmarkertap="markerTap">
    </map>

    <view class="map-controls">
      <view class="control-btn location-btn" bindtap="moveToLocation">
        <view class="location-icon"></view>
      </view>
      <view class="control-btn zoom-in-btn" bindtap="zoomIn">
        <view class="zoom-in-icon"></view>
      </view>
      <view class="control-btn zoom-out-btn" bindtap="zoomOut">
        <view class="zoom-out-icon"></view>
      </view>
    </view>
  </view>

  <!-- 回收点列表 -->
  <view class="list-container">
    <view class="list-header">
      <view class="list-title">附近回收点</view>
      <view class="list-filter" bindtap="showFilterOptions">
        <view class="filter-text">{{currentFilter}}</view>
        <view class="filter-icon"></view>
      </view>
    </view>

    <view class="recycle-list">
      <view class="recycle-item" wx:for="{{recyclePoints}}" wx:key="id" bindtap="selectPoint" data-id="{{item.id}}">
        <view class="item-icon {{item.type}}-icon"></view>
        <view class="item-info">
          <view class="item-name">{{item.name}}</view>
          <view class="item-address">{{item.address}}</view>
          <view class="item-tags">
            <view class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</view>
          </view>
        </view>
        <view class="item-distance">{{item.distance}}km</view>
      </view>
    </view>
  </view>

  <!-- 回收点详情弹窗 -->
  <view class="point-detail" wx:if="{{selectedPoint}}" catchtap="closeDetail">
    <view class="detail-card" catchtap="stopPropagation">
      <view class="detail-header">
        <view class="detail-name">{{selectedPoint.name}}</view>
        <view class="detail-close" bindtap="closeDetail">×</view>
      </view>

      <view class="detail-content">
        <view class="detail-item">
          <view class="detail-label">地址</view>
          <view class="detail-value">{{selectedPoint.address}}</view>
        </view>

        <view class="detail-item">
          <view class="detail-label">营业时间</view>
          <view class="detail-value">{{selectedPoint.hours}}</view>
        </view>

        <view class="detail-item">
          <view class="detail-label">联系电话</view>
          <view class="detail-value phone-value" bindtap="callPhone" data-phone="{{selectedPoint.phone}}">{{selectedPoint.phone}}</view>
        </view>

        <view class="detail-item">
          <view class="detail-label">可回收物品</view>
          <view class="detail-tags">
            <view class="detail-tag" wx:for="{{selectedPoint.acceptItems}}" wx:key="*this">{{item}}</view>
          </view>
        </view>
      </view>

      <view class="detail-actions">
        <view class="action-btn share-btn" bindtap="sharePoint">
          <view class="share-icon"></view>
          <view class="action-text">分享</view>
        </view>
        <view class="action-btn navigate-btn" bindtap="navigateToPoint">
          <view class="navigate-icon"></view>
          <view class="action-text">导航</view>
        </view>
      </view>
    </view>
  </view>
</view>
