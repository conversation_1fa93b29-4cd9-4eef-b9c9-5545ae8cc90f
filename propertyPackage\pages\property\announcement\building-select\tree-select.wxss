/* 楼栋树形选择页样式 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部操作区预留空间 */
}

/* 搜索框 */
.search-bar {
  padding: 24rpx 32rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input-wrap {
  display: flex;
  align-items: center;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

/* 全体住户选项 */
.global-option {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;
}

.global-option.selected {
  background-color: rgba(255, 140, 0, 0.05);
}

.option-text {
  font-size: 32rpx;
  margin-left: 16rpx;
}

/* 已选数量提示 */
.selected-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: rgba(255, 140, 0, 0.1);
  font-size: 28rpx;
  color: #ff8c00;
}

.view-selected {
  font-size: 28rpx;
  color: #ff8c00;
  text-decoration: underline;
}

/* 穿梭选择区域 */
.transfer-container {
  display: flex;
  height: calc(100vh - 300rpx); /* 减去其他区域的高度 */
  position: relative;
}

/* 左侧：树形列表 */
.tree-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 16rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tree-header {
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-bottom: 1rpx solid #eee;
}

.tree-content {
  flex: 1;
  padding: 16rpx 0;
}

.tree-node {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
}

.building-node {
  font-weight: 500;
}

.unit-node {
  padding-left: 48rpx;
}

.room-node {
  padding-left: 72rpx;
  background-color: rgba(255, 140, 0, 0.05);
  margin-left: 24rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
}

.expand-icon {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
  background-size: 24rpx;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s;
  margin-right: 8rpx;
  border-radius: 50%;
}

.expand-icon.expanded {
  transform: rotate(90deg);
  background-color: rgba(255, 140, 0, 0.1);
}

.expand-icon.placeholder {
  background: none;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin: 0 16rpx;
}

.checkbox.checked {
  background-color: #ff8c00;
  border-color: #ff8c00;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: 24rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.node-name {
  flex: 1;
  font-size: 28rpx;
}

/* 右侧：已选列表 */
.selected-container {
  position: absolute;
  top: 0;
  right: -100%;
  width: 80%;
  height: 100%;
  background-color: #fff;
  box-shadow: -2rpx 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: right 0.3s;
  z-index: 20;
}

.selected-container.show {
  right: 0;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-bottom: 1rpx solid #eee;
}

.clear-selected {
  font-size: 28rpx;
  color: #ff8c00;
}

.selected-content {
  flex: 1;
  padding: 16rpx 24rpx;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.selected-name {
  font-size: 28rpx;
}

.remove-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

.empty-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23CCCCCC' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.cancel {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16rpx;
}

.confirm {
  background-color: #ff8c00;
  color: #fff;
  margin-left: 16rpx;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #1f1f1f;
  color: #fff;
}

.dark-mode .search-bar,
.dark-mode .global-option,
.dark-mode .tree-container,
.dark-mode .selected-container,
.dark-mode .bottom-actions {
  background-color: #2d2d2d;
}

.dark-mode .tree-header,
.dark-mode .selected-header {
  border-bottom-color: #3d3d3d;
}

.dark-mode .selected-item {
  border-bottom-color: #3d3d3d;
}

.dark-mode .search-input-wrap {
  background-color: #3d3d3d;
}

.dark-mode .node-name,
.dark-mode .selected-name {
  color: #fff;
}

.dark-mode .checkbox {
  border-color: #555;
}

.dark-mode .cancel {
  background-color: #3d3d3d;
  color: #ddd;
}
