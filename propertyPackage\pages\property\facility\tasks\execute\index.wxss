/* 巡检执行页样式 */

/* 基本容器 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #ff8c00;
  color: #fff;
  z-index: 100;
  padding-left: 16px;
  padding-right: 16px;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.nav-placeholder {
  width: 40px;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding-top: 44px; /* 导航栏高度 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 任务信息 */
.task-info {
  margin-top: 44px; /* 导航栏高度 */
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.task-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.task-meta {
  display: flex;
  margin-bottom: 20rpx;
}

.task-type, .task-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.task-type.inspection {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.task-type.maintenance {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.task-status.pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.task-status.processing {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.task-status.completed {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.task-progress-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #ff8c00;
  border-radius: 8rpx;
}

.task-progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

/* 巡检点信息 */
.point-info {
  margin-top: 20rpx;
  padding: 0 30rpx;
}

.point-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.point-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.point-scan {
  display: flex;
  align-items: center;
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
}

.point-scan.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.scan-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 3H5C3.89543 3 3 3.89543 3 5V7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M17 3H19C20.1046 3 21 3.89543 21 5V7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 17V19C3 20.1046 3.89543 21 5 21H7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 17V19C21 20.1046 20.1046 21 19 21H17' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 12H17' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.point-scan.success .scan-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11L12 14L22 4M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16' stroke='%234CAF50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.point-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.point-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.point-code {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.point-location {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 检查项列表 */
.check-list {
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.check-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.check-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.check-number {
  font-size: 24rpx;
  color: #999;
}

.check-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.check-result {
  display: flex;
}

.result-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.result-option:last-child {
  margin-right: 0;
}

.result-option.active.normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.result-option.active.abnormal {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.option-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.option-icon.normal {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11L12 14L22 4M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.result-option.active .option-icon.normal {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11L12 14L22 4M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16' stroke='%234CAF50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.option-icon.abnormal {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 9V13M12 17H12.01M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.result-option.active .option-icon.abnormal {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 9V13M12 17H12.01M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23F44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.abnormal-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: rgba(244, 67, 54, 0.05);
  border-radius: 8rpx;
  border-left: 6rpx solid #F44336;
}

.abnormal-description {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.abnormal-images {
  display: flex;
  flex-wrap: wrap;
}

.abnormal-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-button {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-button {
  background-color: #ff8c00;
  color: #fff;
}

.complete-button {
  background-color: #4CAF50;
  color: #fff;
}

/* 异常表单弹窗 */
.abnormal-form {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.abnormal-form.active {
  visibility: visible;
  opacity: 1;
}

.abnormal-form-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.abnormal-form-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.abnormal-form.active .abnormal-form-container {
  transform: translateY(0);
}

.abnormal-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.abnormal-form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.abnormal-form-content {
  padding: 30rpx;
}

.abnormal-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.upload-container {
  margin-bottom: 20rpx;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 24rpx 24rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.upload-button {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 5V19M5 12H19' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.upload-button text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.abnormal-form-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.form-button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.submit-button {
  background-color: #ff8c00;
  color: #fff;
}

/* 确认对话框 */
.confirm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.confirm-dialog.active {
  visibility: visible;
  opacity: 1;
}

.confirm-dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.confirm-dialog-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.confirm-dialog-title {
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.confirm-dialog-content {
  padding: 0 30rpx 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.confirm-dialog-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.dialog-button {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-button {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-button {
  color: #ff8c00;
  font-weight: 500;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .custom-nav {
  background-color: #ff8c00;
}

.darkMode .task-info,
.darkMode .point-card,
.darkMode .check-item,
.darkMode .bottom-actions,
.darkMode .abnormal-form-container,
.darkMode .confirm-dialog-container {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .task-title,
.darkMode .point-title,
.darkMode .point-name,
.darkMode .section-title,
.darkMode .check-name,
.darkMode .abnormal-form-title,
.darkMode .upload-title,
.darkMode .confirm-dialog-title,
.darkMode .abnormal-description {
  color: #f5f5f7;
}

.darkMode .task-progress-text,
.darkMode .point-code,
.darkMode .point-location,
.darkMode .check-number,
.darkMode .check-description,
.darkMode .confirm-dialog-content {
  color: #8e8e93;
}

.darkMode .task-progress-bar,
.darkMode .result-option,
.darkMode .abnormal-textarea,
.darkMode .upload-button,
.darkMode .cancel-button {
  background-color: #3a3a3c;
}

.darkMode .upload-button text {
  color: #8e8e93;
}

.darkMode .abnormal-form-header,
.darkMode .abnormal-form-footer,
.darkMode .confirm-dialog-footer,
.darkMode .cancel-button {
  border-color: #3a3a3c;
}
