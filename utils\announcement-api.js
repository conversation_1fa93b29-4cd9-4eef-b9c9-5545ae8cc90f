/**
 * 公告API服务类
 * 提供公告管理模块所需的所有API接口
 */

const apiService = require('./api-service');

// API路径
const API_PATHS = {
  // 公告相关
  ANNOUNCEMENTS: '/api/announcements',
  ANNOUNCEMENT_DETAIL: '/api/announcements/{id}',
  ANNOUNCEMENT_PIN: '/api/announcements/{id}/pin',
  ANNOUNCEMENT_UNPIN: '/api/announcements/{id}/unpin',
  ANNOUNCEMENT_PUBLISH: '/api/announcements/{id}/publish',
  ANNOUNCEMENT_RETRACT: '/api/announcements/{id}/retract',
  ANNOUNCEMENT_DRAFT: '/api/announcements/draft',

  // 楼栋相关
  BUILDINGS: '/api/buildings',

  // 文件上传
  UPLOAD_IMAGE: '/api/upload/image'
};

/**
 * 替换URL中的路径参数
 * @param {string} url 包含路径参数的URL
 * @param {Object} params 路径参数对象
 * @returns {string} 替换后的URL
 */
function replacePathParams(url, params) {
  let result = url;

  Object.keys(params).forEach(key => {
    result = result.replace(`{${key}}`, params[key]);
  });

  return result;
}

/**
 * 获取公告列表
 * @param {Object} params 查询参数 {page, pageSize, type, keyword, status}
 * @returns {Promise<Object>} 公告列表和分页信息
 */
function getAnnouncementList(params = {}) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取公告列表
      let allAnnouncements = wx.getStorageSync('announcements') || [];

      // 如果本地存储中没有数据，使用默认的模拟数据
      if (allAnnouncements.length === 0) {
        const mockData = getMockAnnouncementList(params);
        allAnnouncements = mockData.list;
        // 保存到本地存储
        wx.setStorageSync('announcements', allAnnouncements);

        resolve({
          data: mockData
        });
        return;
      }

      // 根据查询参数筛选数据
      let filteredAnnouncements = [...allAnnouncements];

      // 根据类型筛选
      if (params.type) {
        filteredAnnouncements = filteredAnnouncements.filter(item => item.type === params.type);
      }

      // 根据状态筛选
      if (params.status) {
        filteredAnnouncements = filteredAnnouncements.filter(item => item.status === params.status);
      }

      // 根据关键词筛选
      if (params.keyword) {
        const lowerKeyword = params.keyword.toLowerCase();
        filteredAnnouncements = filteredAnnouncements.filter(item =>
          item.title.toLowerCase().includes(lowerKeyword) ||
          (item.content && item.content.toLowerCase().includes(lowerKeyword))
        );
      }

      // 计算分页
      const { page = 1, pageSize = 10 } = params;
      const total = filteredAnnouncements.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const list = filteredAnnouncements.slice(startIndex, endIndex);

      resolve({
        data: {
          list,
          total,
          currentPage: page,
          totalPages,
          pageSize
        }
      });
    }, 500);
  });
}

/**
 * 生成模拟公告列表数据
 * @param {Object} params 查询参数
 * @returns {Object} 模拟数据
 */
function getMockAnnouncementList(params = {}) {
  const { page = 1, pageSize = 10, type, keyword, status } = params;

  // 生成模拟公告数据
  const allAnnouncements = [];

  // 真实的公告标题和内容
  const announcementData = [
    {
      title: "小区电梯维修通知",
      content: "尊敬的业主：\n因小区1号楼电梯需要进行例行维护保养，将于2023年5月9日上午9:00-12:00暂停使用。维修期间，请各位业主朋友耐心等待或使用楼梯，由此给您带来的不便，敬请谅解。\n物业管理处",
      type: "property_notice"
    },
    {
      title: "端午节活动预告",
      content: "尊敬的业主：\n端午节即将到来，小区将于2023年6月22日上午10:00在中心广场举办包粽子比赛活动，欢迎各位业主朋友报名参加。活动现场还将提供免费的粽子和茶水，期待您的参与！\n物业管理处",
      type: "activity_notice"
    },
    {
      title: "紧急通知：台风预警",
      content: "紧急通知：\n根据气象部门预报，本周末将有台风登陆，预计带来强风暴雨天气。请各位业主做好防范准备，关好门窗，清理阳台杂物，尽量减少外出。物业已启动应急预案，24小时值班电话：0755-12345678。\n物业管理处",
      type: "emergency_notice"
    },
    {
      title: "物业费缴纳通知",
      content: "尊敬的业主：\n2023年第二季度物业费已开始缴纳，缴费截止日期为2023年6月30日。请各位业主及时通过小程序或到物业服务中心缴纳。按时缴费的业主将获赠环保购物袋一个。\n物业管理处",
      type: "property_notice"
    },
    {
      title: "小区游泳池开放通知",
      content: "尊敬的业主：\n随着夏季到来，小区游泳池将于2023年6月1日正式开放，开放时间为每日9:00-21:00。使用游泳池需持有效门禁卡，并遵守游泳池使用规则。祝您游泳愉快！\n物业管理处",
      type: "property_notice"
    },
    {
      title: "社区义诊活动",
      content: "尊敬的业主：\n为关爱社区居民健康，我们将于2023年5月15日上午9:00-11:30在小区会所举办义诊活动。届时将有专业医生提供免费血压、血糖检测及健康咨询服务，欢迎各位业主参加。\n物业管理处",
      type: "activity_notice"
    },
    {
      title: "消防安全演习通知",
      content: "尊敬的业主：\n为提高小区消防安全意识，我们将于2023年5月20日上午10:00在小区中心广场举行消防安全演习。演习期间将模拟火灾报警和疏散，请各位业主积极配合。\n物业管理处",
      type: "property_notice"
    },
    {
      title: "紧急通知：自来水检修",
      content: "紧急通知：\n因市政自来水管道检修，我小区将于2023年5月10日凌晨1:00-5:00暂停供水。请各位业主提前做好储水准备。如有疑问，请联系物业服务中心。\n物业管理处",
      type: "emergency_notice"
    },
    {
      title: "小区绿化养护通知",
      content: "尊敬的业主：\n为保持小区环境整洁美观，我们将于2023年5月12日-14日对小区绿化带进行修剪和养护。工作时间为每日8:00-17:00，期间可能会产生一些噪音，请各位业主谅解。\n物业管理处",
      type: "property_notice"
    },
    {
      title: "六一儿童节活动",
      content: "尊敬的业主：\n六一儿童节即将到来，小区将于2023年6月1日下午3:00-5:00在中心广场举办儿童游园会，活动包括小丑表演、气球造型、亲子游戏等，欢迎小区内的小朋友们参加。\n物业管理处",
      type: "activity_notice"
    },
    {
      title: "地下车库清洗通知",
      content: "尊敬的业主：\n为保持车库环境整洁，我们将于2023年5月18日对地下车库进行全面清洗。清洗时间为上午9:00-下午3:00，请车主朋友在此期间将车辆移出，以免影响清洗工作和车辆被溅湿。\n物业管理处",
      type: "property_notice"
    },
    {
      title: "紧急通知：电力检修",
      content: "紧急通知：\n接供电部门通知，因电网检修需要，我小区将于2023年5月16日上午10:00-下午2:00计划性停电。请各位业主做好相关准备，暂停使用电梯，谨防火灾。\n物业管理处",
      type: "emergency_notice"
    }
  ];

  // 已发布公告
  for (let i = 0; i < 12; i++) {
    const dataIndex = i % announcementData.length;
    const isPinned = i < 2;

    // 创建日期递减，最新的在前面
    const publishTime = new Date();
    publishTime.setDate(publishTime.getDate() - Math.floor(i / 2));
    publishTime.setHours(9 + (i % 8));
    publishTime.setMinutes(Math.floor(Math.random() * 60));

    const formattedDate = `${publishTime.getFullYear()}/${String(publishTime.getMonth() + 1).padStart(2, '0')}/${String(publishTime.getDate()).padStart(2, '0')} ${String(publishTime.getHours()).padStart(2, '0')}:${String(publishTime.getMinutes()).padStart(2, '0')}:${String(publishTime.getSeconds()).padStart(2, '0')}`;

    allAnnouncements.push({
      id: `published-${i + 1}`,
      title: announcementData[dataIndex].title,
      content: announcementData[dataIndex].content,
      type: announcementData[dataIndex].type,
      targetScope: i % 3 === 0 ? 'specific_buildings' : 'all_residents',
      targetBuildingIds: i % 3 === 0 ? ['1', '2', '3'] : [],
      imageUrls: i % 4 === 0 ? [
        'https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia0UoEfVgxcUJR7tBMuYpicnwMnYxRgILqnkibicgpDTGhBTQUAKXKmRwzlMvOdiayIIRzhvTlw/0',
        'https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia0UoEfVgxcUJR7tBMCZB9Wv6ITZYMgKvuGrPCaYfnfHmkiaaYRAL4nxZGgvH0mVmzfDZ2Ng/0'
      ] : [],
      isPinned,
      status: 'published',
      publishTime: formattedDate,
      createdAt: formattedDate,
      updatedAt: formattedDate,
      readCount: 10 + Math.floor(Math.random() * 90)
    });
  }

  // 草稿公告
  for (let i = 0; i < 3; i++) {
    const dataIndex = (i + 5) % announcementData.length;

    // 更新时间递减
    const updatedTime = new Date();
    updatedTime.setHours(updatedTime.getHours() - (i * 2 + 1));
    updatedTime.setMinutes(Math.floor(Math.random() * 60));

    const formattedDate = `${updatedTime.getFullYear()}/${String(updatedTime.getMonth() + 1).padStart(2, '0')}/${String(updatedTime.getDate()).padStart(2, '0')} ${String(updatedTime.getHours()).padStart(2, '0')}:${String(updatedTime.getMinutes()).padStart(2, '0')}:${String(updatedTime.getSeconds()).padStart(2, '0')}`;

    allAnnouncements.push({
      id: `draft-${i + 1}`,
      title: `【草稿】${announcementData[dataIndex].title}`,
      content: announcementData[dataIndex].content,
      type: announcementData[dataIndex].type,
      targetScope: 'all_residents',
      targetBuildingIds: [],
      imageUrls: [],
      isPinned: false,
      status: 'draft',
      createdAt: formattedDate,
      updatedAt: formattedDate
    });
  }

  // 根据查询参数筛选数据
  let filteredAnnouncements = [...allAnnouncements];

  // 根据类型筛选
  if (type) {
    filteredAnnouncements = filteredAnnouncements.filter(item => item.type === type);
  }

  // 根据状态筛选
  if (status) {
    filteredAnnouncements = filteredAnnouncements.filter(item => item.status === status);
  }

  // 根据关键词筛选
  if (keyword) {
    const lowerKeyword = keyword.toLowerCase();
    filteredAnnouncements = filteredAnnouncements.filter(item =>
      item.title.toLowerCase().includes(lowerKeyword) ||
      item.content.toLowerCase().includes(lowerKeyword)
    );
  }

  // 计算分页
  const total = filteredAnnouncements.length;
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const list = filteredAnnouncements.slice(startIndex, endIndex);

  return {
    list,
    total,
    currentPage: page,
    totalPages,
    pageSize
  };
}

/**
 * 获取公告详情
 * @param {string} id 公告ID
 * @returns {Promise<Object>} 公告详情
 */
function getAnnouncementDetail(id) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 首先尝试从最近的公告列表中获取
      const recentAnnouncement = getAnnouncementFromRecentList(id);

      if (recentAnnouncement) {
        // 如果找到了，直接返回
        console.log('从最近列表中获取公告详情', id, recentAnnouncement);
        resolve({
          data: recentAnnouncement
        });
      } else {
        // 否则生成模拟数据
        console.log('从模拟数据中获取公告详情', id);
        const mockData = getMockAnnouncementDetail(id);
        resolve({
          data: mockData
        });
      }
    }, 500);
  });
}

/**
 * 生成模拟公告详情数据
 * @param {string} id 公告ID
 * @returns {Object} 模拟数据
 */
function getMockAnnouncementDetail(id) {
  // 判断是否为草稿
  const isDraft = id.startsWith('draft-');
  const idNumber = parseInt(id.split('-')[1]);

  // 真实的公告详情数据
  const announcementDetails = {
    "小区电梯维修通知": {
      title: "小区电梯维修通知",
      content: `<p style="text-indent: 2em; line-height: 1.6;">尊敬的业主：</p>
<p style="text-indent: 2em; line-height: 1.6;">因小区1号楼电梯需要进行例行维护保养，将于2023年5月9日上午9:00-12:00暂停使用。维修期间，请各位业主朋友耐心等待或使用楼梯，由此给您带来的不便，敬请谅解。</p>
<p style="text-indent: 2em; line-height: 1.6;">本次维修主要内容包括：</p>
<ol style="margin-left: 2em; line-height: 1.6;">
  <li>电梯轿厢内部设施检查与维护</li>
  <li>电梯曳引系统检查与保养</li>
  <li>电梯控制系统检测与调试</li>
  <li>电梯安全装置检测与校准</li>
</ol>
<p style="text-indent: 2em; line-height: 1.6;">维修工作由专业电梯维保公司负责实施，物业将全程监督。如有疑问，请联系物业服务中心：0755-12345678。</p>
<p style="text-indent: 2em; line-height: 1.6;">感谢您的理解与配合！</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">物业管理处</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">2023年5月7日</p>`,
      type: "property_notice",
      imageUrls: [
        "https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia0UoEfVgxcUJR7tBMuYpicnwMnYxRgILqnkibicgpDTGhBTQUAKXKmRwzlMvOdiayIIRzhvTlw/0"
      ]
    },
    "端午节活动预告": {
      title: "端午节活动预告",
      content: `<p style="text-indent: 2em; line-height: 1.6;">尊敬的业主：</p>
<p style="text-indent: 2em; line-height: 1.6;">端午节即将到来，小区将于2023年6月22日上午10:00在中心广场举办包粽子比赛活动，欢迎各位业主朋友报名参加。活动现场还将提供免费的粽子和茶水，期待您的参与！</p>
<p style="text-indent: 2em; line-height: 1.6;"><strong>活动详情：</strong></p>
<p style="text-indent: 2em; line-height: 1.6;">1. 活动时间：2023年6月22日（星期四）上午10:00-12:00</p>
<p style="text-indent: 2em; line-height: 1.6;">2. 活动地点：小区中心广场</p>
<p style="text-indent: 2em; line-height: 1.6;">3. 活动内容：</p>
<ul style="margin-left: 4em; line-height: 1.6;">
  <li>包粽子比赛（提供粽叶、糯米等材料）</li>
  <li>端午习俗知识问答</li>
  <li>儿童折纸活动</li>
</ul>
<p style="text-indent: 2em; line-height: 1.6;">4. 报名方式：请于6月20日前通过小程序或到物业服务中心报名</p>
<p style="text-indent: 2em; line-height: 1.6;">5. 奖品设置：</p>
<ul style="margin-left: 4em; line-height: 1.6;">
  <li>一等奖（1名）：电饭煲一台</li>
  <li>二等奖（3名）：精美餐具套装</li>
  <li>三等奖（5名）：端午节礼品包</li>
  <li>参与奖：精美纪念品一份</li>
</ul>
<p style="text-indent: 2em; line-height: 1.6;">让我们一起度过一个欢乐祥和的端午佳节！</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">物业管理处</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">2023年6月15日</p>`,
      type: "activity_notice",
      imageUrls: [
        "https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia0UoEfVgxcUJR7tBMCZB9Wv6ITZYMgKvuGrPCaYfnfHmkiaaYRAL4nxZGgvH0mVmzfDZ2Ng/0",
        "https://mmbiz.qpic.cn/mmbiz_jpg/UicQ7HgWiaUb3Zib1Zia0UoEfVgxcUJR7tBMuYpicnwMnYxRgILqnkibicgpDTGhBTQUAKXKmRwzlMvOdiayIIRzhvTlw/0"
      ]
    },
    "紧急通知：台风预警": {
      title: "紧急通知：台风预警",
      content: `<p style="text-indent: 2em; line-height: 1.6; color: #ff0000;"><strong>紧急通知：</strong></p>
<p style="text-indent: 2em; line-height: 1.6;">根据气象部门预报，本周末将有台风登陆，预计带来强风暴雨天气。请各位业主做好防范准备，关好门窗，清理阳台杂物，尽量减少外出。物业已启动应急预案，24小时值班电话：0755-12345678。</p>
<p style="text-indent: 2em; line-height: 1.6;"><strong>防范措施建议：</strong></p>
<ol style="margin-left: 2em; line-height: 1.6;">
  <li>关好门窗，加固易被风吹动的物品</li>
  <li>清理阳台和窗台上的花盆等物品</li>
  <li>准备手电筒、充电宝等应急物品</li>
  <li>储备适量饮用水和食物</li>
  <li>尽量减少外出，特别是台风登陆期间</li>
  <li>地下车库可能出现积水，请将车辆停放在较高位置</li>
</ol>
<p style="text-indent: 2em; line-height: 1.6;">物业已做好以下准备工作：</p>
<ul style="margin-left: 2em; line-height: 1.6;">
  <li>检查排水系统，确保畅通</li>
  <li>准备沙袋、抽水泵等防汛设备</li>
  <li>加强巡逻，及时处理险情</li>
  <li>24小时值班，随时应对紧急情况</li>
</ul>
<p style="text-indent: 2em; line-height: 1.6;">如遇紧急情况，请立即联系物业应急电话：0755-12345678</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">物业管理处</p>
<p style="text-align: right; margin-right: 2em; line-height: 1.6;">2023年7月20日</p>`,
      type: "emergency_notice",
      imageUrls: []
    }
  };

  // 生成模拟数据
  if (isDraft) {
    // 草稿公告
    const updatedTime = new Date();
    updatedTime.setHours(updatedTime.getHours() - (idNumber * 2 + 1));
    updatedTime.setMinutes(Math.floor(Math.random() * 60));

    const formattedDate = `${updatedTime.getFullYear()}/${String(updatedTime.getMonth() + 1).padStart(2, '0')}/${String(updatedTime.getDate()).padStart(2, '0')} ${String(updatedTime.getHours()).padStart(2, '0')}:${String(updatedTime.getMinutes()).padStart(2, '0')}:${String(updatedTime.getSeconds()).padStart(2, '0')}`;

    // 从真实数据中选择一个作为草稿
    const titles = Object.keys(announcementDetails);
    const selectedTitle = titles[(idNumber + 2) % titles.length];
    const selectedDetail = announcementDetails[selectedTitle];

    return {
      id,
      title: `【草稿】${selectedTitle}`,
      content: selectedDetail.content,
      type: selectedDetail.type,
      targetScope: 'all_residents',
      targetBuildingIds: [],
      imageUrls: [],
      isPinned: false,
      status: 'draft',
      createdAt: formattedDate,
      updatedAt: formattedDate
    };
  } else {
    // 已发布公告
    const isPinned = idNumber <= 2;
    const publishTime = new Date();
    publishTime.setDate(publishTime.getDate() - Math.floor(idNumber / 2));
    publishTime.setHours(9 + (idNumber % 8));
    publishTime.setMinutes(Math.floor(Math.random() * 60));

    const formattedDate = `${publishTime.getFullYear()}/${String(publishTime.getMonth() + 1).padStart(2, '0')}/${String(publishTime.getDate()).padStart(2, '0')} ${String(publishTime.getHours()).padStart(2, '0')}:${String(publishTime.getMinutes()).padStart(2, '0')}:${String(publishTime.getSeconds()).padStart(2, '0')}`;

    // 从真实数据中选择一个
    const titles = Object.keys(announcementDetails);
    const selectedTitle = titles[idNumber % titles.length];
    const selectedDetail = announcementDetails[selectedTitle];

    return {
      id,
      title: selectedTitle,
      content: selectedDetail.content,
      type: selectedDetail.type,
      targetScope: idNumber % 3 === 0 ? 'specific_buildings' : 'all_residents',
      targetBuildingIds: idNumber % 3 === 0 ? ['1', '2', '3'] : [],
      imageUrls: selectedDetail.imageUrls || [],
      isPinned,
      status: 'published',
      publishTime: formattedDate,
      createdAt: formattedDate,
      updatedAt: formattedDate,
      readCount: 10 + Math.floor(Math.random() * 90)
    };
  }
}

/**
 * 创建公告
 * @param {Object} announcement 公告信息
 * @returns {Promise<Object>} 创建结果
 */
function createAnnouncement(announcement) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 生成新的公告ID
      const id = 'new-' + Date.now();

      // 创建新公告对象
      const newAnnouncement = {
        id,
        ...announcement,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 添加新公告
      announcements.unshift(newAnnouncement);

      // 保存到本地存储
      wx.setStorageSync('announcements', announcements);

      resolve({
        data: newAnnouncement
      });
    }, 500);
  });
}

/**
 * 更新公告
 * @param {string} id 公告ID
 * @param {Object} announcement 公告信息
 * @returns {Promise<Object>} 更新结果
 */
function updateAnnouncement(id, announcement) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 查找要更新的公告
      const index = announcements.findIndex(item => item.id === id);

      if (index !== -1) {
        // 更新公告
        const updatedAnnouncement = {
          ...announcements[index],
          ...announcement,
          id, // 确保ID不变
          updatedAt: new Date().toISOString()
        };

        // 替换原有公告
        announcements[index] = updatedAnnouncement;

        // 保存到本地存储
        wx.setStorageSync('announcements', announcements);

        resolve({
          data: updatedAnnouncement
        });
      } else {
        // 如果找不到公告，返回原始数据
        resolve({
          data: {
            id,
            ...announcement,
            updatedAt: new Date().toISOString()
          }
        });
      }
    }, 500);
  });
}

/**
 * 删除公告
 * @param {string} id 公告ID
 * @returns {Promise<Object>} 删除结果
 */
function deleteAnnouncement(id) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 过滤掉要删除的公告
      announcements = announcements.filter(item => item.id !== id);

      // 保存到本地存储
      wx.setStorageSync('announcements', announcements);

      resolve({
        data: {
          success: true,
          message: '删除成功'
        }
      });
    }, 500);
  });
}

/**
 * 置顶公告
 * @param {string} id 公告ID
 * @returns {Promise<Object>} 操作结果
 */
function pinAnnouncement(id) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 查找要置顶的公告
      const index = announcements.findIndex(item => item.id === id);

      if (index !== -1) {
        // 更新公告的置顶状态
        announcements[index].isPinned = true;

        // 保存到本地存储
        wx.setStorageSync('announcements', announcements);
      }

      resolve({
        data: {
          success: true,
          message: '置顶成功'
        }
      });
    }, 500);
  });
}

/**
 * 取消置顶公告
 * @param {string} id 公告ID
 * @returns {Promise<Object>} 操作结果
 */
function unpinAnnouncement(id) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 查找要取消置顶的公告
      const index = announcements.findIndex(item => item.id === id);

      if (index !== -1) {
        // 更新公告的置顶状态
        announcements[index].isPinned = false;

        // 保存到本地存储
        wx.setStorageSync('announcements', announcements);
      }

      resolve({
        data: {
          success: true,
          message: '取消置顶成功'
        }
      });
    }, 500);
  });
}

/**
 * 发布公告
 * @param {string} id 公告ID
 * @param {Object} params 发布参数 {scheduledAt} 可选，定时发布时间
 * @returns {Promise<Object>} 操作结果
 */
function publishAnnouncement(id, params = {}) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 查找要发布的公告
      const index = announcements.findIndex(item => item.id === id);

      if (index !== -1) {
        // 更新公告的状态
        if (params.scheduledAt) {
          announcements[index].status = 'scheduled';
          announcements[index].scheduledAt = params.scheduledAt;
        } else {
          announcements[index].status = 'published';
          announcements[index].publishTime = new Date().toISOString();
        }

        // 保存到本地存储
        wx.setStorageSync('announcements', announcements);
      }

      resolve({
        data: {
          success: true,
          message: params.scheduledAt ? '定时发布设置成功' : '发布成功'
        }
      });
    }, 500);
  });
}

/**
 * 撤回公告
 * @param {string} id 公告ID
 * @returns {Promise<Object>} 操作结果
 */
function retractAnnouncement(id) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 从本地存储获取现有公告列表
      let announcements = wx.getStorageSync('announcements') || [];

      // 查找要撤回的公告
      const index = announcements.findIndex(item => item.id === id);

      if (index !== -1) {
        // 更新公告的状态
        announcements[index].status = 'draft';

        // 保存到本地存储
        wx.setStorageSync('announcements', announcements);
      }

      resolve({
        data: {
          success: true,
          message: '撤回成功'
        }
      });
    }, 500);
  });
}

/**
 * 保存草稿
 * @param {Object} draft 草稿信息
 * @returns {Promise<Object>} 保存结果
 */
function saveDraft(draft) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      resolve({
        data: {
          id: draft.id || 'draft-' + Date.now(),
          ...draft,
          status: 'draft',
          updatedAt: new Date().toISOString()
        }
      });
    }, 500);
  });
}

/**
 * 获取楼栋列表
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 楼栋列表
 */
function getBuildingList(params = {}) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟延迟
    setTimeout(() => {
      // 生成模拟楼栋数据
      const buildings = [];
      for (let i = 1; i <= 20; i++) {
        buildings.push({
          id: i.toString(),
          name: `${String.fromCharCode(64 + Math.ceil(i / 5))}栋${i % 5 === 0 ? 5 : i % 5}单元`,
          address: `小区${Math.ceil(i / 10)}区`
        });
      }

      resolve({
        data: buildings
      });
    }, 500);
  });
}

/**
 * 上传公告图片
 * @param {string} filePath 图片路径
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise<Object>} 上传结果
 */
function uploadImage(filePath, onProgress) {
  // 使用模拟数据，避免API请求失败
  return new Promise((resolve) => {
    // 模拟上传进度
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (onProgress) {
        onProgress({ progress });
      }

      if (progress >= 100) {
        clearInterval(interval);

        // 模拟上传完成
        setTimeout(() => {
          resolve({
            url: filePath // 直接返回本地路径作为URL
          });
        }, 200);
      }
    }, 200);
  });
}

/**
 * 存储最近查看的公告列表，用于确保详情页显示正确的内容
 */
let recentAnnouncementList = [];

/**
 * 设置最近的公告列表
 * @param {Array} list 公告列表
 */
function setRecentAnnouncementList(list) {
  recentAnnouncementList = list || [];
}

/**
 * 根据ID从最近的公告列表中获取公告
 * @param {string} id 公告ID
 * @returns {Object|null} 公告对象或null
 */
function getAnnouncementFromRecentList(id) {
  if (!recentAnnouncementList || recentAnnouncementList.length === 0) {
    return null;
  }

  return recentAnnouncementList.find(item => item.id === id) || null;
}

// 导出模块
module.exports = {
  getAnnouncementList,
  getAnnouncementDetail,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  pinAnnouncement,
  unpinAnnouncement,
  publishAnnouncement,
  retractAnnouncement,
  saveDraft,
  getBuildingList,
  uploadImage,
  setRecentAnnouncementList,
  getAnnouncementFromRecentList
};
