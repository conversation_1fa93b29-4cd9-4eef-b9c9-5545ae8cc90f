# WXML语法错误修复说明

## 问题描述

在添加居民页面的WXML文件中出现了语法错误：
```
Bad value with message: unexpected `>` at pos48.
```

## 错误原因

WXML不支持复杂的JavaScript表达式，特别是：
1. 三元运算符中的 `>` 符号
2. 数组的 `map()` 方法
3. 数组的 `some()` 方法

## 错误代码

```xml
<!-- 错误的写法 -->
<text class="tag-value">
  {{selectedTags.length > 0 ? selectedTags.map(tag => tag.nameCn).join('、') : '请选择人员标签'}}
</text>

<view class="tag-item {{selectedTags.some(tag => tag.nameEn === item.nameEn) ? 'selected' : ''}}">
```

## 修复方案

### 1. 人员标签显示文本
将复杂的JavaScript表达式移到JS文件中处理：

**WXML修复后：**
```xml
<text class="tag-value">{{selectedTagsText || '请选择人员标签'}}</text>
```

**JS中添加：**
```javascript
data: {
  selectedTagsText: '', // 选中标签的显示文本
}

// 在切换标签时更新显示文本
const selectedTagsText = selectedTags.map(tag => tag.nameCn).join('、');
this.setData({
  selectedTagsText: selectedTagsText
});
```

### 2. 标签选中状态判断
将选中状态的判断逻辑移到JS文件中：

**WXML修复后：**
```xml
<view class="tag-item {{item.selected ? 'selected' : ''}}" wx:for="{{residentTagWithSelected}}">
  <view class="tag-check" wx:if="{{item.selected}}">✓</view>
</view>
```

**JS中添加：**
```javascript
data: {
  residentTagWithSelected: [], // 带选中状态的标签列表
}

// 更新带选中状态的标签列表
updateResidentTagWithSelected: function() {
  const residentTag = this.data.residentTag;
  const selectedTags = this.data.selectedTags;
  
  const residentTagWithSelected = residentTag.map(tag => ({
    ...tag,
    selected: selectedTags.some(selectedTag => selectedTag.nameEn === tag.nameEn)
  }));

  this.setData({
    residentTagWithSelected: residentTagWithSelected
  });
}
```

## 修复后的效果

1. **WXML语法正确**：不再使用复杂的JavaScript表达式
2. **功能正常**：标签选择和显示功能完全正常
3. **性能优化**：减少了WXML中的计算，提升渲染性能

## 注意事项

1. WXML中只能使用简单的表达式和条件判断
2. 复杂的数据处理应该在JS文件中完成
3. 使用计算属性的方式来处理复杂的显示逻辑
4. 及时更新相关的数据状态，保持界面同步

## 相关文件

- `index.wxml` - 修复WXML语法错误
- `index.js` - 添加数据处理逻辑
- 主要涉及人员标签选择功能的显示和交互
