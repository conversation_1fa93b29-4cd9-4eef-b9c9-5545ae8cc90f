<!--mall.wxml-->
<view class="container">
  <view class="header">
    <view class="points-info">
      <view class="points-label">我的积分</view>
      <view class="points-value">{{userPoints}}</view>
      <view class="reset-btn" bindtap="resetGoodsData">重置商品</view>
    </view>
    <view class="search-box">
      <icon class="search-icon" type="search" size="14"></icon>
      <input class="search-input" placeholder="搜索商品" bindinput="onSearch" value="{{searchValue}}"/>
      <icon wx:if="{{searchValue}}" class="clear-icon" type="clear" size="14" bindtap="clearSearch"></icon>
    </view>
  </view>

  <view class="categories">
    <scroll-view scroll-x="true" class="categories-scroll">
      <view class="category-item {{currentCategory === item.id ? 'active' : ''}}"
            wx:for="{{categories}}"
            wx:key="id"
            bindtap="switchCategory"
            data-category="{{item.id}}">
        <view class="category-icon">
          <text>{{item.id === 'all' ? '🔍' : (item.id === 'voucher' ? '🎟️' : (item.id === 'goods' ? '🎁' : '🛠️'))}}</text>
        </view>
        <view class="category-name">{{item.name}}</view>
      </view>
    </scroll-view>
  </view>

  <view class="sort-bar">
    <view class="sort-text" bindtap="showSortOptions">
      {{sortOptions.find(item => item.id === currentSort).name}}
      <text style="font-size: 24rpx; color: #666;">▼</text>
    </view>
    <view class="goods-mall-link" bindtap="navigateToGoodsMall">
      <text>好物商城</text>
      <text class="arrow">→</text>
    </view>
  </view>

  <view class="products-grid">
    <block wx:if="{{filteredProducts.length > 0}}">
      <view class="product-item" wx:for="{{filteredProducts}}" wx:key="id" bindtap="viewProductDetail" data-id="{{item.id}}">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <view class="product-title">{{item.title}}</view>
          <view class="product-points">
            {{item.points}}积分
            <text class="cash-price" wx:if="{{item.cashPrice && item.cashPrice > 0}}">+ ¥{{item.cashPrice}}</text>
          </view>
          <view class="product-stock">库存: {{item.stock}}</view>
          <button class="exchange-btn {{userPoints < item.points || item.stock <= 0 ? 'disabled' : ''}}"
                  catchtap="exchangeProduct"
                  data-id="{{item.id}}"
                  disabled="{{userPoints < item.points || item.stock <= 0}}">
            {{userPoints < item.points ? '积分不足' : (item.stock <= 0 ? '已售罄' : '立即兑换')}}
          </button>
        </view>
      </view>
    </block>

    <view class="empty-products" wx:else>
      <view class="empty-icon">
        <text>🛒</text>
      </view>
      <view class="empty-text">暂无商品</view>
    </view>
  </view>

  <!-- 排序弹窗 -->
  <view class="sort-popup {{showSortPopup ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="sort-popup-mask" bindtap="closeSortPopup"></view>
    <view class="sort-popup-content">
      <view class="sort-popup-title">排序方式</view>
      <view class="sort-options">
        <view class="sort-option {{currentSort === item.id ? 'active' : ''}}"
              wx:for="{{sortOptions}}"
              wx:key="id"
              bindtap="selectSort"
              data-sort="{{item.id}}">
          {{item.name}}
          <text wx:if="{{currentSort === item.id}}" style="color: #FF6B00; font-size: 28rpx;">✓</text>
        </view>
      </view>
    </view>
  </view>
</view>
