/* record.wxss */
/* 容器样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #222;
  color: #fff;
}

.dark-mode .title,
.dark-mode .record-desc {
  color: #fff;
}

.dark-mode .record-time,
.dark-mode .empty-text,
.dark-mode .loading-more,
.dark-mode .no-more,
.dark-mode .date-range-title,
.dark-mode .filter-title {
  color: #aaa;
}

.dark-mode .record-item,
.dark-mode .filter-panel,
.dark-mode .search-input-wrap,
.dark-mode .date-picker {
  background-color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.dark-mode .record-types,
.dark-mode .time-ranges {
  background-color: #444;
}

.dark-mode .record-type,
.dark-mode .time-range {
  color: #aaa;
}

.dark-mode .record-type.active,
.dark-mode .time-range.active {
  background-color: #555;
  color: #ff8c00;
}

.dark-mode .filter-tag {
  background-color: #444;
}

/* 顶部信息 */
.header {
  margin-bottom: 30rpx;
}

.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.level-tag {
  margin-left: 16rpx;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 积分信息 */
.points-info {
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  border-radius: 16rpx;
  padding: 30rpx;
  color: white;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
}

.points-content {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 48rpx;
  font-weight: 700;
}

.level-link {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.arrow {
  margin-left: 8rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-input-wrap {
  flex: 1;
  height: 80rpx;
  background-color: #fff;
  border-radius: 40rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
}

.search-btn {
  width: 120rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ff8c00;
  margin-left: 16rpx;
}

.search-cancel {
  width: 100rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-types {
  flex: 1;
  display: flex;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 8rpx;
}

.record-type {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 32rpx;
}

.record-type.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.toolbar-actions {
  display: flex;
  margin-left: 16rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 40rpx;
  margin-left: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.action-btn image {
  width: 40rpx;
  height: 40rpx;
}

.search-icon image {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
}

.filter-icon image {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='4' y1='21' x2='4' y2='14'%3E%3C/line%3E%3Cline x1='4' y1='10' x2='4' y2='3'%3E%3C/line%3E%3Cline x1='12' y1='21' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12' y2='3'%3E%3C/line%3E%3Cline x1='20' y1='21' x2='20' y2='16'%3E%3C/line%3E%3Cline x1='20' y1='12' x2='20' y2='3'%3E%3C/line%3E%3Cline x1='1' y1='14' x2='7' y2='14'%3E%3C/line%3E%3Cline x1='9' y1='8' x2='15' y2='8'%3E%3C/line%3E%3Cline x1='17' y1='16' x2='23' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
}

/* 筛选面板 */
.filter-panel {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.filter-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.time-ranges {
  display: flex;
  flex-wrap: wrap;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
}

.time-range {
  width: calc(50% - 16rpx);
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 12rpx;
  margin: 8rpx;
}

.time-range.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.custom-date-range {
  margin-bottom: 30rpx;
}

.date-range-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.date-range-inputs {
  display: flex;
  align-items: center;
}

.date-picker {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
}

.date-picker.has-value {
  color: #333;
}

.date-separator {
  margin: 0 16rpx;
  font-size: 28rpx;
  color: #666;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
}

.filter-btn {
  width: 160rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-left: 20rpx;
  padding: 0;
  line-height: 1;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
}

.apply-btn {
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.2);
}

/* 当前筛选条件 */
.current-filter {
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
}

.filter-tag {
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.tag-close {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #999;
}

/* 记录列表 */
.records-list {
  padding-bottom: 40rpx;
}

.record-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 40rpx;
}

.earn-icon {
  background: rgba(76, 217, 100, 0.1);
  color: #4cd964;
}

.use-icon {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.record-info {
  flex: 1;
  margin-right: 20rpx;
}

.record-desc {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-points {
  font-size: 32rpx;
  font-weight: 500;
}

.earn-points {
  color: #4cd964;
}

.use-points {
  color: #ff3b30;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
}

.empty-records {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #e0e0e0;
}

.empty-text {
  font-size: 28rpx;
}
