/* 扫码核销页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background: #f8f8f8;
}

/* 扫码中样式 */
.scanning-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.scanning-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M7 21H3a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpath d='M17 21h4a2 2 0 0 0 2-2v-4'%3E%3C/path%3E%3Cpath d='M7 3H3a2 2 0 0 0-2 2v4'%3E%3C/path%3E%3Cpath d='M17 3h4a2 2 0 0 1 2 2v4'%3E%3C/path%3E%3Cline x1='12' y1='7' x2='12' y2='17'%3E%3C/line%3E%3Cline x1='7' y1='12' x2='17' y2='12'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.scanning-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.scanning-tip {
  font-size: 28rpx;
  color: #666;
}

/* 验证结果样式 */
.result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.result-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.result-icon.success {
  background-color: #e8f5e9;
}

.result-icon.success::before {
  content: '';
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-icon.error {
  background-color: #ffebee;
}

.result-icon.error::before {
  content: '';
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.result-time {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.error-message {
  font-size: 28rpx;
  color: #f44336;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 订单卡片样式 */
.order-card {
  width: 100%;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 40rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 24rpx;
  color: #666;
}

.order-content {
  display: flex;
  padding: 20rpx;
  align-items: center;
}

.buyer-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.order-info {
  flex: 1;
}

.buyer-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-detail {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  width: 100%;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn.primary {
  background: #ff8c00;
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

/* 暗黑模式样式 */
.darkMode {
  background: #1c1c1e;
}

.darkMode .scanning-text {
  color: #f5f5f7;
}

.darkMode .scanning-tip {
  color: #8e8e93;
}

.darkMode .result-title {
  color: #f5f5f7;
}

.darkMode .result-time {
  color: #8e8e93;
}

.darkMode .order-card {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .order-header {
  border-bottom: 1rpx solid #3a3a3c;
  color: #8e8e93;
}

.darkMode .buyer-name {
  color: #f5f5f7;
}

.darkMode .goods-title,
.darkMode .order-detail {
  color: #8e8e93;
}

.darkMode .action-btn.secondary {
  background: #3a3a3c;
  color: #8e8e93;
}

/* 订单确认样式 */
.confirm-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 600rpx;
}

.confirm-icon {
  width: 120rpx;
  height: 120rpx;
  background: #ff8c00;
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
}

.confirm-icon::before {
  content: '?';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 60rpx;
  font-weight: bold;
}

.confirm-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.order-details {
  width: 100%;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  max-width: 60%;
  text-align: right;
  word-break: break-all;
}

/* 暗黑模式下的订单确认样式 */
.darkMode .confirm-title {
  color: #f5f5f7;
}

.darkMode .order-details {
  background: #2c2c2e;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .detail-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .detail-label {
  color: #8e8e93;
}

.darkMode .detail-value {
  color: #f5f5f7;
}



/* 邀请码绑定弹窗样式 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.invite-modal.show {
  opacity: 1;
  visibility: visible;
}

.invite-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.invite-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 400px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 弹窗头部 */
.invite-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.invite-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.invite-modal-close {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
  border-radius: 15px;
  transition: background-color 0.2s;
}

.invite-modal-close:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 住户信息区域 */
.invite-resident-info {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.resident-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f0f0f0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.resident-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  color: #FF9500;
  font-size: 24px;
  font-weight: 600;
}

.resident-details {
  flex: 1;
}

.resident-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.resident-type {
  font-size: 14px;
  color: #666;
  padding: 2px 8px;
  background-color: #E1F0FF;
  color: #007AFF;
  border-radius: 10px;
  display: inline-block;
}

/* 房产列表区域 */
.invite-house-list {
  padding: 20px;
  max-height: 300px;
  overflow: hidden;
}

.house-list-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.house-scroll {
  max-height: 200px;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.house-item.bound {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
}

.house-info {
  flex: 1;
}

.house-address {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.house-status {
  font-size: 12px;
}

.status-bound {
  color: #34C759;
  background-color: #E6FFF2;
  padding: 2px 8px;
  border-radius: 6px;
}

.status-pending {
  color: #FF9500;
  background-color: #FFF3E0;
  padding: 2px 8px;
  border-radius: 6px;
}

.house-icon {
  margin-left: 12px;
}

.home-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpath d='M9 22V12h6v10'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 操作按钮区域 */
.invite-actions {
  display: flex;
  padding: 20px;
  gap: 12px;
  background-color: #f8f9fa;
}

.invite-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.invite-btn-cancel {
  background-color: #f0f0f0;
  color: #666;
}

.invite-btn-cancel:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.invite-btn-confirm {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.invite-btn-confirm:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
}

.invite-btn-confirm:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 4000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 30px;
  border-radius: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 16px;
}
