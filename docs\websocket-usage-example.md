# WebSocket使用示例

本文档展示如何在小程序页面中使用WebSocket功能进行实时通信。

## 基本使用方法

### 1. 在页面中获取WebSocket管理器

```javascript
// pages/chat/chat.js
Page({
  data: {
    messages: [],
    inputText: '',
    receiverId: '', // 对方用户ID
    isConnected: false
  },

  onLoad: function(options) {
    // 获取WebSocket管理器
    const app = getApp();
    this.websocketManager = app.getWebSocketManager();
    
    // 检查连接状态
    this.setData({
      isConnected: app.isWebSocketConnected()
    });

    // 监听WebSocket事件
    this.setupWebSocketListeners();
  },

  // 设置WebSocket事件监听
  setupWebSocketListeners: function() {
    if (!this.websocketManager) return;

    // 监听连接状态变化
    this.websocketManager.on('open', () => {
      this.setData({ isConnected: true });
      console.log('页面: WebSocket连接已建立');
    });

    this.websocketManager.on('close', () => {
      this.setData({ isConnected: false });
      console.log('页面: WebSocket连接已关闭');
    });

    this.websocketManager.on('error', (error) => {
      this.setData({ isConnected: false });
      console.error('页面: WebSocket连接错误', error);
    });
  },

  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function(topic, data) {
    switch (topic) {
      case 'send_private_message':
        this.handleNewMessage(data);
        break;
      case 'query_private_message':
        this.handleMessageHistory(data);
        break;
    }
  },

  // 处理新消息
  handleNewMessage: function(message) {
    const messages = this.data.messages;
    messages.push({
      id: message.id || Date.now(),
      type: message.type,
      content: message.content,
      senderId: message.senderId,
      createTime: message.createTime || new Date().toISOString(),
      isSelf: false
    });
    
    this.setData({ messages });
    
    // 滚动到底部
    this.scrollToBottom();
  },

  // 处理消息历史
  handleMessageHistory: function(messages) {
    if (Array.isArray(messages)) {
      this.setData({ messages });
      this.scrollToBottom();
    }
  },

  // 发送文本消息
  sendTextMessage: function() {
    const { inputText, receiverId } = this.data;
    
    if (!inputText.trim()) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    if (!receiverId) {
      wx.showToast({
        title: '请选择接收者',
        icon: 'none'
      });
      return;
    }

    // 使用app.js的便捷方法发送消息
    const app = getApp();
    const success = app.sendPrivateMessage('text', inputText, receiverId);
    
    if (success) {
      // 添加到本地消息列表
      const messages = this.data.messages;
      messages.push({
        id: Date.now(),
        type: 'text',
        content: inputText,
        senderId: wx.getStorageSync('userInfo').id,
        createTime: new Date().toISOString(),
        isSelf: true
      });
      
      this.setData({ 
        messages,
        inputText: '' 
      });
      
      this.scrollToBottom();
    } else {
      wx.showToast({
        title: '发送失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 发送图片消息
  sendImageMessage: function() {
    const { receiverId } = this.data;
    
    if (!receiverId) {
      wx.showToast({
        title: '请选择接收者',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 上传图片
        this.uploadImage(tempFilePath).then(imagePath => {
          // 发送图片消息
          const app = getApp();
          const success = app.sendPrivateMessage('image', imagePath, receiverId);
          
          if (success) {
            // 添加到本地消息列表
            const messages = this.data.messages;
            messages.push({
              id: Date.now(),
              type: 'image',
              content: imagePath,
              senderId: wx.getStorageSync('userInfo').id,
              createTime: new Date().toISOString(),
              isSelf: true
            });
            
            this.setData({ messages });
            this.scrollToBottom();
          }
        }).catch(error => {
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
        });
      }
    });
  },

  // 上传图片
  uploadImage: function(filePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: wx.getStorageSync('apiUrl') + '/common-api/v1/file/upload',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': wx.getStorageSync('access_token')
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            resolve(data.result);
          } else {
            reject(new Error(data.message));
          }
        },
        fail: reject
      });
    });
  },

  // 查询消息历史
  loadMessageHistory: function() {
    const { receiverId } = this.data;
    
    if (!receiverId) return;
    
    const app = getApp();
    app.queryPrivateMessage(receiverId);
  },

  // 滚动到底部
  scrollToBottom: function() {
    wx.createSelectorQuery().select('#message-list').boundingClientRect((rect) => {
      if (rect) {
        wx.pageScrollTo({
          scrollTop: rect.bottom,
          duration: 300
        });
      }
    }).exec();
  },

  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 页面卸载时清理
  onUnload: function() {
    // 移除事件监听器（如果需要）
    // this.websocketManager.off('open', this.onWebSocketOpen);
  }
});
```

## 在其他页面中使用WebSocket

### 发送系统通知

```javascript
// 在管理页面中发送系统通知
Page({
  sendSystemNotification: function() {
    const app = getApp();
    
    app.sendWebSocketMessage('system_notification', {
      type: 'announcement',
      title: '系统公告',
      content: '系统将于今晚进行维护，请提前保存数据。',
      timestamp: Date.now()
    });
  }
});
```

### 检查连接状态

```javascript
Page({
  onShow: function() {
    const app = getApp();
    
    // 检查WebSocket连接状态
    if (!app.isWebSocketConnected()) {
      wx.showToast({
        title: '网络连接异常',
        icon: 'none'
      });
    }
  }
});
```

## 注意事项

1. **生命周期管理**: WebSocket连接在app.js中管理，页面切换时连接保持不变
2. **消息处理**: 页面需要实现`onWebSocketMessage`方法来处理接收到的消息
3. **错误处理**: 发送消息前应检查连接状态，失败时给用户适当提示
4. **内存管理**: 页面卸载时应清理不必要的事件监听器
5. **网络状态**: 在网络不稳定时，消息会自动加入队列，连接恢复后重新发送

## 调试技巧

1. 在开发者工具的控制台中查看WebSocket相关日志
2. 使用`app.getWebSocketManager().getStats()`查看连接统计信息
3. 监听`reconnect`事件了解重连情况
