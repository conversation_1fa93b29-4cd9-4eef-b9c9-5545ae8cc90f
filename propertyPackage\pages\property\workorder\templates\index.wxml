<!-- pages/property/workorder/templates/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">工单模板管理</view>
    <view class="nav-action"></view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view 
      wx:for="{{tabs}}" 
      wx:key="id" 
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="{{index}}"
    >
      <text class="tab-text">{{item.name}}</text>
    </view>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    <!-- 模板列表 -->
    <view class="template-list">
      <view wx:if="{{templates[tabs[currentTab].id].length === 0}}" class="empty-list">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无模板</view>
      </view>
      
      <view 
        wx:for="{{templates[tabs[currentTab].id]}}" 
        wx:key="id" 
        class="template-item"
      >
        <view class="template-content">
          <view class="template-text">{{item.content}}</view>
          <view class="template-remark" wx:if="{{item.remark}}">{{item.remark}}</view>
        </view>
        <view class="template-actions">
          <view 
            class="action-button edit" 
            bindtap="showEditModal" 
            data-id="{{item.id}}" 
            data-type="{{tabs[currentTab].id}}"
          >
            <image src="/images/icons/edit.svg" class="action-icon" />
          </view>
          <view 
            class="action-button delete" 
            bindtap="deleteTemplate" 
            data-id="{{item.id}}" 
            data-type="{{tabs[currentTab].id}}"
          >
            <image src="/images/icons/delete.svg" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn-add" bindtap="showAddModal">添加模板</button>
      <button class="btn-reset" bindtap="resetTemplates">重置为默认</button>
    </view>
  </view>

  <!-- 添加模板对话框 -->
  <view class="modal-mask" wx:if="{{showAddModal}}" bindtap="hideAddModal"></view>
  <view class="modal-dialog" wx:if="{{showAddModal}}">
    <view class="modal-header">
      <text class="modal-title">添加{{tabs[currentTab].name}}模板</text>
      <view class="modal-close" bindtap="hideAddModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-group">
        <view class="form-label">
          <text wx:if="{{tabs[currentTab].id === 'record'}}">处理动作</text>
          <text wx:else>模板内容</text>
        </view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入模板内容" 
          value="{{newTemplate.content}}" 
          bindinput="inputNewContent"
        ></textarea>
      </view>
      
      <view class="form-group" wx:if="{{tabs[currentTab].id === 'record'}}">
        <view class="form-label">处理备注</view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入处理备注" 
          value="{{newTemplate.remark}}" 
          bindinput="inputNewRemark"
        ></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideAddModal">取消</button>
      <button class="modal-btn confirm" bindtap="addTemplate">确认添加</button>
    </view>
  </view>

  <!-- 编辑模板对话框 -->
  <view class="modal-mask" wx:if="{{showEditModal}}" bindtap="hideEditModal"></view>
  <view class="modal-dialog" wx:if="{{showEditModal}}">
    <view class="modal-header">
      <text class="modal-title">编辑模板</text>
      <view class="modal-close" bindtap="hideEditModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-group">
        <view class="form-label">
          <text wx:if="{{editingTemplate.type === 'record'}}">处理动作</text>
          <text wx:else>模板内容</text>
        </view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入模板内容" 
          value="{{editingTemplate.content}}" 
          bindinput="inputEditContent"
        ></textarea>
      </view>
      
      <view class="form-group" wx:if="{{editingTemplate.type === 'record'}}">
        <view class="form-label">处理备注</view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入处理备注" 
          value="{{editingTemplate.remark}}" 
          bindinput="inputEditRemark"
        ></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideEditModal">取消</button>
      <button class="modal-btn confirm" bindtap="editTemplate">确认更新</button>
    </view>
  </view>
</view>
