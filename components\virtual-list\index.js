// components/virtual-list/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 列表数据
    list: {
      type: Array,
      value: [],
      observer: function(newVal) {
        if (newVal && newVal.length > 0) {
          this.setData({
            totalCount: newVal.length
          });
          this.updateList();
        }
      }
    },
    // 列表项高度（单位px）
    itemHeight: {
      type: Number,
      value: 100
    },
    // 预加载的屏幕数量
    preloadScreenCount: {
      type: Number,
      value: 1
    },
    // 是否显示加载中提示
    showLoading: {
      type: Boolean,
      value: false
    },
    // 加载中提示文本
    loadingText: {
      type: String,
      value: '加载中...'
    },
    // 是否显示空列表提示
    showEmpty: {
      type: Boolean,
      value: true
    },
    // 空列表提示文本
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    // 空列表图标
    emptyIcon: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 可视区域高度
    screenHeight: 0,
    // 可视区域顶部距离列表顶部的距离
    scrollTop: 0,
    // 起始索引
    startIndex: 0,
    // 结束索引
    endIndex: 0,
    // 总数据条数
    totalCount: 0,
    // 可视区域内的列表数据
    visibleList: [],
    // 列表总高度
    listHeight: 0,
    // 顶部填充高度
    topFillHeight: 0
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      this.initList();
    },
    ready: function() {
      this.updateList();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化列表
     */
    initList: function() {
      const that = this;
      // 获取可视区域高度
      wx.createSelectorQuery().in(this).select('.virtual-list').boundingClientRect(function(rect) {
        if (rect) {
          that.setData({
            screenHeight: rect.height
          });
          that.updateList();
        }
      }).exec();
    },

    /**
     * 更新列表
     */
    updateList: function() {
      const { list, itemHeight, screenHeight, scrollTop, preloadScreenCount, totalCount } = this.data;
      
      if (list.length === 0 || screenHeight === 0) {
        return;
      }
      
      // 计算可视区域内能显示的列表项数量
      const visibleCount = Math.ceil(screenHeight / itemHeight);
      
      // 计算预加载的列表项数量
      const preloadCount = Math.ceil(visibleCount * preloadScreenCount);
      
      // 计算起始索引和结束索引
      let startIndex = Math.floor(scrollTop / itemHeight) - preloadCount;
      startIndex = Math.max(0, startIndex);
      
      let endIndex = Math.ceil((scrollTop + screenHeight) / itemHeight) + preloadCount;
      endIndex = Math.min(totalCount - 1, endIndex);
      
      // 计算顶部填充高度
      const topFillHeight = startIndex * itemHeight;
      
      // 计算列表总高度
      const listHeight = totalCount * itemHeight;
      
      // 获取可视区域内的列表数据
      const visibleList = list.slice(startIndex, endIndex + 1);
      
      this.setData({
        startIndex,
        endIndex,
        visibleList,
        topFillHeight,
        listHeight
      });
    },

    /**
     * 滚动事件处理
     */
    onScroll: function(e) {
      const scrollTop = e.detail.scrollTop;
      
      // 如果滚动距离变化不大，不更新列表
      if (Math.abs(scrollTop - this.data.scrollTop) < this.data.itemHeight / 2) {
        return;
      }
      
      this.setData({
        scrollTop
      });
      
      this.updateList();
    },

    /**
     * 点击列表项
     */
    onItemClick: function(e) {
      const { index } = e.currentTarget.dataset;
      const realIndex = this.data.startIndex + index;
      const item = this.data.list[realIndex];
      
      this.triggerEvent('itemclick', {
        item,
        index: realIndex
      });
    },

    /**
     * 长按列表项
     */
    onItemLongPress: function(e) {
      const { index } = e.currentTarget.dataset;
      const realIndex = this.data.startIndex + index;
      const item = this.data.list[realIndex];
      
      this.triggerEvent('itemlongpress', {
        item,
        index: realIndex
      });
    },

    /**
     * 滚动到指定索引
     */
    scrollToIndex: function(index) {
      const { itemHeight } = this.data;
      
      this.setData({
        scrollTop: index * itemHeight
      });
      
      this.updateList();
    },

    /**
     * 滚动到指定位置
     */
    scrollToPosition: function(position) {
      this.setData({
        scrollTop: position
      });
      
      this.updateList();
    },

    /**
     * 刷新列表
     */
    refreshList: function() {
      this.updateList();
    }
  }
});
