<!--pages/renovation/index/index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-bar">
      <icon class="search-icon" type="search" size="14"></icon>
      <input class="search-input" placeholder="搜索申请记录" bindinput="onSearchInput" bindconfirm="onSearchConfirm" value="{{searchKeyword}}" adjust-position="false" confirm-type="search"/>
      <icon wx:if="{{searchKeyword}}" class="clear-icon" type="clear" size="14" bindtap="clearSearch"></icon>
    </view>
  </view>

  <!-- 分段控制器 -->
  <view class="segmented-control">
    <view class="segment-background" style="transform: translateX({{currentTab === 'ongoing' ? '0' : '100%'}});"></view>
    <view class="segment {{currentTab === 'ongoing' ? 'segment-active' : ''}}" bindtap="switchTab" data-tab="ongoing">
      <text>进行中</text>
    </view>
    <view class="segment {{currentTab === 'completed' ? 'segment-active' : ''}}" bindtap="switchTab" data-tab="completed">
      <text>已完成</text>
    </view>
  </view>

  <!-- 进行中申请列表 -->
  <view class="application-list" wx:if="{{currentTab === 'ongoing'}}">
    <view class="empty-state" wx:if="{{ongoingList.length === 0}}">
      <image class="empty-icon" src="/images/icons/empty-state.svg" mode="aspectFit"></image>
      <view class="empty-title">暂无申请记录</view>
      <view class="empty-description">您还没有提交过装修申请，点击下方按钮新建申请</view>
    </view>

    <view class="application-card" wx:for="{{ongoingList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
      <view class="application-header">
        <view class="application-title">{{item.title}}</view>
        <view class="application-status status-{{item.status}}">{{item.statusText}}</view>
      </view>
      <view class="application-info">
        <view class="info-row">
          <view class="info-label">申请类型</view>
          <view class="info-value">{{item.type}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">申请日期</view>
          <view class="info-value">{{item.date}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">当前进度</view>
          <view class="info-value">{{item.progress}}</view>
        </view>
      </view>
      <view class="application-footer">
        <button class="app-btn btn-outline" catchtap="handleAction" data-action="notify" data-id="{{item.id}}">催办</button>
        <button class="app-btn btn-primary" catchtap="handleAction" data-action="detail" data-id="{{item.id}}">查看详情</button>
      </view>
    </view>
  </view>

  <!-- 已完成申请列表 -->
  <view class="application-list" wx:if="{{currentTab === 'completed'}}">
    <view class="empty-state" wx:if="{{completedList.length === 0}}">
      <image class="empty-icon" src="/images/icons/empty-state.svg" mode="aspectFit"></image>
      <view class="empty-title">暂无已完成申请</view>
      <view class="empty-description">您还没有已完成的装修申请记录</view>
    </view>

    <view class="application-card" wx:for="{{completedList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
      <view class="application-header">
        <view class="application-title">{{item.title}}</view>
        <view class="application-status status-{{item.status}}">{{item.statusText}}</view>
      </view>
      <view class="application-info">
        <view class="info-row">
          <view class="info-label">申请类型</view>
          <view class="info-value">{{item.type}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">申请日期</view>
          <view class="info-value">{{item.date}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">{{item.status === 'rejected' ? '驳回理由' : '完成日期'}}</view>
          <view class="info-value">{{item.status === 'rejected' ? item.rejectReason : item.completeDate}}</view>
        </view>
      </view>
      <view class="application-footer">
        <button wx:if="{{item.status === 'rejected'}}" class="app-btn btn-outline" catchtap="handleAction" data-action="resubmit" data-id="{{item.id}}">重新申请</button>
        <button class="app-btn btn-primary" catchtap="handleAction" data-action="detail" data-id="{{item.id}}">查看详情</button>
      </view>
    </view>
  </view>

  <!-- 新建申请按钮 -->
  <button class="create-btn" bindtap="createNewApplication">
    <text class="create-btn-icon">+</text>
    <text>新建申请</text>
  </button>
</view>