/* pages/renovation/status/status.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative;
}

/* 申请状态卡片 */
.status-card {
  margin: 24rpx 32rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.status-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.status-approved {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.status-rejected {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.status-completed {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.status-info {
  padding: 32rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.progress-pending {
  color: #FF9800;
  font-weight: 500;
}

.progress-approved {
  color: #34C759;
  font-weight: 500;
}

.progress-rejected {
  color: #FF3B30;
  font-weight: 500;
}

/* 进度时间线 */
.timeline-section {
  margin: 24rpx 0;
  background-color: white;
  padding: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 16rpx;
  width: 2rpx;
  background-color: #E5E5EA;
}

.timeline-item {
  position: relative;
  padding-left: 60rpx;
  padding-bottom: 40rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #E5E5EA;
  border: 2rpx solid #fff;
  z-index: 1;
}

.timeline-item.completed .timeline-dot {
  background-color: #34C759;
}

.timeline-item.active .timeline-dot {
  background-color: #FF9800;
  width: 24rpx;
  height: 24rpx;
  left: 4rpx;
  top: 4rpx;
}

.timeline-item.rejected .timeline-dot {
  background-color: #FF3B30;
}

.timeline-content {
  padding-bottom: 16rpx;
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.timeline-item.active .timeline-title {
  color: #FF9800;
}

.timeline-time {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.timeline-action {
  margin-top: 16rpx;
}

.action-button {
  display: inline-block;
  padding: 12rpx 24rpx;
  background-color: #F2F2F7;
  color: #333;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: none;
  line-height: 1.5;
}

/* 联系信息 */
.contact-section {
  margin: 24rpx 0;
  background-color: white;
  padding: 32rpx;
}

.contact-card {
  border-radius: 16rpx;
  background-color: #F9F9F9;
  padding: 24rpx;
}

.contact-row {
  display: flex;
  margin-bottom: 24rpx;
}

.contact-row:last-child {
  margin-bottom: 0;
}

.contact-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.call-button {
  padding: 8rpx 24rpx;
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 24rpx;
  border-radius: 32rpx;
  border: none;
  line-height: 1.5;
  margin: 0;
}

/* 物业反馈 */
.feedback-section {
  margin: 24rpx 0;
  background-color: white;
  padding: 32rpx;
}

.feedback-card {
  border-radius: 16rpx;
  background-color: #F9F9F9;
  padding: 24rpx;
}

.feedback-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.feedback-time {
  font-size: 24rpx;
  color: #8E8E93;
  text-align: right;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: white;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.reapply-button, .notify-button, .back-button {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.reapply-button {
  background-color: #FF9800;
  color: white;
}

.notify-button {
  background-color: #007AFF;
  color: white;
}

.back-button {
  background-color: #F2F2F7;
  color: #333;
}