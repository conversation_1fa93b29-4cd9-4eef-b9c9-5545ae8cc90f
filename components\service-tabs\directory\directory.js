// 信息黄页组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    searchKeyword: '',
    categories: [
      {
        id: 1,
        name: '家政保洁',
        icon: '/images/icons/cleaning.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 2,
        name: '家电维修',
        icon: '/images/icons/repair.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 3,
        name: '送水/送奶',
        icon: '/images/icons/delivery.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 4,
        name: '洗衣服务',
        icon: '/images/icons/laundry.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 5,
        name: '开锁换锁',
        icon: '/images/icons/lock.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 6,
        name: '搬家服务',
        icon: '/images/icons/moving.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 7,
        name: '餐饮外卖',
        icon: '/images/icons/food.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 8,
        name: '超市便利',
        icon: '/images/icons/market.svg',
        iconColor: '#ff8c00'
      },
      {
        id: 9,
        name: '更多服务',
        icon: '/images/icons/more.svg',
        iconColor: '#ff8c00'
      }
    ],
    hotServices: [
      {
        id: 101,
        name: '阿姨家政服务',
        category: 1,
        description: '专业保洁、月嫂、育儿嫂服务',
        phone: '020-12345678',
        address: '天河区珠江新城'
      },
      {
        id: 102,
        name: '全能家电维修',
        category: 2,
        description: '各品牌家电维修、安装、清洗',
        phone: '020-87654321',
        address: '天河区天河路'
      },
      {
        id: 103,
        name: '优质桶装水配送',
        category: 3,
        description: '桶装水配送，支持定期配送',
        phone: '13800138000',
        address: '天河区天河东路'
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 搜索输入
    onSearchInput: function(e) {
      const keyword = e.detail.value
      this.setData({
        searchKeyword: keyword
      })
    },

    // 清除搜索
    clearSearch: function() {
      this.setData({
        searchKeyword: ''
      })
    },

    // 导航到分类页面
    navigateToCategory: function(e) {
      const { id, name } = e.currentTarget.dataset
      wx.navigateTo({
        url: `/communityPackage/pages/community/service/directory/category?id=${id}&name=${name}`,
        fail: (err) => {
          console.error('导航失败:', err)
          if (err.errMsg && err.errMsg.indexOf('webviewId') > -1) {
            wx.redirectTo({
              url: `/communityPackage/pages/community/service/directory/category?id=${id}&name=${name}`
            })
          }
        }
      })
    },

    // 导航到详情页面
    navigateToDetail: function(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/communityPackage/pages/community/service/directory/detail?id=${id}`,
        fail: (err) => {
          console.error('导航失败:', err)
          if (err.errMsg && err.errMsg.indexOf('webviewId') > -1) {
            wx.redirectTo({
              url: `/communityPackage/pages/community/service/directory/detail?id=${id}`
            })
          }
        }
      })
    }
  }
})
