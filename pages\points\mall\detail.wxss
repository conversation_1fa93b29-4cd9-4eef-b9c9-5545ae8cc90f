/* pages/points/mall/detail.wxss */
.container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 商品图片轮播 */
.product-swiper {
  width: 100%;
  height: 750rpx;
}

.product-image {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

/* 商品信息 */
.product-info {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.points {
  font-size: 40rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-right: 16rpx;
}

.cash {
  font-size: 32rpx;
  color: #ff8c00;
}

.product-stock {
  font-size: 28rpx;
  color: #999;
}

/* 商品描述 */
.product-description {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #ff8c00;
  border-radius: 4rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  z-index: 10;
}

.quantity-control {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #333;
}

.quantity-btn.disabled {
  color: #ccc;
  background-color: #f5f5f5;
}

.quantity-value {
  width: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}

.exchange-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(to right, #ff8c00, #ff6b00);
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}

.exchange-btn.disabled {
  background: #ccc;
}

/* 兑换确认弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
}

.modal-product {
  display: flex;
  margin-bottom: 30rpx;
}

.modal-product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.modal-product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.modal-product-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.modal-product-price {
  display: flex;
  align-items: center;
}

.modal-detail {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.modal-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.modal-detail-item:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.modal-btn.cancel {
  color: #666;
  background-color: #f5f5f5;
}

.modal-btn.confirm {
  color: #fff;
  background: linear-gradient(to right, #ff8c00, #ff6b00);
  font-weight: 600;
}