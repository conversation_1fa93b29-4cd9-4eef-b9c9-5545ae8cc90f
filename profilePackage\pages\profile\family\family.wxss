/* 我的家人页面样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 32rpx;
  box-sizing: border-box;
  height: auto;
  max-height: calc(100vh - 100rpx); /* 减去底部按钮区域的高度 */
}

/* 家人列表样式 */
.family-list {
  margin-bottom: 160rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  color: #8E8E93;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23C7C7CC' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 00-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 010 7.75'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  opacity: 0.7;
}

/* 家人卡片样式 */
.family-card {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
  position: relative;
}

.family-card-content {
  display: flex;
  padding: 32rpx;
  position: relative;
}

.family-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f0f0f0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  flex-shrink: 0;
  color: #FF9500;
  font-size: 48rpx;
  font-weight: 600;
}

.family-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.family-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.family-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: #333;
}

.family-relation-container {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.family-relation {
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background-color: #E1F0FF;
  color: #007AFF;
  width: auto;
  min-width: 80rpx;
  text-align: center;
  margin-bottom: 0;
  line-height: 1.2;
}

.relation-parent {
  background-color: #E1F0FF;
  color: #007AFF;
}

.relation-child {
  background-color: #E6FFF2;
  color: #34C759;
}

.relation-spouse {
  background-color: #FFE8E6;
  color: #FF3B30;
}

.relation-sibling {
  background-color: #F2E8FF;
  color: #AF52DE;
}

.relation-grandparent {
  background-color: #E6F9FF;
  color: #5AC8FA;
}

.family-house {
  font-size: 28rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
}

.house-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpath d='M9 22V12h6v10'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.family-actions {
  display: flex;
  align-items: center;
}

.action-button {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background: transparent;
}

.more-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='19' cy='12' r='1'%3E%3C/circle%3E%3Ccircle cx='5' cy='12' r='1'%3E%3C/circle%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 添加按钮 */
.add-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx 40rpx;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-top: 2rpx solid rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.add-button {
  width: 100%;
  height: 100rpx;
  background: #FF9500;
  color: white;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.2);
}

.add-button::before {
  content: '+';
  margin-right: 16rpx;
  font-size: 40rpx;
}

/* 操作菜单样式 */
.action-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

.action-menu {
  position: fixed;
  background: white;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 100;
  width: 360rpx;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(0, 0, 0, 0.05);
  /* 确保菜单不会超出屏幕右侧 */
  right: 40rpx;
}

.action-menu-item {
  padding: 28rpx 32rpx;
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
}

.action-menu-item:last-child {
  border-bottom: none;
}

.action-menu-item.delete {
  color: #FF3B30;
}

.edit-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.delete-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF3B30' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-overlay.active {
  display: block;
  opacity: 1;
}

.modal-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: calc(100vh - 100rpx); /* 确保不会超出屏幕，留出顶部空间 */
  box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.15);
  z-index: 201;
  display: flex;
  flex-direction: column;
}

.modal-overlay.active .modal-container {
  transform: translateY(0);
}

.modal-header {
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-actions {
  display: flex;
  align-items: center;
}

.modal-sync {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF9500;
  background: transparent;
  margin-right: 16rpx;
}

.sync-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.62 1.98'%3E%3C/path%3E%3Cpath d='M20 4v5h-5'%3E%3C/path%3E%3Cpath d='M15 9l5-5'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.modal-close {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background: transparent;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.modal-body {
  padding: 40rpx;
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 确保flex子元素可以收缩 */
  /* 为iOS设备添加弹性滚动 */
  -webkit-overflow-scrolling: touch;
}

.modal-footer {
  padding: 32rpx 40rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;
  flex-shrink: 0; /* 防止被压缩 */
  background-color: white; /* 确保背景色 */
}

/* 表单样式 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #333;
}

.required {
  color: #FF3B30;
  margin-left: 8rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 20rpx;
  background-color: #F9F9F9;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input input {
  width: 100%;
  height: 100%;
  font-size: 32rpx;
  color: #333;
  background: transparent;
}

.form-selector {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 20rpx;
  background-color: #F9F9F9;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

/* 下拉菜单样式 */
.form-dropdown {
  width: 100%;
  position: relative;
}

.dropdown-header {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 20rpx;
  background-color: #F9F9F9;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 2rpx solid #E5E5EA;
  border-radius: 20rpx;
  margin-top: 8rpx;
  max-height: 0;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.dropdown-content.show {
  max-height: 600rpx;
  opacity: 1;
  visibility: visible;
}

.dropdown-group {
  border-bottom: 2rpx solid #F2F2F7;
  padding: 16rpx 0;
}

.dropdown-group:last-child {
  border-bottom: none;
}

.dropdown-group-title {
  font-size: 24rpx;
  color: #8E8E93;
  padding: 8rpx 32rpx;
}

.dropdown-item {
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover, .dropdown-item.active {
  background-color: #F2F2F7;
  color: #FF9500;
}

.arrow-icon.up {
  transform: rotate(180deg);
}

.placeholder {
  color: #C7C7CC;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 导入样式 */
.import-container {
  padding: 20rpx 0;
}

.import-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.spinner {
  width: 72rpx;
  height: 72rpx;
  border: 6rpx solid rgba(255, 149, 0, 0.2);
  border-top-color: #FF9500;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.import-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
  line-height: 1.5;
}

.import-text-secondary {
  font-size: 24rpx;
  color: #AEAEB2;
  margin-top: 16rpx;
  text-align: center;
}

.import-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #333;
}

.import-tip {
  font-size: 28rpx;
  color: #8e8e93;
  margin-bottom: 24rpx;
  font-style: italic;
}

.import-list {
  max-height: 600rpx;
  overflow-y: auto;
  border-radius: 24rpx;
  background: #f9f9f9;
}

.import-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #E5E5EA;
}

.import-item:last-child {
  border-bottom: none;
}

.import-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  color: #FF9500;
  font-size: 32rpx;
  font-weight: 600;
}

.import-item-info {
  flex: 1;
}

.import-item-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: #333;
}

.import-item-phone {
  font-size: 28rpx;
  color: #8E8E93;
}

.import-item-checkbox {
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  border: 4rpx solid #C7C7CC;
  position: relative;
  transition: all 0.2s ease;
}

.import-item-checkbox.checked {
  background-color: #FF9500;
  border-color: #FF9500;
}

.import-item-checkbox.checked::after {
  content: '';
  position: absolute;
  top: 6rpx;
  left: 12rpx;
  width: 12rpx;
  height: 20rpx;
  border-right: 4rpx solid white;
  border-bottom: 4rpx solid white;
  transform: rotate(45deg);
}

/* 按钮样式 */
.cancel-button,
.submit-button {
  flex: 1;
  height: 100rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.submit-button {
  background-color: #FF9500;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.2);
}

.submit-button[disabled] {
  background-color: #C7C7CC;
  box-shadow: none;
}

/* 选择器样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 300;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.picker-overlay.active {
  display: block;
  opacity: 1;
}

.picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 301;
}

.picker-container.active {
  transform: translateY(0);
}

.picker-header {
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 32rpx;
  color: #FF9500;
  font-weight: 500;
}

.picker-content {
  padding: 40rpx;
}

.picker-item {
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
}

.default-house {
  color: #FF9500;
  font-weight: 500;
}

/* 确认删除弹窗样式 */
.alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s;
  display: none;
}

.alert-overlay.show {
  opacity: 1;
  display: block;
}

.custom-alert {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 540rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  box-shadow: 0 16rpx 60rpx rgba(0, 0, 0, 0.12);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  text-align: center;
  display: flex;
  flex-direction: column;
  display: none;
}

.custom-alert.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  display: flex;
}

.alert-content {
  padding: 40rpx 32rpx 32rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 16rpx;
}

.alert-message {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 0;
  line-height: 1.4;
}

.alert-button-container {
  width: 100%;
  border-top: 2rpx solid rgba(60, 60, 67, 0.1);
  display: flex;
}

.alert-button {
  padding: 32rpx 0;
  font-size: 34rpx;
  font-weight: 500;
  background: transparent;
  border: none;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.alert-cancel-button {
  border-right: 2rpx solid rgba(60, 60, 67, 0.1);
  color: #FF9500;
  flex: 1;
}

.alert-confirm-button {
  color: #FF3B30;
  font-weight: 600;
  flex: 1;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #8e8e93;
  margin-top: 24rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  text-align: center;
}

/* 家属关系样式 */
.relation-family {
  background-color: #E8F5E8;
  color: #34C759;
}

/* ==================== 邀请二维码弹窗样式 ==================== */

/* 二维码图标 */
.qrcode-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='5' height='5'%3E%3C/rect%3E%3Crect x='16' y='3' width='5' height='5'%3E%3C/rect%3E%3Crect x='3' y='16' width='5' height='5'%3E%3C/rect%3E%3Cpath d='M21 16h-3a2 2 0 00-2 2v3'%3E%3C/path%3E%3Cpath d='M21 21v.01'%3E%3C/path%3E%3Cpath d='M12 7v3a2 2 0 002 2h3'%3E%3C/path%3E%3Cpath d='M3 12h.01'%3E%3C/path%3E%3Cpath d='M12 3h.01'%3E%3C/path%3E%3Cpath d='M12 16v.01'%3E%3C/path%3E%3Cpath d='M16 12h1'%3E%3C/path%3E%3Cpath d='M21 12v.01'%3E%3C/path%3E%3Cpath d='M12 21v-1'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 二维码弹窗遮罩和容器 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.qrcode-modal.show {
  opacity: 1;
  visibility: visible;
}

.qrcode-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.qrcode-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 800rpx;
  background-color: #fff;
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

/* 弹窗头部 */
.qrcode-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.qrcode-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.qrcode-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #fff;
  border-radius: 30rpx;
  transition: background-color 0.2s;
}

.qrcode-modal-close:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 房间信息区域 */
.qrcode-house-info {
  padding: 40rpx;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #f0f0f0;
}

.qrcode-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.qrcode-info-row:last-child {
  margin-bottom: 0;
}

.qrcode-info-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.qrcode-info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 二维码显示区域 */
.qrcode-display-area {
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.qrcode-container {
  width: 400rpx;
  height: 400rpx;
  background-color: #fff;
  border: 4rpx solid #f0f0f0;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.qrcode-image {
  width: 360rpx;
  height: 360rpx;
  border-radius: 16rpx;
  display: block;
  margin: 0 auto;
  object-fit: contain;
  object-position: center;
}

/* 加载状态 */
.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f0f0f0;
  border-top: 6rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 未生成状态 */
.qrcode-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.qrcode-placeholder-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.qrcode-placeholder-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 0 40rpx;
}

/* 过期时间显示 */
.qrcode-expire-time {
  margin-top: 32rpx;
  text-align: center;
  padding: 16rpx 32rpx;
  background-color: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  width: 100%;
  max-width: 600rpx;
  box-sizing: border-box;
}

.expire-label {
  font-size: 24rpx;
  color: #856404;
}

.expire-time {
  font-size: 24rpx;
  color: #856404;
  font-weight: 600;
}

/* 隐藏的canvas */
.qrcode-canvas-hidden {
  position: absolute;
  left: -19998rpx;
  top: -19998rpx;
  width: 600rpx;
  height: 600rpx;
  opacity: 0;
}

/* 操作按钮区域 */
.qrcode-actions {
  display: flex;
  padding: 40rpx;
  gap: 24rpx;
  background-color: #f8f9fa;
}

.qrcode-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.qrcode-btn-secondary:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.qrcode-btn-secondary:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  transform: none;
}

.qrcode-btn-primary {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.qrcode-btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.qrcode-btn-primary:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}
