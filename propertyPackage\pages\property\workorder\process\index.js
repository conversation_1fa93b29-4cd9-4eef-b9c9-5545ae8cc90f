// pages/property/workorder/process/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const propertyApi = require('@/api/propertyApi.js');
const util = require('@/utils/util.js');
const commApi = require('@/api/commApi.js');
Page({
  data: {
    darkMode: false,
    workOrder: null,
    loading: true,
    submitting: false,

    // 页面模式：accept-受理工单
    mode: 'accept',

    // 员工列表
    staffList: [],
    selectedStaffIds: [], // 多选员工ID

    // 字典数据
    personWorkStatusDict: [], // 员工工作状态字典
    personStatusDict: [], // 员工状态字典
    genderDict: [], // 性别字典

    // 组织和职位数据
    orgList: [],
    positionList: [],

    // 状态栏高度
    statusBarHeight: 20,
  },

  onLoad: function(options) {
    const { id, mode } = options;

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏样式和模式
    this.setData({
      statusBarHeight: statusBarHeight,
      mode: mode || 'accept'
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '受理工单'
    });

    // 初始化字典数据
    this.initDictData();

    if (id) {
      this.loadWorkOrderDetail(id);
      this.loadStaffList();
    } else {
      wx.showToast({
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const personWorkStatusDict = util.getDictByNameEn('person_work_status')[0].children;

      // 获取员工状态字典
      const personStatus = util.getDictByNameEn('person_status');
      const personStatusDict = (personStatus && personStatus.length > 0 && personStatus[0].children) ?
        personStatus[0].children : [];

      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender');
      const genderDictData = (genderDict && genderDict.length > 0 && genderDict[0].children) ?
        genderDict[0].children : [];

      this.setData({
        personWorkStatusDict,
        personStatusDict,
        genderDict: genderDictData
      });

      // 加载组织和职位数据
      this.loadOrgAndPositionData();
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载组织和职位数据
  loadOrgAndPositionData: function() {
    // 加载组织数据
    propertyApi.getOrgTree().then(res => {
      if (res && res.list && Array.isArray(res.list)) {
        this.setData({
          orgList: res.list
        });
      }
    }).catch(err => {
      console.error('获取组织列表失败:', err);
    });

    const params = {
      pageNum: 1,
      pageSize: 100 // 获取所有职位
    };
    // 加载职位数据
    commApi.getPositionPage(params).then(res => {
      if (res && res.list && Array.isArray(res.list)) {
        this.setData({
          positionList: res.list
        });
      }
    }).catch(err => {
      console.error('获取职位列表失败:', err);
    });
  },

  // 加载工单详情
  loadWorkOrderDetail: function(id) {
    this.setData({ loading: true });

    workOrderApi.getPropertyWorkOrderDetail(id)
      .then(order => {
        console.log('工单详情数据：', order);
        this.setData({
          workOrder: order,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单详情失败', error);
        this.setData({ loading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载员工列表
  loadStaffList: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };

    workOrderApi.getPropertyPrsonList(params)
      .then(res => {
        console.log('获取员工列表成功', res);

        // 为员工添加选中状态和工作状态显示，参考员工管理列表的显示格式
        const staffList = (res || []).map(staff => this.formatStaffData(staff));

        this.setData({
          staffList
        });
      })
      .catch(err => {
        console.error('获取员工列表失败', err);
        wx.showToast({
          title: '获取员工列表失败',
          icon: 'none'
        });
      });
  },

  // 格式化员工数据，参考员工管理页面的格式
  formatStaffData: function(rawData) {
    // 获取组织名称
    const orgName = this.getOrgName(rawData.orgId);

    // 获取职位名称
    const positionName = this.getPositionName(rawData.positionId);

    // 获取状态显示文本
    const statusText = this.getStatusText(rawData.status);

    // 获取性别显示文本
    const genderText = this.getGenderText(rawData.gender);

    // 获取工作状态文本
    const workStatusText = this.getWorkStatusText(rawData.workStatus);

    return {
      ...rawData,
      selected: false,
      id: rawData.id,
      name: rawData.personName || '',
      gender: genderText,
      age: rawData.age || 0,
      phone: rawData.phone || '',
      employeeId: rawData.personNumber || '',
      organization: orgName,
      position: positionName,
      status: statusText,
      workStatusText: workStatusText,
      // 保留原始数据用于API调用
      orgId: rawData.orgId,
      positionId: rawData.positionId,
      statusCode: rawData.status,
      genderCode: rawData.gender,
      workStatus: rawData.workStatus
    };
  },

  // 根据组织ID获取组织名称
  getOrgName: function(orgId) {
    if (!orgId || !this.data.orgList.length) return '未知组织';

    const findOrg = (orgs) => {
      for (let org of orgs) {
        if (org.id === orgId) {
          return org.orgName;
        }
        if (org.children && org.children.length > 0) {
          const found = findOrg(org.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findOrg(this.data.orgList) || '未知组织';
  },

  // 根据职位ID获取职位名称
  getPositionName: function(positionId) {
    if (!positionId || !this.data.positionList.length) return '未知职位';

    const position = this.data.positionList.find(p => p.id === positionId);
    return position ? position.positionName : '未知职位';
  },

  // 根据状态码获取状态文本
  getStatusText: function(statusCode) {
    if (!statusCode || !this.data.personStatusDict.length) return '未知状态';

    const status = this.data.personStatusDict.find(s => s.nameEn === statusCode);
    return status ? status.nameCn : '未知状态';
  },

  // 根据性别码获取性别文本
  getGenderText: function(genderCode) {
    if (!genderCode || !this.data.genderDict.length) return '未知';

    const gender = this.data.genderDict.find(g => g.nameEn === genderCode);
    return gender ? gender.nameCn : '未知';
  },

  // 获取员工工作状态文本
  getWorkStatusText: function(workStatus) {
    const statusDict = this.data.personWorkStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === workStatus);
    return statusItem ? statusItem.nameCn : (workStatus || '未知');
  },

  // 选择/取消选择员工
  toggleStaffSelection: function(e) {
    const staffId = e.currentTarget.dataset.id;
    const staffList = this.data.staffList.map(staff => {
      if (staff.id === staffId) {
        return { ...staff, selected: !staff.selected };
      }
      return staff;
    });

    // 更新选中的员工ID列表
    const selectedStaffIds = staffList.filter(staff => staff.selected).map(staff => staff.id);

    this.setData({
      staffList,
      selectedStaffIds
    });
  },

  // 提交受理工单
  submitAccept: function() {
    if (this.data.selectedStaffIds.length === 0) {
      wx.showToast({
        title: '请选择至少一个员工',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认受理',
      content: `确定要将工单分配给选中的${this.data.selectedStaffIds.length}个员工吗？`,
      success: (res) => {
        if (res.confirm) {
          this.doSubmitAccept();
        }
      }
    });
  },

  // 执行提交受理
  doSubmitAccept: function() {
    const params = {
      id: this.data.workOrder.id,
      personIds: this.data.selectedStaffIds
    };

    this.setData({ submitting: true });
    wx.showLoading({
      title: '受理中...'
    });

    workOrderApi.propertyAcceptedWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ submitting: false });

        wx.showToast({
          title: '工单受理成功',
          icon: 'success'
        });

        setTimeout(() => {
          // 通知上级页面刷新数据
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2]; // 获取上一个页面

          if (prevPage && prevPage.route.includes('workorder/detail')) {
            // 如果上一个页面是工单详情页，调用其刷新方法
            prevPage.loadWorkOrderDetail(this.data.workOrder.id);
          }

          // 通知更上级的页面刷新（工单管理页面或列表页面）
          if (pages.length >= 3) {
            const grandParentPage = pages[pages.length - 3];
            if (grandParentPage.route.includes('workorder/workorder')) {
              if (typeof grandParentPage.loadWorkOrderData === 'function') {
                grandParentPage.loadWorkOrderData();
              }
            }
            if (grandParentPage.route.includes('workorder/list')) {
              if (typeof grandParentPage.loadWorkOrders === 'function') {
                grandParentPage.loadWorkOrders();
              }
            }
          }

          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        wx.hideLoading();
        this.setData({ submitting: false });

        console.error('受理工单失败', error);
        wx.showToast({
          title: '受理失败，请重试',
          icon: 'none'
        });
      });
  },
  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
