// notification-manager.js
// 通知管理类

/**
 * 通知管理类
 * 用于处理小程序订阅消息相关功能
 */
const NotificationManager = {
  // 模板ID配置
  templates: {
    // 访客码创建成功通知 - 请替换为实际申请的模板ID
    // 开发环境下使用测试模板ID
    visitorCreated: 'GjvPMwi5HYYKVk1m5bY3G-rugHq-ixZGC2q9ZQbZfHs',
    // 访客到达通知 - 请替换为实际申请的模板ID
    // 开发环境下使用测试模板ID
    visitorArrived: 'GjvPMwi5HYYKVk1m5bY3G-rugHq-ixZGC2q9ZQbZfHs',
    // 访客码即将到期通知 - 请替换为实际申请的模板ID
    // 开发环境下使用测试模板ID
    visitorExpiring: 'GjvPMwi5HYYKVk1m5bY3G-rugHq-ixZGC2q9ZQbZfHs'
  },

  /**
   * 请求订阅消息权限
   * @param {Array} tmplIds 模板ID数组
   * @returns {Promise} 订阅结果
   */
  requestSubscription: function(tmplIds) {
    return new Promise((resolve, reject) => {
      // 开发环境下，可以选择跳过实际订阅，直接返回模拟成功结果
      if (tmplIds.length === 0 || tmplIds[0] === '') {
        console.log('开发环境：跳过订阅消息请求');
        const mockResult = {};
        tmplIds.forEach(id => {
          mockResult[id] = 'accept';
        });
        resolve(mockResult);
        return;
      }

      wx.requestSubscribeMessage({
        tmplIds: tmplIds,
        success: (res) => {
          console.log('订阅消息请求成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('订阅消息请求失败:', err);
          // 开发环境下，失败时也返回模拟成功结果，避免阻塞开发
          const mockResult = {};
          tmplIds.forEach(id => {
            mockResult[id] = 'accept';
          });
          resolve(mockResult); // 使用resolve而不是reject，避免中断流程
        }
      });
    });
  },

  /**
   * 请求访客相关的所有订阅消息权限
   * @returns {Promise} 订阅结果
   */
  requestAllVisitorSubscriptions: function() {
    const tmplIds = [
      this.templates.visitorCreated,
      this.templates.visitorArrived,
      this.templates.visitorExpiring
    ];
    return this.requestSubscription(tmplIds);
  },

  /**
   * 请求访客码创建成功通知权限
   * @returns {Promise} 订阅结果
   */
  requestVisitorCreatedSubscription: function() {
    return this.requestSubscription([this.templates.visitorCreated]);
  },

  /**
   * 请求访客到达通知权限
   * @returns {Promise} 订阅结果
   */
  requestVisitorArrivedSubscription: function() {
    return this.requestSubscription([this.templates.visitorArrived]);
  },

  /**
   * 请求访客码即将到期通知权限
   * @returns {Promise} 订阅结果
   */
  requestVisitorExpiringSubscription: function() {
    return this.requestSubscription([this.templates.visitorExpiring]);
  },

  /**
   * 发送访客码创建成功通知
   * 注意：此方法需要在服务端调用，这里仅作为客户端请求的封装
   * @param {Object} visitorData 访客数据
   * @returns {Promise} 发送结果
   */
  sendVisitorCreatedNotification: function(visitorData) {
    // 实际应用中，这里应该调用服务端API发送订阅消息
    // 由于小程序无法直接发送订阅消息，需要通过服务端调用微信接口
    console.log('请求发送访客码创建成功通知:', visitorData);

    // 这里模拟调用服务端API
    return this._mockSendNotification('visitorCreated', visitorData);
  },

  /**
   * 发送访客到达通知
   * 注意：此方法需要在服务端调用，这里仅作为客户端请求的封装
   * @param {Object} visitorData 访客数据
   * @returns {Promise} 发送结果
   */
  sendVisitorArrivedNotification: function(visitorData) {
    console.log('请求发送访客到达通知:', visitorData);

    // 这里模拟调用服务端API
    return this._mockSendNotification('visitorArrived', visitorData);
  },

  /**
   * 发送访客码即将到期通知
   * 注意：此方法需要在服务端调用，这里仅作为客户端请求的封装
   * @param {Object} visitorData 访客数据
   * @returns {Promise} 发送结果
   */
  sendVisitorExpiringNotification: function(visitorData) {
    console.log('请求发送访客码即将到期通知:', visitorData);

    // 这里模拟调用服务端API
    return this._mockSendNotification('visitorExpiring', visitorData);
  },

  /**
   * 模拟发送通知（实际应用中应通过服务端API发送）
   * @param {string} type 通知类型
   * @param {Object} data 通知数据
   * @returns {Promise} 发送结果
   * @private
   */
  _mockSendNotification: function(type, data) {
    return new Promise((resolve) => {
      console.log(`模拟发送${type}通知:`, data);

      // 模拟网络请求延迟
      setTimeout(() => {
        resolve({
          success: true,
          errCode: 0,
          errMsg: 'request:ok'
        });
      }, 500);
    });
  },

  /**
   * 检查是否有即将到期的访客，并发送通知
   * 此方法应在应用启动时或定时调用
   * @param {Array} visitors 访客数组
   * @param {number} hoursThreshold 到期前多少小时发送通知，默认2小时
   */
  checkAndNotifyExpiringVisitors: function(visitors, hoursThreshold = 2) {
    if (!visitors || !visitors.length) return;

    const now = new Date();
    const thresholdMs = hoursThreshold * 60 * 60 * 1000;

    visitors.forEach(visitor => {
      if (visitor.status === 'pending') {
        // 计算访客码到期时间
        const expireTime = new Date(visitor.expireTime);
        const timeUntilExpire = expireTime - now;

        // 如果在阈值范围内即将到期，发送通知
        if (timeUntilExpire > 0 && timeUntilExpire <= thresholdMs) {
          this.sendVisitorExpiringNotification(visitor);
        }
      }
    });
  }
};

module.exports = NotificationManager;
