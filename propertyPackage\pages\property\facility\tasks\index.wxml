<!--巡检任务列表页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 顶部区域 -->
  <view class="header">
    <view class="user-info-card">
      <view class="user-avatar-container">
        <image class="user-avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
        <view class="user-status {{userInfo.status === 'online' ? 'online' : 'offline'}}"></view>
      </view>
      <view class="user-details">
        <view class="user-greeting-row">
          <view class="user-greeting">{{greeting}}</view>
          <view class="user-name">{{userInfo.name}}</view>
        </view>
        <view class="user-info-row">
          <view class="user-id">工号：{{userInfo.employeeId || 'PY-001'}}</view>
          <view class="user-position">{{userInfo.position || '物业维修员'}}</view>
        </view>
        <view class="user-progress-row">
          <view class="progress-text">本周已完成 {{statistics.completedTasks}}/{{statistics.completedTasks + statistics.pendingTasks}} 任务</view>
          <view class="user-progress-bar">
            <view class="progress-inner" style="width: {{statistics.completedTasks / (statistics.completedTasks + statistics.pendingTasks) * 100}}%;"></view>
          </view>
        </view>
        <view class="user-task-reminder" wx:if="{{statistics.todayTasks > 0}}">
          <view class="reminder-icon"></view>
          <text>今日还有 {{statistics.todayTasks}} 个任务待处理</text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrap">
        <view class="search-icon"></view>
        <input class="search-input" type="text" placeholder="搜索任务名称、位置" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
        <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="statistics-card" animation="{{animationData}}">
    <view class="stat-item" bindtap="filterTodayTasks" hover-class="stat-item-hover">
      <view class="stat-value-container">
        <view class="stat-value">{{statistics.todayTasks}}</view>
      </view>
      <view class="stat-label">今日任务</view>
    </view>
    <view class="stat-item" bindtap="filterPendingTasks" hover-class="stat-item-hover">
      <view class="stat-value-container">
        <view class="stat-value">{{statistics.pendingTasks}}</view>
      </view>
      <view class="stat-label">待执行</view>
    </view>
    <view class="stat-item" bindtap="filterCompletedTasks" hover-class="stat-item-hover">
      <view class="stat-value-container">
        <view class="stat-value">{{statistics.completedTasks}}</view>
      </view>
      <view class="stat-label">已完成</view>
    </view>
  </view>

  <!-- 任务类型标签栏 -->
  <scroll-view class="task-type-tabs" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view
      class="task-type-tab {{item.active ? 'active' : ''}}"
      wx:for="{{taskTypes}}"
      wx:key="id"
      bindtap="switchTaskType"
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 任务状态标签栏 -->
  <scroll-view class="task-status-tabs" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view
      class="task-status-tab {{item.active ? 'active' : ''}}"
      wx:for="{{taskStatuses}}"
      wx:key="id"
      bindtap="switchTaskStatus"
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 任务列表 -->
  <view class="task-list" wx:if="{{!isLoading && tasks.length > 0}}">
    <view
      class="task-card"
      wx:for="{{tasks}}"
      wx:key="id"
      bindtap="navigateToTaskDetail"
      data-id="{{item.id}}"
    >
      <view class="task-header">
        <view class="task-title-row">
          <view class="task-title">{{item.title}}</view>
          <view class="task-priority {{item.priority}}" wx:if="{{item.priority}}">{{item.priorityText}}</view>
        </view>
        <view class="task-type {{item.type}}">{{item.typeText}}</view>
      </view>

      <view class="task-info">
        <view class="task-info-item">
          <view class="calendar-icon"></view>
          <text>{{item.date}} {{item.time}}</text>
        </view>
        <view class="task-info-item">
          <view class="location-icon"></view>
          <text>{{item.location}}</text>
        </view>
        <view class="task-info-item">
          <view class="facility-icon"></view>
          <text>{{item.facilityCount}}个设施</text>
        </view>
      </view>

      <!-- 任务状态 -->
      <view class="task-status-row">
        <view class="task-status {{item.status}}">{{item.statusText}}</view>

        <!-- 进行中任务显示进度 -->
        <view class="task-progress" wx:if="{{item.status === 'processing'}}">
          <view class="progress-bar">
            <view class="progress-inner" style="width: {{item.progress}}%;"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>

        <!-- 已完成任务显示完成时间和结果 -->
        <view class="task-result" wx:if="{{item.status === 'completed'}}">
          <text>{{item.completedTime}} 完成</text>
          <view class="result-container">
            <view class="result-icon {{item.result === '正常' ? 'normal' : 'abnormal'}}"></view>
            <text class="result-text {{item.result === '正常' ? 'normal' : 'abnormal'}}">结果: {{item.result}}</text>
            <view class="view-detail" wx:if="{{item.result !== '正常'}}" catchtap="viewAbnormalDetail" data-id="{{item.id}}">查看详情</view>
          </view>
        </view>

        <!-- 待执行任务显示执行按钮 -->
        <view class="task-action" wx:if="{{item.status === 'pending'}}">
          <view class="execute-button" catchtap="navigateToTaskExecution" data-id="{{item.id}}">
            <view class="button-icon execute-icon {{item.type}}"></view>
            <text>执行任务</text>
          </view>
        </view>

        <!-- 进行中任务显示继续按钮 -->
        <view class="task-action" wx:if="{{item.status === 'processing'}}">
          <view class="continue-button" catchtap="navigateToTaskExecution" data-id="{{item.id}}">
            <view class="button-icon continue-icon {{item.type}}"></view>
            <text>继续执行</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <empty-state
    wx:if="{{!isLoading && tasks.length === 0}}"
    icon="empty-search"
    title="{{emptyStateTitle}}"
    description="{{emptyStateDescription}}"
  />
</view>
