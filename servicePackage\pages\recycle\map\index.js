// pages/recycle/map/index.js
Page({
  data: {
    darkMode: false,
    latitude: 39.908823,
    longitude: 116.397470,
    scale: 14,
    markers: [],
    recyclePoints: [],
    selectedPoint: null,
    currentFilter: '全部'
  },

  onLoad: function() {
    // 获取用户位置
    this.getUserLocation()

    // 加载回收点数据
    this.loadRecyclePoints()
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 获取用户位置
  getUserLocation: function() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const latitude = res.latitude
        const longitude = res.longitude

        this.setData({
          latitude: latitude,
          longitude: longitude
        })

        // 更新回收点距离
        this.updateDistance(latitude, longitude)
      },
      fail: (err) => {
        console.error('获取位置失败:', err)
        wx.showToast({
          title: '获取位置失败',
          icon: 'error'
        })
      }
    })
  },

  // 加载回收点数据
  loadRecyclePoints: function() {
    // 模拟从云数据库获取数据
    // 实际项目中应该使用云函数或云数据库API
    const recyclePoints = [
      {
        id: '1',
        name: '社区回收站',
        type: 'station',
        latitude: 39.913823,
        longitude: 116.407470,
        address: '北京市朝阳区建国路88号',
        distance: 0,
        tags: ['可回收物', '有害垃圾'],
        hours: '周一至周日 08:00-20:00',
        phone: '010-12345678',
        acceptItems: ['纸类', '塑料', '金属', '玻璃', '织物', '电子产品', '有害垃圾']
      },
      {
        id: '2',
        name: '智能回收箱',
        type: 'bin',
        latitude: 39.908823,
        longitude: 116.407470,
        address: '北京市朝阳区朝阳公园南路',
        distance: 0,
        tags: ['可回收物'],
        hours: '24小时',
        phone: '************',
        acceptItems: ['纸类', '塑料', '金属', '玻璃']
      },
      {
        id: '3',
        name: '环保回收中心',
        type: 'station',
        latitude: 39.903823,
        longitude: 116.397470,
        address: '北京市朝阳区工体北路',
        distance: 0,
        tags: ['可回收物', '有害垃圾', '厨余垃圾'],
        hours: '周一至周六 09:00-18:00',
        phone: '010-87654321',
        acceptItems: ['纸类', '塑料', '金属', '玻璃', '织物', '电子产品', '有害垃圾', '厨余垃圾']
      },
      {
        id: '4',
        name: '社区垃圾分类亭',
        type: 'bin',
        latitude: 39.918823,
        longitude: 116.397470,
        address: '北京市朝阳区东三环北路',
        distance: 0,
        tags: ['可回收物', '厨余垃圾'],
        hours: '周一至周日 07:00-22:00',
        phone: '010-11112222',
        acceptItems: ['纸类', '塑料', '金属', '玻璃', '厨余垃圾']
      }
    ]

    // 创建地图标记点
    const markers = recyclePoints.map((point, index) => {
      return {
        id: index,
        latitude: point.latitude,
        longitude: point.longitude,
        width: 30,
        height: 30,
        callout: {
          content: point.name,
          color: '#333333',
          fontSize: 12,
          borderRadius: 5,
          padding: 5,
          display: 'BYCLICK'
        }
      }
    })

    this.setData({
      recyclePoints: recyclePoints,
      markers: markers
    })

    // 如果已经获取到用户位置，更新距离
    if (this.data.latitude && this.data.longitude) {
      this.updateDistance(this.data.latitude, this.data.longitude)
    }
  },

  // 更新回收点距离
  updateDistance: function(latitude, longitude) {
    const recyclePoints = this.data.recyclePoints.map(point => {
      // 计算距离（简化版，实际应使用更精确的算法）
      const distance = this.calculateDistance(
        latitude, longitude,
        point.latitude, point.longitude
      )

      return {
        ...point,
        distance: distance.toFixed(1)
      }
    })

    // 按距离排序
    recyclePoints.sort((a, b) => a.distance - b.distance)

    this.setData({
      recyclePoints: recyclePoints
    })
  },

  // 计算两点之间的距离（km）
  calculateDistance: function(lat1, lon1, lat2, lon2) {
    const R = 6371 // 地球半径（km）
    const dLat = this.deg2rad(lat2 - lat1)
    const dLon = this.deg2rad(lon2 - lon1)
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    const d = R * c
    return d
  },

  // 角度转弧度
  deg2rad: function(deg) {
    return deg * (Math.PI/180)
  },

  // 移动到当前位置
  moveToLocation: function() {
    this.getUserLocation()

    const mapCtx = wx.createMapContext('recycleMap')
    mapCtx.moveToLocation()
  },

  // 放大地图
  zoomIn: function() {
    if (this.data.scale < 20) {
      this.setData({
        scale: this.data.scale + 1
      })
    }
  },

  // 缩小地图
  zoomOut: function() {
    if (this.data.scale > 5) {
      this.setData({
        scale: this.data.scale - 1
      })
    }
  },

  // 标记点点击事件
  markerTap: function(e) {
    const markerId = e.markerId
    const point = this.data.recyclePoints[markerId]

    this.setData({
      selectedPoint: point
    })
  },

  // 选择回收点
  selectPoint: function(e) {
    const id = e.currentTarget.dataset.id
    const point = this.data.recyclePoints.find(item => item.id === id)

    if (point) {
      this.setData({
        selectedPoint: point,
        latitude: point.latitude,
        longitude: point.longitude
      })
    }
  },

  // 关闭详情
  closeDetail: function() {
    this.setData({
      selectedPoint: null
    })
  },

  // 阻止冒泡
  stopPropagation: function(e) {
    return
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 拨打电话
  callPhone: function(e) {
    const phone = e.currentTarget.dataset.phone
    wx.makePhoneCall({
      phoneNumber: phone
    })
  },

  // 导航到回收点
  navigateToPoint: function() {
    const point = this.data.selectedPoint

    wx.openLocation({
      latitude: point.latitude,
      longitude: point.longitude,
      name: point.name,
      address: point.address,
      scale: 18
    })
  },

  // 分享回收点
  sharePoint: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 显示筛选选项
  showFilterOptions: function() {
    wx.showActionSheet({
      itemList: ['全部', '回收站', '智能回收箱', '距离最近', '可回收物', '有害垃圾', '厨余垃圾'],
      success: (res) => {
        const filterOptions = ['全部', '回收站', '智能回收箱', '距离最近', '可回收物', '有害垃圾', '厨余垃圾']
        const selectedFilter = filterOptions[res.tapIndex]

        this.setData({
          currentFilter: selectedFilter
        })

        // 根据筛选条件过滤回收点
        this.filterRecyclePoints(selectedFilter)
      }
    })
  },

  // 筛选回收点
  filterRecyclePoints: function(filter) {
    let filteredPoints = [...this.data.recyclePoints]

    switch (filter) {
      case '回收站':
        filteredPoints = filteredPoints.filter(point => point.type === 'station')
        break
      case '智能回收箱':
        filteredPoints = filteredPoints.filter(point => point.type === 'bin')
        break
      case '距离最近':
        filteredPoints.sort((a, b) => a.distance - b.distance)
        break
      case '可回收物':
        filteredPoints = filteredPoints.filter(point =>
          point.tags.includes('可回收物') ||
          point.acceptItems.some(item => ['纸类', '塑料', '金属', '玻璃', '织物'].includes(item))
        )
        break
      case '有害垃圾':
        filteredPoints = filteredPoints.filter(point =>
          point.tags.includes('有害垃圾') ||
          point.acceptItems.includes('有害垃圾')
        )
        break
      case '厨余垃圾':
        filteredPoints = filteredPoints.filter(point =>
          point.tags.includes('厨余垃圾') ||
          point.acceptItems.includes('厨余垃圾')
        )
        break
      default:
        // 全部，不需要筛选
        break
    }

    // 更新标记点
    const markers = filteredPoints.map((point, index) => {
      return {
        id: index,
        latitude: point.latitude,
        longitude: point.longitude,
        width: 30,
        height: 30,
        callout: {
          content: point.name,
          color: '#333333',
          fontSize: 12,
          borderRadius: 5,
          padding: 5,
          display: 'BYCLICK'
        }
      }
    })

    this.setData({
      recyclePoints: filteredPoints,
      markers: markers
    })
  }
})
