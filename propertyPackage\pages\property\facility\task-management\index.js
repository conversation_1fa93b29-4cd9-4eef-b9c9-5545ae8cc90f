// pages/property/facility/task-management/index.js

const dateUtil = require('../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,
    
    // 任务类型标签
    taskTypes: [
      { id: 'all', name: '全部', active: true },
      { id: 'inspection', name: '巡检', active: false },
      { id: 'maintenance', name: '保养', active: false },
      { id: 'repair', name: '报修', active: false }
    ],
    currentTaskType: 'all',
    
    // 任务状态标签
    taskStatuses: [
      { id: 'all', name: '全部', active: true },
      { id: 'pending', name: '待执行', active: false },
      { id: 'processing', name: '进行中', active: false },
      { id: 'completed', name: '已完成', active: false }
    ],
    currentTaskStatus: 'all',
    
    // 任务列表
    tasks: [],
    
    // 搜索相关
    searchValue: '',
    
    // 动画数据
    animationData: {},
    
    // 统计数据
    statistics: {
      todayTasks: 0,
      pendingTasks: 0,
      completedTasks: 0
    },
    
    // 日期筛选
    filterDate: ''
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '任务管理'
    });
    
    // 添加页面进入动画
    this.animatePageEnter();
    
    // 处理URL参数，自动应用筛选条件
    if (options) {
      // 处理任务类型筛选
      if (options.type) {
        const taskType = options.type;
        // 更新任务类型标签状态
        const taskTypes = this.data.taskTypes.map(item => {
          return {
            ...item,
            active: item.id === taskType
          };
        });
        
        this.setData({
          taskTypes: taskTypes,
          currentTaskType: taskType
        });
      }
      
      // 处理任务状态筛选
      if (options.status) {
        const taskStatus = options.status;
        // 更新任务状态标签状态
        const taskStatuses = this.data.taskStatuses.map(item => {
          return {
            ...item,
            active: item.id === taskStatus
          };
        });
        
        this.setData({
          taskStatuses: taskStatuses,
          currentTaskStatus: taskStatus
        });
      }
      
      // 处理日期筛选
      if (options.date) {
        this.setData({
          filterDate: options.date
        });
      }
    }
    
    // 加载任务数据
    this.loadTasks();
  },
  
  // 页面显示时触发
  onShow: function() {
    // 可以在这里刷新数据
    if (!this.data.isLoading) {
      this.loadTasks();
    }
  },
  
  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },
  
  // 加载任务数据
  loadTasks: function() {
    this.setData({ isLoading: true });
    
    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const tasks = this.getMockTasks();
      
      // 计算统计数据
      const statistics = this.calculateStatistics(tasks);
      
      this.setData({
        tasks: tasks,
        statistics: statistics,
        isLoading: false
      });
    }, 500);
  },
  
  // 获取模拟任务数据
  getMockTasks: function() {
    const today = new Date();
    const todayStr = dateUtil.formatDate(today);
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = dateUtil.formatDate(yesterday);
    
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = dateUtil.formatDate(tomorrow);
    
    return [
      {
        id: '1',
        title: '小区监控设备巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'pending',
        statusText: '待执行',
        date: todayStr,
        time: '09:00-10:00',
        location: '小区各监控点位',
        facilityCount: 12,
        description: '检查小区各监控设备运行状态，确保画面清晰，存储正常',
        assignee: '张工'
      },
      {
        id: '2',
        title: '消防设备月度巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'processing',
        statusText: '进行中',
        date: todayStr,
        time: '14:00-16:00',
        location: '小区公共区域',
        facilityCount: 25,
        description: '检查小区消防栓、灭火器、烟感等设备状态',
        assignee: '张工',
        progress: 60
      },
      {
        id: '3',
        title: '电梯季度保养',
        type: 'maintenance',
        typeText: '保养',
        status: 'pending',
        statusText: '待执行',
        date: tomorrowStr,
        time: '10:00-12:00',
        location: '1号楼、2号楼',
        facilityCount: 4,
        description: '对小区电梯进行季度保养，检查各部件运行状态',
        assignee: '张工'
      },
      {
        id: '4',
        title: '门禁系统巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'completed',
        statusText: '已完成',
        date: yesterdayStr,
        time: '15:00-16:00',
        location: '各单元门',
        facilityCount: 8,
        description: '检查各单元门门禁系统运行状态，确保刷卡、密码开门正常',
        assignee: '张工',
        completedTime: '16:05',
        result: '正常'
      },
      {
        id: '5',
        title: '中央空调系统保养',
        type: 'maintenance',
        typeText: '保养',
        status: 'completed',
        statusText: '已完成',
        date: yesterdayStr,
        time: '09:00-11:00',
        location: '小区公共区域',
        facilityCount: 2,
        description: '对中央空调系统进行保养，清洗过滤网，检查制冷效果',
        assignee: '李工',
        completedTime: '10:45',
        result: '正常'
      },
      {
        id: '6',
        title: '1号楼电梯故障报修',
        type: 'repair',
        typeText: '报修',
        status: 'pending',
        statusText: '待处理',
        date: todayStr,
        time: '08:30',
        location: '1号楼',
        description: '1号楼电梯无法正常运行，按键失灵',
        reporter: '王业主',
        contactPhone: '13800138000'
      },
      {
        id: '7',
        title: '小区大门门禁故障',
        type: 'repair',
        typeText: '报修',
        status: 'processing',
        statusText: '处理中',
        date: yesterdayStr,
        time: '16:30',
        location: '小区大门',
        description: '小区大门门禁刷卡无反应',
        reporter: '李业主',
        contactPhone: '13900139000',
        assignee: '张工'
      }
    ];
  },
  
  // 计算统计数据
  calculateStatistics: function(tasks) {
    const today = dateUtil.formatDate(new Date());
    
    // 今日任务数
    const todayTasks = tasks.filter(task => task.date === today).length;
    
    // 待执行任务数
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    
    // 已完成任务数
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    
    return {
      todayTasks: todayTasks,
      pendingTasks: pendingTasks,
      completedTasks: completedTasks
    };
  },
  
  // 切换任务类型标签
  switchTaskType: function(e) {
    const typeId = e.currentTarget.dataset.id;
    
    // 更新任务类型标签状态
    const taskTypes = this.data.taskTypes.map(item => {
      return {
        ...item,
        active: item.id === typeId
      };
    });
    
    this.setData({
      taskTypes: taskTypes,
      currentTaskType: typeId
    });
    
    // 根据筛选条件过滤任务
    this.filterTasks();
  },
  
  // 切换任务状态标签
  switchTaskStatus: function(e) {
    const statusId = e.currentTarget.dataset.id;
    
    // 更新任务状态标签状态
    const taskStatuses = this.data.taskStatuses.map(item => {
      return {
        ...item,
        active: item.id === statusId
      };
    });
    
    this.setData({
      taskStatuses: taskStatuses,
      currentTaskStatus: statusId
    });
    
    // 根据筛选条件过滤任务
    this.filterTasks();
  },
  
  // 搜索任务
  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
    
    // 根据搜索词筛选任务
    this.filterTasks();
  },
  
  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: ''
    });
    
    // 重置筛选
    this.filterTasks();
  },
  
  // 筛选任务
  filterTasks: function() {
    const { currentTaskType, currentTaskStatus, searchValue, filterDate } = this.data;
    const allTasks = this.getMockTasks();
    
    // 根据类型、状态、日期和搜索词筛选
    let filteredTasks = allTasks;
    
    // 类型筛选
    if (currentTaskType !== 'all') {
      filteredTasks = filteredTasks.filter(item => item.type === currentTaskType);
    }
    
    // 状态筛选
    if (currentTaskStatus !== 'all') {
      filteredTasks = filteredTasks.filter(item => item.status === currentTaskStatus);
    }
    
    // 日期筛选
    if (filterDate) {
      filteredTasks = filteredTasks.filter(item => item.date === filterDate);
    }
    
    // 搜索词筛选
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      filteredTasks = filteredTasks.filter(item => 
        item.title.toLowerCase().includes(searchLower) || 
        item.location.toLowerCase().includes(searchLower) || 
        item.description.toLowerCase().includes(searchLower)
      );
    }
    
    this.setData({
      tasks: filteredTasks
    });
  },
  
  // 导航到任务详情页
  navigateToTaskDetail: function(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/tasks/detail/index?id=${taskId}`
    });
  },
  
  // 导航到任务执行页
  navigateToTaskExecution: function(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/tasks/execute/index?id=${taskId}`
    });
  },
  
  // 分配任务
  assignTask: function(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/task-management/assign/index?id=${taskId}`
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载任务数据
    this.loadTasks();
    
    // 停止下拉刷新
    wx.stopPullDownRefresh();
  }
})
