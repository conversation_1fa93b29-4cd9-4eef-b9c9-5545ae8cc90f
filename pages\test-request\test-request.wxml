<!--请求队列管理测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">请求队列管理测试</text>
    <text class="subtitle">验证token有效时直接执行请求的优化效果</text>
  </view>

  <!-- 测试按钮区域 -->
  <view class="test-section">
    <text class="section-title">测试功能</text>
    
    <view class="button-grid">
      <button 
        class="test-button primary" 
        bindtap="testConcurrentWithToken"
        disabled="{{isTestingConcurrent}}"
      >
        {{isTestingConcurrent ? '测试中...' : '并发请求测试'}}
      </button>
      
      <button 
        class="test-button warning" 
        bindtap="testWithoutToken"
        disabled="{{isTestingWithoutToken}}"
      >
        {{isTestingWithoutToken ? '测试中...' : '无Token请求测试'}}
      </button>
      
      <button class="test-button danger" bindtap="testTokenExpired">
        Token过期测试
      </button>
      
      <button class="test-button info" bindtap="testRapidRequests">
        快速连续请求
      </button>
    </view>
  </view>

  <!-- 工具按钮区域 -->
  <view class="tool-section">
    <text class="section-title">工具功能</text>
    
    <view class="button-grid">
      <button class="tool-button" bindtap="manualLogin">
        手动登录
      </button>
      
      <button class="tool-button" bindtap="clearLogin">
        清除登录
      </button>
      
      <button class="tool-button" bindtap="viewStorage">
        查看存储
      </button>
      
      <button class="tool-button" bindtap="clearResults">
        清除结果
      </button>
    </view>
  </view>

  <!-- 测试结果区域 -->
  <view class="results-section">
    <view class="results-header">
      <text class="section-title">测试结果</text>
      <text class="results-count">({{testResults.length}}条)</text>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view 
        wx:for="{{testResults}}" 
        wx:key="id" 
        class="result-item {{item.type}}"
      >
        <view class="result-icon">
          <text wx:if="{{item.type === 'success'}}">✓</text>
          <text wx:elif="{{item.type === 'error'}}">✗</text>
          <text wx:elif="{{item.type === 'warning'}}">⚠</text>
          <text wx:else>ℹ</text>
        </view>
        <text class="result-message">{{item.message}}</text>
      </view>
      
      <view wx:if="{{testResults.length === 0}}" class="empty-results">
        <text>暂无测试结果</text>
        <text>点击上方按钮开始测试</text>
      </view>
    </scroll-view>
  </view>

  <!-- 说明区域 -->
  <view class="info-section">
    <text class="section-title">测试说明</text>
    <view class="info-content">
      <view class="info-item">
        <text class="info-label">并发请求测试：</text>
        <text class="info-desc">验证有token时多个请求是否并发执行</text>
      </view>
      <view class="info-item">
        <text class="info-label">无Token请求测试：</text>
        <text class="info-desc">验证无token时是否正确触发登录流程</text>
      </view>
      <view class="info-item">
        <text class="info-label">Token过期测试：</text>
        <text class="info-desc">验证401响应时的token刷新机制</text>
      </view>
      <view class="info-item">
        <text class="info-label">快速连续请求：</text>
        <text class="info-desc">验证防重复请求和队列管理</text>
      </view>
    </view>
  </view>
</view>
