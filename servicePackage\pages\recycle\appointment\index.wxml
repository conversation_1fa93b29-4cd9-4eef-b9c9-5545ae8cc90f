<!--废品回收预约页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- Banner内容 -->
  <view class="banner-content">
    <view class="banner-desc">填写信息，预约上门回收服务</view>
  </view>

  <!-- 预约表单 -->
  <view class="appointment-form">
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step {{currentStep >= 1 ? 'active' : ''}}">
        <view class="step-number">1</view>
        <view class="step-label">基本信息</view>
      </view>
      <view class="step-line"></view>
      <view class="step {{currentStep >= 2 ? 'active' : ''}}">
        <view class="step-number">2</view>
        <view class="step-label">废品信息</view>
      </view>
      <view class="step-line"></view>
      <view class="step {{currentStep >= 3 ? 'active' : ''}}">
        <view class="step-number">3</view>
        <view class="step-label">确认提交</view>
      </view>
    </view>

    <!-- 步骤1：基本信息 -->
    <view class="form-step" hidden="{{currentStep !== 1}}">
      <view class="form-group">
        <view class="form-label">联系人 <text class="required">*</text></view>
        <input class="form-input" placeholder="请输入联系人姓名" value="{{formData.name}}" bindinput="inputName" />
      </view>

      <view class="form-group">
        <view class="form-label">联系电话 <text class="required">*</text></view>
        <input class="form-input" type="number" placeholder="请输入联系电话" value="{{formData.phone}}" bindinput="inputPhone" />
      </view>

      <view class="form-group">
        <view class="form-label">回收地址 <text class="required">*</text></view>
        <view class="address-tabs">
          <view class="address-tab {{addressInputMode === 'map' ? 'active' : ''}}" bindtap="switchAddressMode" data-mode="map">地图选择</view>
          <view class="address-tab {{addressInputMode === 'manual' ? 'active' : ''}}" bindtap="switchAddressMode" data-mode="manual">手动输入</view>
        </view>
        <view class="address-picker" bindtap="chooseLocation" wx:if="{{addressInputMode === 'map'}}">
          <view class="address-text" wx:if="{{formData.address}}">{{formData.address}}</view>
          <view class="address-placeholder" wx:else>点击选择地址</view>
          <view class="address-icon"></view>
        </view>
        <input class="form-input" placeholder="请输入回收地址" value="{{formData.address}}" bindinput="inputAddress" wx:if="{{addressInputMode === 'manual'}}"/>
      </view>

      <view class="form-group">
        <view class="form-label">详细地址 <text class="required">*</text></view>
        <input class="form-input" placeholder="请输入详细地址（如门牌号）" value="{{formData.addressDetail}}" bindinput="inputAddressDetail" />
      </view>

      <view class="form-group">
        <view class="form-label">预约时间 <text class="required">*</text></view>
        <picker mode="multiSelector" bindchange="bindDateTimeChange" bindcolumnchange="bindDateTimeColumnChange" value="{{dateTimeIndex}}" range="{{dateTimeArray}}">
          <view class="picker-view">
            <view class="picker-text" wx:if="{{formData.appointmentTime}}">{{formData.appointmentTime}}</view>
            <view class="picker-placeholder" wx:else>请选择预约时间</view>
            <view class="picker-icon"></view>
          </view>
        </picker>
      </view>

      <view class="form-buttons">
        <button class="btn-next" bindtap="nextStep">下一步</button>
      </view>
    </view>

    <!-- 步骤2：废品信息 -->
    <view class="form-step" hidden="{{currentStep !== 2}}">
      <view class="form-group">
        <view class="form-label">废品类型 <text class="required">*</text></view>
        <view class="waste-types">
          <view class="waste-type {{item.selected ? 'selected' : ''}}" wx:for="{{wasteTypes}}" wx:key="id" bindtap="selectWasteType" data-id="{{item.id}}">
            <view class="waste-type-icon {{item.icon}}"></view>
            <view class="waste-type-name">{{item.name}}</view>
          </view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">预估重量</view>
        <view class="weight-slider">
          <slider bindchange="weightSliderChange" min="1" max="100" value="{{formData.weight}}" activeColor="#27AE60" block-size="28" show-value />
          <view class="weight-value">{{formData.weight}} kg</view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">备注信息</view>
        <textarea class="form-textarea" placeholder="请输入备注信息（如废品存放位置、搬运注意事项等）" value="{{formData.remark}}" bindinput="inputRemark"></textarea>
      </view>

      <view class="form-group">
        <view class="form-label">上传照片</view>
        <view class="photo-uploader">
          <view class="photo-list">
            <view class="photo-item" wx:for="{{formData.photos}}" wx:key="*this">
              <image class="photo-image" src="{{item}}" mode="aspectFill"></image>
              <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">×</view>
            </view>
            <view class="photo-add" bindtap="chooseImage" wx:if="{{formData.photos.length < 3}}">
              <view class="photo-add-icon">+</view>
              <view class="photo-add-text">添加照片</view>
            </view>
          </view>
          <view class="photo-tip">最多上传3张照片，帮助回收员了解废品情况</view>
        </view>
      </view>

      <view class="form-buttons">
        <button class="btn-prev" bindtap="prevStep">上一步</button>
        <button class="btn-next" bindtap="nextStep">下一步</button>
      </view>
    </view>

    <!-- 步骤3：确认提交 -->
    <view class="form-step" hidden="{{currentStep !== 3}}">
      <view class="confirm-card">
        <view class="confirm-title">预约信息确认</view>

        <view class="confirm-section">
          <view class="confirm-section-title">基本信息</view>
          <view class="confirm-item">
            <view class="confirm-label">联系人：</view>
            <view class="confirm-value">{{formData.name}}</view>
          </view>
          <view class="confirm-item">
            <view class="confirm-label">联系电话：</view>
            <view class="confirm-value">{{formData.phone}}</view>
          </view>
          <view class="confirm-item">
            <view class="confirm-label">回收地址：</view>
            <view class="confirm-value">{{formData.address}} {{formData.addressDetail}}</view>
          </view>
          <view class="confirm-item">
            <view class="confirm-label">预约时间：</view>
            <view class="confirm-value">{{formData.appointmentTime}}</view>
          </view>
        </view>

        <view class="confirm-section">
          <view class="confirm-section-title">废品信息</view>
          <view class="confirm-item">
            <view class="confirm-label">废品类型：</view>
            <view class="confirm-value">{{selectedWasteTypesText}}</view>
          </view>
          <view class="confirm-item">
            <view class="confirm-label">预估重量：</view>
            <view class="confirm-value">{{formData.weight}} kg</view>
          </view>
          <view class="confirm-item" wx:if="{{formData.remark}}">
            <view class="confirm-label">备注信息：</view>
            <view class="confirm-value">{{formData.remark}}</view>
          </view>
        </view>

        <view class="confirm-photos" wx:if="{{formData.photos.length > 0}}">
          <view class="confirm-section-title">废品照片</view>
          <view class="confirm-photo-list">
            <image class="confirm-photo" wx:for="{{formData.photos}}" wx:key="*this" src="{{item}}" mode="aspectFill"></image>
          </view>
        </view>

        <view class="agreement">
          <checkbox-group bindchange="agreementChange">
            <label class="agreement-label">
              <checkbox value="agreed" checked="{{formData.agreed}}" color="#27AE60" />
              <text class="agreement-text">我已阅读并同意<text class="agreement-link" bindtap="showAgreement">《废品回收服务协议》</text></text>
            </label>
          </checkbox-group>
        </view>
      </view>

      <view class="form-buttons">
        <button class="btn-prev" bindtap="prevStep">上一步</button>
        <button class="btn-submit {{formData.agreed ? '' : 'disabled'}}" bindtap="submitForm" disabled="{{!formData.agreed}}">提交预约</button>
      </view>
    </view>
  </view>

  <!-- 提交成功弹窗 -->
  <view class="success-modal" wx:if="{{showSuccessModal}}">
    <view class="success-content">
      <view class="success-icon"></view>
      <view class="success-title">预约成功</view>
      <view class="success-message">您的废品回收预约已提交成功，回收员将在预约时间上门服务</view>
      <view class="success-order">订单编号：{{orderNumber}}</view>
      <view class="success-buttons">
        <button class="success-btn home-btn" bindtap="backToHome">返回首页</button>
        <button class="success-btn view-btn" bindtap="viewMyAppointments">查看我的预约</button>
      </view>
    </view>
  </view>
</view>