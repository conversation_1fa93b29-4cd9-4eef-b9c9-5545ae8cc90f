# 物业员工认证存储键不一致问题修复

## 问题描述

用户反馈：在首页物业员工认证信息获取成功，并且状态为`status: "active"`，但点击物业工作台时仍然提示需要去认证物业人员。

## 问题原因

经过代码分析发现，存在存储键不一致的问题：

### 1. 数据保存阶段
在`checkPropertyStaffAuth`方法中，物业员工认证信息被保存为：
```javascript
wx.setStorageSync('propertyInfo', res);  // 保存原始数据
wx.setStorageSync('isPropertyStaff', true);  // 保存认证状态
```

### 2. 权限检查阶段
在`navigateToPropertyManagement`方法中，权限检查使用的是：
```javascript
const propertyAuth = wx.getStorageSync('propertyAuth') || {};  // 检查不同的键
```

### 3. 问题分析
- **保存时使用**：`propertyInfo` 键
- **检查时使用**：`propertyAuth` 键
- **结果**：权限检查时找不到认证信息，导致提示用户去认证

## 修复方案

### 1. 修改数据保存逻辑

在`checkPropertyStaffAuth`方法中，同时保存两种格式的数据：

```javascript
if (res) {
  // 保存物业员工认证信息（兼容新旧格式）
  wx.setStorageSync('propertyInfo', res);
  wx.setStorageSync('isPropertyStaff', true);
  
  // 保存新格式的物业认证信息，供权限检查使用
  const propertyAuth = {
    isAuthenticated: true,
    status: res.status || 'active', // 如果没有status字段，默认为active
    ...res // 包含其他所有字段
  };
  wx.setStorageSync('propertyAuth', propertyAuth);
  
  console.log('物业员工认证信息已保存:', propertyAuth);
} else {
  wx.removeStorageSync('propertyInfo');
  wx.removeStorageSync('propertyAuth');
  wx.setStorageSync('isPropertyStaff', false);
  console.log('用户不是物业员工');
}
```

### 2. 修改错误处理逻辑

确保在API调用失败时也清除`propertyAuth`：

```javascript
.catch(error => {
  console.error('获取物业员工认证失败:', error);
  wx.removeStorageSync('propertyInfo');
  wx.removeStorageSync('propertyAuth');  // 新增
  wx.setStorageSync('isPropertyStaff', false);
  resolve();
});
```

### 3. 修改无小区ID时的处理

```javascript
console.log('没有小区ID，跳过物业员工认证检查');
wx.removeStorageSync('propertyInfo');
wx.removeStorageSync('propertyAuth');  // 新增
wx.setStorageSync('isPropertyStaff', false);
resolve();
```

## 数据结构对比

### 修复前
```javascript
// 保存的数据
propertyInfo: {
  id: "123",
  name: "张三",
  status: "active",
  // ... 其他字段
}

// 检查时期望的数据（不存在）
propertyAuth: undefined
```

### 修复后
```javascript
// 保存的数据（兼容旧格式）
propertyInfo: {
  id: "123",
  name: "张三", 
  status: "active",
  // ... 其他字段
}

// 新增的权限检查数据
propertyAuth: {
  isAuthenticated: true,
  status: "active",
  id: "123",
  name: "张三",
  // ... 其他字段
}
```

## 权限检查流程

### 修复前的流程
```
用户点击物业工作台
    ↓
检查 propertyAuth（不存在）
    ↓
提示"需要物业认证"
```

### 修复后的流程
```
用户点击物业工作台
    ↓
检查 propertyAuth.isAuthenticated（true）
    ↓
检查 propertyAuth.status（"active"）
    ↓
允许访问物业工作台
```

## API调用链路

### 1. 页面初始化
```javascript
onLoad() → checkLogin() → loadUserInfo() → checkPropertyStaffAuth()
```

### 2. 物业认证检查
```javascript
checkPropertyStaffAuth() → userApi.getProperTyInfo() → 保存 propertyAuth
```

### 3. 权限验证
```javascript
navigateToPropertyManagement() → 检查 propertyAuth → 允许访问
```

## 兼容性考虑

### 1. 向后兼容
- 保留原有的`propertyInfo`存储，确保依赖此数据的其他功能不受影响
- 保留`isPropertyStaff`标记，维持现有的判断逻辑

### 2. 数据一致性
- 两种格式的数据同时更新，确保数据一致性
- 错误处理时同时清除两种格式的数据

### 3. 默认值处理
- 如果API返回的数据没有`status`字段，默认设置为`"active"`
- 确保权限检查时有正确的状态值

## 测试验证

### 1. 正常流程测试
1. 用户登录并选择小区
2. 系统获取物业员工认证信息
3. 认证信息保存成功（两种格式）
4. 点击物业工作台，权限检查通过
5. 成功进入物业工作台

### 2. 异常情况测试
1. **API调用失败**：确保两种格式数据都被清除
2. **无小区ID**：确保两种格式数据都被清除
3. **非物业员工**：确保认证状态正确设置为false

### 3. 状态验证测试
1. **status为"active"**：允许访问
2. **status为其他值**：提示等待审核
3. **无status字段**：默认为"active"，允许访问

## 相关文件

- `pages/index/index.js` - 首页逻辑，包含认证检查和权限验证
- `api/userApi.js` - 用户API，包含`getProperTyInfo`方法

## 注意事项

1. **数据同步**：确保`propertyInfo`和`propertyAuth`数据保持同步
2. **清理逻辑**：在清除认证信息时，需要同时清除两种格式的数据
3. **状态映射**：确保API返回的状态值与权限检查逻辑一致
4. **错误处理**：所有可能导致认证失败的情况都要正确处理

修复后，用户的物业员工认证信息将被正确保存和检查，解决了权限验证失败的问题。
