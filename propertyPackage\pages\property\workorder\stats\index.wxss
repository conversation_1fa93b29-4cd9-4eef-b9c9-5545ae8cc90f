/* 工单统计分析页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}



/* 时间范围选择器样式 */
.time-range-selector {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.time-range-option {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.time-range-option.active {
  background-color: #ff8c00;
  color: #fff;
}

/* 标签页导航样式 */
.tab-nav {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  justify-content: space-around; /* 4个标签均匀分布 */
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  position: relative;
  padding: 0 10rpx; /* 添加水平内边距 */
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.overview-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='7' height='7'%3E%3C/rect%3E%3Crect x='14' y='3' width='7' height='7'%3E%3C/rect%3E%3Crect x='14' y='14' width='7' height='7'%3E%3C/rect%3E%3Crect x='3' y='14' width='7' height='7'%3E%3C/rect%3E%3C/svg%3E");
}

.type-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21.21 15.89A10 10 0 1 1 8 2.83'%3E%3C/path%3E%3Cpath d='M22 12A10 10 0 0 0 12 2v10z'%3E%3C/path%3E%3C/svg%3E");
}

.efficiency-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.trend-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='20' x2='18' y2='10'%3E%3C/line%3E%3Cline x1='12' y1='20' x2='12' y2='4'%3E%3C/line%3E%3Cline x1='6' y1='20' x2='6' y2='14'%3E%3C/line%3E%3C/svg%3E");
}

.tab-item.active .overview-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='7' height='7'%3E%3C/rect%3E%3Crect x='14' y='3' width='7' height='7'%3E%3C/rect%3E%3Crect x='14' y='14' width='7' height='7'%3E%3C/rect%3E%3Crect x='3' y='14' width='7' height='7'%3E%3C/rect%3E%3C/svg%3E");
}

.tab-item.active .type-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21.21 15.89A10 10 0 1 1 8 2.83'%3E%3C/path%3E%3Cpath d='M22 12A10 10 0 0 0 12 2v10z'%3E%3C/path%3E%3C/svg%3E");
}

.tab-item.active .efficiency-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.tab-item.active .trend-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='20' x2='18' y2='10'%3E%3C/line%3E%3Cline x1='12' y1='20' x2='12' y2='4'%3E%3C/line%3E%3Cline x1='6' y1='20' x2='6' y2='14'%3E%3C/line%3E%3C/svg%3E");
}

.tab-text {
  font-size: 24rpx;
  color: #666;
}

.tab-item.active .tab-text {
  color: #ff8c00;
}

/* 加载中提示样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 标签页内容样式 */
.tab-content {
  padding: 30rpx;
}

/* 统计卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-body {
  padding: 30rpx;
}

/* 概览网格样式 */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 16rpx;
}

.summary-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 10rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #999;
}

/* 进度条样式 */
.progress-container {
  padding: 20rpx 0;
}

.progress-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}

.progress-bar {
  flex: 1;
  height: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
  margin: 0 20rpx;
}

.progress-fill {
  height: 100%;
  border-radius: 10rpx;
}

.progress-fill.completed {
  background-color: #4caf50;
}

.progress-fill.processing {
  background-color: #2196f3;
}

.progress-fill.pending {
  background-color: #ff9800;
}

.progress-fill.cancelled {
  background-color: #9e9e9e;
}

.progress-value {
  width: 80rpx;
  font-size: 24rpx;
  color: #666;
  text-align: right;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  min-height: 500rpx;
  position: relative;
}

/* ECharts容器样式 */
.echarts-container {
  width: 100%;
  height: 400rpx;
  margin-bottom: 20rpx;
}

.echarts-canvas {
  width: 100%;
  height: 100%;
}

/* 饼图样式 */
.pie-chart {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
}

.pie-segments {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  left: 0;
  top: 0;
}

.pie-segment::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 50%;
  top: 0;
  left: 0;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  margin-top: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
  width: 45%;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #999;
}

/* 处理时效样式 */
.efficiency-metrics {
  padding: 20rpx 0;
}

.efficiency-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.efficiency-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.response-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='22 12 18 12 15 21 9 3 6 12 2 12'%3E%3C/polyline%3E%3C/svg%3E");
}

.processing-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.ontime-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.efficiency-info {
  flex: 1;
}

.efficiency-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.efficiency-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
}

/* 柱状图样式 */
.bar-chart {
  display: flex;
  height: 100%;
}

.chart-y-axis {
  width: 60rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 10rpx;
}

.chart-y-axis text {
  font-size: 20rpx;
  color: #999;
  text-align: right;
}

.chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding-bottom: 40rpx;
}

.chart-bar {
  width: 20rpx;
  background-color: #ff8c00;
  border-radius: 10rpx 10rpx 0 0;
  margin: 0 5rpx;
}

.chart-x-axis {
  height: 40rpx;
  display: flex;
  justify-content: space-around;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.x-label {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .stats-card {
  background-color: #2a2a2a;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-title {
  color: #fff;
}

.darkMode .card-header {
  border-bottom-color: #333;
}

.darkMode .summary-item {
  background-color: #333;
}

.darkMode .summary-label,
.darkMode .tab-text,
.darkMode .progress-label,
.darkMode .progress-value,
.darkMode .legend-text,
.darkMode .efficiency-label,
.darkMode .chart-y-axis text,
.darkMode .x-label {
  color: #aaa;
}

.darkMode .progress-bar {
  background-color: #444;
}

/* 类型统计样式 */
.type-statistics {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.stats-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1px solid #eee;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.stats-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 无数据提示 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.no-data-text {
  font-size: 14px;
  color: #999;
}

/* 暗黑模式适配 */
.darkMode .type-statistics {
  background-color: #2a2a2a;
}

.darkMode .stats-title {
  color: #fff;
}

.darkMode .stats-item {
  border-bottom-color: #444;
}

.darkMode .stats-label {
  color: #aaa;
}

.darkMode .stats-value {
  color: #fff;
}

.darkMode .no-data-text {
  color: #666;
}

/* 趋势统计样式 */
.trend-statistics {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.trend-summary {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1px solid #eee;
}

.trend-item:last-child {
  border-bottom: none;
}

.trend-label {
  font-size: 14px;
  color: #666;
}

.trend-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 暗黑模式适配 - 趋势统计 */
.darkMode .trend-statistics {
  background-color: #2a2a2a;
}

.darkMode .trend-item {
  border-bottom-color: #444;
}

.darkMode .trend-label {
  color: #aaa;
}

.darkMode .trend-value {
  color: #fff;
}

.darkMode .time-range-option {
  color: #aaa;
}

.darkMode .time-range-selector,
.darkMode .tab-nav {
  background-color: #2a2a2a;
  border-bottom-color: #333;
}
