# 小区ID请求头自动添加功能实现

## 功能概述

在微信小程序的网络请求中，除了登录、刷新token、选择小区等前置请求外，所有其他请求都会在请求头中自动添加小区ID参数。

## 实现方案

### 1. 修改的文件
- `utils/request.js` - 主要的网络请求封装文件

### 2. 核心实现逻辑

#### 2.1 前置请求识别函数
```javascript
// 检查是否为前置请求（不需要小区ID的请求）
const isPrerequisiteRequest = (url) => {
  // 登录相关请求
  if (url.includes('/auth/token') || url.includes('/oauth/login') || url.includes('/auth/refresh-token')) {
    return true;
  }
  
  // 小区选择相关请求
  if (url.includes('/community/page')) {
    return true;
  }

  // 用户信息相关请求（获取用户基本信息，不依赖小区）
  if (url.includes('/member/info') || url.includes('/member/logout')) {
    return true;
  }

  // 字典数据请求（全局数据，不依赖小区）
  if (url.includes('/dict/page')) {
    return true;
  }

  // 组织架构请求（全局数据，不依赖小区）
  if (url.includes('/org/tree') || url.includes('/position/page')) {
    return true;
  }
  
  return false;
};
```

#### 2.2 普通请求头添加逻辑
在 `baseRequest` 函数中：
```javascript
// 对于非前置请求，添加小区ID到请求头
if (!isPrerequisiteRequest(url)) {
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  if (selectedCommunity && selectedCommunity.id) {
    dict['Community-Id'] = selectedCommunity.id;
    console.log(`请求 ${url} 添加小区ID: ${selectedCommunity.id}`);
  } else {
    console.log(`请求 ${url} 未添加小区ID - 小区信息:`, selectedCommunity);
  }
} else {
  console.log(`请求 ${url} 是前置请求，跳过添加小区ID`);
}
```

#### 2.3 文件上传请求头添加逻辑
在 `uploadPhoto` 函数中：
```javascript
// 对于非前置请求，添加小区ID到请求头
if (!isPrerequisiteRequest(url)) {
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  if (selectedCommunity && selectedCommunity.id) {
    headerGet['Community-Id'] = selectedCommunity.id;
    console.log(`文件上传请求 ${url} 添加小区ID: ${selectedCommunity.id}`);
  } else {
    console.log(`文件上传请求 ${url} 未添加小区ID - 小区信息:`, selectedCommunity);
  }
} else {
  console.log(`文件上传请求 ${url} 是前置请求，跳过添加小区ID`);
}
```

### 3. 前置请求列表

以下请求被识别为前置请求，不会添加小区ID：

#### 3.1 登录相关
- `/auth/token` - 获取访问令牌
- `/oauth/login` - OAuth登录
- `/auth/refresh-token` - 刷新令牌

#### 3.2 小区选择
- `/community/page` - 获取小区列表

#### 3.3 用户基本信息
- `/member/info` - 获取用户信息
- `/member/logout` - 用户登出

#### 3.4 全局数据
- `/dict/page` - 获取字典数据
- `/org/tree` - 获取组织架构
- `/position/page` - 获取职位列表

### 4. 小区ID获取方式

小区ID从本地存储中获取：
```javascript
const selectedCommunity = wx.getStorageSync('selectedCommunity');
if (selectedCommunity && selectedCommunity.id) {
  // 使用 selectedCommunity.id 作为小区ID
}
```

### 5. 请求头格式

添加的请求头格式为：
```javascript
{
  'Community-Id': selectedCommunity.id
}
```

### 6. 调试日志

实现中包含了详细的调试日志，可以在开发者工具的控制台中查看：
- 前置请求会显示跳过添加小区ID的日志
- 非前置请求会显示是否成功添加小区ID的日志
- 如果小区信息不存在，会显示相应的警告日志

### 7. 使用示例

#### 7.1 会添加小区ID的请求
```javascript
// 访客管理API
visitorsApi.getVisitorList() // 会添加 Community-Id 请求头

// 房屋管理API  
houseApi.getMyHouses() // 会添加 Community-Id 请求头

// 车辆管理API
vehicleApi.getVehicleList() // 会添加 Community-Id 请求头
```

#### 7.2 不会添加小区ID的请求
```javascript
// 登录请求
REQUEST.request('/users-api/v1/auth/token', 'POST', {code: 'xxx'}, false)

// 获取小区列表
commApi.communityList() // 调用 /users-api/v1/community/page

// 获取字典数据
commApi.getAllDict() // 调用 /users-api/v1/dict/page
```

## 注意事项

1. **小区选择状态检查**：确保用户已选择小区后再进行需要小区ID的操作
2. **错误处理**：如果请求需要小区ID但用户未选择小区，应引导用户先选择小区
3. **调试模式**：在生产环境中可以考虑移除或减少调试日志的输出
4. **扩展性**：如果有新的前置请求类型，需要在 `isPrerequisiteRequest` 函数中添加相应的判断逻辑

## 测试验证

可以通过以下方式验证功能是否正常工作：

1. 在开发者工具中查看网络请求的请求头
2. 查看控制台中的调试日志
3. 确认前置请求没有 `Community-Id` 请求头
4. 确认业务请求包含正确的 `Community-Id` 请求头
