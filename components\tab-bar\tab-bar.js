// 自定义底部选项卡组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    activeTab: {
      type: String,
      value: 'directory'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    tabs: [
      {
        id: 'directory',
        name: '信息黄页',
        icon: '/images/icons/directory.svg',
        activeIcon: '/images/icons/directory-active.svg'
      },
      {
        id: 'lostfound',
        name: '失物招领',
        icon: '/images/icons/lost-found.svg',
        activeIcon: '/images/icons/lost-found-active.svg'
      },
      {
        id: 'property',
        name: '物业服务',
        icon: '/images/icons/property-service.svg',
        activeIcon: '/images/icons/property-service-active.svg'
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 切换选项卡
    switchTab(e) {
      const tabId = e.currentTarget.dataset.id;
      if (tabId !== this.properties.activeTab) {
        this.setData({
          activeTab: tabId
        });
        // 触发自定义事件，通知父组件切换选项卡
        this.triggerEvent('tabchange', { tabId });
      }
    }
  }
})
