<!-- 信息黄页组件 -->
<view class="directory-container">
  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/icons/search.svg" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索服务或商家" bindinput="onSearchInput" value="{{searchKeyword}}"/>
      <view class="search-clear" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="/images/icons/clear.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 服务分类 -->
  <view class="section">
    <view class="section-title">服务分类</view>
    <view class="category-grid">
      <view
        class="category-item"
        wx:for="{{categories}}"
        wx:key="id"
        bindtap="navigateToCategory"
        data-id="{{item.id}}"
        data-name="{{item.name}}"
      >
        <view class="category-icon" style="background-color: rgba(255, 140, 0, 0.1);">
          <image src="{{item.icon}}" mode="aspectFit" style="width: 32px; height: 32px;"></image>
        </view>
        <view class="category-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 热门服务 -->
  <view class="section">
    <view class="section-title">热门服务</view>
    <view class="service-list">
      <view
        class="service-item"
        wx:for="{{hotServices}}"
        wx:key="id"
        bindtap="navigateToDetail"
        data-id="{{item.id}}"
      >
        <view class="service-content">
          <view class="service-name">{{item.name}}</view>
          <view class="service-desc">{{item.description}}</view>
          <view class="service-address">
            <image class="address-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
            <text>{{item.address}}</text>
          </view>
        </view>
        <view class="service-action">
          <view class="call-btn" catchtap="callPhone" data-phone="{{item.phone}}">
            <image class="call-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域，防止内容被底部选项卡遮挡 -->
  <view class="safe-bottom"></view>
</view>
