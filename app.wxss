/**app.wxss**/
@import "./styles/iconfont.wxss";
@import "./styles/profile-icons.wxss";
@import "./styles/icons.wxss";

/* 默认模式样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f8f8f8;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
  transition: background-color 0.3s, color 0.3s;
  padding: 0;
  margin: 0;
}
::-webkit-scrollbar {
  display: none;
}
/* 暗黑模式样式 */
.darkMode, page[data-darkmode="true"] {
  color: #f5f5f7 !important;
  background-color: #1c1c1e !important;
}

/* 暗黑模式下的卡片样式 */
.darkMode .card, page[data-darkmode="true"] .card {
  background: #2c2c2e !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2) !important;
}

/* 暗黑模式下的标题样式 */
.darkMode .section-title, page[data-darkmode="true"] .section-title {
  color: #f5f5f7 !important;
}

/* 暗黑模式下的消息样式 */
.darkMode .message-title, page[data-darkmode="true"] .message-title {
  color: #f5f5f7 !important;
}

.darkMode .message-icon, page[data-darkmode="true"] .message-icon {
  background: rgba(44, 44, 46, 0.8) !important;
}

.darkMode .message-icon .icon-megaphone, page[data-darkmode="true"] .message-icon .icon-megaphone {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 11l18-5v12L3 14v-3z'%3E%3C/path%3E%3Cpath d='M6 10.6V14H3'%3E%3C/path%3E%3Cpath d='M11.6 16.8a3 3 0 1 1-5.8-1.6'%3E%3C/path%3E%3C/svg%3E") !important;
}

/* 暗黑模式下的活动卡片样式 */
.darkMode .event-card, page[data-darkmode="true"] .event-card {
  background: #2c2c2e !important;
}

.darkMode .icon-calendar-event, page[data-darkmode="true"] .icon-calendar-event {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23aeaeb2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E") !important;
}

.darkMode .icon-people-event, page[data-darkmode="true"] .icon-people-event {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23aeaeb2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E") !important;
}

.darkMode .event-title, page[data-darkmode="true"] .event-title {
  color: #f5f5f7 !important;
}

.darkMode .event-description, page[data-darkmode="true"] .event-description {
  color: #a8a8a8 !important;
}

/* 暗黑模式下的服务图标样式 */
.darkMode .service-icon, page[data-darkmode="true"] .service-icon {
  background: #2c2c2e !important;
  border: 1rpx solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.2) !important;
  color: #a8a8a8 !important;
}

.darkMode .service-name, page[data-darkmode="true"] .service-name {
  color: #a8a8a8 !important;
}

/* 暗黑模式下的容器样式 */
.darkMode .container, page[data-darkmode="true"] .container {
  background-color: #1c1c1e !important;
}

/* 暗黑模式下的欢迎区域样式 */
.darkMode .welcome-section, page[data-darkmode="true"] .welcome-section {
  background: linear-gradient(135deg, #1f1f1f, #2c2c2e) !important;
}

/* 暗黑模式下的渐变背景样式 */
.darkMode .gradient-bg, page[data-darkmode="true"] .gradient-bg {
  background: linear-gradient(to bottom, #1f1f1f, #1c1c1e 50%) !important;
}

/* 暗黑模式下的按钮样式 */
.darkMode .btn-primary, page[data-darkmode="true"] .btn-primary {
  background: #0A84FF !important;
  color: white !important;
}

.darkMode .btn-outline, page[data-darkmode="true"] .btn-outline {
  border: 1rpx solid #0A84FF !important;
  color: #0A84FF !important;
}

/* 暗黑模式下的输入框样式 */
.darkMode input, page[data-darkmode="true"] input,
.darkMode textarea, page[data-darkmode="true"] textarea {
  background-color: #2c2c2e !important;
  color: #f5f5f7 !important;
  border-color: #3a3a3c !important;
}

/* 暗黑模式下的列表样式 */
.darkMode .list-item, page[data-darkmode="true"] .list-item {
  background-color: #2c2c2e !important;
  border-color: #3a3a3c !important;
}

/* 暗黑模式下的分割线样式 */
.darkMode .divider, page[data-darkmode="true"] .divider {
  background-color: #3a3a3c !important;
}

/* 暗黑模式下的导航栏样式 */
.darkMode .nav-bar, page[data-darkmode="true"] .nav-bar {
  background-color: #1f1f1f !important;
  border-bottom: 1rpx solid #3a3a3c !important;
}

/* 暗黑模式下的“我的”页面样式 */
.darkMode .profile-section, page[data-darkmode="true"] .profile-section {
  background: linear-gradient(135deg, #1f1f1f, #2c2c2e) !important;
}

.darkMode .auth-status-tag, page[data-darkmode="true"] .auth-status-tag {
  background-color: rgba(255, 149, 0, 0.3) !important;
}

.darkMode .auth-status-tag.resident, page[data-darkmode="true"] .auth-status-tag.resident {
  background-color: rgba(52, 199, 89, 0.3) !important;
}

.darkMode .auth-status-tag.property, page[data-darkmode="true"] .auth-status-tag.property {
  background-color: rgba(0, 122, 255, 0.3) !important;
}

.darkMode .auth-status-tag.dual, page[data-darkmode="true"] .auth-status-tag.dual {
  background-color: rgba(88, 86, 214, 0.3) !important;
}

.darkMode .user-location, page[data-darkmode="true"] .user-location {
  color: rgba(255, 255, 255, 0.7) !important;
}

.darkMode .function-group, page[data-darkmode="true"] .function-group {
  background-color: #2c2c2e !important;
}

.darkMode .function-item, page[data-darkmode="true"] .function-item {
  border-bottom-color: #3a3a3c !important;
}

.darkMode .function-title, page[data-darkmode="true"] .function-title {
  color: #f5f5f7 !important;
}

.darkMode .function-arrow, page[data-darkmode="true"] .function-arrow {
  color: #8e8e93 !important;
}

.darkMode .auth-reminder-modal-content, page[data-darkmode="true"] .auth-reminder-modal-content {
  background-color: #2c2c2e !important;
}

.darkMode .auth-reminder-modal-title, page[data-darkmode="true"] .auth-reminder-modal-title {
  color: #f5f5f7 !important;
}

.darkMode .auth-reminder-modal-subtitle, page[data-darkmode="true"] .auth-reminder-modal-subtitle {
  color: #8e8e93 !important;
}

.darkMode .auth-reminder-modal-btn.cancel, page[data-darkmode="true"] .auth-reminder-modal-btn.cancel {
  background-color: rgba(60, 60, 67, 0.3) !important;
}

.darkMode .logout-btn, page[data-darkmode="true"] .logout-btn {
  background-color: #2c2c2e !important;
  color: #ff453a !important;
}

/* 暗黑模式下的标签栏样式 */
.darkMode .tab-bar, page[data-darkmode="true"] .tab-bar {
  background-color: #1f1f1f !important;
  border-top: 1rpx solid #3a3a3c !important;
}

/* 暗黑模式下的消息详情弹窗样式 */
.darkMode .message-detail-content, page[data-darkmode="true"] .message-detail-content {
  background-color: #2c2c2e !important;
  color: #f5f5f7 !important;
}

.darkMode .message-detail-title, page[data-darkmode="true"] .message-detail-title {
  color: #f5f5f7 !important;
}

.darkMode .message-detail-time, page[data-darkmode="true"] .message-detail-time {
  color: #8e8e93 !important;
}

.darkMode .message-detail-text, page[data-darkmode="true"] .message-detail-text {
  color: #f5f5f7 !important;
}

.darkMode .message-detail-close-btn, page[data-darkmode="true"] .message-detail-close-btn {
  background-color: #0A84FF !important;
  color: white !important;
}

/* 暗黑模式下的积分页面样式 */
.darkMode .points-card, page[data-darkmode="true"] .points-card {
  background: linear-gradient(135deg, #1f1f1f, #2c2c2e) !important;
}

.darkMode .points-title, page[data-darkmode="true"] .points-title,
.darkMode .points-value, page[data-darkmode="true"] .points-value {
  color: #f5f5f7 !important;
}

.darkMode .points-action, page[data-darkmode="true"] .points-action {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.darkMode .category-tab, page[data-darkmode="true"] .category-tab {
  color: #8e8e93 !important;
  border-color: #3a3a3c !important;
}

.darkMode .category-tab.active, page[data-darkmode="true"] .category-tab.active {
  color: #0A84FF !important;
  border-color: #0A84FF !important;
}

.darkMode .product-item, page[data-darkmode="true"] .product-item,
.darkMode .goods-item, page[data-darkmode="true"] .goods-item {
  background-color: #2c2c2e !important;
}

.darkMode .product-name, page[data-darkmode="true"] .product-name,
.darkMode .goods-name, page[data-darkmode="true"] .goods-name {
  color: #f5f5f7 !important;
}

.darkMode .product-points, page[data-darkmode="true"] .product-points {
  color: #0A84FF !important;
}

.darkMode .goods-price, page[data-darkmode="true"] .goods-price {
  color: #ff9500 !important;
}

.darkMode .goods-meta, page[data-darkmode="true"] .goods-meta,
.darkMode .goods-location, page[data-darkmode="true"] .goods-location,
.darkMode .goods-sales, page[data-darkmode="true"] .goods-sales {
  color: #8e8e93 !important;
}

.darkMode .product-detail-content, page[data-darkmode="true"] .product-detail-content {
  background-color: #2c2c2e !important;
  color: #f5f5f7 !important;
}

.darkMode .product-detail-name, page[data-darkmode="true"] .product-detail-name {
  color: #f5f5f7 !important;
}

.darkMode .product-detail-points, page[data-darkmode="true"] .product-detail-points {
  color: #0A84FF !important;
}

.darkMode .product-detail-desc, page[data-darkmode="true"] .product-detail-desc {
  color: #8e8e93 !important;
}

.darkMode .product-detail-btn, page[data-darkmode="true"] .product-detail-btn {
  background-color: #0A84FF !important;
  color: white !important;
}

.darkMode .product-detail-btn.disabled, page[data-darkmode="true"] .product-detail-btn.disabled {
  background-color: #3a3a3c !important;
  color: #8e8e93 !important;
}

.darkMode .search-input-wrapper, page[data-darkmode="true"] .search-input-wrapper {
  background-color: #3a3a3c !important;
}

.darkMode .search-input, page[data-darkmode="true"] .search-input {
  color: #f5f5f7 !important;
}

.darkMode .page-title, page[data-darkmode="true"] .page-title {
  color: #f5f5f7 !important;
}

.darkMode .section-title, page[data-darkmode="true"] .section-title,
.darkMode .group-title, page[data-darkmode="true"] .group-title {
  color: #f5f5f7 !important;
}

.darkMode .section-subtitle, page[data-darkmode="true"] .section-subtitle {
  color: #8e8e93 !important;
}

.darkMode .no-result-text, page[data-darkmode="true"] .no-result-text {
  color: #8e8e93 !important;
}

/* 通用容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* 底部安全区域 - 确保内容不被tabBar遮挡 */
.safe-bottom-area {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

/* 列表容器 - 确保列表底部有足够空间 */
.list-container {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

/* 页面底部安全区域 - 适用于所有页面 */
.page-bottom-safe-area {
  padding-bottom: calc(400rpx + env(safe-area-inset-bottom));
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 标题样式 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  color: #1C1C1E;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}

.section-title .more {
  font-size: 28rpx;
  font-weight: normal;
  color: #007AFF;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(to bottom, #ff8c00, #ffffff 50%);
}

/* 欢迎区域 */
.welcome-section {
  padding: 30rpx;
  color: white;
}

/* 快捷服务网格 */
.quick-services {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
  padding: 20rpx 0;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.service-icon {
  width: 112rpx;
  height: 112rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  color: #666;
  background: #f8f8f8;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);
}

.service-name {
  font-size: 24rpx;
  color: #3A3A3C;
  text-align: center;
}

/* 消息列表 */
.message-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.message-item:last-child {
  border-bottom: none;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  color: #007AFF;
  background: rgba(245, 245, 247, 0.8);
}

.message-content {
  flex: 1;
}

.message-title {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.message-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.message-badge {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background: #FF3B30;
  margin-left: 16rpx;
}

/* 社区活动卡片 */
.event-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
}

.event-image {
  height: 320rpx;
  background-color: #E5E5EA;
  background-size: cover;
  background-position: center;
}

.event-content {
  padding: 32rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #1C1C1E;
}

.event-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.event-info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}

.btn-primary {
  background: #007AFF;
  color: white;
}

.btn-outline {
  border: 1rpx solid #007AFF;
  color: #007AFF;
}

/* 轮播Banner */
.banner-carousel {
  margin: 20rpx;
}

.feature-banner {
  height: 160rpx;
  border-radius: 32rpx;
  overflow: hidden;
  position: relative;
}

.feature-banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.feature-banner-content {
  position: relative;
  z-index: 3;
  padding: 30rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-banner-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.feature-banner-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}
