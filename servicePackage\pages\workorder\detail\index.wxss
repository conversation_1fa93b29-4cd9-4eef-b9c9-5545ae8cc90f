/* pages/workorder/detail/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}



/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #FF8C00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 详情内容 */
.detail-content {
  flex: 1;
  padding: 16px;
  padding-bottom: 80px; /* 为底部固定按钮留出空间 */
}

/* 工单状态卡片 */
.status-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
}

.status-pending {
  background-color: #FFF7E6; /* 警告色的浅色背景 */
}

.status-processing {
  background-color: #FFF0E0; /* 主色的浅色背景 */
}

.status-completed {
  background-color: #F6FFED; /* 成功色的浅色背景 */
}

.status-cancelled {
  background-color: #F5F5F5; /* 中性色背景 */
}

.status-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.status-icon {
  width: 32px;
  height: 32px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.status-pending .status-name {
  color: #FAAD14; /* 警告色 */
}

.status-processing .status-name {
  color: #FF8C00; /* 主品牌色 */
}

.status-completed .status-name {
  color: #52C41A; /* 成功色 */
}

.status-cancelled .status-name {
  color: #999999; /* 文本辅助色 */
}

.status-desc {
  font-size: 14px;
  color: #666;
}

/* 详情卡片 */
.detail-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #FF8C00; /* 主品牌色 */
  margin-right: 8px;
  border-radius: 2px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #999;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.priority-high {
  color: #F5222D; /* 错误色 */
  font-weight: 500; /* 高优先级加粗 */
}

.priority-medium {
  color: #FAAD14; /* 警告色 */
}

.priority-low {
  color: #999999; /* 文本辅助色 */
}

/* 问题描述 */
.description-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.image-item {
  position: relative;
}

.problem-image, .evaluation-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  border: 1px solid #eee;
  background-color: #f8f9fa;
}

/* 无图片提示 */
.no-images {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 12px;
}

.no-images-text {
  color: #999;
  font-size: 14px;
}

/* 报修地址 */
.address-type {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.address-value {
  font-size: 16px;
  color: #333;
}

/* 报修人信息 */
.reporter-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.reporter-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.avatar-icon {
  width: 32px;
  height: 32px;
}

.reporter-detail {
  flex: 1;
}

.reporter-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.reporter-role {
  font-size: 14px;
  color: #999;
}

/* 处理进度 */
.progress-timeline {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  bottom: 0;
  width: 1px;
  background-color: #e8e8e8;
}

.timeline-item.last::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #FF8C00; /* 主品牌色 */
}

.timeline-content {
  position: relative;
}

.timeline-time {
  font-size: 14px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-action {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-operator {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-remark {
  font-size: 14px;
  color: #666;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
}

/* 时间线状态颜色 */
.timeline-dot.status-wait_process {
  background-color: #ff9800;
}

.timeline-dot.status-accepted {
  background-color: #2196f3;
}

.timeline-dot.status-processing {
  background-color: #2196f3;
}

.timeline-dot.status-pending {
  background-color: #ff9800;
}

.timeline-dot.status-completed {
  background-color: #4caf50;
}

.timeline-dot.status-cancelled {
  background-color: #9e9e9e;
}

.timeline-content {
  padding-left: 12px;
}

/* 时间线图片 */
.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.timeline-image-item {
  position: relative;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

/* 评价信息 */
.evaluation-info {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.rating-stars {
  display: flex;
  margin-bottom: 12px;
}

.star-icon {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}

.evaluation-comment {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.evaluation-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}

.no-evaluation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
}

.no-evaluation text {
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
}

.evaluate-btn {
  background-color: #FF8C00; /* 主品牌色 */
  color: #fff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500; /* 加粗文字 */
  transition: background-color 0.3s;
}

.evaluate-btn:hover {
  background-color: #E67A00; /* 主色的深色变体 */
}

/* 底部操作区域 */
.bottom-actions {
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 固定在底部的操作区域 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.action-btn {
  width: 100%;
  height: 44px;
  line-height: 24px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500; /* 加粗文字 */
  transition: all 0.3s;
  border: none;
}

.cancel-btn {
  background-color: #FFFFFF; /* 白色背景 */
  color: #F5222D; /* 错误色 */
  border: 1px solid #F5222D; /* 错误色边框 */
}

.cancel-btn:hover {
  background-color: #FFF1F0; /* 错误色的浅色背景 */
}

/* 取消确认对话框 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-content {
  padding: 24px 16px;
  font-size: 16px;
  color: #333;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #eee;
}

.modal-btn {
  flex: 1;
  height: 48px;
  line-height: 48px;
  font-size: 16px;
  text-align: center;
  border-radius: 0;
}

.modal-btn.cancel {
  color: #999;
  background-color: #fff;
  border-right: 1px solid #eee;
}

.modal-btn.confirm {
  color: #F5222D; /* 错误色 */
  background-color: #fff;
  font-weight: 500; /* 加粗文字 */
}
