/* 信息黄页组件样式 */
.directory-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: #f9fafb;
  padding-bottom: 70px; /* 为底部选项卡预留空间 */
}

/* 搜索框样式 */
.search-container {
  padding: 12px 16px;
  background-color: #fff;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0 12px;
  height: 40px;
}

.search-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #333;
}

.search-clear {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear image {
  width: 14px;
  height: 14px;
  opacity: 0.5;
}

/* 分类样式 */
.section {
  margin-bottom: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  margin: 0 16px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 8px 0;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-icon {
  width: 56px;
  height: 56px;
  background-color: #fff8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.category-icon image {
  width: 32px;
  height: 32px;
}

.category-name {
  font-size: 14px;
  color: #333;
  text-align: center;
  margin-top: 4px;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
}

.service-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-content {
  flex: 1;
}

.service-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.service-address {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.address-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.service-action {
  display: flex;
  align-items: center;
}

.call-btn {
  width: 36px;
  height: 36px;
  background-color: #ff8c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-icon {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1);
}

/* 底部安全区域 */
.safe-bottom {
  height: 20px;
}
