<!-- pages/property/workorder/batch-assign/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">批量分配工单</view>
    <view class="nav-action"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 选中工单信息 -->
    <view class="section-title">
      <text class="title-text">已选工单 ({{orders.length}})</text>
    </view>
    
    <view class="order-list">
      <view 
        wx:for="{{orders}}" 
        wx:key="id" 
        class="order-item status-{{item.status}}"
      >
        <view class="order-icon-container">
          <view class="order-icon {{item.type}}-icon"></view>
        </view>
        <view class="order-info">
          <view class="order-title">{{item.content.title}}</view>
          <view class="order-meta">
            <text class="order-id">{{item.id}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="order-status">
          <text class="status-text">{{item.statusName}}</text>
        </view>
      </view>
    </view>

    <!-- 分配表单 -->
    <view class="assign-form">
      <view class="section-title">
        <text class="title-text">选择处理人员</text>
      </view>
      
      <view class="staff-list">
        <view 
          wx:for="{{staffList}}" 
          wx:key="id" 
          class="staff-item {{selectedStaffId === item.id ? 'selected' : ''}}"
          bindtap="selectStaff"
          data-id="{{item.id}}"
        >
          <view class="staff-avatar">
            <image src="/images/icons/user.svg" class="avatar-icon" />
          </view>
          <view class="staff-info">
            <view class="staff-name">{{item.name}}</view>
            <view class="staff-position">{{item.department}} - {{item.position}}</view>
          </view>
          <view class="staff-check" wx:if="{{selectedStaffId === item.id}}">
            <image src="/images/icons/check.svg" class="check-icon" />
          </view>
        </view>
      </view>
      
      <view class="section-title">
        <text class="title-text">分配备注</text>
      </view>
      
      <view class="form-group">
        <textarea 
          class="form-textarea" 
          placeholder="请输入分配备注" 
          value="{{assignRemark}}" 
          bindinput="inputAssignRemark"
        ></textarea>
        <view class="form-tips">请填写分配工单的原因或注意事项</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn-cancel" bindtap="cancelAssign" disabled="{{submitting}}">取消</button>
      <button class="btn-submit" bindtap="submitAssign" loading="{{submitting}}" disabled="{{submitting}}">确认分配</button>
    </view>
  </view>
</view>
