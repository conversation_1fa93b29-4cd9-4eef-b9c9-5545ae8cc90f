<!-- pages/property/workorder/batch-complete/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">批量完成工单</view>
    <view class="nav-action"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 选中工单信息 -->
    <view class="section-title">
      <text class="title-text">已选工单 ({{orders.length}})</text>
    </view>
    
    <view class="order-list">
      <view 
        wx:for="{{orders}}" 
        wx:key="id" 
        class="order-item status-{{item.status}}"
      >
        <view class="order-icon-container">
          <view class="order-icon {{item.type}}-icon"></view>
        </view>
        <view class="order-info">
          <view class="order-title">{{item.content.title}}</view>
          <view class="order-meta">
            <text class="order-id">{{item.id}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="order-status">
          <text class="status-text">{{item.statusName}}</text>
        </view>
      </view>
    </view>

    <!-- 完成表单 -->
    <view class="complete-form">
      <view class="section-title">
        <text class="title-text">处理结果 <text class="required">*</text></text>
      </view>
      
      <view class="form-group">
        <textarea 
          class="form-textarea" 
          placeholder="请输入处理结果" 
          value="{{completeResult}}" 
          bindinput="inputCompleteResult"
        ></textarea>
        <view class="form-tips">请详细描述工单的处理过程和结果</view>
      </view>
      
      <view class="section-title">
        <text class="title-text">完成备注</text>
      </view>
      
      <view class="form-group">
        <textarea 
          class="form-textarea" 
          placeholder="请输入完成备注" 
          value="{{completeRemark}}" 
          bindinput="inputCompleteRemark"
        ></textarea>
        <view class="form-tips">请填写完成工单的补充说明或注意事项</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn-cancel" bindtap="cancelComplete" disabled="{{submitting}}">取消</button>
      <button class="btn-submit" bindtap="submitComplete" loading="{{submitting}}" disabled="{{submitting}}">确认完成</button>
    </view>
  </view>
</view>
