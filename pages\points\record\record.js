// record.js
const PointsUtil = require('../../../utils/points');
const app = getApp();

Page({
  data: {
    records: [],
    recordTypes: [
      { id: 'all', name: '全部' },
      { id: 'earn', name: '获取' },
      { id: 'use', name: '使用' }
    ],
    timeRanges: [
      { id: 'all', name: '全部时间' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '近三个月' },
      { id: 'year', name: '今年' }
    ],
    currentType: 'all',
    currentTimeRange: 'all',
    filteredRecords: [],
    page: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    userPoints: 0,
    userLevel: null,
    darkMode: false,
    showSearch: false,
    searchValue: '',
    showFilter: false,
    startDate: '',
    endDate: '',
    customDateRange: false
  },

  onLoad: function() {
    // 获取暗黑模式设置
    this.setData({
      darkMode: app.globalData.darkMode,
      currentTimeRangeName: '全部时间' // 初始化时间范围名称
    });

    // 初始化日期范围
    this.initDateRange();

    // 加载用户信息
    this.loadUserInfo();

    // 加载积分记录
    this.loadRecords();
  },

  onShow: function() {
    // 刷新用户信息
    this.loadUserInfo();

    // 如果搜索值或筛选条件变化，重新加载记录
    if (this.data.searchValue || this.data.customDateRange) {
      this.setData({
        page: 1,
        hasMore: true,
        records: []
      });
      this.loadRecords();
    }
  },

  onPullDownRefresh: function() {
    this.setData({
      page: 1,
      hasMore: true,
      records: []
    });
    this.loadUserInfo();
    this.loadRecords().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreRecords();
    }
  },

  // 加载用户信息
  loadUserInfo: function() {
    return PointsUtil.getUserLevelInfo().then(levelInfo => {
      this.setData({
        userPoints: levelInfo.points,
        userLevel: levelInfo.level
      });
    }).catch(err => {
      console.error('获取用户等级信息失败:', err);
    });
  },

  // 初始化日期范围
  initDateRange: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();

    // 格式化当前日期
    const today = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

    // 计算本月第一天
    const firstDayOfMonth = `${year}-${month.toString().padStart(2, '0')}-01`;

    // 计算近三个月第一天
    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    const firstDayOfQuarter = `${threeMonthsAgo.getFullYear()}-${(threeMonthsAgo.getMonth() + 1).toString().padStart(2, '0')}-01`;

    // 计算今年第一天
    const firstDayOfYear = `${year}-01-01`;

    this.setData({
      today: today,
      firstDayOfMonth: firstDayOfMonth,
      firstDayOfQuarter: firstDayOfQuarter,
      firstDayOfYear: firstDayOfYear,
      endDate: today
    });
  },

  // 加载积分记录
  loadRecords: function() {
    this.setData({ loading: true });

    // 确定日期范围
    let startDate = '';
    let endDate = this.data.today;

    if (this.data.customDateRange) {
      // 使用自定义日期范围
      startDate = this.data.startDate;
      endDate = this.data.endDate;
    } else {
      // 使用预设日期范围
      switch (this.data.currentTimeRange) {
        case 'month':
          startDate = this.data.firstDayOfMonth;
          break;
        case 'quarter':
          startDate = this.data.firstDayOfQuarter;
          break;
        case 'year':
          startDate = this.data.firstDayOfYear;
          break;
        default:
          startDate = ''; // 全部时间
      }
    }

    // 构建查询参数
    const params = {
      type: this.data.currentType === 'all' ? '' : this.data.currentType,
      page: this.data.page,
      pageSize: this.data.pageSize,
      startDate: startDate,
      endDate: endDate,
      keyword: this.data.searchValue
    };

    return PointsUtil.getPointsRecords(params).then(result => {
      // 格式化日期
      const records = result.records.map(record => {
        return {
          ...record,
          formattedDate: this.formatDate(record.date)
        };
      });

      this.setData({
        records: records,
        filteredRecords: records,
        loading: false,
        hasMore: result.hasMore
      });

      return result;
    }).catch(err => {
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 加载更多记录
  loadMoreRecords: function() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
      page: this.data.page + 1
    });

    // 确定日期范围
    let startDate = '';
    let endDate = this.data.today;

    if (this.data.customDateRange) {
      // 使用自定义日期范围
      startDate = this.data.startDate;
      endDate = this.data.endDate;
    } else {
      // 使用预设日期范围
      switch (this.data.currentTimeRange) {
        case 'month':
          startDate = this.data.firstDayOfMonth;
          break;
        case 'quarter':
          startDate = this.data.firstDayOfQuarter;
          break;
        case 'year':
          startDate = this.data.firstDayOfYear;
          break;
        default:
          startDate = ''; // 全部时间
      }
    }

    // 构建查询参数
    const params = {
      type: this.data.currentType === 'all' ? '' : this.data.currentType,
      page: this.data.page,
      pageSize: this.data.pageSize,
      startDate: startDate,
      endDate: endDate,
      keyword: this.data.searchValue
    };

    PointsUtil.getPointsRecords(params).then(result => {
      if (result.records.length > 0) {
        // 格式化日期
        const newRecords = result.records.map(record => {
          return {
            ...record,
            formattedDate: this.formatDate(record.date)
          };
        });

        // 合并记录
        const records = [...this.data.records, ...newRecords];

        this.setData({
          records: records,
          filteredRecords: records,
          loading: false,
          hasMore: result.hasMore
        });
      } else {
        this.setData({
          loading: false,
          hasMore: false
        });
      }
    }).catch(err => {
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 筛选记录类型
  filterRecordType: function(type) {
    this.setData({
      currentType: type,
      page: 1,
      hasMore: true,
      records: []
    });
    return this.loadRecords();
  },

  // 筛选时间范围
  filterTimeRange: function(timeRange) {
    // 计算当前时间范围的显示名称
    let timeRangeName = '';
    if (timeRange !== 'all') {
      const timeRangeItem = this.data.timeRanges.find(item => item.id === timeRange);
      timeRangeName = timeRangeItem ? timeRangeItem.name : '';
    }

    this.setData({
      currentTimeRange: timeRange,
      currentTimeRangeName: timeRangeName,
      customDateRange: false,
      page: 1,
      hasMore: true,
      records: []
    });
    return this.loadRecords();
  },

  // 应用自定义日期范围
  applyCustomDateRange: function() {
    if (!this.data.startDate || !this.data.endDate) {
      wx.showToast({
        title: '请选择完整的日期范围',
        icon: 'none'
      });
      return;
    }

    // 检查日期范围是否有效
    if (new Date(this.data.startDate) > new Date(this.data.endDate)) {
      wx.showToast({
        title: '开始日期不能晚于结束日期',
        icon: 'none'
      });
      return;
    }

    // 设置自定义日期范围的显示名称
    const timeRangeName = this.data.startDate + ' 至 ' + this.data.endDate;

    this.setData({
      customDateRange: true,
      currentTimeRange: 'custom',
      currentTimeRangeName: timeRangeName,
      page: 1,
      hasMore: true,
      records: [],
      showFilter: false
    });
    return this.loadRecords();
  },

  // 重置筛选条件
  resetFilter: function() {
    this.setData({
      currentTimeRange: 'all',
      currentTimeRangeName: '全部时间',
      customDateRange: false,
      startDate: '',
      endDate: this.data.today,
      page: 1,
      hasMore: true,
      records: []
    });
    return this.loadRecords();
  },

  // 切换记录类型
  switchRecordType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.filterRecordType(type);
  },

  // 切换时间范围
  switchTimeRange: function(e) {
    const timeRange = e.currentTarget.dataset.range;
    this.filterTimeRange(timeRange);
  },

  // 切换显示搜索框
  toggleSearch: function() {
    this.setData({
      showSearch: !this.data.showSearch,
      searchValue: ''
    });
  },

  // 切换显示筛选面板
  toggleFilter: function() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 输入搜索内容
  inputSearch: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // 执行搜索
  doSearch: function() {
    this.setData({
      page: 1,
      hasMore: true,
      records: []
    });
    this.loadRecords();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: '',
      page: 1,
      hasMore: true,
      records: []
    });
    this.loadRecords();
  },

  // 选择开始日期
  bindStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  // 选择结束日期
  bindEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  // 格式化日期
  formatDate: function(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
})
