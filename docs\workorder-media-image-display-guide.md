# 工单详情页面media字段多图片显示处理指南

## 概述
工单详情页面需要处理media字段，该字段是逗号分隔的多图片字符串。本文档总结了如何正确处理和显示这些图片。

## 当前实现分析

### 文件：`servicePackage/pages/workorder/detail/index.js`

#### 1. 数据处理逻辑
```javascript
// 处理工单数据
const processedOrder = {
  ...order,
  // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
  imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
  // ... 其他字段处理
}
```

**关键点：**
- 使用 `split(',')` 将逗号分隔的字符串转换为数组
- 使用 `filter(img => img.trim())` 过滤掉空字符串和只有空格的项
- 如果media为空，返回空数组

#### 2. 图片URL处理
```javascript
// 预览图片
previewImage(e) {
  const { index } = e.currentTarget.dataset
  const { workOrder, apiUrl } = this.data

  if (workOrder.imageList && workOrder.imageList.length > 0) {
    // 处理图片URL，支持完整URL和相对路径
    const fullUrls = workOrder.imageList.map(img => {
      if (img.startsWith('http')) {
        return img // 已经是完整URL
      } else {
        return apiUrl + '/common-api/v1/file/' + img
      }
    })

    wx.previewImage({
      current: fullUrls[index],
      urls: fullUrls
    })
  }
}
```

**关键点：**
- 检查图片是否已经是完整URL（以http开头）
- 对于相对路径，拼接API基础URL和文件路径
- 使用标准的 `/common-api/v1/file/` 路径前缀

### 文件：`servicePackage/pages/workorder/detail/index.wxml`

#### 3. 图片显示模板
```xml
<!-- 问题图片 -->
<view class="image-list" wx:if="{{workOrder.imageList && workOrder.imageList.length > 0}}">
  <view class="image-item" wx:for="{{workOrder.imageList}}" wx:key="index">
    <image
      src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}"
      class="problem-image"
      mode="aspectFill"
      bindtap="previewImage"
      data-index="{{index}}"
    />
  </view>
</view>

<!-- 无图片提示 -->
<view class="no-images" wx:if="{{!workOrder.imageList || workOrder.imageList.length === 0}}">
  <text class="no-images-text">暂无图片</text>
</view>
```

**关键点：**
- 条件渲染：只有当图片数组存在且长度大于0时才显示
- 在模板中直接处理URL拼接逻辑
- 提供无图片时的友好提示
- 绑定点击事件支持图片预览

### 文件：`servicePackage/pages/workorder/detail/index.wxss`

#### 4. 图片样式设计
```css
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.image-item {
  position: relative;
}

.problem-image, .evaluation-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  border: 1px solid #eee;
  background-color: #f8f9fa;
}
```

**关键点：**
- 使用flex布局支持多行显示
- 设置固定尺寸确保图片显示一致
- 添加边框和背景色提升视觉效果

## 其他页面的实现参考

### 1. 商品详情页面 (`profilePackage/pages/goods/detail/detail.js`)
```javascript
// 处理图片数组
if (goods.media) {
  goods.images = goods.media.split(',').map(image => {
    const trimmedImage = image.trim();
    if (trimmedImage && !trimmedImage.startsWith('http')) {
      return this.data.apiUrl + trimmedImage;
    }
    return trimmedImage;
  });
} else {
  goods.images = [];
}
```

### 2. 商品列表页面 (`pages/goods/goods.js`)
```javascript
// 处理media字段（多个图片逗号分隔）
let images = [];
let firstImage = '';
if (item.media) {
  images = item.media.split(',').map(img => {
    const trimmedImg = img.trim();
    return trimmedImg.startsWith('http') ? trimmedImg : this.data.apiUrl + trimmedImg;
  });
  firstImage = images[0] || '';
}
```

### 3. 工单列表页面 (`servicePackage/pages/workorder/list/index.js`)
```javascript
// 处理图片字段，可能是单个图片或逗号分隔的多个图片
imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
```

## 最佳实践总结

### 1. 数据处理标准流程
```javascript
// 标准的media字段处理方法
function processMediaField(media, apiUrl) {
  if (!media) return [];
  
  return media.split(',')
    .map(img => img.trim())
    .filter(img => img.length > 0)
    .map(img => {
      if (img.startsWith('http')) {
        return img; // 已经是完整URL
      } else {
        return apiUrl + '/common-api/v1/file/' + img;
      }
    });
}
```

### 2. WXML模板标准结构
```xml
<!-- 图片列表 -->
<view class="image-list" wx:if="{{imageList && imageList.length > 0}}">
  <view class="image-item" wx:for="{{imageList}}" wx:key="index">
    <image
      src="{{item}}"
      class="content-image"
      mode="aspectFill"
      bindtap="previewImage"
      data-index="{{index}}"
    />
  </view>
</view>

<!-- 无图片提示 -->
<view class="no-images" wx:if="{{!imageList || imageList.length === 0}}">
  <text class="no-images-text">暂无图片</text>
</view>
```

### 3. CSS样式标准设计
```css
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.image-item {
  position: relative;
}

.content-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  border: 1px solid #eee;
  background-color: #f8f9fa;
}

.no-images {
  text-align: center;
  padding: 20px;
  color: #999;
}
```

### 4. 图片预览功能
```javascript
// 预览图片
previewImage: function(e) {
  const { index } = e.currentTarget.dataset;
  const { imageList } = this.data;
  
  wx.previewImage({
    current: imageList[index],
    urls: imageList
  });
}
```

## 注意事项

### 1. 数据安全
- 始终检查media字段是否存在
- 过滤掉空字符串和无效图片路径
- 处理网络异常情况

### 2. 性能优化
- 使用懒加载减少初始加载时间
- 设置合适的图片尺寸避免过大图片
- 考虑图片压缩和缓存策略

### 3. 用户体验
- 提供加载状态提示
- 支持图片预览功能
- 显示无图片时的友好提示

### 4. 兼容性
- 支持完整URL和相对路径
- 处理不同格式的图片路径
- 兼容旧版本的数据格式

这个实现方案已经在工单详情页面得到验证，可以作为其他页面处理media字段的标准参考。
