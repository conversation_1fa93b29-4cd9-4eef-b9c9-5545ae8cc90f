/* garbage.wxss */
.container {
  padding: 30rpx;
}

.page-header {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.page-subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 8rpx;
}

.search-bar {
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-icon {
  margin-left: 10rpx;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.category-tab {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.tab-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  background: #f5f5f5;
  color: #999;
}

.tab-icon.recyclable {
  background: #e8f5e9;
  color: #4caf50;
}

.tab-icon.harmful {
  background: #ffebee;
  color: #f44336;
}

.tab-icon.kitchen {
  background: #fff8e1;
  color: #ffc107;
}

.tab-icon.other {
  background: #e0f7fa;
  color: #00bcd4;
}

.tab-name {
  font-size: 24rpx;
  color: #666;
}

.category-tab.active .tab-name {
  color: #ff8c00;
  font-weight: 500;
}

.garbage-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.garbage-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.garbage-item:last-child {
  border-bottom: none;
}

.garbage-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background: #f5f5f5;
  color: #999;
}

.garbage-icon.recyclable {
  background: #e8f5e9;
  color: #4caf50;
}

.garbage-icon.harmful {
  background: #ffebee;
  color: #f44336;
}

.garbage-icon.kitchen {
  background: #fff8e1;
  color: #ffc107;
}

.garbage-icon.other {
  background: #e0f7fa;
  color: #00bcd4;
}

.garbage-info {
  flex: 1;
}

.garbage-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.garbage-category {
  font-size: 26rpx;
  color: #999;
}

.garbage-arrow {
  font-size: 28rpx;
  color: #999;
}

.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-result-icon {
  margin-bottom: 20rpx;
}

.no-result-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.no-result-tip {
  font-size: 24rpx;
  color: #999;
}

.category-guide {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #ff8c00;
  color: white;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
}

.guide-close {
  font-size: 48rpx;
  font-weight: 300;
}

.guide-content {
  flex: 1;
  background: white;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
}

.guide-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  background: #f5f5f5;
  color: #999;
  font-size: 48rpx;
}

.guide-icon.recyclable {
  background: #e8f5e9;
  color: #4caf50;
}

.guide-icon.harmful {
  background: #ffebee;
  color: #f44336;
}

.guide-icon.kitchen {
  background: #fff8e1;
  color: #ffc107;
}

.guide-icon.other {
  background: #e0f7fa;
  color: #00bcd4;
}

.guide-description {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
  text-align: center;
}

.guide-examples, .guide-tips {
  width: 100%;
  margin-bottom: 30rpx;
}

.examples-title, .tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.examples-list, .tips-list {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
