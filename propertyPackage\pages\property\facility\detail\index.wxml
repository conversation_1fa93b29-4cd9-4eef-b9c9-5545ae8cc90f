<!--设施详情页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">设施详情</view>
    <view class="nav-action" bindtap="navigateToEdit" wx:if="{{userPermissions.canEdit}}">
      <view class="edit-icon"></view>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 设施详情内容 -->
  <block wx:if="{{!isLoading && facility}}">
    <!-- 设施主图区域 -->
    <view class="facility-images">
      <swiper
        class="image-swiper"
        indicator-dots="{{facility.images.length > 1}}"
        autoplay="{{false}}"
        circular="{{true}}"
      >
        <!-- 使用默认图片 -->
        <swiper-item>
          <view class="facility-image-container">
            <view class="facility-image-placeholder">
              <view class="facility-icon {{facility.category}}-icon"></view>
              <text>{{facility.name}}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
      <view class="camera-icon" bindtap="addImage">
        <view class="icon-camera"></view>
      </view>
    </view>

    <!-- 设施核心信息 -->
    <view class="facility-core-info">
      <view class="facility-name-container">
        <view class="facility-name">{{facility.name}}</view>
        <view class="facility-status {{facility.status}}">{{facility.statusText}}</view>
      </view>
      <view class="facility-code">编号: {{facility.code}}</view>
      <view class="facility-qrcode" bindtap="showQRCode">
        <view class="qrcode-icon"></view>
        <text>查看二维码</text>
      </view>
      <view class="facility-location">
        <view class="location-icon"></view>
        <text>{{facility.location}}</text>
      </view>
      <view class="facility-responsible">
        <view class="person-icon"></view>
        <text>负责人: {{facility.responsiblePerson}}</text>
      </view>
    </view>

    <!-- 详细信息版块 -->
    <view class="facility-details">
      <view class="detail-tabs">
        <view
          class="detail-tab {{item.active ? 'active' : ''}}"
          wx:for="{{detailTabs}}"
          wx:key="id"
          bindtap="switchDetailTab"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>

      <!-- 基本参数 -->
      <view class="detail-content" wx:if="{{currentDetailTab === 'basic'}}">
        <view class="detail-item">
          <view class="detail-label">设施类型</view>
          <view class="detail-value">{{facility.categoryText}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">品牌</view>
          <view class="detail-value">{{facility.brand}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">型号</view>
          <view class="detail-value">{{facility.model}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">供应商</view>
          <view class="detail-value">{{facility.supplier}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">联系电话</view>
          <view class="detail-value">{{facility.contactPhone}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">安装日期</view>
          <view class="detail-value">{{facility.installDate}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">保修期至</view>
          <view class="detail-value">{{facility.warrantyEndDate}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">上次保养</view>
          <view class="detail-value">{{facility.lastMaintenance}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">下次保养</view>
          <view class="detail-value">{{facility.nextMaintenance}}</view>
        </view>
      </view>

      <!-- 运行数据 -->
      <view class="detail-content" wx:if="{{currentDetailTab === 'operation'}}">
        <block wx:if="{{facility.operationData}}">
          <view class="detail-item" wx:for="{{facility.operationData}}" wx:key="index" wx:for-index="key" wx:for-item="value">
            <view class="detail-label">{{key}}</view>
            <view class="detail-value">{{value}}</view>
          </view>
        </block>
        <view class="no-data" wx:else>
          <view class="no-data-icon"></view>
          <text>暂无运行数据</text>
        </view>
      </view>

      <!-- 关联文档 -->
      <view class="detail-content" wx:if="{{currentDetailTab === 'document'}}">
        <block wx:if="{{facility.documents && facility.documents.length > 0}}">
          <view class="document-item" wx:for="{{facility.documents}}" wx:key="index">
            <view class="document-icon"></view>
            <view class="document-name">{{item.name}}</view>
            <view class="document-action">查看</view>
          </view>
        </block>
        <view class="no-data" wx:else>
          <view class="no-data-icon"></view>
          <text>暂无关联文档</text>
        </view>
      </view>
    </view>

    <!-- 维护历史卡片 -->
    <view class="maintenance-history">
      <view class="card-title">维护历史</view>
      <view class="timeline">
        <block wx:if="{{maintenanceHistory && maintenanceHistory.length > 0}}">
          <view class="timeline-item" wx:for="{{maintenanceHistory}}" wx:key="id">
            <view class="timeline-dot {{item.type}}"></view>
            <view class="timeline-content">
              <view class="timeline-header">
                <view class="timeline-date">{{item.date}}</view>
                <view class="timeline-type {{item.type}}">{{item.typeText}}</view>
              </view>
              <view class="timeline-description">{{item.description}}</view>
              <view class="timeline-footer">
                <view class="timeline-operator">操作人: {{item.operator}}</view>
                <view class="timeline-result">结果: {{item.result}}</view>
              </view>
            </view>
          </view>
        </block>
        <view class="no-data" wx:else>
          <view class="no-data-icon"></view>
          <text>暂无维护历史</text>
        </view>
      </view>
    </view>

    <!-- 故障信息 (如果有) -->
    <view class="fault-info" wx:if="{{facility.status === 'fault' && facility.faultDescription}}">
      <view class="card-title">故障信息</view>
      <view class="fault-description">{{facility.faultDescription}}</view>
    </view>

    <!-- 底部操作按钮区域 -->
    <view class="bottom-actions">
      <!-- 发起报修 -->
      <view class="action-button" bindtap="initiateRepair" wx:if="{{userPermissions.canRepair}}">
        <view class="action-icon repair-icon"></view>
        <text>发起报修</text>
      </view>

      <!-- 记录维修/保养 -->
      <view class="action-button" bindtap="recordMaintenance" wx:if="{{userPermissions.canMaintain}}">
        <view class="action-icon maintenance-icon"></view>
        <text>记录维修/保养</text>
      </view>

      <!-- 扫码巡检 -->
      <view class="action-button" bindtap="scanInspection" wx:if="{{userPermissions.canInspect}}">
        <view class="action-icon inspection-icon"></view>
        <text>扫码巡检</text>
      </view>

      <!-- 更新状态 -->
      <view class="action-button" bindtap="updateStatus" wx:if="{{userPermissions.canUpdateStatus}}">
        <view class="action-icon status-icon"></view>
        <text>更新状态</text>
      </view>

      <!-- 查看监控 (仅监控设施) -->
      <view class="action-button" bindtap="viewMonitor" wx:if="{{facility.category === 'monitor'}}">
        <view class="action-icon monitor-icon"></view>
        <text>查看监控</text>
      </view>

      <!-- 设备控制 (如适用) -->
      <view class="action-button" bindtap="controlDevice" wx:if="{{facility.category === 'hvac' || facility.category === 'light'}}">
        <view class="action-icon control-icon"></view>
        <text>设备控制</text>
      </view>
    </view>
  </block>

  <!-- 图片查看器 -->
  <view class="image-viewer {{showImageViewer ? 'active' : ''}}" bindtap="closeImageViewer">
    <image
      class="viewer-image"
      src="{{facility.images[currentImageIndex]}}"
      mode="aspectFit"
      catchtap="stopPropagation"
    ></image>
    <view class="viewer-close" catchtap="closeImageViewer">
      <view class="close-icon"></view>
    </view>
  </view>

  <!-- 二维码查看器 -->
  <view class="qrcode-viewer {{showQRCode ? 'active' : ''}}" bindtap="closeQRCode">
    <view class="qrcode-container" catchtap="stopPropagation">
      <view class="qrcode-title">设施二维码</view>
      <!-- 使用默认二维码图标 -->
      <view class="qrcode-image-placeholder">
        <view class="qrcode-placeholder-icon"></view>
        <text>设施ID: {{facility.code}}</text>
      </view>
      <view class="qrcode-tip">扫描此二维码可快速查看设施信息</view>
      <view class="qrcode-close" catchtap="closeQRCode">关闭</view>
    </view>
  </view>
</view>
