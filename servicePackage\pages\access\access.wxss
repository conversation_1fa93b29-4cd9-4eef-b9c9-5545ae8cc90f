/* 智慧通行页面样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.tab.active .tab-text {
  color: #ff8c00;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #ff8c00;
  border-radius: 3rpx;
}

/* 门禁卡列表样式 */
.card-list {
  margin-bottom: 30rpx;
}

.card-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f3fbf5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.card-icon-inner {
  width: 40rpx;
  height: 40rpx;
  color: #34c759;
}

.card-info {
  flex: 1;
}

.card-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.card-number, .card-last-used {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.card-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.card-status.active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.card-status.inactive {
  background-color: #ffebee;
  color: #f44336;
}

.card-actions {
  display: flex;
}

.card-action-btn {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.card-action-btn.enable {
  background-color: #e8f5e9;
  color: #4caf50;
}

.card-action-btn.disable {
  background-color: #ffebee;
  color: #f44336;
}

/* 访客记录列表样式 */
.visitor-list {
  margin-bottom: 30rpx;
}

.visitor-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.visitor-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #fff5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.visitor-icon-inner {
  width: 40rpx;
  height: 40rpx;
  color: #ff3b30;
}

.visitor-info {
  flex: 1;
}

.visitor-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.visitor-phone, .visitor-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.visitor-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.visitor-status.upcoming {
  background-color: #fff3e0;
  color: #ff9800;
}

.visitor-status.completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.visitor-status.cancelled {
  background-color: #ffebee;
  color: #f44336;
}

.visitor-actions {
  display: flex;
}

.visitor-action-btn {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.visitor-action-btn.qrcode {
  background-color: #e3f2fd;
  color: #2196f3;
}

/* 无数据提示样式 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.no-data-icon {
  margin-bottom: 20rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加按钮样式 */
.add-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 200rpx;
  height: 80rpx;
  background-color: #ff8c00;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
}

.add-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.add-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .title {
  color: #f5f5f7;
}

.darkMode .subtitle {
  color: #8e8e93;
}

.darkMode .tabs {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .tab-text {
  color: #8e8e93;
}

.darkMode .tab.active .tab-text {
  color: #ff8c00;
}

.darkMode .card-item,
.darkMode .visitor-item {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-icon {
  background-color: rgba(52, 199, 89, 0.2);
}

.darkMode .visitor-icon {
  background-color: rgba(255, 59, 48, 0.2);
}

.darkMode .card-name,
.darkMode .visitor-name {
  color: #f5f5f7;
}

.darkMode .card-number,
.darkMode .card-last-used,
.darkMode .visitor-phone,
.darkMode .visitor-time {
  color: #8e8e93;
}

.darkMode .card-status.active {
  background-color: rgba(76, 175, 80, 0.2);
}

.darkMode .card-status.inactive {
  background-color: rgba(244, 67, 54, 0.2);
}

.darkMode .visitor-status.upcoming {
  background-color: rgba(255, 152, 0, 0.2);
}

.darkMode .visitor-status.completed {
  background-color: rgba(76, 175, 80, 0.2);
}

.darkMode .visitor-status.cancelled {
  background-color: rgba(244, 67, 54, 0.2);
}

.darkMode .card-action-btn.enable {
  background-color: rgba(76, 175, 80, 0.2);
}

.darkMode .card-action-btn.disable {
  background-color: rgba(244, 67, 54, 0.2);
}

.darkMode .visitor-action-btn.qrcode {
  background-color: rgba(33, 150, 243, 0.2);
}

.darkMode .no-data-text {
  color: #8e8e93;
}
