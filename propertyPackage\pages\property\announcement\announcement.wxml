<!-- 公告列表页 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 搜索框 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索公告标题" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
    </view>
  </view>

  <!-- 分类筛选区 -->
  <scroll-view class="filter-tabs" scroll-x enable-flex>
    <view class="tab-item {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view
      wx:for="{{noticeTypeDict}}"
      wx:key="nameEn"
      class="tab-item {{activeTab === item.nameEn ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="{{item.nameEn}}"
    >
      {{item.nameCn}}
    </view>
    <view class="tab-item {{activeTab === 'draft' ? 'active' : ''}}" bindtap="switchTab" data-tab="draft">草稿箱</view>
  </scroll-view>

  <!-- 公告列表 -->
  <view class="announcement-list" wx:if="{{!isLoading && !isEmpty}}">
    <view class="announcement-card" wx:for="{{announcements}}" wx:key="id" bindtap="onAnnouncementTap" data-id="{{item.id}}" data-status="{{item.status}}" bindlongpress="showActionMenu" data-item="{{item}}">
      <!-- 左侧类型图标 -->
      <view class="announcement-icon {{item.type}}"></view>

      <!-- 内容区域 -->
      <view class="announcement-content">
        <!-- 标题区 -->
        <view class="announcement-title-row">
          <text class="announcement-title">{{item.title}}</text>
          <view class="announcement-tags">
            <view class="tag pin" wx:if="{{item.top}}">置顶</view>
            <view class="tag new" wx:if="{{item.isNew}}">新</view>
            <view class="tag draft" wx:if="{{item.status === 'draft'}}">草稿</view>
          </view>
        </view>

        <!-- 内容预览 -->
        <view class="announcement-preview">{{item.preview}}</view>

        <!-- 底部信息栏 -->
        <view class="announcement-info">
          <view class="info-left">
            <text class="type-text">{{item.typeText}}</text>
            <text class="date-text">{{item.formattedDate}}</text>
          </view>
          <view class="info-right">
            <text class="read-count" wx:if="{{item.readCount !== undefined}}">{{item.readCount}} 阅读</text>
            <view class="more-icon" catchtap="showActionMenu" data-item="{{item}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && isEmpty}}">
    <view class="empty-icon"></view>
    <view class="empty-text">{{searchValue ? '未找到相关公告，试试其他关键词吧！' : '暂无公告，去发布一条吧！'}}</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{isLoadingMore}}">
    <view class="loading-spinner small"></view>
    <view class="loading-text">加载更多...</view>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && announcements.length > 0 && !isLoading}}">
    <view class="no-more-text">没有更多数据了</view>
  </view>

  <!-- 添加按钮 -->
  <!-- <view class="add-button" bindtap="addAnnouncement">
    <view class="add-icon">+</view>
  </view> -->

  <!-- 操作菜单弹窗 -->
  <view class="action-menu-mask" wx:if="{{showActionMenu}}" bindtap="closeActionMenu"></view>
  <view class="action-menu" wx:if="{{showActionMenu}}">
    <view class="action-menu-title">操作菜单</view>
    <view class="action-menu-item" bindtap="editAnnouncement">
      <view class="action-icon edit-icon"></view>
      <text>编辑</text>
    </view>
    <view class="action-menu-item" bindtap="togglePinAnnouncement" wx:if="{{currentAnnouncement.top}}">
      <view class="action-icon {{currentAnnouncement.top ? 'unpin-icon' : 'pin-icon'}}"></view>
      <text>{{currentAnnouncement.top ? '取消置顶' : '置顶'}}</text>
    </view>
    <view class="action-menu-item" bindtap="retractAnnouncement" wx:if="{{currentAnnouncement.status === 'published'}}">
      <view class="action-icon retract-icon"></view>
      <text>撤回</text>
    </view>
    <view class="action-menu-item delete" bindtap="deleteAnnouncement">
      <view class="action-icon delete-icon"></view>
      <text>删除</text>
    </view>
    <view class="action-menu-cancel" bindtap="closeActionMenu">取消</view>
  </view>

  <!-- 浮动发布按钮 -->
  <view class="fab-button" bindtap="goToPublish">
    <text class="fab-icon">+</text>
  </view>
</view>
