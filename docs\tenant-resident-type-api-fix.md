# 租客编辑住户身份字段提交问题修复

## 问题描述

用户在编辑租客信息时，修改了住户身份类型（从"租客"改为"租客家属"），但提交时住户身份字段没有被正确保存到后端。

## 问题分析

### 1. 前端代码检查

通过调试发现前端代码是正确的：

#### ✅ 表单数据更新正确
```javascript
// 住户身份选择变化处理
onResidentTypeChange: function (e) {
  const selectedType = residentTypes[index]
  this.setData({
    'formData.residentType': selectedType.nameEn,  // 正确更新
    residentTypePickerValue: [index],
    showResidentTypePicker: false
  })
}
```

#### ✅ 提交数据构建正确
```javascript
// 保存时构建的数据
const familyData = {
  residentName: formData.name,
  phone: formData.phone,
  idCardNumber: formData.idCardNumber,
  certificateType: formData.certificateType,
  roomId: formData.roomId || null,
  residentType: formData.residentType || 'tenant'  // 包含住户身份
}
```

#### ✅ API请求包含字段
从网络请求截图可以看到，Request Payload中确实包含了：
```json
{
  "residentType": "tenant_family"
}
```

### 2. API接口问题发现

问题出现在 `api/familyApi.js` 的 `updateFamily` 方法中：

#### ❌ 原代码（有问题）
```javascript
updateFamily: function (familyData) {
  const params = {
    id: familyData.id,
    familyResidentId: familyData.familyResidentId || familyData.residentId,
    residentName: familyData.residentName || familyData.name,
    certificateType: familyData.certificateType,
    idCardNumber: familyData.idCardNumber || familyData.idCard,
    phone: familyData.phone,
    roomId: familyData.roomId
    // 缺少 residentType 字段！
  }
  
  return request.request('/users-api/v1/member/resident/children', 'PUT', params, true)
}
```

**问题**：
- `updateFamily` 方法在构建 `params` 时，没有包含 `residentType` 字段
- 导致前端传递的 `residentType` 被丢弃，没有发送到后端
- 虽然前端Request Payload显示有这个字段，但那是因为前端直接传递了 `familyData`，而API方法重新构建了 `params`

## 解决方案

### 1. 修复API方法

在 `api/familyApi.js` 的 `updateFamily` 方法中添加 `residentType` 字段：

#### ✅ 修复后代码
```javascript
updateFamily: function (familyData) {
  const params = {
    id: familyData.id,
    familyResidentId: familyData.familyResidentId || familyData.residentId,
    residentName: familyData.residentName || familyData.name,
    certificateType: familyData.certificateType,
    idCardNumber: familyData.idCardNumber || familyData.idCard,
    phone: familyData.phone,
    roomId: familyData.roomId,
    residentType: familyData.residentType  // ✅ 添加住户身份类型字段
  }
  
  return request.request('/users-api/v1/member/resident/children', 'PUT', params, true)
}
```

### 2. 更新API文档注释

同时更新了方法的JSDoc注释，添加 `residentType` 参数说明：

```javascript
/**
 * 编辑家属
 * @param {Object} familyData - 家属信息
 * @param {number} familyData.id - 家属ID
 * @param {number} familyData.familyResidentId - 家属居民ID
 * @param {string} familyData.residentName - 家属姓名
 * @param {string} familyData.certificateType - 证件类型
 * @param {string} familyData.idCardNumber - 身份证号码
 * @param {string} familyData.phone - 手机号码
 * @param {number} familyData.roomId - 房间ID
 * @param {string} familyData.residentType - 住户身份类型（tenant/tenant_family等）
 * @returns {Promise} 返回编辑结果
 */
```

### 3. 检查新增方法

确认 `addFamily` 方法是正确的，因为它直接传递了完整的 `params`：

```javascript
addFamily: function (params) {
  return request.request('/users-api/v1/member/resident/children', 'POST', params, true)
}
```

## 数据流程

### 修复前的问题流程
```
前端表单 → formData.residentType ✅
构建familyData → residentType: "tenant_family" ✅
调用updateFamily → 重新构建params ❌ (丢失residentType)
发送到后端 → 缺少residentType字段 ❌
```

### 修复后的正确流程
```
前端表单 → formData.residentType ✅
构建familyData → residentType: "tenant_family" ✅
调用updateFamily → 重新构建params ✅ (包含residentType)
发送到后端 → 包含residentType字段 ✅
```

## 测试验证

修复后，请按以下步骤测试：

1. **进入租客编辑页面**
2. **修改住户身份**（从"租客"改为"租客家属"或反之）
3. **点击保存**
4. **检查列表页面**，确认住户身份显示正确更新
5. **重新编辑该租客**，确认住户身份选择器显示正确的值

## 相关文件

### 修改的文件
- `api/familyApi.js` - 修复updateFamily方法，添加residentType字段

### 相关文件（无需修改）
- `profilePackage/pages/profile/tenant/tenant.js` - 前端逻辑正确
- `profilePackage/pages/profile/tenant/tenant.wxml` - 界面正确
- `utils/util.js` - 字典处理正确

## 总结

这个问题是典型的**API数据映射遗漏**问题：

1. **前端逻辑完全正确**：表单更新、数据构建、字典使用都没有问题
2. **API方法有缺陷**：在重新构建请求参数时遗漏了关键字段
3. **修复简单有效**：只需在API方法中添加一行代码

通过这次修复，租客的住户身份类型现在可以正确保存和更新了。这也提醒我们在API方法中要确保所有必要的字段都被正确传递到后端。
