// points.js
const util = require('../../utils/util.js')
const PointsUtil = require('../../utils/points.js')

Page({
  data: {
    isAuthenticated: false,
    points: 0,
    currentCategory: 'all',
    darkMode: false,
    products: [
      {
        id: 1,
        name: '环保购物袋',
        points: 200,
        category: 'daily',
        image: 'https://images.unsplash.com/photo-1591195853828-11db59a44f6b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '可折叠环保购物袋，采用高强度面料制作，承重能力强，携带方便。'
      },
      {
        id: 2,
        name: '不锈钢保温杯',
        points: 500,
        category: 'daily',
        image: 'https://images.unsplash.com/photo-1577937927133-66ef06acdf18?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '304不锈钢保温杯，双层真空保温，可保温12小时以上，容量500ml。'
      },
      {
        id: 3,
        name: '物业费8折券',
        points: 800,
        category: 'service',
        image: 'https://images.unsplash.com/photo-1586892477838-2b96e85e0f96?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '物业费8折优惠券，可用于下次物业费缴纳，有效期3个月。'
      },
      {
        id: 4,
        name: '免费停车券',
        points: 300,
        category: 'service',
        image: 'https://images.unsplash.com/photo-1506521781263-d8422e82f27a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '访客免费停车券，可为访客提供3小时免费停车服务，有效期6个月。'
      },
      {
        id: 5,
        name: '蓝牙音箱',
        points: 1500,
        category: 'digital',
        image: 'https://images.unsplash.com/photo-1589003077984-894e133dabab?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '便携式蓝牙音箱，支持蓝牙5.0，续航时间8小时，防水防尘。'
      },
      {
        id: 6,
        name: '智能台灯',
        points: 1200,
        category: 'digital',
        image: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        description: '智能LED台灯，支持触控调光，多级亮度调节，护眼模式，USB充电。'
      }
    ],
    filteredProducts: [],
    showProductModal: false,
    currentProduct: {}
  },

  onLoad: function () {
    this.filterProducts('all')
    this.loadUserPoints()
  },

  onShow: function () {
    this.checkAuthStatus()
    this.loadUserPoints()

    // 更新底部tabbar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  // 加载用户积分
  loadUserPoints: function() {
    // 检查是否已认证
    if (!this.data.isAuthenticated) {
      return
    }

    // 使用积分工具获取用户积分
    PointsUtil.getUserPoints().then(points => {
      this.setData({
        points: points
      })
      console.log('积分中心页面加载积分:', points)
    }).catch(err => {
      console.error('获取用户积分失败:', err)
    })
  },

  checkAuthStatus: function () {
    const isAuthenticated = util.checkAuthentication()

    this.setData({
      isAuthenticated
    })
  },

  switchCategory: function (e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category
    })
    this.filterProducts(category)
  },

  filterProducts: function (category) {
    if (category === 'all') {
      this.setData({
        filteredProducts: this.data.products
      })
    } else {
      const filtered = this.data.products.filter(item => item.category === category)
      this.setData({
        filteredProducts: filtered
      })
    }
  },

  // 简化版本的商品详情功能
  showProductDetail: function (e) {
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  navigateWithAuth: function (e) {
    const url = e.currentTarget.dataset.url

    // 允许跳转到积分规则、积分明细、赚积分和会员等级页面，其他页面显示开发中提示
    if (url === '/pages/points/rules/rules' || url === '/pages/points/record/record' ||
        url === '/pages/points/earn/earn' || url === '/pages/points/level/level') {
      // 检查用户是否已认证
      if (this.data.isAuthenticated) {
        // 已认证用户直接跳转
        wx.navigateTo({
          url: url
        })
      } else {
        // 未认证用户显示认证提示
        util.showAuthModal(url)
      }
    } else {
      // 其他页面显示开发中提示
      wx.showToast({
        title: '功能开发中',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 跳转到积分商城
  navigateToMall: function() {
    // 显示开发中提示
    wx.showToast({
      title: '积分商城开发中',
      icon: 'none',
      duration: 2000
    })
  }
})
