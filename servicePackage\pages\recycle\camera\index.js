// pages/recycle/camera/index.js
Page({
  data: {
    darkMode: false,
    imageSrc: '',
    analyzing: false,
    result: null,
    historyList: []
  },

  onLoad: function() {
    // 从本地存储加载历史记录
    this.loadHistory()
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 加载历史记录
  loadHistory: function() {
    const historyList = wx.getStorageSync('cameraHistory') || []
    this.setData({
      historyList: historyList
    })
  },

  // 保存历史记录
  saveHistory: function(result) {
    let historyList = wx.getStorageSync('cameraHistory') || []
    
    // 添加时间戳
    const now = new Date()
    const timeStr = `${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
    
    // 创建新的历史记录
    const newHistory = {
      id: Date.now().toString(),
      imageSrc: this.data.imageSrc,
      name: result.name,
      type: result.type,
      typeName: result.typeName,
      typeColor: result.typeColor,
      time: timeStr
    }
    
    // 将新记录添加到历史列表的开头
    historyList.unshift(newHistory)
    
    // 限制历史记录数量为10条
    if (historyList.length > 10) {
      historyList = historyList.slice(0, 10)
    }
    
    // 保存到本地存储
    wx.setStorageSync('cameraHistory', historyList)
    
    // 更新数据
    this.setData({
      historyList: historyList
    })
  },

  // 清空历史记录
  clearHistory: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('cameraHistory')
          this.setData({
            historyList: []
          })
          wx.showToast({
            title: '已清空历史记录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 拍照
  takePhoto: function() {
    const ctx = wx.createCameraContext()
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        this.setData({
          imageSrc: res.tempImagePath
        })
      },
      fail: (err) => {
        console.error('拍照失败:', err)
        wx.showToast({
          title: '拍照失败',
          icon: 'error'
        })
      }
    })
  },

  // 取消拍照
  cancelPhoto: function() {
    this.setData({
      imageSrc: ''
    })
  },

  // 分析照片
  analyzePhoto: function() {
    this.setData({
      analyzing: true
    })
    
    // 模拟AI分析过程
    setTimeout(() => {
      // 模拟分析结果
      const result = {
        name: '塑料饮料瓶',
        type: 'recyclable',
        typeName: '可回收物',
        typeColor: '#2196f3',
        suggestion: '清空内容物，压扁后投入可回收物垃圾桶。瓶盖和瓶身可以分开投放，但都属于可回收物。'
      }
      
      this.setData({
        analyzing: false,
        result: result
      })
      
      // 保存到历史记录
      this.saveHistory(result)
      
      // 更新用户贡献数据
      this.updateContribution()
    }, 2000)
    
    // 实际项目中，这里应该调用云函数进行图像识别
    /*
    wx.uploadFile({
      url: 'https://your-cloud-function-url',
      filePath: this.data.imageSrc,
      name: 'image',
      success: (res) => {
        const result = JSON.parse(res.data)
        this.setData({
          analyzing: false,
          result: result
        })
        this.saveHistory(result)
        this.updateContribution()
      },
      fail: (err) => {
        console.error('分析失败:', err)
        wx.showToast({
          title: '分析失败',
          icon: 'error'
        })
        this.setData({
          analyzing: false
        })
      }
    })
    */
  },

  // 更新用户贡献数据
  updateContribution: function() {
    // 从本地存储获取当前贡献数据
    let contribution = wx.getStorageSync('userContribution') || {
      count: 0,
      carbon: 0
    }
    
    // 更新数据
    contribution.count += 1
    contribution.carbon += 0.5 // 假设每次正确分类减少0.5kg碳排放
    
    // 保存回本地存储
    wx.setStorageSync('userContribution', contribution)
  },

  // 重置相机
  resetCamera: function() {
    this.setData({
      imageSrc: '',
      result: null
    })
  },

  // 查看垃圾分类详情
  viewTypeDetail: function(e) {
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/pages/recycle/guide/detail?type=${type}`
    })
  },

  // 查看历史记录详情
  viewHistoryDetail: function(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    
    // 设置当前图片和结果
    this.setData({
      imageSrc: item.imageSrc,
      result: {
        name: item.name,
        type: item.type,
        typeName: item.typeName,
        typeColor: item.typeColor,
        suggestion: '查看历史记录中的详细投放建议。'
      }
    })
  },

  // 相机错误处理
  cameraError: function(e) {
    console.error('相机错误:', e.detail)
    wx.showToast({
      title: '相机启动失败',
      icon: 'error'
    })
  }
})
