<!--pages/payment/analysis/analysis.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 固定导航栏 -->
  <view class="fixed-nav">
    <!-- 状态栏 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px"></view>

    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-bar-left" bindtap="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="nav-bar-title">缴费分析</view>
      <view class="nav-bar-right"></view>
    </view>
  </view>

  <!-- 内容容器 -->
  <view class="content-container" style="padding-top: {{statusBarHeight + 44}}px;">
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view
        wx:for="{{timeRanges}}"
        wx:key="index"
        class="time-range-item {{selectedTimeRange === index ? 'active' : ''}}"
        bindtap="changeTimeRange"
        data-index="{{index}}"
      >
        {{item}}
      </view>
    </view>

    <!-- 图表类型选择 -->
    <view class="chart-type-selector">
      <view
        wx:for="{{chartTypes}}"
        wx:key="index"
        class="chart-type-item {{selectedChartType === index ? 'active' : ''}}"
        bindtap="changeChartType"
        data-index="{{index}}"
      >
        {{item}}
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="chart-container">
      <canvas
        type="2d"
        id="analysisCanvas"
        class="analysis-canvas"
        style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"
      ></canvas>

      <view class="loading-overlay" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
      </view>
    </view>

    <!-- 统计数据 -->
    <view class="statistics-container">
      <view class="statistics-title">
        <view class="statistics-icon"></view>
        <text>统计数据</text>
      </view>

      <view class="statistics-grid">
        <view class="statistics-item">
          <view class="statistics-value">¥{{statistics.totalAmount}}</view>
          <view class="statistics-label">总支出</view>
        </view>

        <view class="statistics-item">
          <view class="statistics-value">¥{{statistics.averageAmount}}</view>
          <view class="statistics-label">月均支出</view>
        </view>

        <view class="statistics-item">
          <view class="statistics-value">¥{{statistics.maxAmount}}</view>
          <view class="statistics-label">最高月份</view>
        </view>

        <view class="statistics-item">
          <view class="statistics-value">{{statistics.paymentCount}}次</view>
          <view class="statistics-label">缴费次数</view>
        </view>
      </view>
    </view>

    <!-- 节约建议 -->
    <view class="saving-tips-container">
      <view class="saving-tips-title">
        <view class="saving-tips-icon"></view>
        <text>节约建议</text>
      </view>

      <view class="saving-tips-list">
        <view
          wx:for="{{savingTips}}"
          wx:key="index"
          class="saving-tip-item"
        >
          <view class="saving-tip-bullet"></view>
          <text>{{item}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
