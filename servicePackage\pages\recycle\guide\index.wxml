<!--分类指南页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- Banner内容 -->
  <view class="banner-content">
    <view class="banner-desc">了解垃圾分类知识，保护环境从我做起</view>
  </view>

  <!-- 功能卡片区域 -->
  <view class="feature-cards">
    <!-- AI 拍照识别卡片 -->
    <view class="feature-card" bindtap="navigateToCamera">
      <view class="card-icon camera-icon"></view>
      <view class="card-content">
        <view class="card-title">AI 拍照识别</view>
        <view class="card-desc">拍照自动识别垃圾类别</view>
      </view>
      <view class="card-arrow"></view>
    </view>

    <!-- 每日5题闯关卡片 -->
    <view class="feature-card" bindtap="navigateToQuiz">
      <view class="card-icon quiz-icon"></view>
      <view class="card-content">
        <view class="card-title">每日5题闯关</view>
        <view class="card-desc">答满分+10碳积分</view>
      </view>
      <view class="card-arrow"></view>
    </view>

    <!-- 附近回收点卡片 -->
    <view class="feature-card" bindtap="navigateToRecyclePoints">
      <view class="card-icon location-icon"></view>
      <view class="card-content">
        <view class="card-title">附近回收点</view>
        <view class="card-desc">查找附近垃圾回收站点</view>
      </view>
      <view class="card-arrow"></view>
    </view>

    <!-- 我的贡献卡片 -->
    <view class="feature-card" bindtap="navigateToContribution">
      <view class="card-icon contribution-icon"></view>
      <view class="card-content">
        <view class="card-title">我的贡献</view>
        <view class="card-desc">查看个人环保成就</view>
      </view>
      <view class="card-arrow"></view>
    </view>
  </view>

  <!-- 分类知识卡片 -->
  <view class="knowledge-section">
    <view class="section-title">
      <view class="title-text">分类知识</view>
    </view>

    <view class="knowledge-cards">
      <view class="knowledge-card" wx:for="{{recycleTypes}}" wx:key="id" bindtap="showTypeDetail" data-type="{{item.id}}">
        <view class="knowledge-icon {{item.icon}}-icon" style="background-color: {{item.color}}10;"></view>
        <view class="knowledge-content">
          <view class="knowledge-title" style="color: {{item.color}};">{{item.name}}</view>
          <view class="knowledge-examples">
            <text wx:for="{{item.examples}}" wx:key="*this" wx:for-item="example">{{example}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分类小贴士 -->
  <view class="tips-section">
    <view class="section-title">
      <view class="title-text">分类小贴士</view>
    </view>

    <view class="tips-swiper-container">
      <swiper class="tips-swiper"
              indicator-dots="{{true}}"
              indicator-color="rgba(0, 0, 0, .15)"
              indicator-active-color="#4caf50"
              autoplay="{{true}}"
              interval="{{4000}}"
              duration="{{500}}"
              circular="{{true}}"
              current="{{currentTipIndex}}"
              bindchange="onTipChange">
        <swiper-item wx:for="{{recycleTips}}" wx:key="id" class="tip-swiper-item">
          <view class="tip-card">
            <view class="tip-content">{{item.content}}</view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</view>
