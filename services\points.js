// services/points.js
const PointsUtil = require('../utils/points');

/**
 * 任务类型配置
 */
const TASK_TYPES = {
  DAILY: 'daily',         // 每日任务
  ONCE: 'once',           // 一次性任务
  CONTINUOUS: 'continuous', // 连续任务
  CONDITION: 'condition',   // 条件任务
  COUNT: 'count'          // 计数任务
};

/**
 * 任务分类配置
 */
const TASK_CATEGORIES = {
  BASIC: 'basic',         // 基础活跃任务
  COMMUNITY: 'community', // 社区互动任务
  PROPERTY: 'property',   // 物业服务互动任务
  PAYMENT: 'payment',     // 缴费相关任务
  ONCE: 'once'            // 一次性任务
};

/**
 * 积分服务类
 */
const PointsService = {
  /**
   * 初始化积分系统
   */
  init: function() {
    // 初始化示例商品数据
    const products = wx.getStorageSync('mallProducts');
    if (!products || products.length === 0) {
      const defaultProducts = [
        {
          id: 1,
          title: '5元无门槛优惠券',
          points: 500,
          image: 'https://img.freepik.com/free-vector/coupon-design-template-with-best-offer-details_1017-30318.jpg',
          category: 'voucher',
          popularity: 98,
          stock: 999
        },
        {
          id: 2,
          title: '物业费9折优惠券',
          points: 800,
          image: 'https://img.freepik.com/free-vector/modern-sale-banner-template-with-fluid-shapes_1361-1389.jpg',
          category: 'voucher',
          popularity: 95,
          stock: 50
        },
        {
          id: 3,
          title: '小区定制帆布袋',
          points: 1200,
          image: 'https://img.freepik.com/free-psd/tote-bag-mockup-psd-shopping-beige-minimal-style_53876-129425.jpg',
          category: 'goods',
          popularity: 85,
          stock: 30
        },
        {
          id: 4,
          title: '环保购物袋',
          points: 600,
          image: 'https://img.freepik.com/free-psd/fabric-tote-bag-mockup-psd-shopping-essential_53876-130275.jpg',
          category: 'goods',
          popularity: 80,
          stock: 45
        },
        {
          id: 5,
          title: '免费洗车服务',
          points: 1500,
          image: 'https://img.freepik.com/free-photo/car-wash-detailing-station_1303-22304.jpg',
          category: 'service',
          popularity: 90,
          stock: 20
        },
        {
          id: 6,
          title: '小区健身房月卡',
          points: 2000,
          image: 'https://img.freepik.com/free-photo/modern-gym-with-various-equipment_23-2149346315.jpg',
          category: 'service',
          popularity: 88,
          stock: 15
        }
      ];
      wx.setStorageSync('mallProducts', defaultProducts);
    }

    // 初始化示例任务数据
    const tasks = wx.getStorageSync('pointsTasks');
    if (!tasks || tasks.length === 0) {
      const defaultTasks = [
        // 基础活跃任务
        {
          id: 1,
          title: '每日签到',
          description: '每日签到即可获得积分',
          points: 5,
          icon: 'calendar-check',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.BASIC,
          status: 'available',
          buttonText: '签到',
          continuous: {
            enabled: true,
            days: [7, 30],
            rewards: [30, 150]
          }
        },
        {
          id: 2,
          title: '浏览社区公告',
          description: '浏览社区公告可获得积分，每日上限10积分',
          points: 1,
          icon: 'eye',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.BASIC,
          status: 'available',
          buttonText: '去浏览',
          dailyLimit: 10,
          countTarget: 10
        },
        {
          id: 3,
          title: '浏览社区活动详情',
          description: '浏览社区活动详情可获得积分，每日上限5积分',
          points: 1,
          icon: 'eye',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.BASIC,
          status: 'available',
          buttonText: '去浏览',
          dailyLimit: 5,
          countTarget: 5
        },

        // 社区互动任务
        {
          id: 4,
          title: '发布社区帖子',
          description: '发布社区帖子可获得积分，每日上限15积分',
          points: 5,
          icon: 'edit',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.COMMUNITY,
          status: 'available',
          buttonText: '去发布',
          dailyLimit: 15,
          countTarget: 3
        },
        {
          id: 5,
          title: '回复/评论社区帖子',
          description: '回复/评论社区帖子可获得积分，每日上限10积分',
          points: 2,
          icon: 'comment',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.COMMUNITY,
          status: 'available',
          buttonText: '去评论',
          dailyLimit: 10,
          countTarget: 5
        },

        // 物业服务互动任务
        {
          id: 6,
          title: '提交报事报修申请',
          description: '提交报事报修申请可获得积分',
          points: 15,
          icon: 'tool',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.PROPERTY,
          status: 'available',
          buttonText: '去报修',
          dailyLimit: 15,
          countTarget: 1
        },
        {
          id: 7,
          title: '报事报修评价',
          description: '报事报修评价可获得积分',
          points: 5,
          icon: 'star',
          type: TASK_TYPES.DAILY,
          category: TASK_CATEGORIES.PROPERTY,
          status: 'available',
          buttonText: '去评价',
          dailyLimit: 5,
          countTarget: 1
        },

        // 缴费相关任务
        {
          id: 8,
          title: '按时缴纳物业费',
          description: '按时缴纳物业费可获得积分',
          points: 30,
          icon: 'credit-card',
          type: TASK_TYPES.CONDITION,
          category: TASK_CATEGORIES.PAYMENT,
          status: 'available',
          buttonText: '去缴费',
          condition: 'payment_on_time'
        },
        {
          id: 9,
          title: '提前10天以上缴费',
          description: '提前10天以上缴费可获得额外积分',
          points: 10,
          icon: 'clock',
          type: TASK_TYPES.CONDITION,
          category: TASK_CATEGORIES.PAYMENT,
          status: 'available',
          buttonText: '去缴费',
          condition: 'payment_early'
        },

        // 一次性任务
        {
          id: 10,
          title: '完善个人资料',
          description: '完善个人资料获得一次性奖励',
          points: 20,
          icon: 'user',
          type: TASK_TYPES.ONCE,
          category: TASK_CATEGORIES.ONCE,
          status: 'available',
          buttonText: '去完善'
        },
        {
          id: 11,
          title: '绑定手机号',
          description: '绑定手机号获得一次性奖励',
          points: 10,
          icon: 'phone',
          type: TASK_TYPES.ONCE,
          category: TASK_CATEGORIES.ONCE,
          status: 'available',
          buttonText: '去绑定'
        },
        {
          id: 12,
          title: '绑定房屋信息',
          description: '绑定房屋信息并通过认证获得一次性奖励',
          points: 50,
          icon: 'home',
          type: TASK_TYPES.ONCE,
          category: TASK_CATEGORIES.ONCE,
          status: 'available',
          buttonText: '去绑定'
        }
      ];
      wx.setStorageSync('pointsTasks', defaultTasks);
    }

    // 初始化用户等级
    const userInfo = wx.getStorageSync('userInfo') || {};
    if (!userInfo.levelId) {
      userInfo.levelId = 1; // 默认普通会员
      wx.setStorageSync('userInfo', userInfo);
    }

    // 检查积分过期
    this.checkPointsExpiration();

    // 检查等级变更
    this.checkLevelChange();
  },

  /**
   * 获取任务列表
   * @param {String} category 任务分类，不传则获取全部
   * @returns {Promise} 任务列表
   */
  getTasks: function(category) {
    return new Promise((resolve) => {
      // 获取所有任务
      const tasks = wx.getStorageSync('pointsTasks') || [];

      // 按分类筛选
      let filteredTasks = tasks;
      if (category) {
        filteredTasks = tasks.filter(task => task.category === category);
      }

      // 更新任务状态
      const updatedTasks = this.updateTasksStatus(filteredTasks);

      // 按状态排序：可完成 > 进行中 > 已完成 > 已达上限
      updatedTasks.sort((a, b) => {
        const statusOrder = { 'available': 0, 'in_progress': 1, 'completed': 2, 'limit_reached': 3 };
        return statusOrder[a.status] - statusOrder[b.status];
      });

      resolve(updatedTasks);
    });
  },

  /**
   * 更新任务状态
   * @param {Array} tasks 任务列表
   * @returns {Array} 更新后的任务列表
   */
  updateTasksStatus: function(tasks) {
    const today = new Date().toISOString().split('T')[0];
    const completedTasks = wx.getStorageSync('completedTasks') || [];
    const completedDailyTasks = wx.getStorageSync('completedDailyTasks') || {};
    const taskProgress = wx.getStorageSync('taskProgress') || {};

    return tasks.map(task => {
      const taskCopy = { ...task };

      // 一次性任务
      if (task.type === TASK_TYPES.ONCE) {
        if (completedTasks.includes(task.id)) {
          taskCopy.status = 'completed';
          taskCopy.buttonText = '已完成';
        } else {
          taskCopy.status = 'available';
        }
      }

      // 每日任务
      else if (task.type === TASK_TYPES.DAILY) {
        // 检查今日是否已完成
        if (completedDailyTasks[task.id] === today) {
          // 检查是否达到每日上限
          const dailyCount = taskProgress[`${task.id}_${today}`] || 0;
          if (task.countTarget && dailyCount >= task.countTarget) {
            taskCopy.status = 'limit_reached';
            taskCopy.buttonText = '已达上限';
          } else {
            taskCopy.status = 'completed';
            taskCopy.buttonText = '已完成';
          }
        } else {
          taskCopy.status = 'available';
        }

        // 添加进度信息
        if (task.countTarget) {
          const dailyCount = taskProgress[`${task.id}_${today}`] || 0;
          taskCopy.progress = {
            current: dailyCount,
            target: task.countTarget,
            percent: Math.min(100, Math.round(dailyCount / task.countTarget * 100))
          };
        }

        // 添加连续任务信息
        if (task.continuous && task.continuous.enabled) {
          const continuousDays = this.getContinuousDays(task.id);
          taskCopy.continuous.currentDays = continuousDays;

          // 计算距离下一个奖励还需天数
          let nextRewardDays = 0;
          for (let i = 0; i < task.continuous.days.length; i++) {
            if (continuousDays < task.continuous.days[i]) {
              nextRewardDays = task.continuous.days[i] - continuousDays;
              break;
            }
          }

          taskCopy.continuous.nextRewardDays = nextRewardDays;
        }
      }

      // 条件任务
      else if (task.type === TASK_TYPES.CONDITION) {
        // 检查条件是否满足
        const conditionMet = this.checkTaskCondition(task);
        if (conditionMet) {
          taskCopy.status = 'available';
        } else {
          taskCopy.status = 'unavailable';
          taskCopy.buttonText = '条件未满足';
        }
      }

      // 计数任务
      else if (task.type === TASK_TYPES.COUNT) {
        const count = taskProgress[task.id] || 0;
        if (count >= task.countTarget) {
          taskCopy.status = 'completed';
          taskCopy.buttonText = '已完成';
        } else {
          taskCopy.status = 'in_progress';
          taskCopy.buttonText = '进行中';
        }

        taskCopy.progress = {
          current: count,
          target: task.countTarget,
          percent: Math.min(100, Math.round(count / task.countTarget * 100))
        };
      }

      return taskCopy;
    });
  },

  /**
   * 获取连续完成天数
   * @param {Number} taskId 任务ID
   * @returns {Number} 连续天数
   */
  getContinuousDays: function(taskId) {
    const continuousRecord = wx.getStorageSync('continuousRecord') || {};
    const taskRecord = continuousRecord[taskId] || [];

    if (taskRecord.length === 0) {
      return 0;
    }

    // 按日期排序
    taskRecord.sort((a, b) => new Date(b) - new Date(a));

    // 计算连续天数
    let continuousDays = 1;
    const today = new Date().toISOString().split('T')[0];

    // 如果今天已完成，从昨天开始检查
    let checkDate = new Date();
    if (taskRecord[0] === today) {
      checkDate.setDate(checkDate.getDate() - 1);
    }

    for (let i = 1; i < taskRecord.length; i++) {
      const dateToCheck = checkDate.toISOString().split('T')[0];
      if (taskRecord.includes(dateToCheck)) {
        continuousDays++;
        checkDate.setDate(checkDate.getDate() - 1);
      } else {
        break;
      }
    }

    return continuousDays;
  },

  /**
   * 检查任务条件是否满足
   * @param {Object} task 任务对象
   * @returns {Boolean} 条件是否满足
   */
  checkTaskCondition: function(task) {
    // 根据不同条件类型检查
    switch (task.condition) {
      case 'payment_on_time':
        // 检查是否有按时缴费记录
        const paymentRecords = wx.getStorageSync('paymentRecords') || [];
        return paymentRecords.some(record => record.onTime);

      case 'payment_early':
        // 检查是否有提前10天缴费记录
        const earlyPaymentRecords = wx.getStorageSync('paymentRecords') || [];
        return earlyPaymentRecords.some(record => record.daysEarly >= 10);

      default:
        return false;
    }
  },

  /**
   * 更新连续任务记录
   * @param {Number} taskId 任务ID
   * @param {String} date 日期，格式：YYYY-MM-DD
   */
  updateContinuousRecord: function(taskId, date) {
    const continuousRecord = wx.getStorageSync('continuousRecord') || {};
    const taskRecord = continuousRecord[taskId] || [];

    // 如果日期不在记录中，添加
    if (!taskRecord.includes(date)) {
      taskRecord.push(date);
    }

    // 更新记录
    continuousRecord[taskId] = taskRecord;
    wx.setStorageSync('continuousRecord', continuousRecord);
  },

  /**
   * 完成任务并获取积分
   * @param {Number} taskId 任务ID
   * @param {Object} extraData 额外数据
   */
  completeTask: function(taskId, extraData = {}) {
    return new Promise((resolve, reject) => {
      // 获取任务信息
      const tasks = wx.getStorageSync('pointsTasks') || [];
      const taskIndex = tasks.findIndex(t => t.id === taskId);

      if (taskIndex === -1) {
        reject(new Error('任务不存在'));
        return;
      }

      const task = tasks[taskIndex];
      const today = new Date().toISOString().split('T')[0];

      // 根据任务类型处理
      switch (task.type) {
        case TASK_TYPES.ONCE:
          // 检查任务是否已完成
          if (PointsUtil.isTaskCompleted(taskId)) {
            reject(new Error('任务已完成'));
            return;
          }

          // 添加积分
          PointsUtil.addPoints(task.points, 'task', `完成任务：${task.title}`).then(newPoints => {
            // 标记任务为已完成
            PointsUtil.markTaskCompleted(taskId);

            // 更新任务状态
            tasks[taskIndex].status = 'completed';
            tasks[taskIndex].buttonText = '已完成';
            wx.setStorageSync('pointsTasks', tasks);

            resolve({
              success: true,
              points: task.points,
              newPoints: newPoints,
              task: task
            });
          }).catch(reject);
          break;

        case TASK_TYPES.DAILY:
          // 获取任务进度
          const taskProgress = wx.getStorageSync('taskProgress') || {};
          const dailyKey = `${taskId}_${today}`;
          const currentCount = taskProgress[dailyKey] || 0;

          // 检查是否达到每日上限
          if (task.dailyLimit && currentCount >= task.countTarget) {
            reject(new Error('已达到每日上限'));
            return;
          }

          // 更新任务进度
          taskProgress[dailyKey] = currentCount + 1;
          wx.setStorageSync('taskProgress', taskProgress);

          // 添加积分
          PointsUtil.addPoints(task.points, 'task', `完成任务：${task.title}`).then(newPoints => {
            // 标记今日已完成
            const completedDailyTasks = wx.getStorageSync('completedDailyTasks') || {};
            completedDailyTasks[taskId] = today;
            wx.setStorageSync('completedDailyTasks', completedDailyTasks);

            // 如果是连续任务，更新连续记录
            if (task.continuous && task.continuous.enabled) {
              this.updateContinuousRecord(taskId, today);

              // 检查是否达到连续奖励
              const continuousDays = this.getContinuousDays(taskId);
              let extraPoints = 0;

              for (let i = 0; i < task.continuous.days.length; i++) {
                if (continuousDays === task.continuous.days[i]) {
                  extraPoints = task.continuous.rewards[i];
                  break;
                }
              }

              // 如果有额外奖励，添加积分
              if (extraPoints > 0) {
                PointsUtil.addPoints(extraPoints, 'task', `连续${continuousDays}天完成任务：${task.title}`).then(() => {
                  resolve({
                    success: true,
                    points: task.points + extraPoints,
                    newPoints: newPoints + extraPoints,
                    task: task,
                    continuousDays: continuousDays,
                    extraPoints: extraPoints
                  });
                }).catch(reject);
              } else {
                resolve({
                  success: true,
                  points: task.points,
                  newPoints: newPoints,
                  task: task,
                  continuousDays: continuousDays
                });
              }
            } else {
              resolve({
                success: true,
                points: task.points,
                newPoints: newPoints,
                task: task
              });
            }
          }).catch(reject);
          break;

        case TASK_TYPES.CONDITION:
          // 检查条件是否满足
          if (!this.checkTaskCondition(task)) {
            reject(new Error('任务条件未满足'));
            return;
          }

          // 添加积分
          PointsUtil.addPoints(task.points, 'task', `完成任务：${task.title}`).then(newPoints => {
            resolve({
              success: true,
              points: task.points,
              newPoints: newPoints,
              task: task
            });
          }).catch(reject);
          break;

        case TASK_TYPES.COUNT:
          // 获取任务进度
          const countProgress = wx.getStorageSync('taskProgress') || {};
          const currentCountValue = countProgress[taskId] || 0;

          // 更新任务进度
          countProgress[taskId] = currentCountValue + 1;
          wx.setStorageSync('taskProgress', countProgress);

          // 如果达到目标，添加积分
          if (currentCountValue + 1 >= task.countTarget) {
            PointsUtil.addPoints(task.points, 'task', `完成任务：${task.title}`).then(newPoints => {
              resolve({
                success: true,
                points: task.points,
                newPoints: newPoints,
                task: task,
                completed: true
              });
            }).catch(reject);
          } else {
            resolve({
              success: true,
              points: 0,
              task: task,
              progress: currentCountValue + 1,
              target: task.countTarget
            });
          }
          break;

        default:
          reject(new Error('未知任务类型'));
      }
    });
  },

  /**
   * 消费返积分
   * @param {Number} amount 消费金额
   * @param {String} orderType 订单类型
   * @param {String} orderDesc 订单描述
   */
  earnPointsFromPurchase: function(amount, orderType, orderDesc) {
    // 消费1元返1积分，四舍五入到整数
    const points = Math.round(amount);

    return PointsUtil.addPoints(points, 'purchase', `消费返积分：${orderDesc}`);
  },

  /**
   * 使用积分抵扣订单金额
   * @param {Number} points 使用的积分数量
   * @param {Number} orderAmount 订单金额
   * @param {String} orderDesc 订单描述
   */
  usePointsForOrder: function(points, orderAmount, orderDesc) {
    return new Promise((resolve, reject) => {
      // 获取用户等级信息，以确定积分抵扣比例
      PointsUtil.getPointsDiscountRatio().then(discountInfo => {
        const { pointsRatio, maxDiscountRatio } = discountInfo;

        // 计算积分可抵扣的金额
        const discountAmount = points / pointsRatio;

        // 计算最大可抵扣金额
        const maxDiscount = orderAmount * maxDiscountRatio;
        const actualDiscount = Math.min(discountAmount, maxDiscount);
        const actualPoints = Math.floor(actualDiscount * pointsRatio);

        PointsUtil.usePoints(actualPoints, 'order', `积分抵扣：${orderDesc}`).then(newPoints => {
          resolve({
            success: true,
            points: actualPoints,
            discount: actualDiscount,
            newPoints: newPoints,
            pointsRatio: pointsRatio,
            maxDiscountRatio: maxDiscountRatio
          });
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 检查积分是否过期
   */
  checkPointsExpiration: function() {
    PointsUtil.checkPointsExpiration().then(expiredPoints => {
      if (expiredPoints > 0) {
        // 显示过期提醒
        wx.showToast({
          title: `${expiredPoints}积分已过期`,
          icon: 'none',
          duration: 3000
        });
      }
    }).catch(err => {
      console.error('检查积分过期失败:', err);
    });
  },

  /**
   * 检查会员等级变更
   */
  checkLevelChange: function() {
    PointsUtil.checkLevelChange().then(result => {
      if (result.changed && result.isUpgrade) {
        // 显示升级提醒
        wx.showModal({
          title: '会员升级',
          content: `恭喜您已升级为${result.newLevel.name}，可享受更多特权！`,
          showCancel: false,
          confirmText: '查看特权',
          success: (res) => {
            if (res.confirm) {
              // 跳转到等级页面
              wx.navigateTo({
                url: '/pages/points/level/level'
              });
            }
          }
        });
      }
    }).catch(err => {
      console.error('检查会员等级变更失败:', err);
    });
  },

  /**
   * 获取等级特权
   * @param {Number} levelId 等级ID
   */
  getLevelPrivileges: function(levelId) {
    return PointsUtil.getLevelPrivileges(levelId);
  }
};

module.exports = PointsService;
