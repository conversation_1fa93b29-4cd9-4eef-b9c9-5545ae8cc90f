# 物业认证字段变化检测功能测试

## 功能概述
完善了物业认证编辑模式下的字段变化检测功能，当用户修改任何字段后，修改认证按钮会从不可点击状态变为可点击状态。

## 已完成的修改

### 1. 扩展了 `checkFieldChanges` 方法
- 原来只检测姓名和手机号的变化
- 现在对物业认证模式额外检测：
  - 证件号码变化 (`idCard`)
  - 证件类型变化 (`certificateType`)
  - 员工编号变化 (`employeeId`)
  - 部门变化 (`departmentId`)
  - 职位变化 (`positionId`)
  - 工作证照片变化 (`workCardPhotoServerPath`)

### 2. 保存更多原始数据用于比较
在 `matchPropertyAuthData` 方法中保存了所有字段的原始值：
- `originalIdCard`
- `originalCertificateType`
- `originalEmployeeId`
- `originalDepartmentId`
- `originalPositionId`
- `originalWorkCardPhotoServerPath`

### 3. 在各个输入事件中添加字段变化检测
- `onIdCardInput` - 证件号码输入
- `onEmployeeIdInput` - 员工编号输入
- `onCertificateTypeChange` - 证件类型选择
- `confirmDepartment` - 部门选择确认
- `confirmPosition` - 职位选择确认
- `uploadWorkCardPhoto` - 工作证照片上传成功

### 4. 修复了注销按钮显示逻辑
- 注销按钮只在实名认证模式下显示 (`authMethod === 'resident'`)
- 物业认证模式下不显示注销按钮

### 5. 修复了工作证照片反显问题
- 使用 `media` 字段获取服务器路径
- 显示时加上 `apiUrl + '/common-api/v1/file/'` 前缀
- 正确处理 `workCardPhotoPath` (显示路径) 和 `workCardPhotoServerPath` (服务器路径)

## 测试场景

### 场景1：物业认证编辑模式
1. 用户已完成物业认证
2. 从我的页面点击物业员工认证进入编辑模式
3. 修改任意字段（姓名、手机号、证件类型、证件号码、员工编号、部门、职位、工作证照片）
4. 修改认证按钮应该从禁用状态变为可点击状态

### 场景2：实名认证编辑模式
1. 用户已完成实名认证
2. 从我的页面点击实名认证进入编辑模式
3. 显示注销认证按钮
4. 修改姓名或手机号后，修改认证按钮变为可点击状态

### 场景3：物业认证编辑模式
1. 用户已完成物业认证
2. 从我的页面点击物业员工认证进入编辑模式
3. 不显示注销认证按钮
4. 修改任意字段后，修改认证按钮变为可点击状态

## 关键代码逻辑

```javascript
// 检查字段变化的核心逻辑
checkFieldChanges: function () {
  const nameChanged = this.data.name !== this.data.originalName;
  const phoneChanged = this.data.phone !== this.data.originalPhone;
  
  let hasChanged = nameChanged || phoneChanged;
  
  // 如果是物业员工认证，还需要检查其他字段
  if (this.data.authMethod === 'property') {
    const idCardChanged = this.data.idCard !== this.data.originalIdCard;
    const certificateTypeChanged = this.data.certificateType !== this.data.originalCertificateType;
    const employeeIdChanged = this.data.employeeId !== this.data.originalEmployeeId;
    const departmentChanged = this.data.departmentId !== this.data.originalDepartmentId;
    const positionChanged = this.data.positionId !== this.data.originalPositionId;
    const workCardPhotoChanged = this.data.workCardPhotoServerPath !== this.data.originalWorkCardPhotoServerPath;
    
    hasChanged = hasChanged || idCardChanged || certificateTypeChanged || 
                 employeeIdChanged || departmentChanged || positionChanged || workCardPhotoChanged;
  }

  // 如果姓名或手机号发生变化，需要验证码
  const needVerifyCode = nameChanged || phoneChanged;

  this.setData({
    hasFieldChanged: hasChanged,
    needVerifyCode: needVerifyCode,
    canSubmitEdit: hasChanged && (!needVerifyCode || this.data.verifyCodeValid)
  });
}
```

## 预期效果
- 物业认证编辑模式下，修改任何字段都会触发按钮状态变化
- 实名认证模式显示注销按钮，物业认证模式不显示
- 工作证照片正确反显和上传
- 用户体验更加流畅和直观
