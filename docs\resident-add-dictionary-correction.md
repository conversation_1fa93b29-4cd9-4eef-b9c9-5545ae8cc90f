# 添加居民页面字典配置修正

## 字典配置修正

根据最新要求，修正了添加居民页面的字典配置：

### 正确的字典配置

```javascript
// 获取人员标签字典
const residentTag = util.getDictByNameEn('resident_tag')[0].children || [];

// 获取证件类型字典  
const certificateType = util.getDictByNameEn('certificate_type')[0].children || [];

// 获取居民住户类型字典
const residentType = util.getDictByNameEn('resident_type')[0].children || [];

// 获取性别字典
const genderDict = util.getDictByNameEn('gender')[0].children || [];
```

### 之前的错误配置

```javascript
// 错误：人员标签使用了vehicle_status字典
const residentTag = util.getDictByNameEn('vehicle_status')[0].children || [];
```

## 人员标签功能说明

### 功能特点
- **多选支持**: 用户可以选择多个人员标签
- **弹窗选择**: 点击标签输入框弹出选择弹窗
- **实时反馈**: 选中状态有视觉反馈（蓝色背景+勾选标记）
- **中文显示**: 选中的标签以中文名称显示，用顿号分隔

### 数据结构
```javascript
// 表单数据中的标签字段
formData: {
  tags: ['tag1_en', 'tag2_en', 'tag3_en'] // 存储标签的nameEn值数组
}

// 选中的标签对象数组
selectedTags: [
  { nameEn: 'tag1_en', nameCn: '标签1' },
  { nameEn: 'tag2_en', nameCn: '标签2' }
]

// 显示文本
selectedTagsText: '标签1、标签2' // 用于界面显示
```

### 提交数据格式
```javascript
// API提交时的标签格式
params: {
  tags: "tag1_en,tag2_en,tag3_en" // 多个标签用逗号分隔
}
```

## 选择流程

1. **点击标签输入框** → 弹出标签选择弹窗
2. **选择标签** → 点击标签项进行多选/取消选择
3. **视觉反馈** → 选中的标签显示蓝色背景和勾选标记
4. **确认选择** → 点击确定按钮关闭弹窗
5. **更新显示** → 输入框显示选中标签的中文名称

## 相关方法

### 标签选择相关方法
```javascript
showTagSelector()           // 显示标签选择弹窗
hideTagSelector()           // 隐藏标签选择弹窗
toggleTag(e)               // 切换标签选择状态
confirmTagSelection()       // 确认标签选择
updateResidentTagWithSelected() // 更新带选中状态的标签列表
```

### 数据处理方法
```javascript
// 更新显示文本
const selectedTagsText = selectedTags.map(tag => tag.nameCn).join('、');

// 更新表单数据
formData.tags = selectedTags.map(tag => tag.nameEn);

// 提交时的数据格式
tags: formData.tags.join(',')
```

## 界面效果

### 输入框显示
- **未选择**: "请选择人员标签"
- **已选择**: "标签1、标签2、标签3"

### 弹窗显示
- **未选中标签**: 灰色背景
- **已选中标签**: 蓝色背景 + 右侧勾选标记

## 注意事项

1. 人员标签字典必须使用 `resident_tag`
2. 支持多选，可以选择0个或多个标签
3. 标签的nameEn用于数据存储和提交
4. 标签的nameCn用于界面显示
5. 提交API时多个标签用逗号分隔
