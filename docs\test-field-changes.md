# 物业认证字段变化检测修复测试

## 问题描述
物业认证反显时，修改姓名和手机号码，修改按钮仍是灰色不可点击的。

## 问题原因
原来的 `checkFieldChanges` 方法混合了实名认证和物业认证的逻辑，导致字段变化检测不够清晰和准确。

## 解决方案
创建了两套独立的检测方法：
1. `checkResidentFieldChanges` - 专门处理实名认证的字段变化
2. `checkPropertyFieldChanges` - 专门处理物业员工认证的字段变化

## 修复内容

### 1. 重构了主检测方法
```javascript
// 检查字段是否发生变化（编辑模式）
checkFieldChanges: function () {
  if (this.data.authMethod === 'property') {
    this.checkPropertyFieldChanges();
  } else {
    this.checkResidentFieldChanges();
  }
}
```

### 2. 实名认证字段变化检测
```javascript
checkResidentFieldChanges: function () {
  const nameChanged = this.data.name !== this.data.originalName;
  const phoneChanged = this.data.phone !== this.data.originalPhone;
  
  const hasChanged = nameChanged || phoneChanged;
  
  // 如果姓名或手机号发生变化，需要验证码
  const needVerifyCode = nameChanged || phoneChanged;

  this.setData({
    hasFieldChanged: hasChanged,
    needVerifyCode: needVerifyCode,
    canSubmitEdit: hasChanged && (!needVerifyCode || this.data.verifyCodeValid)
  });
}
```

### 3. 物业员工认证字段变化检测
```javascript
checkPropertyFieldChanges: function () {
  const nameChanged = this.data.name !== this.data.originalName;
  const phoneChanged = this.data.phone !== this.data.originalPhone;
  const idCardChanged = this.data.idCard !== this.data.originalIdCard;
  const certificateTypeChanged = this.data.certificateType !== this.data.originalCertificateType;
  const employeeIdChanged = this.data.employeeId !== this.data.originalEmployeeId;
  const departmentChanged = this.data.departmentId !== this.data.originalDepartmentId;
  const positionChanged = this.data.positionId !== this.data.originalPositionId;
  const workCardPhotoChanged = this.data.workCardPhotoServerPath !== this.data.originalWorkCardPhotoServerPath;
  
  const hasChanged = nameChanged || phoneChanged || idCardChanged || certificateTypeChanged || 
                     employeeIdChanged || departmentChanged || positionChanged || workCardPhotoChanged;
  
  // 物业员工认证中，如果姓名或手机号发生变化，需要验证码
  const needVerifyCode = nameChanged || phoneChanged;

  this.setData({
    hasFieldChanged: hasChanged,
    needVerifyCode: needVerifyCode,
    canSubmitEdit: hasChanged && (!needVerifyCode || this.data.verifyCodeValid)
  });
}
```

### 4. 完善了原始数据保存
- 实名认证：保存 `originalName`, `originalPhone`, `originalIdCard`, `originalCertificateType`
- 物业认证：保存所有字段的原始值，包括部门、职位、工作证照片等

## 测试场景

### 场景1：物业认证编辑模式 - 修改姓名
1. 用户已完成物业认证
2. 进入编辑模式，反显所有信息
3. 修改姓名字段
4. **预期结果**：修改认证按钮变为可点击状态，需要验证码

### 场景2：物业认证编辑模式 - 修改手机号
1. 用户已完成物业认证
2. 进入编辑模式，反显所有信息
3. 修改手机号字段
4. **预期结果**：修改认证按钮变为可点击状态，需要验证码

### 场景3：物业认证编辑模式 - 修改其他字段
1. 用户已完成物业认证
2. 进入编辑模式，反显所有信息
3. 修改证件号码、员工编号、部门、职位或工作证照片
4. **预期结果**：修改认证按钮变为可点击状态，不需要验证码

### 场景4：实名认证编辑模式
1. 用户已完成实名认证
2. 进入编辑模式，反显所有信息
3. 修改姓名或手机号
4. **预期结果**：修改认证按钮变为可点击状态，需要验证码

## 关键改进点

1. **逻辑分离**：实名认证和物业认证使用完全独立的检测逻辑
2. **字段完整性**：物业认证检测所有相关字段的变化
3. **验证码逻辑**：只有姓名或手机号变化时才需要验证码
4. **状态一致性**：确保按钮状态与实际字段变化保持一致
5. **调试信息**：添加详细的控制台日志便于调试

## 预期效果
- 物业认证编辑模式下，修改任何字段都能正确触发按钮状态变化
- 实名认证和物业认证的逻辑完全独立，互不干扰
- 用户体验更加流畅和直观
- 代码结构更清晰，便于维护
