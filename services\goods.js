// goods.js
const PointsUtil = require('../utils/points');

/**
 * 商品服务类
 */
const GoodsService = {
  /**
   * 规范化商品数据的字段
   * 统一数据字段，确保不同页面和来源的数据字段格式一致
   * @param {Object} goods 商品数据对象
   * @returns {Object} 规范化后的商品数据对象
   */
  normalizeGoodsData: function(goods) {
    if (!goods) return null;

    const normalizedGoods = { ...goods };

    // ID字段统一
    if (normalizedGoods.goodsId && !normalizedGoods.id) {
      normalizedGoods.id = normalizedGoods.goodsId;
    }

    // 名称字段统一
    if (normalizedGoods.title && !normalizedGoods.name) {
      normalizedGoods.name = normalizedGoods.title;
    } else if (normalizedGoods.name && !normalizedGoods.title) {
      normalizedGoods.title = normalizedGoods.name;
    }

    // 商品类型统一
    if (normalizedGoods.goodsType && !normalizedGoods.type) {
      normalizedGoods.type = normalizedGoods.goodsType;
    }

    // --- 图片处理开始 ---
    let imagesArray = [];
    if (goods.images) {
      if (Array.isArray(goods.images)) {
        // 已经是数组，过滤掉非字符串项
        imagesArray = goods.images.filter(img => typeof img === 'string');
      } else if (typeof goods.images === 'string') {
        try {
          // 尝试解析JSON字符串
          const parsed = JSON.parse(goods.images);
          if (Array.isArray(parsed)) {
            // 解析成功且是数组，过滤非字符串项
            imagesArray = parsed.filter(img => typeof img === 'string');
          } else if (typeof parsed === 'string') {
            // 解析结果是单个字符串
             imagesArray = [parsed];
          } else {
             // 解析结果不是预期格式，尝试将原始字符串作为单图处理
             if (goods.images.trim()) { // 避免空字符串
                 imagesArray = [goods.images.trim()];
             }
          }
        } catch (e) {
          // 解析失败，将原始字符串视为单个图片URL（如果非空）
          if (goods.images.trim()) {
               imagesArray = [goods.images.trim()];
          }
        }
      }
    } else if (goods.image && typeof goods.image === 'string' && goods.image.trim()) {
      // 如果没有 images 但有 image 字段
      imagesArray = [goods.image.trim()];
    }

    normalizedGoods.images = imagesArray; // 始终设置为数组

    // 设置主图片 (image 字段) 为数组中的第一个有效图片，否则为 null
    normalizedGoods.image = imagesArray.length > 0 ? imagesArray[0] : null;
    // --- 图片处理结束 ---

    // 卖家信息统一
    if (normalizedGoods.sellerId && !normalizedGoods.userId) {
      normalizedGoods.userId = normalizedGoods.sellerId;
    }
    if (normalizedGoods.sellerName && !normalizedGoods.userName) {
      normalizedGoods.userName = normalizedGoods.sellerName;
    }
    if (normalizedGoods.sellerAvatar && !normalizedGoods.userAvatar) {
      normalizedGoods.userAvatar = normalizedGoods.sellerAvatar;
    }

    // 确保商品类型名称存在
    if (!normalizedGoods.typeName && normalizedGoods.type) {
      if (normalizedGoods.type === this.GOODS_TYPES.NORMAL) {
        normalizedGoods.typeName = '全新';
      } else if (normalizedGoods.type === this.GOODS_TYPES.SECOND_HAND) {
        normalizedGoods.typeName = '二手';
      } else if (normalizedGoods.type === this.GOODS_TYPES.FREE) {
        normalizedGoods.typeName = '免费送';
      } else if (normalizedGoods.type === this.GOODS_TYPES.POINTS) {
        normalizedGoods.typeName = '积分兑换';
      }
    }

    // 确保商品描述字段存在
    if (normalizedGoods.detail && !normalizedGoods.description) {
      normalizedGoods.description = normalizedGoods.detail;
    } else if (normalizedGoods.description && !normalizedGoods.detail) {
      normalizedGoods.detail = normalizedGoods.description;
    }

    // 确保积分相关信息格式一致
    if (!normalizedGoods.pointsDiscount) {
      normalizedGoods.pointsDiscount = {
        enabled: !!normalizedGoods.allowPoint,
        ratio: normalizedGoods.pointRate || 100,
        maxRatio: normalizedGoods.maxPointDiscount ? parseFloat(normalizedGoods.maxPointDiscount) / parseFloat(normalizedGoods.price || 1) : 0.3
      };
    }

    // 确保价格是数字格式
    if (normalizedGoods.price && typeof normalizedGoods.price === 'string') {
      normalizedGoods.price = parseFloat(normalizedGoods.price);
    }

    return normalizedGoods;
  },
  /**
   * 商品类型
   */
  GOODS_TYPES: {
    NORMAL: 1,      // 普通商品
    SECOND_HAND: 2, // 二手商品
    FREE: 3,        // 免费商品
    POINTS: 4       // 积分兑换商品
  },

  /**
   * 初始化商品数据
   */
  init: function() {
    // 初始化示例商品数据
    const goods = wx.getStorageSync('goods');
    if (!goods || goods.length === 0) {
      // 创建示例商品数据
      const defaultGoods = this.createDefaultGoods();
      wx.setStorageSync('goods', defaultGoods);
    }
  },

  /**
   * 创建默认商品数据
   * @returns {Array} 默认商品数据
   */
  createDefaultGoods: function() {
    return [
      {
        id: 1,
        title: '精选水果礼盒',
        description: '精选时令水果，新鲜美味',
        price: 99.00,
        points: 0,
        pointsDiscount: {
          enabled: true,
          ratio: 100, // 100积分=1元
          maxRatio: 0.3 // 最高可抵扣订单金额的30%
        },
        pointsReward: 1, // 1元返1积分
        images: ['fruit_box.jpg'],
        category: 'food',
        type: this.GOODS_TYPES.NORMAL,
        stock: 100,
        status: 1,
        popularity: 95,
        viewCount: 1200,
        likeCount: 85,
        exchangeCount: 0
      },
      {
        id: 2,
        title: '家居清洁套装',
        description: '全面清洁，呵护家居',
        price: 69.00,
        points: 0,
        pointsDiscount: {
          enabled: true,
          ratio: 100,
          maxRatio: 0.3
        },
        pointsReward: 1,
        images: ['cleaning_set.jpg'],
        category: 'household',
        type: this.GOODS_TYPES.NORMAL,
        stock: 50,
        status: 1,
        popularity: 85,
        viewCount: 800,
        likeCount: 60,
        exchangeCount: 0
      },
      {
        id: 3,
        title: '环保购物袋',
        description: '可重复使用的环保购物袋',
        price: 0,
        points: 600,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['eco_bag.jpg'],
        category: 'household',
        type: this.GOODS_TYPES.POINTS,
        stock: 200,
        status: 1,
        popularity: 90,
        viewCount: 1500,
        likeCount: 120,
        exchangeCount: 50
      },
      {
        id: 4,
        title: '小区定制帆布袋',
        description: '小区专属定制，展示社区文化',
        price: 0,
        points: 1200,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['canvas_bag.jpg'],
        category: 'household',
        type: this.GOODS_TYPES.POINTS,
        stock: 100,
        status: 1,
        popularity: 88,
        viewCount: 950,
        likeCount: 75,
        exchangeCount: 30
      },
      {
        id: 5,
        title: '智能门禁卡',
        description: '小区智能门禁系统专用卡',
        price: 0,
        points: 1000,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['access_card.jpg'],
        category: 'service',
        type: this.GOODS_TYPES.POINTS,
        stock: 500,
        status: 1,
        popularity: 95,
        viewCount: 2000,
        likeCount: 150,
        exchangeCount: 100
      },
      {
        id: 6,
        title: '免费洗车服务',
        description: '小区内免费洗车服务一次',
        price: 0,
        points: 1500,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['car_wash.jpg'],
        category: 'service',
        type: this.GOODS_TYPES.POINTS,
        stock: 30,
        status: 1,
        popularity: 98,
        viewCount: 1800,
        likeCount: 160,
        exchangeCount: 25
      },
      {
        id: 7,
        title: '厨房用品套装',
        description: '实用厨房工具，烹饪更便捷',
        price: 129.00,
        points: 2500,
        pointsDiscount: {
          enabled: true,
          ratio: 100,
          maxRatio: 0.3
        },
        pointsReward: 1,
        images: ['kitchen_set.jpg'],
        category: 'household',
        type: this.GOODS_TYPES.NORMAL,
        stock: 40,
        status: 1,
        popularity: 92,
        viewCount: 1100,
        likeCount: 95,
        exchangeCount: 0
      },
      {
        id: 8,
        title: '智能家居产品',
        description: '智能控制，让生活更便捷',
        price: 299.00,
        points: 3000,
        pointsDiscount: {
          enabled: true,
          ratio: 100,
          maxRatio: 0.3
        },
        pointsReward: 1,
        images: ['smart_home.jpg'],
        category: 'digital',
        type: this.GOODS_TYPES.NORMAL,
        stock: 20,
        status: 1,
        popularity: 96,
        viewCount: 1500,
        likeCount: 130,
        exchangeCount: 0
      },
      {
        id: 9,
        title: '物业费9折优惠券',
        description: '可抵扣一次物业费，9折优惠',
        price: 0,
        points: 800,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['coupon.jpg'],
        category: 'coupon',
        type: this.GOODS_TYPES.POINTS,
        stock: 100,
        status: 1,
        popularity: 90,
        viewCount: 1200,
        likeCount: 100,
        exchangeCount: 80
      },
      {
        id: 10,
        title: '5元无门槛优惠券',
        description: '小程序商城通用，无门槛抵扣5元',
        price: 0,
        points: 500,
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['coupon.jpg'],
        category: 'coupon',
        type: this.GOODS_TYPES.POINTS,
        stock: 200,
        status: 1,
        popularity: 92,
        viewCount: 1500,
        likeCount: 120,
        exchangeCount: 150
      },
      // 添加支持积分+现金的商品
      {
        id: 11,
        title: '高级智能手表',
        description: '多功能智能手表，支持心率监测、运动追踪等功能',
        price: 0,
        points: 5000,
        cashPrice: 199.00, // 现金部分
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['smart_watch.jpg'],
        category: 'digital',
        type: this.GOODS_TYPES.POINTS,
        stock: 15,
        status: 1,
        popularity: 98,
        viewCount: 2200,
        likeCount: 180,
        exchangeCount: 10
      },
      {
        id: 12,
        title: '豪华按摩椅体验券',
        description: '小区健身房豪华按摩椅30分钟体验券',
        price: 0,
        points: 2000,
        cashPrice: 49.00, // 现金部分
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['massage_chair.jpg'],
        category: 'service',
        type: this.GOODS_TYPES.POINTS,
        stock: 50,
        status: 1,
        popularity: 96,
        viewCount: 1800,
        likeCount: 150,
        exchangeCount: 20
      },
      {
        id: 13,
        title: '高级咖啡机',
        description: '全自动意式咖啡机，享受在家制作专业咖啡的乐趣',
        price: 0,
        points: 10000,
        cashPrice: 599.00, // 现金部分
        pointsDiscount: {
          enabled: false,
          ratio: 0,
          maxRatio: 0
        },
        pointsReward: 0,
        images: ['coffee_machine.jpg'],
        category: 'household',
        type: this.GOODS_TYPES.POINTS,
        stock: 5,
        status: 1,
        popularity: 99,
        viewCount: 2500,
        likeCount: 200,
        exchangeCount: 3
      }
    ];
  },

  /**
   * 获取商品列表
   * @param {Object} params 查询参数
   * @returns {Promise} 商品列表
   */
  getGoods: function(params = {}) {
    return new Promise((resolve) => {
      // 获取所有商品
      let goods = wx.getStorageSync('goods') || [];

      // 按类型筛选
      if (params.type) {
        goods = goods.filter(item => item.type === params.type);
      }

      // 按分类筛选
      if (params.category) {
        goods = goods.filter(item => item.category === params.category);
      }

      // 按价格范围筛选
      if (params.minPrice !== undefined && params.maxPrice !== undefined) {
        goods = goods.filter(item => item.price >= params.minPrice && item.price <= params.maxPrice);
      }

      // 按积分范围筛选
      if (params.minPoints !== undefined && params.maxPoints !== undefined) {
        goods = goods.filter(item => {
          // 对于普通商品，检查是否支持积分抵扣
          if (item.type === this.GOODS_TYPES.NORMAL) {
            return item.pointsDiscount && item.pointsDiscount.enabled;
          }
          // 对于积分兑换商品，检查积分范围
          else if (item.type === this.GOODS_TYPES.POINTS) {
            return item.points >= params.minPoints && item.points <= params.maxPoints;
          }
          return false;
        });
      }

      // 按关键词搜索
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        goods = goods.filter(item =>
          item.title.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        );
      }

      // 按状态筛选
      if (params.status !== undefined) {
        goods = goods.filter(item => item.status === params.status);
      }

      // 排序
      if (params.sort) {
        switch (params.sort) {
          case 'priceAsc':
            goods.sort((a, b) => a.price - b.price);
            break;
          case 'priceDesc':
            goods.sort((a, b) => b.price - a.price);
            break;
          case 'pointsAsc':
            goods.sort((a, b) => a.points - b.points);
            break;
          case 'pointsDesc':
            goods.sort((a, b) => b.points - a.points);
            break;
          case 'popularity':
            goods.sort((a, b) => b.popularity - a.popularity);
            break;
          default:
            // 默认按ID排序
            goods.sort((a, b) => a.id - b.id);
        }
      }

      // 分页
      let result = goods;
      if (params.page && params.pageSize) {
        const start = (params.page - 1) * params.pageSize;
        const end = start + params.pageSize;
        result = goods.slice(start, end);
      }

      // 对每个商品应用数据规范化处理
      const normalizedResult = result.map(item => this.normalizeGoodsData(item));

      resolve({
        list: normalizedResult,
        total: goods.length,
        pages: params.pageSize ? Math.ceil(goods.length / params.pageSize) : 1
      });
    });
  },

  /**
   * 获取积分商城商品
   * @param {Object} params 查询参数
   * @returns {Promise} 积分商城商品列表
   */
  getPointsGoods: function(params = {}) {
    return this.getGoods({
      ...params,
      type: this.GOODS_TYPES.POINTS
    });
  },

  /**
   * 获取周边好物商品
   * @param {Object} params 查询参数
   * @returns {Promise} 周边好物商品列表
   */
  getNormalGoods: function(params = {}) {
    return this.getGoods({
      ...params,
      type: this.GOODS_TYPES.NORMAL
    });
  },

  /**
   * 获取商品详情
   * @param {Number} goodsId 商品ID
   * @returns {Promise} 商品详情
   */
  getGoodsDetail: function(goodsId) {
    return new Promise((resolve, reject) => {
      // 直接从本地存储获取商品详情
      console.log('从本地存储获取商品详情, ID:', goodsId);
      const goods = wx.getStorageSync('goods') || [];
      const item = goods.find(item => item.id === parseInt(goodsId) || item.goodsId === parseInt(goodsId));

      if (item) {
        // 更新浏览次数
        item.viewCount = (item.viewCount || 0) + 1;
        wx.setStorageSync('goods', goods);

        // 确保图片路径正确
        if (item.images && Array.isArray(item.images)) {
          item.images = item.images.map(img => {
            // 如果图片路径不是URL，使用在线默认图片
            if (!img || img === '' || (!img.startsWith('http') && !img.startsWith('/'))) {
              return 'https://img.freepik.com/free-photo/modern-stationary-collection-arrangement_23-2149309642.jpg';
            }
            return img;
          });
        }

        // 使用统一字段格式
        const normalizedItem = this.normalizeGoodsData(item);

        // 确保积分折扣信息存在且格式正确
        if (normalizedItem.type === this.GOODS_TYPES.NORMAL) {
          if (!normalizedItem.pointsDiscount) {
            normalizedItem.pointsDiscount = {
              enabled: normalizedItem.allowPoint === 1,
              ratio: normalizedItem.pointRate || 100,
              maxRatio: 0.3
            };
          }

          // 确保返积分信息存在
          if (normalizedItem.pointsReward === undefined) {
            normalizedItem.pointsReward = 1; // 默认1元返1积分
          }
        }

        // 确保库存信息存在
        if (normalizedItem.stock === undefined) {
          normalizedItem.stock = 999; // 默认库存
        }

        console.log('获取到规范化商品详情:', normalizedItem);
        resolve(normalizedItem);
      } else {
        console.log('商品不存在，创建默认商品, ID:', goodsId);

        // 创建一个默认商品
        const defaultItem = {
          id: parseInt(goodsId),
          title: '商品' + goodsId,
          name: '商品' + goodsId,
          description: '这是一个自动生成的商品描述，该商品ID为' + goodsId + '。这是一款优质商品，设计精良，品质保证。适合各种场合使用，是您理想的选择。',
          price: 99.00,
          type: this.GOODS_TYPES.NORMAL,
          typeName: '全新',
          stock: 10,
          sold: 0,
          viewCount: 1,
          images: [
            'https://img.freepik.com/free-photo/modern-stationary-collection-arrangement_23-2149309642.jpg',
            'https://img.freepik.com/free-photo/top-view-stationary-arrangement-desk_23-2149309651.jpg'
          ],
          pointsDiscount: {
            enabled: true,
            ratio: 100,
            maxRatio: 0.3
          },
          pointsReward: 1,
          userName: '商城管理员',
          userAvatar: 'https://img.freepik.com/free-photo/portrait-white-man-isolated_53876-40306.jpg',
          favoriteCount: 0,
          likeCount: 0
        };

        // 将新商品添加到本地存储
        goods.push(defaultItem);
        wx.setStorageSync('goods', goods);

        // 使用统一字段格式
        const normalizedItem = this.normalizeGoodsData(defaultItem);

        console.log('创建默认商品成功:', normalizedItem);
        resolve(normalizedItem);
      }
    });
  },

  /**
   * 计算积分抵扣金额
   * @param {Number} goodsId 商品ID
   * @param {Number} points 使用的积分数量
   * @param {Number} quantity 商品数量，默认1
   * @returns {Promise} 抵扣计算结果
   */
  calculatePointsDiscount: function(goodsId, points, quantity = 1) {
    return new Promise((resolve, reject) => {
      this.getGoodsDetail(goodsId).then(goods => {
        // 检查商品是否支持积分抵扣
        if (goods.type !== this.GOODS_TYPES.NORMAL || !goods.pointsDiscount || !goods.pointsDiscount.enabled) {
          reject(new Error('该商品不支持积分抵扣'));
          return;
        }

        // 计算商品总价
        const totalPrice = goods.price * quantity;

        // 获取用户等级信息，以确定积分抵扣比例
        PointsUtil.getPointsDiscountRatio().then(discountInfo => {
          const { pointsRatio, maxDiscountRatio } = discountInfo;

          // 计算积分可抵扣的金额
          let discountAmount = points / pointsRatio;

          // 计算最大可抵扣金额
          const maxDiscountAmount = totalPrice * maxDiscountRatio;

          // 确保抵扣金额不超过最大可抵扣金额
          discountAmount = Math.min(discountAmount, maxDiscountAmount);

          // 确保抵扣金额不超过商品总价
          discountAmount = Math.min(discountAmount, totalPrice);

          // 计算实际使用的积分
          const usedPoints = Math.floor(discountAmount * pointsRatio);

          // 计算抵扣后的实际价格
          const actualPrice = totalPrice - discountAmount;

          resolve({
            totalPrice: totalPrice,
            discountAmount: discountAmount,
            usedPoints: usedPoints,
            actualPrice: actualPrice,
            maxDiscountAmount: maxDiscountAmount,
            maxDiscountRatio: maxDiscountRatio
          });
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 计算购买商品可获得的积分
   * @param {Number} goodsId 商品ID
   * @param {Number} quantity 商品数量，默认1
   * @param {Number} actualPrice 实际支付金额（抵扣后）
   * @returns {Promise} 可获得的积分
   */
  calculateRewardPoints: function(goodsId, quantity = 1, actualPrice = null) {
    return new Promise((resolve, reject) => {
      this.getGoodsDetail(goodsId).then(goods => {
        // 检查商品是否支持返积分
        if (goods.type !== this.GOODS_TYPES.NORMAL || !goods.pointsReward) {
          resolve(0);
          return;
        }

        // 如果提供了实际支付金额，使用实际支付金额计算
        // 否则使用商品原价计算
        const price = actualPrice !== null ? actualPrice : (goods.price * quantity);

        // 计算可获得的积分
        const rewardPoints = Math.floor(price * goods.pointsReward);

        resolve(rewardPoints);
      }).catch(reject);
    });
  },

  /**
   * 使用积分兑换商品
   * @param {Number} goodsId 商品ID
   * @param {Number} quantity 商品数量，默认1
   * @param {Object} address 收货地址信息，可选
   * @returns {Promise} 兑换结果
   */
  exchangeGoods: function(goodsId, quantity = 1, address = null) {
    return new Promise((resolve, reject) => {
      this.getGoodsDetail(goodsId).then(goods => {
        // 检查商品是否为积分兑换商品
        if (goods.type !== this.GOODS_TYPES.POINTS) {
          reject(new Error('该商品不支持积分兑换'));
          return;
        }

        // 检查库存
        if (goods.stock < quantity) {
          reject(new Error('商品库存不足'));
          return;
        }

        // 计算所需积分
        const requiredPoints = goods.points * quantity;

        // 获取用户积分
        PointsUtil.getUserPoints().then(userPoints => {
          // 检查积分是否足够
          if (userPoints < requiredPoints) {
            reject(new Error('积分不足'));
            return;
          }

          // 扣除积分
          PointsUtil.usePoints(requiredPoints, 'exchange', `兑换商品：${goods.title}`).then(() => {
            // 更新商品库存和兑换次数
            goods.stock -= quantity;
            goods.exchangeCount += quantity;

            // 保存商品数据
            const allGoods = wx.getStorageSync('goods') || [];
            const index = allGoods.findIndex(item => item.id === goodsId);
            if (index !== -1) {
              allGoods[index] = goods;
              wx.setStorageSync('goods', allGoods);
            }

            // 创建订单
            const order = {
              id: new Date().getTime(),
              goodsId: goodsId,
              goodsTitle: goods.title,
              goodsImage: goods.images[0],
              quantity: quantity,
              points: requiredPoints,
              price: 0,
              totalAmount: 0,
              address: address,
              status: 1, // 已完成
              createTime: new Date().toISOString(),
              type: 'points' // 积分兑换订单
            };

            // 保存订单
            const orders = wx.getStorageSync('orders') || [];
            orders.push(order);
            wx.setStorageSync('orders', orders);

            resolve({
              success: true,
              order: order
            });
          }).catch(reject);
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 购买商品并使用积分抵扣
   * @param {Number} goodsId 商品ID
   * @param {Number} quantity 商品数量，默认1
   * @param {Number} points 使用的积分数量，默认0
   * @param {Object} address 收货地址信息，可选
   * @returns {Promise} 购买结果
   */
  purchaseGoods: function(goodsId, quantity = 1, points = 0, address = null) {
    return new Promise((resolve, reject) => {
      this.getGoodsDetail(goodsId).then(goods => {
        // 检查商品是否为普通商品或免费商品
        if (goods.type !== this.GOODS_TYPES.NORMAL && goods.type !== this.GOODS_TYPES.FREE) {
          reject(new Error('该商品不支持直接购买'));
          return;
        }

        // 检查库存
        if (goods.stock < quantity) {
          reject(new Error('商品库存不足'));
          return;
        }

        // 计算商品总价
        const totalPrice = goods.price * quantity;

        // 如果使用积分抵扣
        if (points > 0 && goods.pointsDiscount && goods.pointsDiscount.enabled) {
          // 计算积分抵扣金额
          this.calculatePointsDiscount(goodsId, points, quantity).then(discountInfo => {
            const { actualPrice, usedPoints } = discountInfo;

            // 扣除积分
            PointsUtil.usePoints(usedPoints, 'discount', `购买商品抵扣：${goods.title}`).then(() => {
              // 更新商品库存
              goods.stock -= quantity;

              // 保存商品数据
              const allGoods = wx.getStorageSync('goods') || [];
              const index = allGoods.findIndex(item => item.id === goodsId);
              if (index !== -1) {
                allGoods[index] = goods;
                wx.setStorageSync('goods', allGoods);
              }

              // 创建订单
              const order = {
                id: new Date().getTime(),
                goodsId: goodsId,
                goodsTitle: goods.title,
                goodsImage: goods.images[0],
                quantity: quantity,
                points: usedPoints,
                price: goods.price,
                totalAmount: actualPrice,
                address: address,
                status: 1, // 已完成
                createTime: new Date().toISOString(),
                type: 'normal' // 普通订单
              };

              // 保存订单
              const orders = wx.getStorageSync('orders') || [];
              orders.push(order);
              wx.setStorageSync('orders', orders);

              // 计算返还积分
              this.calculateRewardPoints(goodsId, quantity, actualPrice).then(rewardPoints => {
                if (rewardPoints > 0) {
                  // 添加积分
                  PointsUtil.addPoints(rewardPoints, 'purchase', `购买商品返积分：${goods.title}`).then(() => {
                    resolve({
                      success: true,
                      order: order,
                      rewardPoints: rewardPoints
                    });
                  }).catch(err => {
                    // 即使返积分失败，订单仍然成功
                    console.error('返积分失败:', err);
                    resolve({
                      success: true,
                      order: order,
                      rewardPoints: 0
                    });
                  });
                } else {
                  resolve({
                    success: true,
                    order: order,
                    rewardPoints: 0
                  });
                }
              }).catch(err => {
                // 即使计算返积分失败，订单仍然成功
                console.error('计算返积分失败:', err);
                resolve({
                  success: true,
                  order: order,
                  rewardPoints: 0
                });
              });
            }).catch(reject);
          }).catch(reject);
        } else {
          // 不使用积分抵扣
          // 更新商品库存
          goods.stock -= quantity;

          // 保存商品数据
          const allGoods = wx.getStorageSync('goods') || [];
          const index = allGoods.findIndex(item => item.id === goodsId);
          if (index !== -1) {
            allGoods[index] = goods;
            wx.setStorageSync('goods', allGoods);
          }

          // 创建订单
          const order = {
            id: new Date().getTime(),
            goodsId: goodsId,
            goodsTitle: goods.title,
            goodsImage: goods.images[0],
            quantity: quantity,
            points: 0,
            price: goods.price,
            totalAmount: totalPrice,
            address: address,
            status: 1, // 已完成
            createTime: new Date().toISOString(),
            type: 'normal' // 普通订单
          };

          // 保存订单
          const orders = wx.getStorageSync('orders') || [];
          orders.push(order);
          wx.setStorageSync('orders', orders);

          // 计算返还积分
          this.calculateRewardPoints(goodsId, quantity, totalPrice).then(rewardPoints => {
            if (rewardPoints > 0) {
              // 添加积分
              PointsUtil.addPoints(rewardPoints, 'purchase', `购买商品返积分：${goods.title}`).then(() => {
                resolve({
                  success: true,
                  order: order,
                  rewardPoints: rewardPoints
                });
              }).catch(err => {
                // 即使返积分失败，订单仍然成功
                console.error('返积分失败:', err);
                resolve({
                  success: true,
                  order: order,
                  rewardPoints: 0
                });
              });
            } else {
              resolve({
                success: true,
                order: order,
                rewardPoints: 0
              });
            }
          }).catch(err => {
            // 即使计算返积分失败，订单仍然成功
            console.error('计算返积分失败:', err);
            resolve({
              success: true,
              order: order,
              rewardPoints: 0
            });
          });
        }
      }).catch(reject);
    });
  },

  /**
   * 使用积分+现金兑换商品
   * @param {Number} goodsId 商品ID
   * @param {Number} quantity 商品数量，默认1
   * @param {Object} address 收货地址信息，可选
   * @returns {Promise} 兑换结果
   */
  exchangeGoodsWithCash: function(goodsId, quantity = 1, address = null) {
    return new Promise((resolve, reject) => {
      this.getGoodsDetail(goodsId).then(goods => {
        // 检查商品是否支持积分+现金兑换
        if (goods.type !== this.GOODS_TYPES.POINTS || !goods.cashPrice) {
          reject(new Error('该商品不支持积分+现金兑换'));
          return;
        }

        // 检查库存
        if (goods.stock < quantity) {
          reject(new Error('商品库存不足'));
          return;
        }

        // 计算所需积分和现金
        const requiredPoints = goods.points * quantity;
        const requiredCash = goods.cashPrice * quantity;

        // 获取用户积分
        PointsUtil.getUserPoints().then(userPoints => {
          // 检查积分是否足够
          if (userPoints < requiredPoints) {
            reject(new Error('积分不足'));
            return;
          }

          // 扣除积分
          PointsUtil.usePoints(requiredPoints, 'exchange', `兑换商品：${goods.title}`).then(() => {
            // 更新商品库存和兑换次数
            goods.stock -= quantity;
            goods.exchangeCount = (goods.exchangeCount || 0) + quantity;

            // 保存商品数据
            const allGoods = wx.getStorageSync('goods') || [];
            const index = allGoods.findIndex(item => item.id === goodsId);
            if (index !== -1) {
              allGoods[index] = goods;
              wx.setStorageSync('goods', allGoods);
            }

            // 创建订单
            const order = {
              id: new Date().getTime(),
              goodsId: goodsId,
              goodsTitle: goods.title,
              goodsImage: goods.images[0],
              quantity: quantity,
              points: requiredPoints,
              price: requiredCash,
              totalAmount: requiredCash,
              address: address,
              status: 'pending_payment', // 待支付状态
              createTime: new Date().toISOString(),
              type: 'points_cash' // 积分+现金兑换订单
            };

            // 保存订单
            const orders = wx.getStorageSync('orders') || [];
            orders.push(order);
            wx.setStorageSync('orders', orders);

            resolve({
              success: true,
              order: order
            });
          }).catch(reject);
        }).catch(reject);
      }).catch(reject);
    });
  },

  /**
   * 获取订单详情
   * @param {Number} orderId 订单ID
   * @returns {Promise} 订单详情
   */
  getOrderDetail: function(orderId) {
    return new Promise((resolve, reject) => {
      try {
        // 获取所有订单
        const orders = wx.getStorageSync('orders') || [];

        // 查找指定订单
        const order = orders.find(item => item.id == orderId);

        if (order) {
          // 获取商品详情
          this.getGoodsDetail(order.goodsId).then(goods => {
            // 合并订单和商品信息
            const orderDetail = {
              ...order,
              goods: goods
            };

            resolve(orderDetail);
          }).catch(err => {
            // 即使获取商品详情失败，仍然返回订单信息
            console.error('获取商品详情失败:', err);
            resolve(order);
          });
        } else {
          reject(new Error('订单不存在'));
        }
      } catch (err) {
        reject(err);
      }
    });
  },

  /**
   * 创建订单（兼容旧代码）
   * @param {Object} order 订单对象
   * @returns {Promise} 创建结果
   */
  createOrder: function(order) {
    console.log('使用createOrder方法创建订单，建议使用purchaseGoods方法替代');
    return new Promise((resolve, reject) => {
      try {
        // 设置订单状态为待取货，而不是直接完成
        if (!order.status) {
          order.status = 'pending_pickup';
        }

        // 生成二维码内容（简单示例，实际应用中可能需要更复杂的加密）
        if (!order.qrCode) {
          order.qrCode = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAACECAYAAABRRIOnAAAAAklEQVR4AewaftIAAAOPSURBVO3BQY4cSRLAQDLQ//8yV0c/JZCoain2xjYz/mGMyzHG5RjjcoxxtcNDKW9UOlPJTaUzlTdUnjjGuBxjXI4xrnZ4g0puKk9UclPJTSU3ldxU3lDJTeVnFRyU8lNJTeVnFRyUslNJTeVnFRyU8lJ5YljjMsxxuUY42qHH1bJTSU3lZxUclM5qeSJY4zLMcblGONqhy+p5A2V3FRyU8lNJSeVnFRyUjmp5KaSm8o3jjEuxxiXY4yrHR5SeUMlN5WcVHJTyU0lN5WcVHJTyUnlG8cYl2OMyzHG1Q5fUslJJTeVnFRyU8lNJSeVnFRyUjmp5KaSm8o3jjEuxxiXY4yrHR5SeUMlN5WcVHJTyU0lN5WcVHJTyUnlppKbyhPHGJdjjMsxxtUO/2MquankpPLEMcblGONyjHG1w5dUclLJSSU3lZxUclLJTSUnlZxUclLJTSU3lZxUclLJSeWJY4zLMcblGONqhzdUclLJTSU3lZxUclPJTSU3lZxUclPJTSUnlZxUclPJSeUbxxiXY4zLMcbVDg+p5KaSk0puKrmp5KaSm0puKrmp5KaSm0puKrmp5KaSm0puKt84xrgcY1yOMS7HGFc7PKSSm0pOKrmp5KaSk0puKjmp5KaSk0puKrmp5KaSm0puKt84xrgcY1yOMa52+GGVnFRyU8lJJTeVnFRyU8lNJTeVnFRyU8lJ5aSSm0pOKk8cY1yOMS7HGFc7fEklJ5WcVHJTyU0lN5WcVHJTyUklN5WcVHJTyUklN5UnJP5hjMsxxuUY42qHh1TeUMlNJTeVnFRyU8lNJTeVnFRyU8lNJTeVnFRyU8lJ5YljjMsxxuUY42qHN6jkppKbSk4qOankppKbSk4qOankppKbSk4quankpPKNY4zLMcblGONqhy+p5KaSk0puKrmp5KaSk0puKrmp5KaSm0puKrmp5KaSk8oTxxiXY4zLMcbVDj+skptKbio5qeSmkptKTiq5qeSmkpPKTSU3lW8cY1yOMS7HGFc7/LBKbiq5qeSmkptKbiq5qeSmkpNKbiq5qeSmkpPKE8cYl2OMyzHG1Q5fUslNJTeVnFRyU8lNJTeVnFRyUslNJTeVnFRyU8lJ5RvHGJdjjMsxxtUOD6m8oZKbSm4qOankppKbSm4quankpJKbSm4qOak8cYxxOca4HGP8wxiXY4zLMcblGOP6P0l2/EYcFl3pAAAAAElFTkSuQmCC`;
        }

        // 保存订单
        const orders = wx.getStorageSync('orders') || [];
        orders.push(order);
        wx.setStorageSync('orders', orders);

        // 更新商品的待核销订单数量
        const goods = wx.getStorageSync('goods') || [];
        const index = goods.findIndex(item => item.id === order.goodsId);
        if (index !== -1) {
          // 不立即扣减库存，等核销时再扣减
          goods[index].pendingOrders = (goods[index].pendingOrders || 0) + 1;
          wx.setStorageSync('goods', goods);
        }

        resolve(order);
      } catch (err) {
        console.error('创建订单失败:', err);
        reject(err);
      }
    });
  },

  /**
   * 核销订单
   * @param {String} qrCodeContent 二维码内容
   * @returns {Promise} 核销结果
   */
  verifyOrder: function(qrCodeContent) {
    return new Promise((resolve, reject) => {
      try {
        // 获取所有订单
        const orders = wx.getStorageSync('orders') || [];

        // 查找待核销的订单
        const orderIndex = orders.findIndex(item =>
          item.qrCode === qrCodeContent && item.status === 'pending_pickup'
        );

        if (orderIndex === -1) {
          reject(new Error('订单不存在或已核销'));
          return;
        }

        const order = orders[orderIndex];

        // 更新订单状态
        order.status = 'completed';
        order.verifyTime = new Date().toISOString();
        orders[orderIndex] = order;
        wx.setStorageSync('orders', orders);

        // 更新商品库存和待核销数量
        const goods = wx.getStorageSync('goods') || [];
        const goodsIndex = goods.findIndex(item => item.id === order.goodsId);
        if (goodsIndex !== -1) {
          // 核销时扣减库存
          goods[goodsIndex].stock -= order.quantity;
          goods[goodsIndex].sold = (goods[goodsIndex].sold || 0) + order.quantity;
          goods[goodsIndex].pendingOrders = Math.max(0, (goods[goodsIndex].pendingOrders || 0) - 1);
          wx.setStorageSync('goods', goods);
        }

        resolve({
          success: true,
          order: order
        });
      } catch (err) {
        console.error('核销订单失败:', err);
        reject(err);
      }
    });
  },

  /**
   * 切换商品收藏状态
   * @param {Number} goodsId 商品ID
   * @param {Boolean} isFavorite 新的收藏状态
   * @returns {Promise} 更新结果
   */
  toggleFavorite: function(goodsId, isFavorite) {
    return new Promise((resolve, reject) => {
      try {
        const goodsList = wx.getStorageSync('goods') || [];
        const index = goodsList.findIndex(item => item.id === goodsId);
        if (index !== -1) {
          const goods = goodsList[index];
          const currentFavoriteCount = goods.favoriteCount || 0;
          goods.isFavorite = isFavorite;
          goods.favoriteCount = isFavorite ? currentFavoriteCount + 1 : Math.max(0, currentFavoriteCount - 1);
          goodsList[index] = goods;
          wx.setStorageSync('goods', goodsList);
          resolve({ success: true, isFavorite: goods.isFavorite, favoriteCount: goods.favoriteCount });
        } else {
          reject(new Error('商品未找到'));
        }
      } catch (err) {
        console.error('切换收藏状态失败:', err);
        reject(err);
      }
    });
  },

  /**
   * 切换商品点赞状态
   * @param {Number} goodsId 商品ID
   * @param {Boolean} isLike 新的点赞状态
   * @returns {Promise} 更新结果
   */
  toggleLike: function(goodsId, isLike) {
    return new Promise((resolve, reject) => {
      try {
        const goodsList = wx.getStorageSync('goods') || [];
        const index = goodsList.findIndex(item => item.id === goodsId);
        if (index !== -1) {
          const goods = goodsList[index];
          const currentLikeCount = goods.likeCount || 0;
          goods.isLike = isLike;
          goods.likeCount = isLike ? currentLikeCount + 1 : Math.max(0, currentLikeCount - 1);
          goodsList[index] = goods;
          wx.setStorageSync('goods', goodsList);
          resolve({ success: true, isLike: goods.isLike, likeCount: goods.likeCount });
        } else {
          reject(new Error('商品未找到'));
        }
      } catch (err) {
        console.error('切换点赞状态失败:', err);
        reject(err);
      }
    });
  }
};

module.exports = GoodsService;
