<!--员工统计页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 统计内容 -->
  <block wx:if="{{!isLoading}}">
    <!-- 总览卡片 -->
    <view class="stats-card overview-card">
      <view class="card-title">员工总览</view>
      <view class="overview-data">
        <view class="overview-item">
          <text class="overview-value">{{totalStaff}}</text>
          <text class="overview-label">员工总数</text>
        </view>
        <view class="overview-divider"></view>
        <view class="overview-item">
          <text class="overview-value active">{{activeStaff}}</text>
          <text class="overview-label">在职员工</text>
        </view>
        <view class="overview-divider"></view>
        <view class="overview-item">
          <text class="overview-value inactive">{{inactiveStaff}}</text>
          <text class="overview-label">离职员工</text>
        </view>
      </view>
    </view>
    
    <!-- 组织分布 -->
    <view class="stats-card">
      <view class="card-title">组织分布</view>
      <view class="stats-list">
        <view class="stats-item" wx:for="{{organizationData}}" wx:key="name">
          <view class="stats-item-info">
            <text class="stats-item-name">{{item.name}}</text>
            <text class="stats-item-value">{{item.value}}人 ({{item.percentage}})</text>
          </view>
          <view class="stats-progress-bg">
            <view class="stats-progress" style="width: {{item.percentage}}; background-color: {{index % 2 === 0 ? '#ff8c00' : '#ffb74d'}}"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 性别比例 -->
    <view class="stats-card">
      <view class="card-title">性别比例</view>
      <view class="gender-chart">
        <view class="gender-chart-container">
          <view class="gender-chart-male" style="width: {{genderData[0].percentage}}">
            <view class="gender-chart-icon-male"></view>
          </view>
          <view class="gender-chart-female" style="width: {{genderData[1].percentage}}">
            <view class="gender-chart-icon-female"></view>
          </view>
        </view>
        <view class="gender-chart-labels">
          <view class="gender-chart-label">
            <view class="gender-chart-color male"></view>
            <text>男: {{genderData[0].value}}人 ({{genderData[0].percentage}})</text>
          </view>
          <view class="gender-chart-label">
            <view class="gender-chart-color female"></view>
            <text>女: {{genderData[1].value}}人 ({{genderData[1].percentage}})</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 年龄分布 -->
    <view class="stats-card">
      <view class="card-title">年龄分布</view>
      <view class="age-chart">
        <view class="age-chart-bars">
          <view class="age-chart-bar-container" wx:for="{{ageData}}" wx:key="name">
            <view class="age-chart-bar" style="height: {{item.value / totalStaff * 200}}rpx; background-color: {{index % 2 === 0 ? '#ff8c00' : '#ffb74d'}}"></view>
            <text class="age-chart-bar-value">{{item.value}}</text>
            <text class="age-chart-bar-label">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-btn" bindtap="goBack">返回员工列表</view>
  </block>
</view>
