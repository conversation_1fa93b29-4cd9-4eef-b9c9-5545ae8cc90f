/* 废品回收预约页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}



.banner-content {
  text-align: center;
  padding: 20rpx 30rpx 40rpx;
  background: linear-gradient(135deg, #27AE60, #1E8449);
  color: white;
  margin-bottom: 30rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 预约表单 */
.appointment-form {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #27AE60;
  color: white;
}

.step-label {
  font-size: 24rpx;
  color: #999;
  transition: all 0.3s;
}

.step.active .step-label {
  color: #27AE60;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 0 10rpx;
  margin-bottom: 30rpx;
}

/* 表单步骤 */
.form-step {
  margin-bottom: 30rpx;
}

/* 表单组件 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #f44336;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

/* 地址选择模式切换 */
.address-tabs {
  display: flex;
  margin-bottom: 16rpx;
}

.address-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 26rpx;
  color: #666;
  background-color: #f0f0f0;
  transition: all 0.3s;
}

.address-tab:first-child {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}

.address-tab:last-child {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}

.address-tab.active {
  background-color: #27AE60;
  color: white;
  font-weight: 500;
}

.address-picker {
  width: 100%;
  height: 90rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.address-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.address-placeholder {
  font-size: 28rpx;
  color: #999;
}

.address-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-left: 16rpx;
}

.picker-view {
  width: 100%;
  height: 90rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-left: 16rpx;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

/* 废品类型选择 */
.waste-types {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.waste-type {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
}

.waste-type.selected {
  background-color: rgba(39, 174, 96, 0.1);
  border: 1rpx solid #27AE60;
}

.waste-type-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.paper-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.plastic-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 6l3 18h12l3-18H3z'%3E%3C/path%3E%3Cpath d='M7 6V3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v3'%3E%3C/path%3E%3C/svg%3E");
}

.metal-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z'%3E%3C/path%3E%3C/svg%3E");
}

.glass-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 21h8'%3E%3C/path%3E%3Cpath d='M12 21v-4'%3E%3C/path%3E%3Cpath d='M5 3l14 0'%3E%3C/path%3E%3Cpath d='M17 7l-10 0'%3E%3C/path%3E%3Cpath d='M7 7v7.7c0 1.3 1 2.3 2.3 2.3l5.4 0c1.3 0 2.3-1 2.3-2.3l0-7.7'%3E%3C/path%3E%3C/svg%3E");
}

.electronic-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='4' y='4' width='16' height='16' rx='2' ry='2'%3E%3C/rect%3E%3Crect x='9' y='9' width='6' height='6'%3E%3C/rect%3E%3Cline x1='9' y1='1' x2='9' y2='4'%3E%3C/line%3E%3Cline x1='15' y1='1' x2='15' y2='4'%3E%3C/line%3E%3Cline x1='9' y1='20' x2='9' y2='23'%3E%3C/line%3E%3Cline x1='15' y1='20' x2='15' y2='23'%3E%3C/line%3E%3Cline x1='20' y1='9' x2='23' y2='9'%3E%3C/line%3E%3Cline x1='20' y1='14' x2='23' y2='14'%3E%3C/line%3E%3Cline x1='1' y1='9' x2='4' y2='9'%3E%3C/line%3E%3Cline x1='1' y1='14' x2='4' y2='14'%3E%3C/line%3E%3C/svg%3E");
}

.furniture-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 8V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v2'%3E%3C/path%3E%3Cpath d='M4 10v10a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V10'%3E%3C/path%3E%3Cpath d='M12 10v11'%3E%3C/path%3E%3Cpath d='M4 15h16'%3E%3C/path%3E%3C/svg%3E");
}

.waste-type-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 重量滑块 */
.weight-slider {
  padding: 0 10rpx;
}

.weight-value {
  text-align: center;
  font-size: 28rpx;
  color: #27AE60;
  margin-top: 10rpx;
  font-weight: 500;
}

/* 照片上传 */
.photo-uploader {
  margin-top: 10rpx;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.photo-add {
  width: 180rpx;
  height: 180rpx;
  border: 1rpx dashed #ccc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.photo-add-icon {
  font-size: 48rpx;
  color: #ccc;
  line-height: 1;
  margin-bottom: 10rpx;
}

.photo-add-text {
  font-size: 24rpx;
  color: #999;
}

.photo-tip {
  font-size: 24rpx;
  color: #999;
}

/* 按钮 */
.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn-prev {
  width: 200rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.btn-next {
  width: 200rpx;
  height: 80rpx;
  background-color: #27AE60;
  color: white;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.btn-submit {
  width: 200rpx;
  height: 80rpx;
  background-color: #27AE60;
  color: white;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.btn-submit.disabled {
  background-color: #ccc;
  color: #fff;
}

/* 确认信息卡片 */
.confirm-card {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 24rpx;
}

.confirm-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.confirm-section {
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.confirm-section:last-child {
  border-bottom: none;
}

.confirm-section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.confirm-item {
  display: flex;
  margin-bottom: 12rpx;
}

.confirm-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.confirm-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.confirm-photos {
  margin-top: 24rpx;
}

.confirm-photo-list {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.confirm-photo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

/* 协议 */
.agreement {
  margin-top: 24rpx;
}

.agreement-label {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.agreement-link {
  color: #27AE60;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-content {
  width: 80%;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(39, 174, 96, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='48' height='48' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 60rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 24rpx;
}

.success-order {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.success-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.success-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.success-btn.home-btn {
  background-color: #f0f0f0;
  color: #666;
}

.success-btn.view-btn {
  background-color: #27AE60;
  color: white;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .appointment-form {
  background-color: #2c2c2e;
}

.darkMode .form-label {
  color: #f5f5f7;
}

.darkMode .form-input,
.darkMode .address-tab {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .address-tab.active {
  background-color: #27AE60;
  color: #f5f5f7;
}

.darkMode .address-picker,
.darkMode .picker-view,
.darkMode .form-textarea {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .address-placeholder,
.darkMode .picker-placeholder {
  color: #8e8e93;
}

.darkMode .waste-type {
  background-color: #3a3a3c;
}

.darkMode .waste-type.selected {
  background-color: rgba(39, 174, 96, 0.2);
}

.darkMode .waste-type-name {
  color: #f5f5f7;
}

.darkMode .confirm-card {
  background-color: #3a3a3c;
}

.darkMode .confirm-title,
.darkMode .confirm-section-title {
  color: #f5f5f7;
}

.darkMode .confirm-value {
  color: #f5f5f7;
}

.darkMode .confirm-section {
  border-bottom-color: #4a4a4c;
}

.darkMode .btn-prev {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .success-content {
  background-color: #2c2c2e;
}

.darkMode .success-title {
  color: #f5f5f7;
}

.darkMode .success-message {
  color: #8e8e93;
}

.darkMode .success-btn.home-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
}