// mall.js
const app = getApp();
const PointsUtil = require('../../../utils/points');
const PointsService = require('../../../services/points');
const GoodsService = require('../../../services/goods');

Page({
  data: {
    userPoints: 0,
    categories: [
      { id: 'all', name: '全部', icon: 'all' },
      { id: 'voucher', name: '优惠券', icon: 'ticket' },
      { id: 'goods', name: '实物商品', icon: 'gift' },
      { id: 'service', name: '服务', icon: 'service' },
      { id: 'community', name: '周边好物', icon: 'community' }
    ],
    currentCategory: 'all',
    sortOptions: [
      { id: 'default', name: '默认排序' },
      { id: 'points_asc', name: '积分从低到高' },
      { id: 'points_desc', name: '积分从高到低' },
      { id: 'popularity', name: '人气优先' }
    ],
    currentSort: 'default',
    showSortPopup: false,
    products: [],
    filteredProducts: [],
    searchValue: '',
    loading: false
  },

  onLoad: function() {
    try {
      // 初始化积分系统
      PointsService.init();

      // 初始化商品服务
      GoodsService.init();

      // 加载商品数据
      this.loadProducts();

      // 加载用户积分
      this.loadUserPoints();
    } catch (e) {
      console.error('加载积分商城页面失败:', e);
      // 显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  onShow: function() {
    try {
      // 刷新用户积分
      this.loadUserPoints();

      // 检查是否有积分变动事件
      if (app.globalData && app.globalData.pointsChangeEvent) {
        console.log('检测到积分变动事件:', app.globalData.pointsChangeEvent);
        // 更新积分显示
        this.setData({
          userPoints: app.globalData.userPoints || 0
        });
      }
    } catch (e) {
      console.error('刷新积分商城页面失败:', e);
    }
  },

  // 加载商品数据
  loadProducts: function() {
    try {
      this.setData({ loading: true });
      console.log('加载积分商品数据...');

      // 判断是否是周边好物分类
      if (this.data.currentCategory === 'community') {
        // 加载周边好物商品
        this.loadCommunityGoods();
        return;
      }

      // 使用商品服务获取积分商品
      GoodsService.getPointsGoods({
        sort: this.getSortParam(),
        category: this.data.currentCategory === 'all' ? '' : this.data.currentCategory,
        keyword: this.data.searchValue
      }).then(result => {
        console.log('获取积分商品成功:', result);

        const products = result.list.map(item => {
          return {
            id: item.id,
            title: item.title,
            points: item.points,
            cashPrice: item.cashPrice, // 添加现金价格
            image: item.images && item.images.length > 0 ? item.images[0] : 'coupon.jpg',
            category: item.category,
            popularity: item.popularity || 0,
            stock: item.stock || 0,
            description: item.description
          };
        });

        console.log('处理后的商品数据:', products);

        this.setData({
          products: products,
          filteredProducts: products,
          loading: false
        });
      }).catch(err => {
        console.error('获取积分商品失败:', err);
        this.setData({
          loading: false,
          products: [],
          filteredProducts: []
        });

        // 显示错误提示
        wx.showToast({
          title: '获取商品失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    } catch (e) {
      console.error('加载商品数据失败:', e);
      this.setData({
        loading: false,
        products: [],
        filteredProducts: []
      });
    }
  },

  // 加载周边好物商品
  loadCommunityGoods: function() {
    try {
      this.setData({
        loading: true
      });
      
      // 使用商品服务获取支持积分抵扣的普通商品
      // 注意：使用了统一的 getNormalGoods 方法
      GoodsService.getNormalGoods({
        sort: this.getSortParam(),
        keyword: this.data.searchValue
      }).then(result => {
        console.log('获取周边好物成功:', result);
        
        // 筛选出支持积分抵扣的商品
        const supportPointsGoods = result.list.filter(item => 
          item.pointsDiscount && item.pointsDiscount.enabled
        );

        const products = supportPointsGoods.map(item => {
          // 计算可使用的最大积分
          const pointsDiscount = item.pointsDiscount || {};
          const maxRatio = pointsDiscount.maxRatio || 0.3;
          const ratio = pointsDiscount.ratio || 100;
          // 确保价格是数字类型
          const price = typeof item.price === 'string' ? parseFloat(item.price) : item.price;
          // 计算最大可用积分
          const maxPoints = Math.floor(price * maxRatio * ratio);
          // 计算使用最大积分后的价格
          const cashPrice = parseFloat((price - (maxPoints / ratio)).toFixed(2));

          return {
            id: item.id,  // 使用统一字段
            title: item.title || item.name,  // 使用规范化的名称字段
            points: maxPoints,  // 使用的积分数量
            cashPrice: cashPrice,  // 使用最大积分后的价格
            price: price,  // 原价
            image: item.images && item.images.length > 0 ? item.images[0] : 'default.jpg',
            category: 'community',  // 周边好物分类
            popularity: item.popularity || item.viewCount || 0,
            stock: item.stock || 0,
            description: item.description || item.detail || '',
            isGoodsItem: true,  // 标记为周边好物商品
            // 保存原始数据，便于跳转详情页使用
            originalData: item
          };
        });

        console.log('处理后的周边好物数据:', products);

        this.setData({
          products: products,
          filteredProducts: products,
          loading: false
        });
        
        // 将商品信息保存到本地，确保详情页可以获取
        wx.setStorageSync('tempCommunityGoods', products);
      }).catch(err => {
        console.error('获取周边好物失败:', err);
        this.setData({
          loading: false,
          products: [],
          filteredProducts: []
        });

        // 显示错误提示
        wx.showToast({
          title: '获取商品失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    } catch (e) {
      console.error('加载周边好物失败:', e);
      this.setData({
        loading: false,
        products: [],
        filteredProducts: []
      });
    }
  },

  // 获取排序参数
  getSortParam: function() {
    switch (this.data.currentSort) {
      case 'points_asc':
        return 'pointsAsc';
      case 'points_desc':
        return 'pointsDesc';
      case 'popularity':
        return 'popularity';
      default:
        return 'default';
    }
  },

  // 加载用户积分
  loadUserPoints: function() {
    try {
      // 使用积分工具获取用户积分
      PointsUtil.getUserPoints().then(points => {
        this.setData({
          userPoints: points
        });
      }).catch(err => {
        console.error('获取用户积分失败:', err);
        // 设置默认积分
        this.setData({
          userPoints: 0
        });
      });
    } catch (e) {
      console.error('加载用户积分失败:', e);
      // 设置默认积分
      this.setData({
        userPoints: 0
      });
    }
  },

  // 筛选商品
  filterProducts: function() {
    // 重新加载商品数据，应用筛选条件
    this.loadProducts();
  },

  // 切换分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    }, () => {
      this.filterProducts();
    });
  },

  // 显示排序弹窗
  showSortOptions: function() {
    this.setData({
      showSortPopup: true
    });
  },

  // 关闭排序弹窗
  closeSortPopup: function() {
    this.setData({
      showSortPopup: false
    });
  },

  // 选择排序方式
  selectSort: function(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      currentSort: sort,
      showSortPopup: false
    }, () => {
      this.filterProducts();
    });
  },

  // 搜索
  onSearch: function(e) {
    this.setData({
      searchValue: e.detail.value
    }, () => {
      this.filterProducts();
    });
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: ''
    }, () => {
      this.filterProducts();
    });
  },

  // 兑换商品
  exchangeProduct: function(e) {
    const productId = parseInt(e.currentTarget.dataset.id);
    const product = this.data.products.find(p => p.id === productId);

    if (!product) return;

    if (this.data.userPoints < product.points) {
      wx.showToast({
        title: '积分不足',
        icon: 'none'
      });
      return;
    }

    if (product.stock <= 0) {
      wx.showToast({
        title: '商品库存不足',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认兑换',
      content: `确定使用${product.points}积分兑换"${product.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 执行兑换
          this.doExchangeProduct(productId);
        }
      }
    });
  },

  // 执行兑换商品
  doExchangeProduct: function(productId) {
    try {
      wx.showLoading({
        title: '兑换中...',
        mask: true
      });

      // 使用商品服务兑换商品
      GoodsService.exchangeGoods(productId, 1).then(result => {
        wx.hideLoading();

        // 显示兑换成功
        wx.showToast({
          title: '兑换成功',
          icon: 'success',
          duration: 2000
        });

        // 更新商品数据和用户积分
        this.loadProducts();
        this.loadUserPoints();

        // 跳转到订单详情页面
        setTimeout(() => {
          wx.navigateTo({
            url: `/pages/goods/order/order?id=${result.order.id}`
          });
        }, 1500);
      }).catch(err => {
        wx.hideLoading();
        console.error('兑换商品失败:', err);

        // 显示错误信息
        wx.showToast({
          title: err.message || '兑换失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    } catch (e) {
      wx.hideLoading();
      console.error('兑换商品失败:', e);

      // 显示错误信息
      wx.showToast({
        title: '兑换失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 查看商品详情
  viewProductDetail: function(e) {
    const productId = parseInt(e.currentTarget.dataset.id);
    console.log('查看商品详情, productId:', productId);

    // 查找商品
    const product = this.data.products.find(p => p.id === productId);
    
    if (!product) {
      console.error('找不到ID为', productId, '的商品');
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    if (product.isGoodsItem) {
      console.log('跳转到周边好物详情页, 商品数据:', product);
      
      // 直接将完整商品数据保存到专用存储，启用优先加载
      wx.setStorageSync('currentGoodsDetail', product.originalData || product);
      
      // 同时更新goods全局存储
      if (product.originalData) {
        const allGoods = wx.getStorageSync('goods') || [];
        // 查找商品是否已存在
        const existingIndex = allGoods.findIndex(g => g.id === productId);
        
        if (existingIndex >= 0) {
          console.log('商品已存在于本地存储，更新数据');
          allGoods[existingIndex] = product.originalData;
        } else {
          console.log('商品不存在于本地存储，添加数据');
          allGoods.push(product.originalData);
        }
        
        // 保存更新后的商品列表
        wx.setStorageSync('goods', allGoods);
      }
      
      // 跳转到商品详情页
      wx.navigateTo({
        url: `/pages/goods/detail/detail?id=${productId}`
      });
    } else {
      // 其他积分商品，跳转到积分商品详情页
      wx.navigateTo({
        url: `/pages/points/product-detail/product-detail?id=${productId}`
      });
    }
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  },

  // 跳转到好物商城
  navigateToGoodsMall: function() {
    wx.navigateTo({
      url: '/pages/goods/goods'
    });
  },

  // 重置商品数据
  resetGoodsData: function() {
    wx.showModal({
      title: '确认重置',
      content: '这将重置所有商品数据，包括新增积分+现金商品。确定要继续吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 清除商品数据
            wx.removeStorageSync('goods');

            // 清除订单数据
            wx.removeStorageSync('orders');

            // 重新初始化商品服务
            const GoodsService = require('../../../services/goods');
            GoodsService.init();

            // 重新加载积分商品
            this.loadProducts();

            // 刷新用户积分
            this.loadUserPoints();

            wx.showToast({
              title: '商品数据已重置',
              icon: 'success',
              duration: 2000
            });

            // 延迟一下，确保数据已经重置
            setTimeout(() => {
              // 刷新页面
              this.onLoad();
            }, 500);
          } catch (e) {
            console.error('重置商品数据失败:', e);
            wx.showToast({
              title: '重置失败，请重试',
              icon: 'none',
              duration: 2000
            });
          }
        }
      }
    });
  }
})
