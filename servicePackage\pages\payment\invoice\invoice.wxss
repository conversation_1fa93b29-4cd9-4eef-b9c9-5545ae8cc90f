/* pages/payment/invoice/invoice.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}



/* 内容容器 */
.content-container {
  width: 100%;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.2);
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 发票预览区域 */
.invoice-preview-container {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.invoice-scroll-view {
  height: 800rpx;
  width: 100%;
}

.invoice-canvas-container {
  padding: 20rpx;
  display: flex;
  justify-content: center;
}

.invoice-canvas {
  width: 750rpx;
  height: 1000rpx;
  background-color: #fff;
}

/* 发票信息卡片 */
.invoice-info-card {
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #ff8c00;
  border-radius: 4rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.info-value.highlight {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  margin: 0 15rpx;
  border: none;
}

.action-button.save {
  background-color: #f2f2f7;
  color: #333;
}

.action-button.save[disabled] {
  opacity: 0.5;
}

.action-button.share {
  background-color: #ff8c00;
  color: white;
}

.button-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.save-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 21H5C3.9 21 3 20.1 3 19V5C3 3.9 3.9 3 5 3H14L21 10V19C21 20.1 20.1 21 19 21Z' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 3V8H14' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 13H17' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 17H17' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.share-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z' fill='white'/%3E%3C/svg%3E");
}

/* 保存成功提示 */
.save-success-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx 30rpx;
  padding: 20rpx;
  background-color: #f0f9eb;
  border-radius: 8rpx;
  border: 1rpx solid #e1f3d8;
}

.tip-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' fill='%2367C23A' fill-opacity='0.2' stroke='%2367C23A' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M9 12L11 14L15 10' stroke='%2367C23A' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tip-text {
  font-size: 26rpx;
  color: #67c23a;
}

/* 分享选项弹窗 */
.share-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.share-options-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-options-content {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.share-options-modal.show .share-options-content {
  transform: translateY(0);
}

.share-options-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.share-options-list {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border: none;
  padding: 0;
  line-height: 1.2;
}

.share-option::after {
  border: none;
}

.share-option-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
  background-size: 60%;
  background-repeat: no-repeat;
  background-position: center;
}

.share-option-icon.wechat {
  background-color: #07c160;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.5 8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5zm6.5 1.5c.83 0 1.5-.67 1.5-1.5S16.83 7 16 7s-1.5.67-1.5 1.5.67 1.5 1.5 1.5zm1.5 3c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5zm-9 0c0 .83-.67 1.5-1.5 1.5S5 13.83 5 13s.67-1.5 1.5-1.5 1.5.67 1.5 1.5z' fill='white'/%3E%3C/svg%3E");
}

.share-option-icon.save {
  background-color: #ff8c00;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 21H5C3.9 21 3 20.1 3 19V5C3 3.9 3.9 3 5 3H14L21 10V19C21 20.1 20.1 21 19 21Z' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 3V8H14' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 13H17' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 17H17' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.share-option-name {
  font-size: 28rpx;
  color: #333;
}

.share-cancel-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background-color: #f2f2f7;
  color: #333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
  color: #fff;
}

.darkMode .invoice-preview-container,
.darkMode .invoice-info-card,
.darkMode .share-options-content {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .invoice-canvas {
  background-color: #fff; /* 保持发票背景为白色 */
}

.darkMode .card-title,
.darkMode .info-value,
.darkMode .share-options-title,
.darkMode .share-option-name {
  color: #fff;
}

.darkMode .info-label {
  color: #8e8e93;
}

.darkMode .action-button.save {
  background-color: #3a3a3c;
  color: #fff;
}

.darkMode .save-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 21H5C3.9 21 3 20.1 3 19V5C3 3.9 3.9 3 5 3H14L21 10V19C21 20.1 20.1 21 19 21Z' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 3V8H14' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 13H17' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 17H17' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.darkMode .save-success-tip {
  background-color: rgba(103, 194, 58, 0.1);
  border-color: rgba(103, 194, 58, 0.2);
}

.darkMode .share-cancel-button {
  background-color: #3a3a3c;
  color: #fff;
}
