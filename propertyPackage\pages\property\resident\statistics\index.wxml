<view class="resident-stats-container">
  <!-- 标题栏 -->
  <view class="stats-header">
    <view class="stats-title">居民统计分析</view>
    <view class="stats-actions">
      <view class="stats-action" bindtap="exportStatistics">
        <text>导出</text>
      </view>
    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">
      <text>概览</text>
    </view>
    <view class="tab-item {{activeTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">
      <text>趋势分析</text>
    </view>
    <view class="tab-item {{activeTab === 'age' ? 'active' : ''}}" bindtap="switchTab" data-tab="age">
      <text>年龄分布</text>
    </view>
    <view class="tab-item {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">
      <text>房屋分析</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 概览标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'overview' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民类型分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view wx:for="{{statistics.typeData}}" wx:key="nameEn" class="summary-item">
            <text class="summary-value">{{item.value}}</text>
            <text class="summary-label">{{item.name}}</text>
          </view>
        </view>
        <view class="container">
          <view class="echarts">
            <!-- 居民类型分布饼图 -->
            <ec-canvas id="residentTypeChart" canvas-id="residentTypeChart" ec="{{ec}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">性别分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view wx:for="{{statistics.genderData}}" wx:key="nameEn" class="summary-item">
            <text class="summary-value">{{item.value}}</text>
            <text class="summary-label">{{item.name}}</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 性别分布图表 -->
          <view class="gender-chart">
            <view wx:for="{{statistics.genderData}}" wx:key="nameEn" class="gender-bar-container">
              <view class="gender-label">{{item.name}}</view>
              <view class="gender-bar-wrapper">
                <view class="gender-bar {{item.nameEn === 'man' ? 'male' : 'female'}}" style="width: {{item.value * 100 / statistics.genderTotal}}%;"></view>
                <text class="gender-value">{{item.value}}人 ({{item.percent}}%)</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势分析标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民增长趋势</text>
        <text class="card-subtitle">近12个月数据</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.trendCount[11] || 0}}</text>
            <text class="summary-label">本月新增</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.yearlyTotal || 0}}</text>
            <text class="summary-label">年度新增</text>
          </view>
        </view>


        <view class="container" style="height: 500rpx !important;">
          <view class="echarts" style="height: 500rpx !important;">
            <ec-canvas id="trendChart" canvas-id="trendChart" ec="{{ec}}" style="height: 500rpx !important;"></ec-canvas>
          </view>

        </view>

      </view>
    </view>
  </view>

  <!-- 年龄分布标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'age' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民年龄分布</text>
      </view>
      <view class="card-body">
        <view class="container">
          <view class="echarts">
            <!-- 年龄分布图表 -->
            <ec-canvas id="ageChart" canvas-id="ageChart" ec="{{ec}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 房屋分析标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">房屋类型分布</text>
      </view>
      <view class="card-body">
        <view class="container" style="height: 600rpx;">
          <view class="echarts"  style="height: 600rpx;">
            <!-- 房屋类型分布图表 -->
            <ec-canvas id="roomTypeChart" canvas-id="roomTypeChart" ec="{{ec}}"  style="height: 600rpx;"></ec-canvas>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">楼栋居民分布</text>
      </view>
      <view class="card-body">
        <view class="container">
          <view class="echarts">
            <!-- 楼栋分布图表 -->
            <ec-canvas id="buildingChart" canvas-id="buildingChart" ec="{{ec}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>