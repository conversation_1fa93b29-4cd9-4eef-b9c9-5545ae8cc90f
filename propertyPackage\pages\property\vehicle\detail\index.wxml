<!--车辆详情页-->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <block wx:if="{{!isEditing}}">
      <button class="btn-edit" bindtap="enterEditMode">编辑</button>
    </block>
    <block wx:else>
      <button class="btn-cancel" bindtap="cancelEdit">取消</button>
      <button class="btn-save" bindtap="saveEdit" disabled="{{submitting}}">保存</button>
    </block>
  </view>

  <!-- 车辆信息卡片 -->
  <view class="detail-card">
    <view class="card-title">车辆信息</view>
    
    <!-- 非编辑模式 -->
    <block wx:if="{{!isEditing}}">
      <view class="info-list">
        <view class="info-item">
          <view class="info-label">车牌号</view>
          <view class="info-value">{{vehicleData.plateNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">品牌</view>
          <view class="info-value">{{vehicleData.brand}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">型号</view>
          <view class="info-value">{{vehicleData.model}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">颜色</view>
          <view class="info-value">{{vehicleData.color}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">车辆类型</view>
          <view class="info-value">{{vehicleData.type}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">发动机号</view>
          <view class="info-value">{{vehicleData.engineNumber}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">车架号</view>
          <view class="info-value">{{vehicleData.vin}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">注册日期</view>
          <view class="info-value">{{vehicleData.registerDate}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">车位号</view>
          <view class="info-value">{{vehicleData.parkingSpace}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">最后更新</view>
          <view class="info-value">{{vehicleData.lastUpdateTime}}</view>
        </view>
      </view>
    </block>
    
    <!-- 编辑模式 -->
    <block wx:else>
      <view class="info-list">
        <view class="info-item">
          <view class="info-label">车牌号</view>
          <input class="info-input" type="text" value="{{editData.plateNumber}}" data-field="plateNumber" bindinput="onEditInput" placeholder="请输入车牌号" />
        </view>
        <view class="info-item">
          <view class="info-label">品牌</view>
          <input class="info-input" type="text" value="{{editData.brand}}" data-field="brand" bindinput="onEditInput" placeholder="请输入品牌" />
        </view>
        <view class="info-item">
          <view class="info-label">型号</view>
          <input class="info-input" type="text" value="{{editData.model}}" data-field="model" bindinput="onEditInput" placeholder="请输入型号" />
        </view>
        <view class="info-item">
          <view class="info-label">颜色</view>
          <input class="info-input" type="text" value="{{editData.color}}" data-field="color" bindinput="onEditInput" placeholder="请输入颜色" />
        </view>
        <view class="info-item">
          <view class="info-label">车辆类型</view>
          <input class="info-input" type="text" value="{{editData.type}}" data-field="type" bindinput="onEditInput" placeholder="请输入车辆类型" />
        </view>
        <view class="info-item">
          <view class="info-label">发动机号</view>
          <input class="info-input" type="text" value="{{editData.engineNumber}}" data-field="engineNumber" bindinput="onEditInput" placeholder="请输入发动机号" />
        </view>
        <view class="info-item">
          <view class="info-label">车架号</view>
          <input class="info-input" type="text" value="{{editData.vin}}" data-field="vin" bindinput="onEditInput" placeholder="请输入车架号" />
        </view>
        <view class="info-item">
          <view class="info-label">注册日期</view>
          <picker mode="date" value="{{editData.registerDate}}" data-field="registerDate" bindchange="onEditInput">
            <view class="picker-value">{{editData.registerDate || '请选择注册日期'}}</view>
          </picker>
        </view>
        <view class="info-item">
          <view class="info-label">车位号</view>
          <input class="info-input" type="text" value="{{editData.parkingSpace}}" data-field="parkingSpace" bindinput="onEditInput" placeholder="请输入车位号" />
        </view>
      </view>
    </block>
  </view>

  <!-- 车主信息卡片 -->
  <view class="detail-card">
    <view class="card-header">
      <view class="card-title">车主信息</view>
      <view class="card-action" bindtap="showOwnerModal">更换</view>
    </view>
    
    <block wx:if="{{owner}}">
      <view class="owner-info" bindtap="viewOwnerDetail">
        <view class="owner-name">{{owner.name}}</view>
        <view class="owner-details">
          <text>{{owner.phone}}</text>
          <text class="owner-id">{{owner.idNumber}}</text>
        </view>
        <view class="owner-date">关联时间: {{owner.startDate}}</view>
      </view>
    </block>
    
    <block wx:else>
      <view class="empty-state">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无车主信息</view>
      </view>
    </block>
  </view>
  
  <!-- 更换车主弹窗 -->
  <view class="modal {{showOwnerModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideOwnerModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">更换车主</text>
      </view>
      <view class="modal-body">
        <!-- 搜索框 -->
        <view class="search-box">
          <input class="search-input" type="text" placeholder="搜索居民姓名、手机号、身份证" value="{{searchText}}" bindinput="searchResident" />
        </view>
        
        <!-- 搜索结果 -->
        <scroll-view scroll-y="true" class="search-results" wx:if="{{searchResults.length > 0}}">
          <view class="result-item {{selectedOwner && selectedOwner.id === item.id ? 'selected' : ''}}" 
                wx:for="{{searchResults}}" 
                wx:key="id" 
                bindtap="selectOwner" 
                data-id="{{item.id}}">
            <view class="result-name">{{item.name}}</view>
            <view class="result-info">
              <text>{{item.phone}}</text>
              <text class="result-id">{{item.idNumber}}</text>
            </view>
          </view>
        </scroll-view>
        
        <view class="empty-search" wx:elif="{{searchText && searchResults.length === 0}}">
          <text>未找到匹配的居民</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideOwnerModal">取消</button>
        <button class="btn-confirm" bindtap="changeOwner" disabled="{{!selectedOwner || submitting}}">确认</button>
      </view>
    </view>
  </view>
</view>
