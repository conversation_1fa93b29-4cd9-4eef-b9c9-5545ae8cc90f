// pages/renovation/create/create.js
const app = getApp();
const util = require('../../../../utils/util.js');
const dateUtil = require('../../../../utils/dateUtil.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    houses: [],
    houseIndex: 0,
    renovationTypes: [
      { id: 'full', name: '全屋装修' },
      { id: 'partial', name: '局部改造' },
      { id: 'wall', name: '墙体拆改' },
      { id: 'equipment', name: '设备安装' },
      { id: 'other', name: '其他' }
    ],
    typeIndex: 0,
    minDate: dateUtil.formatDate(new Date()),
    formData: {
      house: '',
      houseId: '',
      renovationType: '',
      renovationTypeId: '',
      startDate: '',
      endDate: '',
      contactName: '',
      contactPhone: '',
      constructionCompany: '',
      constructionManager: '',
      managerPhone: ''
    },
    isFormValid: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果是复制已有申请
    if (options.copy) {
      this.loadApplicationData(options.copy);
    } else {
      // 加载用户房屋信息
      this.loadHouses();

      // 预填联系人信息
      this.prefillContactInfo();
    }

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 加载用户房屋信息
   */
  loadHouses() {
    // 模拟加载用户房屋信息
    setTimeout(() => {
      const houses = [
        { id: 'house001', address: '紫荆花园1栋1单元101' },
        { id: 'house002', address: '紫荆花园2栋2单元202' },
        { id: 'house003', address: '紫荆花园3栋2单元1801' }
      ];

      this.setData({
        houses: houses
      });
    }, 500);
  },

  /**
   * 预填联系人信息
   */
  prefillContactInfo() {
    // 模拟从用户信息中获取联系人信息
    setTimeout(() => {
      this.setData({
        'formData.contactName': '张三',
        'formData.contactPhone': '13800138000'
      });

      // 检查表单有效性
      this.checkFormValidity();
    }, 500);
  },

  /**
   * 加载申请数据（用于复制已有申请）
   */
  loadApplicationData(applicationId) {
    // 模拟加载申请数据
    setTimeout(() => {
      const applicationData = {
        house: '紫荆花园3栋2单元1801',
        houseId: 'house003',
        renovationType: '局部改造',
        renovationTypeId: 'partial',
        startDate: '2023-12-15',
        endDate: '2023-12-30',
        contactName: '张三',
        contactPhone: '13800138000',
        constructionCompany: '优质装修公司',
        constructionManager: '李四',
        managerPhone: '13900139000'
      };

      // 设置房屋索引
      const houseIndex = this.data.houses.findIndex(h => h.id === applicationData.houseId);

      // 设置装修类型索引
      const typeIndex = this.data.renovationTypes.findIndex(t => t.id === applicationData.renovationTypeId);

      this.setData({
        formData: applicationData,
        houseIndex: houseIndex > -1 ? houseIndex : 0,
        typeIndex: typeIndex > -1 ? typeIndex : 0
      });

      // 检查表单有效性
      this.checkFormValidity();
    }, 500);
  },

  /**
   * 房屋选择变化
   */
  onHouseChange(e) {
    const index = e.detail.value;
    const house = this.data.houses[index];

    this.setData({
      houseIndex: index,
      'formData.house': house.address,
      'formData.houseId': house.id
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 装修类型选择变化
   */
  onTypeChange(e) {
    const index = e.detail.value;
    const type = this.data.renovationTypes[index];

    this.setData({
      typeIndex: index,
      'formData.renovationType': type.name,
      'formData.renovationTypeId': type.id
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 开始日期选择变化
   */
  onStartDateChange(e) {
    this.setData({
      'formData.startDate': e.detail.value
    });

    // 如果结束日期早于开始日期，清空结束日期
    if (this.data.formData.endDate && this.data.formData.endDate < e.detail.value) {
      this.setData({
        'formData.endDate': ''
      });
    }

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 结束日期选择变化
   */
  onEndDateChange(e) {
    this.setData({
      'formData.endDate': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 联系人姓名输入
   */
  onContactNameInput(e) {
    this.setData({
      'formData.contactName': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 联系电话输入
   */
  onContactPhoneInput(e) {
    this.setData({
      'formData.contactPhone': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 施工单位输入
   */
  onCompanyInput(e) {
    this.setData({
      'formData.constructionCompany': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 施工负责人输入
   */
  onManagerInput(e) {
    this.setData({
      'formData.constructionManager': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 负责人电话输入
   */
  onManagerPhoneInput(e) {
    this.setData({
      'formData.managerPhone': e.detail.value
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 检查表单有效性
   */
  checkFormValidity() {
    const formData = this.data.formData;
    const phoneRegex = /^1[3-9]\d{9}$/;

    // 检查所有必填字段是否已填写
    const isValid =
      formData.house &&
      formData.renovationType &&
      formData.startDate &&
      formData.endDate &&
      formData.contactName &&
      formData.contactPhone && phoneRegex.test(formData.contactPhone) &&
      formData.constructionCompany &&
      formData.constructionManager &&
      formData.managerPhone && phoneRegex.test(formData.managerPhone);

    this.setData({
      isFormValid: isValid
    });
  },

  /**
   * 进入下一步
   */
  goToNextStep() {
    // 检查表单有效性
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    // 保存表单数据到全局或本地存储
    const formData = this.data.formData;
    wx.setStorageSync('renovationFormData', formData);

    // 跳转到下一步
    wx.navigateTo({
      url: '/servicePackage/pages/renovation/plan/plan'
    });
  }
})