<!--AI拍照识别页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <view class="header">
    <view class="title">AI 拍照识别</view>
    <view class="subtitle">拍照自动识别垃圾类别</view>
  </view>

  <!-- 相机组件 -->
  <view class="camera-container" wx:if="{{!imageSrc && !analyzing && !result}}">
    <camera device-position="back" flash="auto" binderror="cameraError" class="camera"></camera>
    <view class="camera-tips">
      <view class="tip-text">将物品放在取景框内，点击拍照按钮</view>
    </view>
    <view class="camera-controls">
      <view class="camera-btn" bindtap="takePhoto">
        <view class="camera-btn-inner"></view>
      </view>
    </view>
  </view>

  <!-- 预览图片 -->
  <view class="preview-container" wx:if="{{imageSrc && !analyzing && !result}}">
    <image class="preview-image" src="{{imageSrc}}" mode="aspectFit"></image>
    <view class="preview-controls">
      <view class="preview-btn cancel-btn" bindtap="cancelPhoto">重拍</view>
      <view class="preview-btn confirm-btn" bindtap="analyzePhoto">识别</view>
    </view>
  </view>

  <!-- 分析中 -->
  <view class="analyzing-container" wx:if="{{analyzing}}">
    <view class="loading-icon"></view>
    <view class="analyzing-text">AI 识别中...</view>
  </view>

  <!-- 识别结果 -->
  <view class="result-container" wx:if="{{result}}">
    <view class="result-header">
      <view class="result-title">识别结果</view>
      <view class="result-subtitle">AI 识别结果仅供参考</view>
    </view>
    
    <view class="result-card">
      <view class="result-image-container">
        <image class="result-image" src="{{imageSrc}}" mode="aspectFit"></image>
      </view>
      
      <view class="result-info">
        <view class="result-item">
          <view class="result-label">物品名称</view>
          <view class="result-value">{{result.name}}</view>
        </view>
        
        <view class="result-item">
          <view class="result-label">垃圾类别</view>
          <view class="result-type" style="background-color: {{result.typeColor}};">
            {{result.typeName}}
          </view>
        </view>
        
        <view class="result-item">
          <view class="result-label">投放建议</view>
          <view class="result-value">{{result.suggestion}}</view>
        </view>
      </view>
    </view>
    
    <view class="result-controls">
      <view class="result-btn again-btn" bindtap="resetCamera">再次识别</view>
      <view class="result-btn detail-btn" bindtap="viewTypeDetail" data-type="{{result.type}}">查看详情</view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{!imageSrc && !analyzing && !result}}">
    <view class="section-title">
      <view class="title-text">历史识别</view>
      <view class="clear-text" bindtap="clearHistory" wx:if="{{historyList.length > 0}}">清空</view>
    </view>
    
    <view class="history-list" wx:if="{{historyList.length > 0}}">
      <view class="history-item" wx:for="{{historyList}}" wx:key="id" bindtap="viewHistoryDetail" data-index="{{index}}">
        <image class="history-image" src="{{item.imageSrc}}" mode="aspectFill"></image>
        <view class="history-info">
          <view class="history-name">{{item.name}}</view>
          <view class="history-type" style="background-color: {{item.typeColor}}10; color: {{item.typeColor}};">
            {{item.typeName}}
          </view>
        </view>
        <view class="history-time">{{item.time}}</view>
      </view>
    </view>
    
    <view class="no-history" wx:else>
      <view class="no-history-icon"></view>
      <view class="no-history-text">暂无识别记录</view>
    </view>
  </view>
</view>
