# 消息中心功能测试文档

## 已完成的功能

### 1. 未读数量统计
- ✅ 添加了 `getUnReadCount` API调用
- ✅ 在页面显示时自动加载未读数量
- ✅ 在tab标签上显示未读数量角标
- ✅ 支持99+的显示格式

### 2. 已读状态标记
- ✅ 添加了 `markSystemMessageRead` API（系统消息）
- ✅ 添加了 `markPrivateMessageRead` API（私信）
- ✅ 保留了 `setNoticeRead` API（通知公告）
- ✅ 点击消息详情时自动标记为已读
- ✅ 已读后更新本地未读数量

### 3. UI改进
- ✅ tab标签显示未读数量角标（红色圆点）
- ✅ 消息列表项显示未读状态指示器（红色小圆点）
- ✅ 支持系统消息、通知公告、私信的未读状态显示

### 4. WebSocket集成
- ✅ 收到新私信时自动更新未读数量
- ✅ 收到新消息时更新底部tabBar的未读数量
- ✅ 新消息自动标记为未读状态

### 5. 数据结构
```javascript
// 未读数量结构
unreadCounts: {
  system_message: 0,        // 系统消息未读数
  notice_announcement: 0,   // 通知公告未读数
  private_message: 0,       // 私信未读数
  total: 0                  // 总未读数
}

// 消息项数据结构
message: {
  id: 'xxx',
  title: 'xxx',
  content: 'xxx',
  time: 'xxx',
  read: false,              // 已读状态
  type: 'xxx'
}
```

## 测试场景

### 场景1：页面加载时的未读数量显示
1. 进入消息中心页面
2. 检查各tab标签是否显示正确的未读数量
3. 检查消息列表中未读消息是否有红点标识

### 场景2：点击消息标记已读
1. 点击未读的系统消息
2. 检查消息是否被标记为已读
3. 检查tab标签的未读数量是否减少
4. 检查底部tabBar的未读数量是否更新

### 场景3：收到新私信
1. 在消息中心页面等待
2. 从其他用户收到新私信
3. 检查私信tab的未读数量是否增加
4. 检查底部tabBar的未读数量是否更新

### 场景4：切换tab时的数据刷新
1. 在系统消息tab查看消息
2. 切换到私信tab
3. 检查未读数量是否正确更新
4. 切换回系统消息tab，检查数据是否保持一致

## API接口

### 获取未读数量
```javascript
messageApi.getUnReadCount()
// 返回: { data: { sysMessageCount, noticeMessageCount, privateMessageCount, totalMessageCount } }
```

### 标记已读
```javascript
// 系统消息
messageApi.markSystemMessageRead(id)

// 私信
messageApi.markPrivateMessageRead(id)

// 通知公告
noticeApi.setNoticeRead(id)
```

## 样式说明

### 未读数量角标
- 位置：tab标签右上角
- 颜色：红色背景，白色文字
- 显示规则：大于0时显示，大于99显示"99+"

### 未读状态指示器
- 位置：消息项右侧
- 样式：红色小圆点
- 显示规则：只在未读时显示

## 注意事项

1. **数据一致性**：确保本地未读数量与服务器数据保持同步
2. **性能优化**：避免频繁调用未读数量API
3. **用户体验**：标记已读操作应该有即时的UI反馈
4. **错误处理**：API调用失败时应该恢复UI状态

## 后续优化建议

1. 添加批量标记已读功能
2. 支持消息推送时的实时未读数量更新
3. 添加消息分类过滤功能
4. 优化大量消息时的性能表现
