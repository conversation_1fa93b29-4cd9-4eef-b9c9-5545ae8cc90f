<!--pages/renovation/commitment/commitment.wxml-->
<view class="container">
  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="progress-bar-container">
      <view class="progress-bar-active" style="right: 0;"></view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">基本信息</view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">施工方案</view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">材料上传</view>
    </view>
    <view class="step step-active">
      <view class="step-circle">4</view>
      <view class="step-label">承诺签署</view>
    </view>
  </view>

  <!-- 承诺书内容 -->
  <view class="commitment-section">
    <view class="section-title">装修承诺书</view>

    <scroll-view class="commitment-content" scroll-y>
      <view class="commitment-header" style="display: flex; flex-direction: column; align-items: center;">
        <view class="commitment-title">业主装修承诺书</view>
        <view class="commitment-subtitle">（请认真阅读并签署）</view>
      </view>

      <view class="commitment-text">
        <view class="paragraph">本人作为{{basicInfo.house}}的业主，现申请进行装修工程，特向物业管理公司做出如下承诺：</view>

        <view class="paragraph-title">一、遵守规定</view>
        <view class="paragraph">1. 本人将严格遵守《物业管理条例》、《住宅室内装饰装修管理办法》等相关法律法规及小区管理规定。</view>
        <view class="paragraph">2. 装修施工时间为：工作日8:00-12:00，14:00-18:00；周末及节假日9:00-12:00，15:00-18:00。严禁在其他时间进行装修作业。</view>
        <view class="paragraph">3. 装修期间不得擅自改动房屋承重结构、主体结构和公共设施设备。</view>

        <view class="paragraph-title">二、安全责任</view>
        <view class="paragraph">1. 本人对装修期间的安全问题负全责，并督促施工人员做好安全防护工作。</view>
        <view class="paragraph">2. 施工人员必须持有效证件进入小区，并遵守小区的各项管理规定。</view>
        <view class="paragraph">3. 装修材料必须当天使用完毕，不得在公共区域堆放。</view>

        <view class="paragraph-title">三、环境保护</view>
        <view class="paragraph">1. 装修垃圾必须装袋，并按物业指定地点堆放，不得随意丢弃。</view>
        <view class="paragraph">2. 装修过程中产生的噪音、粉尘等污染，应采取有效措施控制，避免影响其他业主的正常生活。</view>

        <view class="paragraph-title">四、违约责任</view>
        <view class="paragraph">1. 如违反上述承诺，本人愿意接受物业管理公司的处罚，并承担由此造成的一切后果和责任。</view>
        <view class="paragraph">2. 因装修造成的任何损失或纠纷，由本人负责解决并承担相应的法律责任。</view>

        <view class="paragraph-title">五、其他事项</view>
        <view class="paragraph">1. 本承诺书自签署之日起生效，至装修工程验收合格之日终止。</view>
        <view class="paragraph">2. 本人已详细阅读并理解上述内容，自愿签署本承诺书。</view>
      </view>

      <view class="commitment-footer">
        <view class="signature-row">
          <text>业主签名：</text>
          <text class="signature-name">{{basicInfo.contactName}}</text>
        </view>
        <view class="signature-row">
          <text>日期：</text>
          <text class="signature-date">{{currentDate}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 签名区域 -->
  <view class="signature-section">
    <view class="section-title">手写签名</view>
    <view class="signature-description">请在下方区域手写签名</view>

    <view class="signature-pad-container">
      <canvas
        class="signature-pad"
        canvas-id="signaturePad"
        disable-scroll="true"
        bindtouchstart="handleTouchStart"
        bindtouchmove="handleTouchMove"
        bindtouchend="handleTouchEnd"
        bindtouchcancel="handleTouchEnd">
      </canvas>
      <view class="signature-actions">
        <button class="clear-button" bindtap="clearSignature">清除</button>
      </view>
    </view>
  </view>

  <!-- 同意条款 -->
  <view class="agreement-section">
    <view class="checkbox-item {{isAgreed ? 'checked' : ''}}" bindtap="toggleAgreement">
      <view class="checkbox-icon"></view>
      <text class="agreement-text">我已阅读并同意遵守上述装修承诺书的全部内容</text>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="back-button" bindtap="goToPrevStep">上一步</button>
    <button class="submit-button" bindtap="submitApplication" disabled="{{!isFormValid}}">提交申请</button>
  </view>
</view>