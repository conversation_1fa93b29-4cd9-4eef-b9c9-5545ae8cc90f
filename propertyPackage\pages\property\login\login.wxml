<!--物业管理登录页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <view class="login-header">
    <view class="logo-container">
      <view class="logo"></view>
      <view class="logo-text">智慧物业管理系统</view>
    </view>
  </view>
  
  <view class="login-form">
    <view class="form-title">物业管理登录</view>
    
    <!-- 错误提示 -->
    <view class="error-message" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <!-- 账号输入 -->
    <view class="input-group">
      <view class="input-icon account-icon"></view>
      <input class="form-input" placeholder="请输入账号" bindinput="onAccountInput" value="{{account}}" />
      <view class="input-clear" wx:if="{{account}}" bindtap="clearAccount">×</view>
    </view>
    
    <!-- 密码输入 -->
    <view class="input-group">
      <view class="input-icon password-icon"></view>
      <input class="form-input" placeholder="请输入密码" password="{{!showPassword}}" bindinput="onPasswordInput" value="{{password}}" />
      <view class="input-action" bindtap="togglePasswordVisibility">
        <view class="{{showPassword ? 'icon-eye' : 'icon-eye-off'}}"></view>
      </view>
    </view>
    
    <!-- 忘记密码 -->
    <view class="forgot-password" bindtap="forgotPassword">忘记密码?</view>
    
    <!-- 登录按钮 -->
    <button class="login-btn {{isLoading ? 'loading' : ''}}" bindtap="login" disabled="{{isLoading}}">
      <text wx:if="{{!isLoading}}">登录</text>
      <view class="loading-spinner" wx:else></view>
    </button>
  </view>
  
  <view class="login-footer">
    <view class="footer-text">© 2023 智慧物业管理系统</view>
  </view>
</view>
