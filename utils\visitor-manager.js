// visitor-manager.js
// 访客数据管理类

const VisitorUtils = require('./visitor-utils');

const VisitorManager = {
  /**
   * 获取所有访客记录
   * @returns {Array} 访客记录数组
   */
  getAllVisitors: function() {
    return wx.getStorageSync('visitors') || [];
  },
/**
   * 设置接口获取的访客列表
   * @returns {Array} 访客记录数组
   */
  setAllVisitors: function(visitors) {

    return wx.setStorageSync('visitors', visitors)
  },

  /**
   * 获取指定ID的访客记录
   * @param {string} id 访客ID
   * @returns {Object|null} 访客记录
   */
  getVisitorById: function(id) {
    const visitors = this.getAllVisitors();
    return visitors.find(v => v.id === id) || null;
  },

  /**
   * 保存访客记录
   * @param {Object} visitorData 访客数据
   * @returns {boolean} 是否成功
   */
  saveVisitor: function(visitorData) {
    try {
      // 获取现有访客记录
      let visitors = this.getAllVisitors();

      // 加密敏感信息
      if (visitorData.phone) {
        visitorData.phone = VisitorUtils.encrypt(visitorData.phone);
      }
      if (visitorData.carNumber) {
        visitorData.carNumber = VisitorUtils.encrypt(visitorData.carNumber);
      }

      // 检查是否已存在
      const index = visitors.findIndex(v => v.id === visitorData.id);

      if (index !== -1) {
        // 更新现有记录
        visitors[index] = {
          ...visitors[index],
          ...visitorData,
          lastModified: new Date().toISOString()
        };
      } else {
        // 添加新记录
        visitors.push({
          ...visitorData,
          createTime: new Date().toISOString(),
          lastModified: new Date().toISOString()
        });
      }

      // 保存到本地存储
      wx.setStorageSync('visitors', visitors);

      // 如果有车牌号，保存到历史记录
      if (visitorData.carNumber) {
        this.saveCarNumberToHistory(VisitorUtils.decrypt(visitorData.carNumber));
      }

      return true;
    } catch (error) {
      console.error('保存访客记录失败', error);
      return false;
    }
  },

  /**
   * 删除访客记录
   * @param {string} id 访客ID
   * @returns {boolean} 是否成功
   */
  deleteVisitor: function(id) {
    try {
      // 获取现有访客记录
      let visitors = this.getAllVisitors();

      // 过滤掉要删除的记录
      visitors = visitors.filter(v => v.id !== id);

      // 保存到本地存储
      wx.setStorageSync('visitors', visitors);

      return true;
    } catch (error) {
      console.error('删除访客记录失败', error);
      return false;
    }
  },

  /**
   * 更新访客状态
   * @param {string} id 访客ID
   * @param {string} status 状态
   * @returns {boolean} 是否成功
   */
  updateVisitorStatus: function(id, status) {
    try {
      // 获取现有访客记录
      let visitors = this.getAllVisitors();

      // 查找要更新的记录
      const index = visitors.findIndex(v => v.id === id);

      if (index !== -1) {
        // 更新状态
        visitors[index].status = status;
        visitors[index].lastModified = new Date().toISOString();

        // 保存到本地存储
        wx.setStorageSync('visitors', visitors);

        return true;
      }

      return false;
    } catch (error) {
      console.error('更新访客状态失败', error);
      return false;
    }
  },

  /**
   * 检查访客状态
   * 自动将过期的访客状态更新为已过期
   * @returns {boolean} 是否有更新
   */
  checkVisitorsStatus: function() {
    try {
      // 获取现有访客记录
      let visitors = this.getAllVisitors();
      let updated = false;

      // 当前时间
      const now = new Date();

      // 检查每个访客的状态
      visitors.forEach((visitor, index) => {
        if (visitor.status === 'pending') {

          // 计算过期时间
          const dateUtil = require('./dateUtil');
          const expireDate = dateUtil.parseDateTime(visitor.visitTime, visitor.endTime);

          // 如果已过期，更新状态
          if (now > expireDate) {
            visitors[index].status = 'expired';
            visitors[index].lastModified = new Date().toISOString();
            updated = true;
          }
        }
      });

      // 如果有更新，保存到本地存储
      if (updated) {
        wx.setStorageSync('visitors', visitors);
      }

      return updated;
    } catch (error) {
      console.error('检查访客状态失败', error);
      return false;
    }
  },

  /**
   * 获取今日将到访的访客数量
   * @returns {number} 访客数量
   */
  getTodayVisitorsCount: function() {
    try {
      // 获取现有访客记录
      const visitors = this.getAllVisitors();

      // 当前日期
      const dateUtil = require('./dateUtil');
      const today = dateUtil.formatDate(new Date());

      // 筛选今日将到访的访客
      const todayVisitors = visitors.filter(visitor =>
        visitor.visitTime === today && visitor.status === 'pending'
      );

      return todayVisitors.length;
    } catch (error) {
      console.error('获取今日访客数量失败', error);
      return 0;
    }
  },

  /**
   * 保存车牌号到历史记录
   * @param {string} carNumber 车牌号
   */
  saveCarNumberToHistory: function(carNumber) {
    try {
      // 获取现有历史记录
      let history = wx.getStorageSync('carNumberHistory') || [];

      // 如果已存在，移除旧记录
      history = history.filter(item => item !== carNumber);

      // 添加到历史记录开头
      history.unshift(carNumber);

      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      // 保存到本地存储
      wx.setStorageSync('carNumberHistory', history);
    } catch (error) {
      console.error('保存车牌号历史记录失败', error);
    }
  },

  /**
   * 获取车牌号历史记录
   * @returns {Array} 历史记录数组
   */
  getCarNumberHistory: function() {
    return wx.getStorageSync('carNumberHistory') || [];
  },


  /**
   * 分组访客数据
   * @param {Array} visitors 访客数组
   * @returns {Object} 分组后的访客数据
   */
  groupVisitors: function(visitors) {

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const groups = {
      today: [],
      tomorrow: [],
      week: [],
      earlier: []
    };

    visitors.forEach(visitor => {
      const visitDate = new Date(visitor.visitTime.replace(/-/g, '/'));
      visitDate.setHours(0, 0, 0, 0);

      if (visitDate.getTime() === today.getTime()) {
        groups.today.push(visitor);
      } else if (visitDate.getTime() === tomorrow.getTime()) {
        groups.tomorrow.push(visitor);
      } else if (visitDate > today && visitDate < nextWeek) {
        groups.week.push(visitor);
      } else {
        groups.earlier.push(visitor);
      }
    });

    return groups;
  },

  /**
   * 筛选访客数据
   * @param {string} filter 筛选条件
   * @param {string} keyword 搜索关键词
   * @returns {Array} 筛选后的访客数据
   */
  filterVisitors: function(filter, keyword) {
    // 获取所有访客
    let visitors = this.getAllVisitors();

    // 应用筛选
    if (filter && filter !== 'all') {
      visitors = visitors.filter(visitor => visitor.status === filter);
    }

    // 应用搜索
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      visitors = visitors.filter(visitor =>
        visitor.name.toLowerCase().includes(lowerKeyword) ||
        visitor.phone.includes(lowerKeyword) ||
        (visitor.purpose && visitor.purpose.toLowerCase().includes(lowerKeyword))
      );
    }

    // 按日期排序（降序）
    const dateUtil = require('./dateUtil');
    visitors.sort((a, b) => {
      const dateA = dateUtil.parseDateTime(a.date, a.startTime);
      const dateB = dateUtil.parseDateTime(b.date, b.startTime);
      return dateB - dateA;
    });

    return visitors;
  },

  /**
   * 保存常用访客
   * @param {Object} visitorData 访客数据
   * @returns {boolean} 是否成功
   */
  saveFrequentVisitor: function(visitorData) {
    try {
      // 获取现有常用访客
      let frequentVisitors = this.getFrequentVisitors();

      // 创建常用访客对象
      const frequentVisitor = {
        id: visitorData.id || VisitorUtils.generateVisitorId(),
        name: visitorData.name,
        phone: visitorData.phone,
        purpose: visitorData.purpose || '',
        carNumber: visitorData.carNumber || '',
        createTime: new Date().toISOString()
      };

      // 检查是否已存在（根据手机号判断）
      const index = frequentVisitors.findIndex(v => v.phone === frequentVisitor.phone);

      if (index !== -1) {
        // 更新现有记录
        frequentVisitors[index] = {
          ...frequentVisitors[index],
          ...frequentVisitor,
          lastModified: new Date().toISOString()
        };
      } else {
        // 添加新记录
        frequentVisitors.push(frequentVisitor);
      }

      // 保存到本地存储
      wx.setStorageSync('frequentVisitors', frequentVisitors);

      return true;
    } catch (error) {
      console.error('保存常用访客失败', error);
      return false;
    }
  },

  /**
   * 获取所有常用访客
   * @returns {Array} 常用访客数组
   */
  getFrequentVisitors: function() {
    return wx.getStorageSync('frequentVisitors') || [];
  },

  /**
   * 删除常用访客
   * @param {string} id 常用访客ID
   * @returns {boolean} 是否成功
   */
  deleteFrequentVisitor: function(id) {
    try {
      // 获取现有常用访客
      let frequentVisitors = this.getFrequentVisitors();

      // 过滤掉要删除的记录
      frequentVisitors = frequentVisitors.filter(v => v.id !== id);

      // 保存到本地存储
      wx.setStorageSync('frequentVisitors', frequentVisitors);

      return true;
    } catch (error) {
      console.error('删除常用访客失败', error);
      return false;
    }
  },

  /**
   * 批量保存访客记录
   * @param {Array} visitorDataArray 访客数据数组
   * @returns {Promise<Object>} 保存结果
   */
  saveBatchVisitors: function(visitorDataArray) {
    return new Promise((resolve, reject) => {
      try {
        // 获取现有访客记录
        let visitors = this.getAllVisitors();
        let successCount = 0;
        let resultVisitors = [];

        // 处理每个访客数据
        visitorDataArray.forEach(visitorData => {
          try {
            // 加密敏感信息
            const encryptedData = { ...visitorData };
            if (encryptedData.phone) {
              encryptedData.phone = VisitorUtils.encrypt(encryptedData.phone);
            }
            if (encryptedData.carNumber) {
              encryptedData.carNumber = VisitorUtils.encrypt(encryptedData.carNumber);
            }

            // 添加新记录
            const newVisitor = {
              ...encryptedData,
              createTime: new Date().toISOString(),
              lastModified: new Date().toISOString()
            };

            visitors.push(newVisitor);

            // 如果有车牌号，保存到历史记录
            if (visitorData.carNumber) {
              this.saveCarNumberToHistory(visitorData.carNumber);
            }

            // 记录成功
            successCount++;
            resultVisitors.push({
              id: visitorData.id,
              name: visitorData.name,
              success: true,
              data: visitorData
            });
          } catch (error) {
            console.error('保存单个访客记录失败', error);
            resultVisitors.push({
              name: visitorData.name,
              success: false,
              error: error.message
            });
          }
        });

        // 保存到本地存储
        wx.setStorageSync('visitors', visitors);

        // 返回结果
        resolve({
          success: successCount > 0,
          successCount: successCount,
          totalCount: visitorDataArray.length,
          visitors: resultVisitors
        });
      } catch (error) {
        console.error('批量保存访客记录失败', error);
        reject(error);
      }
    });
  },

  /**
   * 更新访客数据
   * @param {Object} visitorData 访客数据
   * @returns {boolean} 是否成功
   */
  updateVisitor: function(visitorData) {
    try {
      // 获取现有访客记录
      let visitors = this.getAllVisitors();

      // 查找要更新的记录
      const index = visitors.findIndex(v => v.id === visitorData.id);

      if (index !== -1) {
        // 加密敏感信息
        const encryptedData = { ...visitorData };
        if (encryptedData.phone) {
          encryptedData.phone = VisitorUtils.encrypt(encryptedData.phone);
        }
        if (encryptedData.carNumber) {
          encryptedData.carNumber = VisitorUtils.encrypt(encryptedData.carNumber);
        }

        // 更新记录
        visitors[index] = {
          ...visitors[index],
          ...encryptedData,
          lastModified: new Date().toISOString()
        };

        // 保存到本地存储
        wx.setStorageSync('visitors', visitors);

        return true;
      }

      return false;
    } catch (error) {
      console.error('更新访客数据失败', error);
      return false;
    }
  }
};

module.exports = VisitorManager;
