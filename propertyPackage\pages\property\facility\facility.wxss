/* 设施管理主页样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部操作区域预留空间 */
}

/* 顶部区域 */
.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.scan-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-scan {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 3H5C3.89543 3 3 3.89543 3 5V7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M17 3H19C20.1046 3 21 3.89543 21 5V7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 17V19C3 20.1046 3.89543 21 5 21H7' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 17V19C21 20.1046 20.1046 21 19 21H17' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 12H17' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 10rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21L16.65 16.65M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11Z' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 分类标签栏 */
.category-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-tab {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.category-tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 快捷统计 */
.quick-stats {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 设施列表 */
.facility-list {
  padding: 0 30rpx;
}

.facility-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.facility-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设施图标样式 */
.monitor-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 8C2 7.44772 2.44772 7 3 7H21C21.5523 7 22 7.44772 22 8V19C22 19.5523 21.5523 20 21 20H3C2.44772 20 2 19.5523 2 19V8Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 8V8C7 5.23858 9.23858 3 12 3V3C14.7614 3 17 5.23858 17 8V8' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='13' r='3' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.door-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cline x1='9' y1='3' x2='9' y2='21' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.fire-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.5 8.5C14.5 8.5 15 6.5 12 5C12 5 12 2 9.5 1C9.5 1 10 4 8 5C6 6 5 8.5 5 10.5C5 12.5 6 15 9 16.5C12 18 14.5 17 16 15.5C17.5 14 17.5 12 17.5 10.5C17.5 9 16.5 7 14.5 8.5Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 19C12 19 15 17.5 15 15.5' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.light-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 21H15M12 3V4M18.364 5.636L17.657 6.343M21 12H20M4 12H3M6.343 6.343L5.636 5.636M12 18C8.686 18 6 15.314 6 12C6 8.686 8.686 6 12 6C15.314 6 18 8.686 18 12C18 15.314 15.314 18 12 18Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.elevator-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='4' y='4' width='16' height='16' rx='2' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M8 12L12 8L16 12' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M8 16L12 12L16 16' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.water-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C16.4183 22 20 18.4183 20 14C20 9.58172 12 2 12 2C12 2 4 9.58172 4 14C4 18.4183 7.58172 22 12 22Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.hvac-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 12H4M20 12H22M12 2V4M12 20V22M4.93 4.93L6.34 6.34M17.66 17.66L19.07 19.07M19.07 4.93L17.66 6.34M6.34 17.66L4.93 19.07M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.other-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 17H12.01' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: 50rpx 50rpx;
  background-color: rgba(255, 140, 0, 0.1);
}

.facility-info {
  flex: 1;
}

.facility-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.facility-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.facility-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.facility-status.normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.facility-status.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.facility-status.fault {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.facility-status.offline {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9E9E9E;
}

.facility-status.maintenance {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.facility-code {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.facility-location {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.facility-maintenance {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.maintenance-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.facility-actions {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  display: flex;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
}

.repair-btn {
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-more .loading-spinner {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.loading-more text {
  font-size: 28rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more-data {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-data text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80rpx;
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.add-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 5V19M5 12H19' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.task-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11L12 14L22 4M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.action-button text {
  font-size: 24rpx;
  color: #333;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .header {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .page-title {
  color: #f5f5f7;
}

.darkMode .search-input-wrap {
  background-color: #3a3a3c;
}

.darkMode .search-input {
  color: #f5f5f7;
}

.darkMode .category-tabs {
  background-color: #2c2c2e;
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .category-tab {
  color: #8e8e93;
}

.darkMode .quick-stats {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .stat-label {
  color: #8e8e93;
}

.darkMode .facility-card {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .facility-name {
  color: #f5f5f7;
}

.darkMode .facility-code,
.darkMode .facility-location,
.darkMode .facility-maintenance {
  color: #8e8e93;
}

.darkMode .bottom-actions {
  background-color: #2c2c2e;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .action-button text {
  color: #f5f5f7;
}
