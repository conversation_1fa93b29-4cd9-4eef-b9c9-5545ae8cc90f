/**
 * 表单验证工具类
 * 提供常用的表单验证功能
 */

/**
 * 验证规则
 */
const RULES = {
  // 必填
  required: {
    validate: value => {
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
      return value !== null && value !== undefined;
    },
    message: '此项不能为空'
  },
  
  // 最小长度
  minLength: {
    validate: (value, minLength) => {
      if (typeof value !== 'string') {
        return false;
      }
      return value.length >= minLength;
    },
    message: (minLength) => `长度不能少于${minLength}个字符`
  },
  
  // 最大长度
  maxLength: {
    validate: (value, maxLength) => {
      if (typeof value !== 'string') {
        return false;
      }
      return value.length <= maxLength;
    },
    message: (maxLength) => `长度不能超过${maxLength}个字符`
  },
  
  // 手机号码
  mobile: {
    validate: value => {
      if (typeof value !== 'string') {
        return false;
      }
      return /^1[3-9]\d{9}$/.test(value);
    },
    message: '请输入有效的手机号码'
  },
  
  // 电子邮箱
  email: {
    validate: value => {
      if (typeof value !== 'string') {
        return false;
      }
      return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
    },
    message: '请输入有效的电子邮箱'
  },
  
  // 身份证号码
  idCard: {
    validate: value => {
      if (typeof value !== 'string') {
        return false;
      }
      return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value);
    },
    message: '请输入有效的身份证号码'
  },
  
  // 数字
  number: {
    validate: value => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return !isNaN(Number(value));
    },
    message: '请输入有效的数字'
  },
  
  // 整数
  integer: {
    validate: value => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return /^-?\d+$/.test(value);
    },
    message: '请输入有效的整数'
  },
  
  // 正数
  positive: {
    validate: value => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return Number(value) > 0;
    },
    message: '请输入正数'
  },
  
  // 非负数
  nonNegative: {
    validate: value => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return Number(value) >= 0;
    },
    message: '请输入非负数'
  },
  
  // 最小值
  min: {
    validate: (value, min) => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return Number(value) >= min;
    },
    message: (min) => `不能小于${min}`
  },
  
  // 最大值
  max: {
    validate: (value, max) => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return Number(value) <= max;
    },
    message: (max) => `不能大于${max}`
  },
  
  // 自定义正则表达式
  pattern: {
    validate: (value, pattern) => {
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      return pattern.test(value);
    },
    message: '格式不正确'
  }
};

/**
 * 验证表单字段
 * @param {Object} value 字段值
 * @param {Array} rules 验证规则
 * @returns {Object} 验证结果
 */
function validateField(value, rules) {
  for (const rule of rules) {
    let ruleName, ruleValue;
    
    if (typeof rule === 'string') {
      ruleName = rule;
      ruleValue = null;
    } else {
      ruleName = Object.keys(rule)[0];
      ruleValue = rule[ruleName];
    }
    
    const ruleConfig = RULES[ruleName];
    
    if (!ruleConfig) {
      console.warn(`未知的验证规则: ${ruleName}`);
      continue;
    }
    
    const isValid = ruleValue !== null
      ? ruleConfig.validate(value, ruleValue)
      : ruleConfig.validate(value);
    
    if (!isValid) {
      const message = typeof ruleConfig.message === 'function'
        ? ruleConfig.message(ruleValue)
        : ruleConfig.message;
      
      return {
        valid: false,
        message
      };
    }
  }
  
  return {
    valid: true,
    message: ''
  };
}

/**
 * 验证表单
 * @param {Object} form 表单数据
 * @param {Object} schema 表单验证模式
 * @returns {Object} 验证结果
 */
function validateForm(form, schema) {
  const errors = {};
  let isValid = true;
  
  for (const field in schema) {
    const value = form[field];
    const rules = schema[field];
    
    const result = validateField(value, rules);
    
    if (!result.valid) {
      errors[field] = result.message;
      isValid = false;
    }
  }
  
  return {
    valid: isValid,
    errors
  };
}

/**
 * 显示表单错误提示
 * @param {Object} errors 错误信息
 * @param {Function} setError 设置错误的函数
 */
function showFormErrors(errors, setError) {
  for (const field in errors) {
    setError(field, errors[field]);
  }
}

/**
 * 显示第一个错误提示
 * @param {Object} errors 错误信息
 */
function showFirstError(errors) {
  const firstField = Object.keys(errors)[0];
  
  if (firstField) {
    wx.showToast({
      title: errors[firstField],
      icon: 'none',
      duration: 2000
    });
  }
}

// 导出模块
module.exports = {
  validateField,
  validateForm,
  showFormErrors,
  showFirstError,
  RULES
};
