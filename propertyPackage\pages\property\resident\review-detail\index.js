// pages/property/resident/review-detail/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    id: null, // 审核ID
    type: '', // 审核类型：resident_room, resident_vehicle
    reviewData: {}, // 审核数据
    statusOptions: [], // 审核状态选项
    selectedStatus: '', // 选择的审核状态
    showStatusModal: false, // 是否显示状态选择弹窗
    submitting: false, // 是否正在提交
    isLoading: true, // 是否正在加载

    // 字典数据
    residentTypeDict: [], // 居民类型字典
    residentStatusDict: [], // 房产审核状态字典
    vehicleStatusDict: [], // 车辆审核状态字典
  },

  onLoad: function(options) {
    const { id, type, reviewData } = options;

    this.setData({
      id: id,
      type: type
    });
      
    // 设置导航栏标题
    let title = '审核详情';
    switch(type) {
      case 'resident_room':
        title = '房产审核';
        break;
      case 'resident_vehicle':
        title = '车辆审核';
        break;
    }

    wx.setNavigationBarTitle({
      title: title
    });

    // 初始化字典数据
    this.initDictData();

    // 加载审核状态字典
    this.loadStatusOptions();

    // 如果有传入审核数据，直接使用
    if (reviewData) {
      try {
        const data = JSON.parse(decodeURIComponent(reviewData));
        // 处理审核数据，添加字典匹配
        const processedData = this.processReviewData(data);
        this.setData({
          reviewData: processedData,
          isLoading: false
        });
      } catch (error) {
        console.error('解析审核数据失败', error);
      }
    }


  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取字典数据并存储到data中
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;
      const residentStatusDict = util.getDictByNameEn('resident_status')[0].children;
      const vehicleStatusDict = util.getDictByNameEn('vehicle_status')[0].children;
        debugger
      this.setData({
        residentTypeDict: residentTypeDict || [],
        residentStatusDict: residentStatusDict || [],
        vehicleStatusDict: vehicleStatusDict || []
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: residentTypeDict?.length || 0,
        residentStatusDict: residentStatusDict?.length || 0,
        vehicleStatusDict: vehicleStatusDict?.length || 0
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 处理审核数据
  processReviewData: function(data) {
    // 使用已存储的字典数据进行匹配
    const { residentTypeDict, residentStatusDict, vehicleStatusDict } = this.data;
    debugger
    // 匹配居民类型
    let residentTypeName = data.residentType || '';
    if (data.residentType && residentTypeDict.length > 0) {
      const residentTypeItem = residentTypeDict.find(dict => dict.nameEn === data.residentType);
      if (residentTypeItem) {
        residentTypeName = residentTypeItem.nameCn;
      }
    }
      
    // 构建房产全名或车牌号
    let addressText = '';
    if (data.type === 'resident_room') {
      addressText = data.buildingNumber || '';
      if (data.unitNumber) {
        addressText += data.unitNumber;
      }
      addressText += data.roomNumber || '';
    } else if (data.type === 'resident_vehicle') {
      addressText = data.plateNumber || '';
    }

    // 根据审核类型选择对应的状态字典
    let statusDict = [];
    if (data.type === 'resident_room') {
      statusDict = residentStatusDict;
    } else if (data.type === 'resident_vehicle') {
      statusDict = vehicleStatusDict;
    }

    // 匹配状态文本
    let statusText = '待审核'; // 默认状态
    if (data.status && statusDict.length > 0) {
      const statusItem = statusDict.find(dict => dict.nameEn === data.status);
      if (statusItem) {
        statusText = statusItem.nameCn;
      }
    }

    return {
      ...data,
      residentTypeName: residentTypeName,
      typeText: data.type === 'resident_room' ? '房产审核' : '车辆审核',
      addressText: addressText,
      statusText: statusText
    };
  },

  // 加载审核状态选项
  loadStatusOptions: function() {
    const { type, residentStatusDict, vehicleStatusDict } = this.data;
    let statusOptions = [];

    if (type === 'resident_room') {
      statusOptions = residentStatusDict;
    } else if (type === 'resident_vehicle') {
      statusOptions = vehicleStatusDict;
    }

    this.setData({
      statusOptions: statusOptions
    });

    console.log(`${type}状态选项:`, statusOptions);
  },

  // 显示状态选择弹窗
  showStatusSelector: function() {
    this.setData({
      showStatusModal: true
    });
  },

  // 关闭状态选择弹窗
  closeStatusModal: function() {
    this.setData({
      showStatusModal: false
    });
  },

  // 选择审核状态
  selectStatus: function(e) {
    const status = e.currentTarget.dataset.status;
    const statusItem = this.data.statusOptions.find(item => item.nameEn === status);

    this.setData({
      selectedStatus: status,
      selectedStatusName: statusItem ? statusItem.nameCn : status,
      showStatusModal: false
    });
  },

  // 提交审核
  submitReview: function() {
    if (!this.data.selectedStatus) {
      wx.showToast({
        title: '请选择审核状态',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认审核',
      content: `确定要将审核状态设置为"${this.data.selectedStatusName}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.doSubmitReview();
        }
      }
    });
  },

  // 执行审核提交
  doSubmitReview: function() {
    this.setData({ submitting: true });

    const params = {
      id: this.data.id,
      status: this.data.selectedStatus
    };

    const apiMethod = this.data.type === 'resident_room'
      ? propertyApi.examineResidentRoom
      : propertyApi.examineResidentVehicle;

    apiMethod(params)
      .then(res => {
        console.log('审核提交成功', res);

        // 标记需要刷新列表
        this.markNeedRefresh();

        wx.showToast({
          title: '审核成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        console.error('审核提交失败', error);
        wx.showToast({
          title: '审核失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ submitting: false });
      });
  },

  // 加载审核详情（备用方法）
  loadReviewDetail: function() {
    // 如果需要从服务器重新加载详情数据，可以在这里实现
    console.log('加载审核详情');
  },

  // 标记需要刷新列表
  markNeedRefresh: function() {
    const pages = getCurrentPages();
    console.log('当前页面栈长度:', pages.length);

    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];
      console.log('上一个页面路由:', prevPage.route);
      console.log('上一个页面完整路径:', prevPage.__route__);

      // 如果上一个页面是居民管理页面
      if (prevPage.route.includes('resident/resident') || prevPage.__route__.includes('resident/resident')) {
        console.log('匹配到居民管理页面，开始设置needRefresh');
        prevPage.setData({ needRefresh: true });
        console.log('已标记居民管理页面需要刷新');
        console.log('设置后的needRefresh状态:', prevPage.data.needRefresh);
      }
      // 如果上一个页面是信息审核列表页面
      else if (prevPage.route.includes('resident/review-list') || prevPage.__route__.includes('resident/review-list')) {
        console.log('匹配到信息审核列表页面，开始设置needRefresh');
        prevPage.setData({ needRefresh: true });
        console.log('已标记信息审核列表页面需要刷新');
      }
      else {
        console.log('未匹配到需要刷新的页面类型');
      }
    }
  }
});
