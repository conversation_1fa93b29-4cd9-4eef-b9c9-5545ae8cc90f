/**
 * 导航工具类
 * 提供安全的页面导航方法，防止重复点击和webviewId冲突
 */

const navigator = {
  /**
   * 节流标记，防止短时间内重复导航
   */
  _throttleMap: {},

  /**
   * 安全导航到指定页面
   * @param {string} url 导航URL
   * @param {string} key 节流标识符，默认为url
   * @param {number} delay 节流延迟时间，默认300ms
   */
  navigateTo(url, key = url, delay = 300) {
    // 使用节流，防止短时间内多次点击
    if (this._throttleMap[key]) {
      return;
    }
    
    // 设置节流标记
    this._throttleMap[key] = true;
    
    // 延迟后重置节流标记
    setTimeout(() => {
      this._throttleMap[key] = false;
    }, delay);
    
    // 执行导航
    wx.navigateTo({
      url: url,
      fail: (err) => {
        console.error('导航失败:', err);
        
        // 如果是因为页面已存在，尝试使用redirectTo
        if (err.errMsg && err.errMsg.indexOf('webviewId') > -1) {
          wx.redirectTo({
            url: url,
            fail: (redirectErr) => {
              console.error('重定向也失败:', redirectErr);
              
              // 如果重定向也失败，尝试使用switchTab（如果是tabBar页面）
              if (url.indexOf('?') > -1) {
                const baseUrl = url.split('?')[0];
                wx.switchTab({
                  url: baseUrl,
                  fail: (switchErr) => {
                    console.error('switchTab也失败:', switchErr);
                  }
                });
              }
            }
          });
        }
      }
    });
  },

  /**
   * 安全重定向到指定页面
   * @param {string} url 导航URL
   * @param {string} key 节流标识符，默认为url
   * @param {number} delay 节流延迟时间，默认300ms
   */
  redirectTo(url, key = url, delay = 300) {
    // 使用节流，防止短时间内多次点击
    if (this._throttleMap[key]) {
      return;
    }
    
    // 设置节流标记
    this._throttleMap[key] = true;
    
    // 延迟后重置节流标记
    setTimeout(() => {
      this._throttleMap[key] = false;
    }, delay);
    
    // 执行重定向
    wx.redirectTo({
      url: url,
      fail: (err) => {
        console.error('重定向失败:', err);
        
        // 如果重定向失败，尝试使用switchTab（如果是tabBar页面）
        if (url.indexOf('?') > -1) {
          const baseUrl = url.split('?')[0];
          wx.switchTab({
            url: baseUrl,
            fail: (switchErr) => {
              console.error('switchTab也失败:', switchErr);
            }
          });
        }
      }
    });
  },

  /**
   * 清除所有节流标记
   */
  clearThrottles() {
    this._throttleMap = {};
  }
};

module.exports = navigator;
