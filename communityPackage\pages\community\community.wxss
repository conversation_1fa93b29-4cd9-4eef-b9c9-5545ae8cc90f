/* community.wxss */
.container {
  padding: 30rpx;
}

.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.event-list {
  margin-bottom: 40rpx;
}

.event-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
}

.event-image {
  height: 320rpx;
  background-color: #E5E5EA;
  background-size: cover;
  background-position: center;
}

.event-content {
  padding: 32rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #1C1C1E;
}

.event-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.event-info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
}
