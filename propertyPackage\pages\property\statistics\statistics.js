// pages/property/statistics/statistics.js
const dateUtil = require('../../../../utils/dateUtil.js')
const propertyApi = require('../../../../api/propertyApi.js')
const workOrderApi = require('../../../../api/workOrderApi.js')
const util = require('../../../../utils/util.js')
import * as echarts from "../../../../components/ec-canvas/echarts"

Page({
  data: {
    darkMode: false,
    isLoading: true,
    activeTab: 'dashboard', // 当前活动标签页：dashboard, category

    // 页面状态
    pageState: 'loading', // loading, success, error
    errorMsg: '',

    // 仪表盘数据
    dashboardData: {},
    currentDate: '',

    // 分类数据
    currentCategory: 'resident', // 当前选中的分类
    timeLevel: 0, // 时间级别：0今日,1本周,2本月,3本季度,4本年度
    categoryData: {
      resident: {},
      house: {},
      parking: {},
      facility: {},
      workorder: {},
      visitor: {}
    },

    // ECharts配置
    ec: {
      lazyLoad: true
    }
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '数据统计'
    });

    // 页面进入时只加载仪表盘数据
    this.loadDashboardData();
  },

  // 页面显示时触发
  onShow: function() {
    // 页面显示时不自动刷新数据，避免不必要的请求
    // 用户可以通过下拉刷新或切换标签来主动刷新数据
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 根据当前活动标签页刷新对应数据
    if (this.data.activeTab === 'dashboard') {
      // 刷新运营概览数据
      this.loadDashboardData(true);
    } else if (this.data.activeTab === 'category') {
      // 刷新分类快照数据
      this.loadCategoryData(this.data.currentCategory, this.data.timeLevel, true);
    }

    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 加载标签页数据
  loadTabData: function(tab, forceRefresh = false) {
    switch (tab) {
      case 'dashboard':
        this.loadDashboardData(forceRefresh);
        break;
      case 'category':
        this.loadCategoryData(this.data.currentCategory, this.data.timeLevel, forceRefresh);
        break;
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({ activeTab: tab });

      // 根据点击的标签加载对应数据
      if (tab === 'dashboard') {
        // 点击运营概览标签，加载运营概览数据
        this.loadDashboardData();
      } else if (tab === 'category') {
        // 点击分类快照标签，加载分类快照数据
        this.loadCategoryData(this.data.currentCategory, this.data.timeLevel);
      }
    }
  },

  // 重试加载数据
  retryLoad: function() {
    this.setData({
      pageState: 'loading',
      errorMsg: ''
    });

    // 根据当前活动标签重试加载对应数据
    if (this.data.activeTab === 'dashboard') {
      this.loadDashboardData(true);
    } else if (this.data.activeTab === 'category') {
      this.loadCategoryData(this.data.currentCategory, this.data.timeLevel, true);
    }
  },

  // 加载仪表盘数据
  loadDashboardData: function(forceRefresh = false) {
    console.log('开始加载运营概览数据', { forceRefresh });
    this.setData({
      isLoading: true,
      pageState: 'loading'
    });

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区或小区ID不存在');
      this.setData({
        isLoading: false,
        pageState: 'error',
        errorMsg: '请先选择小区'
      });
      return;
    }

    const communityId = selectedCommunity.id;
    const params = {
      timeLevel: this.data.timeLevel,
      communityId: communityId
    };

    // 并行请求所有仪表盘数据
    Promise.all([
      propertyApi.getTodayWorkOrderStatistics(params),
      propertyApi.getTodayVisitorStatistics(params),
      propertyApi.getPersonCount(params)
    ]).then(([workOrderRes, visitorRes, residentRes]) => {
      console.log('API响应数据:', { workOrderRes, visitorRes, residentRes });

      const dashboardData = {
        // 工单数据
        workOrderCount: (workOrderRes && workOrderRes.total) || 0,
        newWorkOrderCount: (workOrderRes && workOrderRes.today) || 0,

        // 访客数据
        visitorCount: (visitorRes && visitorRes.totalVisitors) || 0,
        currentVisitorCount: (visitorRes && visitorRes.todayVisitedCount) || 0,
        todayVisitors: (visitorRes && visitorRes.todayVisitors) || 0,

        // 居民数据
        residentCount: (residentRes && residentRes.total) || 0,
        newResidentCount: (residentRes && residentRes.today) || 0, // 今日新增替代认证率

        // 设施数据暂时不对接
        abnormalFacilityCount: 0,
        monitorOnlineRate: '100%'
      };

      this.setData({
        dashboardData: dashboardData,
        currentDate: dateUtil.formatDate(new Date()),
        isLoading: false,
        pageState: 'success'
      });
    }).catch(error => {
      console.error('加载仪表盘数据失败:', error);
      this.setData({
        isLoading: false,
        pageState: 'error',
        errorMsg: '加载数据失败，请稍后重试'
      });
    });
  },

  // 切换分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ currentCategory: category });
    this.loadCategoryData(category, this.data.timeLevel);
  },

  // 切换时间范围
  switchTimeRange: function(e) {
    const timeLevel = parseInt(e.currentTarget.dataset.level);
    this.setData({ timeLevel: timeLevel });
    this.loadCategoryData(this.data.currentCategory, timeLevel);
  },

  // 加载分类数据
  loadCategoryData: function(category, timeLevel = 0, forceRefresh = false) {
    console.log('开始加载分类快照数据', { category, timeLevel, forceRefresh });
    this.setData({ isLoading: true });

    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      timeLevel: timeLevel,
      communityId: communityId
    };

    let apiPromise;
    switch (category) {
      case 'resident':
        apiPromise = this.loadResidentData(params);
        break;
      case 'house':
        apiPromise = this.loadHouseData(params);
        break;
      case 'parking':
        apiPromise = this.loadParkingData(params);
        break;
      case 'facility':
        // 设施暂时不对接接口
        apiPromise = Promise.resolve({});
        break;
      case 'workorder':
        apiPromise = this.loadWorkOrderData(params);
        break;
      case 'visitor':
        apiPromise = this.loadVisitorData(params);
        break;
      default:
        apiPromise = Promise.resolve({});
    }

    apiPromise.then(data => {
      // 更新对应分类的数据
      const categoryDataKey = `categoryData.${category}`;
      const updateData = {};
      updateData[categoryDataKey] = data;
      updateData.isLoading = false;

      this.setData(updateData, () => {
        // 数据更新后，初始化图表
        setTimeout(() => {
          if (category === 'resident') {
            this.initResidentTypeChart();
          } else if (category === 'workorder') {
            this.initWorkorderTypeChart();
            this.initWorkorderStatusChart();
          } else if (category === 'house') {
            this.initHouseTypeChart();
            this.initHouseBuildingChart();
          } else if (category === 'parking') {
            this.initParkingTypeChart();
            this.initVehicleColorChart();
          } else if (category === 'visitor') {
            this.initVisitorPurposeChart();
            this.initVisitorTimeChart();
          }
        }, 100);
      });
    }).catch(error => {
      console.error(`加载${category}分类数据失败:`, error);
      this.setData({
        isLoading: false,
        pageState: 'error',
        errorMsg: '加载数据失败，请稍后重试'
      });
    });
  },

  // 加载居民数据
  loadResidentData: function(params) {
    return Promise.all([
      propertyApi.getPersonCount(params),
      propertyApi.getResidentStatistics(params)
    ]).then(([countRes, statsRes]) => {
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;
      const typeCount = statsRes.typeCount || {};

      // 格式化类型分布数据
      const typeDistribution = residentTypeDict.map(type => ({
        name: type.nameCn,
        value: typeCount[type.nameEn] || 0
      }));

      return {
        total: countRes.total || 0,
        today: countRes.today || 0,
        typeDistribution: typeDistribution
      };
    });
  },

  // 加载房屋数据
  loadHouseData: function(params) {
    return propertyApi.getRoomDataStatistics(params).then(res => {
      const roomTypeDict = util.getDictByNameEn('room_type')[0].children;
      const roomTypeDistribution = res.roomTypeDistribution || {};
      const buildingRoomCount = res.buildingRoomCount || {};

      // 格式化房屋类型分布
      const typeDistribution = roomTypeDict.map(type => ({
        name: type.nameCn,
        value: roomTypeDistribution[type.nameEn] || 0
      })).filter(item => item.value > 0);

      // 格式化楼栋分布
      const buildingDistribution = Object.keys(buildingRoomCount).map(building => ({
        name: building,
        value: buildingRoomCount[building]
      }));

      return {
        total: res.total || 0,
        occupancyRate: res.occupancyRate || 0,
        typeDistribution: typeDistribution,
        buildingDistribution: buildingDistribution
      };
    });
  },

  // 加载车辆数据
  loadParkingData: function(params) {
    return propertyApi.getVehicleDataStatistics(params).then(res => {
      const parkingTypeCount = res.parkingTypeCount || {};
      const vehicleColorCount = res.vehicleColorCount || {};

      // 格式化车位类型分布
      const parkingTypeDistribution = Object.keys(parkingTypeCount).map(type => ({
        name: type,
        value: parkingTypeCount[type]
      }));

      // 格式化车辆颜色分布
      const vehicleColorDistribution = Object.keys(vehicleColorCount).map(color => ({
        name: color,
        value: vehicleColorCount[color]
      }));

      return {
        parkingTypeDistribution: parkingTypeDistribution,
        vehicleColorDistribution: vehicleColorDistribution
      };
    });
  },

  // 加载工单数据
  loadWorkOrderData: function(params) {
    return Promise.all([
      workOrderApi.getPropertyStatusCount(params),
      workOrderApi.getPropertyTypeCount(params)
    ]).then(([statusRes, typeRes]) => {
      const workOrderTypeDict = util.getDictByNameEn('work_order_type')[0].children;

      // 处理状态统计数据
      const statusData = statusRes || {};
      const total = statusData.total || 0;
      delete statusData.total; // 移除total字段

      // 格式化状态分布
      const statusDistribution = workOrderTypeDict.map(type => ({
        name: type.nameCn,
        value: statusData[type.nameEn] || 0
      }));

      // 处理类型统计数据
      const typeData = typeRes || {};
      delete typeData.total; // 移除total字段

      // 格式化类型分布
      const typeDistribution = workOrderTypeDict.map(type => ({
        name: type.nameCn,
        value: typeData[type.nameEn] || 0
      }));

      return {
        total: total,
        statusDistribution: statusDistribution,
        typeDistribution: typeDistribution
      };
    });
  },

  // 加载访客数据
  loadVisitorData: function(params) {
    return propertyApi.getVisitorStatistics(params).then(res => {
      const data = res || {};
      const purposeDistribution = data.purposeDistribution || {};
      const timeSlotDistribution = data.timeSlotDistribution || {};

      // 格式化访客目的分布
      const purposeData = Object.keys(purposeDistribution).map(purpose => ({
        name: purpose,
        value: purposeDistribution[purpose]
      }));

      // 格式化访客时段分布
      const timeSlotMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
      };
      const timeData = Object.keys(timeSlotDistribution).map(slot => ({
        name: timeSlotMap[slot] || slot,
        value: timeSlotDistribution[slot]
      }));

      return {
        totalVisitors: data.totalVisitors || 0,
        todayVisited: data.todayVisited || 0,
        purposeDistribution: purposeData,
        timeDistribution: timeData
      };
    });
  },

  // 初始化居民类型分布饼图
  initResidentTypeChart: function() {
    try {
      const residentData = this.data.categoryData.resident;
      if (!residentData || !residentData.typeDistribution) {
        console.log('居民数据不完整，无法绘制饼图');
        return;
      }

      const chartData = residentData.typeDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('residentTypeChart', {
        title: {
          text: '居民类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '居民类型',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化居民类型分布饼图失败:', e);
    }
  },

  // ECharts通用初始化方法
  initEChart: function(chartId, option) {
    try {
      const chart = this.selectComponent(`#${chartId}`);
      if (!chart) {
        console.error(`无法找到图表组件: ${chartId}`);
        return;
      }

      chart.init((canvas, width, height, dpr) => {
        const chartInstance = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        chartInstance.setOption(option);
        return chartInstance;
      });
    } catch (e) {
      console.error(`初始化图表${chartId}失败:`, e);
    }
  },

  // ECharts通用初始化方法
  initEChart: function(chartId, option) {
    try {
      const chart = this.selectComponent(`#${chartId}`);
      if (!chart) {
        console.error(`无法找到图表组件: ${chartId}`);
        return;
      }

      chart.init((canvas, width, height, dpr) => {
        const chartInstance = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        chartInstance.setOption(option);
        return chartInstance;
      });
    } catch (e) {
      console.error(`初始化图表${chartId}失败:`, e);
    }
  },

  // 初始化工单类型分布饼图
  initWorkorderTypeChart: function() {
    try {
      const workorderData = this.data.categoryData.workorder;
      if (!workorderData || !workorderData.typeDistribution) {
        console.log('工单类型数据不完整，无法绘制饼图');
        return;
      }

      const chartData = workorderData.typeDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('workorderTypeChart', {
        title: {
          text: '工单类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '工单类型',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化工单类型分布饼图失败:', e);
    }
  },

  // 初始化工单状态分布柱状图
  initWorkorderStatusChart: function() {
    try {
      const workorderData = this.data.categoryData.workorder;
      if (!workorderData || !workorderData.statusDistribution) {
        console.log('工单状态数据不完整，无法绘制柱状图');
        return;
      }

      const categories = workorderData.statusDistribution.map(item => item.name);
      const values = workorderData.statusDistribution.map(item => item.value);

      this.initEChart('workorderStatusChart', {
        title: {
          text: '工单状态分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '数量'
        },
        series: [{
          name: '工单数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: function(params) {
              const colors = ['#f97316', '#2563eb', '#059669'];
              return colors[params.dataIndex % colors.length];
            }
          },
          barWidth: '60%'
        }]
      });
    } catch (e) {
      console.error('初始化工单状态分布柱状图失败:', e);
    }
  },

  // 初始化房屋类型分布饼图
  initHouseTypeChart: function() {
    try {
      const houseData = this.data.categoryData.house;
      if (!houseData || !houseData.typeDistribution) {
        console.log('房屋类型数据不完整，无法绘制饼图');
        return;
      }

      const chartData = houseData.typeDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('houseTypeChart', {
        title: {
          text: '房屋类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '房屋类型',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化房屋类型分布饼图失败:', e);
    }
  },

  // 初始化楼栋分布柱状图
  initHouseBuildingChart: function() {
    try {
      const houseData = this.data.categoryData.house;
      if (!houseData || !houseData.buildingDistribution) {
        console.log('楼栋分布数据不完整，无法绘制柱状图');
        return;
      }

      const categories = houseData.buildingDistribution.map(item => item.name);
      const values = houseData.buildingDistribution.map(item => item.value);

      this.initEChart('houseBuildingChart', {
        title: {
          text: '楼栋房屋分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '房屋数量'
        },
        series: [{
          name: '房屋数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: function(params) {
              const colors = ['#4338ca', '#3b82f6', '#60a5fa', '#93c5fd'];
              return colors[params.dataIndex % colors.length];
            }
          },
          barWidth: '60%'
        }]
      });
    } catch (e) {
      console.error('初始化楼栋分布柱状图失败:', e);
    }
  },

  // 初始化车位类型分布饼图
  initParkingTypeChart: function() {
    try {
      const parkingData = this.data.categoryData.parking;
      if (!parkingData || !parkingData.parkingTypeDistribution) {
        console.log('车位类型数据不完整，无法绘制饼图');
        return;
      }

      const chartData = parkingData.parkingTypeDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('parkingTypeChart', {
        title: {
          text: '车位类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '车位类型',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化车位类型分布饼图失败:', e);
    }
  },

  // 初始化车辆颜色分布饼图
  initVehicleColorChart: function() {
    try {
      const parkingData = this.data.categoryData.parking;
      if (!parkingData || !parkingData.vehicleColorDistribution) {
        console.log('车辆颜色数据不完整，无法绘制饼图');
        return;
      }

      const chartData = parkingData.vehicleColorDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('vehicleColorChart', {
        title: {
          text: '车辆颜色分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '车辆颜色',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6', '#60a5fa'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化车辆颜色分布饼图失败:', e);
    }
  },

  // 初始化访客目的分布饼图
  initVisitorPurposeChart: function() {
    try {
      const visitorData = this.data.categoryData.visitor;
      if (!visitorData || !visitorData.purposeDistribution) {
        console.log('访客目的数据不完整，无法绘制饼图');
        return;
      }

      const chartData = visitorData.purposeDistribution.map(item => ({
        name: item.name,
        value: item.value
      }));

      this.initEChart('visitorPurposeChart', {
        title: {
          text: '访客目的分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} {b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [{
          name: '访客目的',
          type: 'pie',
          radius: '50%', // 缩小饼图
          center: ['50%', '35%'], // 将饼图向上移动
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function(params) {
              const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }]
      });
    } catch (e) {
      console.error('初始化访客目的分布饼图失败:', e);
    }
  },

  // 初始化访客时段分布柱状图
  initVisitorTimeChart: function() {
    try {
      const visitorData = this.data.categoryData.visitor;
      if (!visitorData || !visitorData.timeDistribution) {
        console.log('访客时段数据不完整，无法绘制柱状图');
        return;
      }

      const categories = visitorData.timeDistribution.map(item => item.name);
      const values = visitorData.timeDistribution.map(item => item.value);

      this.initEChart('visitorTimeChart', {
        title: {
          text: '访客时段分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '访客数量'
        },
        series: [{
          name: '访客数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: function(params) {
              const colors = ['#1e40af', '#3b82f6', '#60a5fa'];
              return colors[params.dataIndex % colors.length];
            }
          },
          barWidth: '60%'
        }]
      });
    } catch (e) {
      console.error('初始化访客时段分布柱状图失败:', e);
    }
  },

  // 获取分类标题
  getCategoryTitle: function() {
    const titles = {
      resident: '居民统计',
      house: '房屋统计',
      parking: '车位统计',
      facility: '设施统计',
      workorder: '工单统计',
      visitor: '访客统计'
    };
    return titles[this.data.currentCategory] || '分类统计';
  },

  // 导航到工单页面
  navigateToWorkOrder: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/workorder/workorder'
    });
  },

  // 导航到访客页面
  navigateToVisitor: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/visitor-stats/index'
    });
  },

  // 导航到设施页面
  navigateToFacility: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/facility/facility'
    });
  },

  // 导航到居民页面
  navigateToResident: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/resident'
    });
  },

  // 初始化设施类型分布饼图
  initFacilityTypeChart: function() {
    try {
      const facilityData = this.data.categoryData.facility;
      if (!facilityData || !facilityData.typeDistribution || facilityData.typeDistribution.length === 0) {
        console.log('设施类型数据不完整，无法绘制饼图');
        return;
      }

      // 准备饼图数据
      const pieData = facilityData.typeDistribution.map(item => {
        return {
          name: item.type,
          data: item.count
        };
      });

      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 计算饼图尺寸 (将rpx转为px)
      const canvasSize = 280 * systemInfo.windowWidth / 750;

      // 创建饼图
      const ctx = wx.createCanvasContext('facilityTypeChart');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      // 创建图表
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 确保颜色与图例一致
        const colors = ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe'];

        wxCharts.create({
          type: 'pie',
          canvas: ctx,
          series: pieData,
          width: canvasSize,
          height: canvasSize,
          padding: 20,
          colors: colors,
          showLegend: false // 不显示图例，因为我们已经有自定义图例
        });

        // 绘制到画布
        ctx.draw();
      } catch (chartError) {
        console.error('创建设施类型分布图表失败:', chartError);
      }
    } catch (e) {
      console.error('初始化设施类型分布饼图失败:', e);
    }
  },



 

  // 获取分类标题
  getCategoryTitle: function() {
    const titles = {
      resident: '居民统计',
      house: '房屋统计',
      parking: '车位统计',
      facility: '设施统计',
      workorder: '工单统计',
      visitor: '访客统计'
    };
    return titles[this.data.currentCategory] || '分类统计';
  },

  // 导航到工单页面
  navigateToWorkOrder: function() {
    wx.navigateTo({
      url: '/pages/property/workorder/workorder'
    });
  },

  // 导航到访客页面
  navigateToVisitor: function() {
    wx.navigateTo({
      url: '/pages/property/visitor-stats/index'
    });
  },

  // 导航到设施页面
  navigateToFacility: function() {
    wx.navigateTo({
      url: '/pages/property/facility/facility'
    });
  },

  // 导航到居民页面
  navigateToResident: function() {
    wx.navigateTo({
      url: '/pages/property/resident/resident'
    });
  }
});
