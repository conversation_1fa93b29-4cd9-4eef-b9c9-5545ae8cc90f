<!-- pages/workorder/evaluate/index.wxml -->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">评价工单</view>
    <view class="nav-action"></view>
  </view>

  <!-- 工单基本信息 -->
  <view class="order-info-card" wx:if="{{orderInfo}}">
    <view class="order-title">{{orderInfo.content.title}}</view>
    <view class="order-meta">
      <view class="order-type">{{orderInfo.typeName}}</view>
      <view class="order-id">{{orderInfo.id}}</view>
    </view>
  </view>

  <!-- 评价表单 -->
  <view class="evaluate-form">
    <!-- 星级评分 -->
    <view class="rating-section">
      <view class="section-title">服务评分</view>
      <view class="rating-stars">
        <view
          wx:for="{{5}}"
          wx:key="index"
          class="star-item {{index < rating ? 'active' : ''}}"
          bindtap="setRating"
          data-rating="{{index + 1}}"
        >
          <image
            src="/images/icons/{{index < rating ? 'star-filled' : 'star-empty'}}.svg"
            class="star-icon"
          />
        </view>
      </view>
      <view class="rating-text">{{ratingTexts[rating - 1]}}</view>
    </view>

    <!-- 评价内容 -->
    <view class="comment-section">
      <view class="section-title">评价内容</view>
      <view class="textarea-wrapper">
        <textarea
          class="comment-textarea"
          placeholder="请输入您的评价内容..."
          value="{{comment}}"
          bindinput="inputComment"
          maxlength="200"
        />
        <view class="textarea-counter">{{comment.length}}/200</view>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="upload-section">
      <view class="section-title">上传图片</view>
      <view class="image-list">
        <view class="upload-button" bindtap="chooseImage" wx:if="{{images.length < 3}}">
          <view class="upload-icon">+</view>
          <text class="upload-text">上传图片</text>
        </view>

        <view class="image-item" wx:for="{{images}}" wx:key="index">
          <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
          <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}">×</view>
        </view>
      </view>
      <view class="upload-tip">最多上传3张图片</view>
    </view>

    <!-- 匿名评价 -->
    <view class="anonymous-section">
      <view class="anonymous-switch">
        <switch checked="{{isAnonymous}}" bindchange="toggleAnonymous" color="#FF8C00" />
        <text class="anonymous-text">匿名评价</text>
      </view>
      <view class="anonymous-tip">开启后，您的个人信息将不会显示在评价中</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-btn-wrapper">
    <button class="submit-btn" bindtap="submitEvaluation" loading="{{submitting}}">提交评价</button>
  </view>
</view>
