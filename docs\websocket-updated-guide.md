# WebSocket更新版使用指南

## 概述

WebSocket管理器已根据新的消息协议进行了更新，支持标准的私聊消息发送和心跳机制。

## 新的消息格式

### 1. 发送私聊消息

**发送格式：**
```javascript
{
  "topic": "send_private_message",
  "message": {
    "type": "text",        // text文本消息 或 image图片消息
    "content": "Hello!",   // text就是文本，image类型就是图片路径
    "receiverId": "100000" // 接收者的ID
  }
}
```

**响应格式：**
```javascript
{
  "topic": "send_private_message_response",
  "message": "12345"  // 响应ID，大于0表示发送成功
}
```

### 2. 心跳机制

**发送心跳：**
```javascript
{
  "topic": "ping",
  "message": {}
}
```

**心跳响应：**
```javascript
{
  "topic": "pong",
  "message": {}
}
```

## 使用方法

### 基本消息发送

```javascript
// 在页面中发送消息
const app = getApp();

// 发送文本消息
app.sendPrivateMessage('text', 'Hello World!', 'receiverId');

// 发送图片消息
app.sendPrivateMessage('image', '/path/to/image.jpg', 'receiverId');

// 检查连接状态
if (app.isWebSocketConnected()) {
  console.log('WebSocket已连接');
}
```

### 监听消息事件

```javascript
Page({
  onLoad: function() {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    
    // 监听接收到的私聊消息
    wsManager.on('privateMessage', (message) => {
      console.log('收到私聊消息:', message);
      // message.type: 'text' 或 'image'
      // message.content: 消息内容
      // message.receiverId: 接收者ID
    });
    
    // 监听消息发送响应
    wsManager.on('messageResponse', (response) => {
      if (response.success) {
        console.log('消息发送成功:', response.responseId);
      } else {
        console.log('消息发送失败:', response.responseId);
      }
    });
  },
  
  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function(topic, data) {
    switch (topic) {
      case 'privateMessage':
        // 处理接收到的私聊消息
        this.handlePrivateMessage(data);
        break;
      case 'messageResponse':
        // 处理消息发送响应
        this.handleMessageResponse(data);
        break;
    }
  },
  
  handlePrivateMessage: function(message) {
    // 显示收到的消息
    console.log('收到消息:', message.content);
  },
  
  handleMessageResponse: function(response) {
    if (response.success) {
      wx.showToast({
        title: '发送成功',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '发送失败',
        icon: 'error'
      });
    }
  }
});
```

### 高级用法

```javascript
const app = getApp();
const wsManager = app.getWebSocketManager();

// 直接发送自定义消息
wsManager.sendMessage({
  topic: 'send_private_message',
  message: {
    type: 'text',
    content: 'Custom message',
    receiverId: 'user123'
  }
});

// 监听连接状态变化
wsManager.on('open', () => {
  console.log('WebSocket连接已建立');
});

wsManager.on('close', () => {
  console.log('WebSocket连接已关闭');
});

// 获取详细状态
const stats = wsManager.getStats();
console.log('连接统计:', stats);
```

## 主要变化

### 1. 消息格式更新
- 消息结构从 `{topic, data}` 改为 `{topic, message}`
- 私聊消息不再包含 `senderId`，由服务器处理
- 响应格式标准化

### 2. 心跳机制优化
- 心跳主题从 `heartbeat` 改为 `ping`
- 心跳响应从 `heartbeat_response` 改为 `pong`
- 简化心跳消息内容

### 3. 功能简化
- 移除了查询消息历史功能
- 专注于实时消息发送和接收
- 优化了消息响应处理

### 4. 事件系统增强
- 新增 `privateMessage` 事件：接收到私聊消息时触发
- 新增 `messageResponse` 事件：消息发送响应时触发
- 保留原有的连接状态事件

## 测试页面

测试页面已更新到 `pages/ws-test/`，提供以下功能：

1. **连接状态监控**：实时显示WebSocket连接状态
2. **消息发送测试**：支持发送文本消息到指定接收者
3. **消息接收显示**：显示接收到的消息和发送响应
4. **心跳测试**：手动发送心跳包测试
5. **连接统计**：查看详细的连接统计信息

### 使用测试页面

1. 在 `app.json` 中添加页面路径：
```json
{
  "pages": [
    "pages/ws-test/websocket-test"
  ]
}
```

2. 访问测试页面进行功能验证

## 配置说明

WebSocket服务器地址：`ws://10.37.13.5:8080/websocket/` + access_token

如需修改配置，请编辑 `utils/websocket-config.js` 文件。

## 注意事项

1. **消息发送**：确保在连接建立后发送消息
2. **响应处理**：监听 `messageResponse` 事件获取发送结果
3. **错误处理**：连接异常时消息会自动加入队列，连接恢复后重发
4. **心跳机制**：自动维护，无需手动处理
5. **Token管理**：自动检测token状态，无token时进入等待模式

## 故障排查

1. **消息发送失败**：检查连接状态和接收者ID
2. **连接异常**：检查token是否有效
3. **心跳超时**：检查网络状态和服务器连接
4. **事件未触发**：确保正确设置事件监听器

这个更新版本完全符合新的消息协议，提供了更简洁和高效的WebSocket通信功能。
