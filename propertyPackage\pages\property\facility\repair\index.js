// pages/property/facility/repair/index.js
const util = require('../../../../../utils/util.js')
const dateUtil = require('../../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,
    
    // 设施ID
    facilityId: '',
    
    // 设施信息
    facility: null,
    
    // 表单数据
    formData: {
      problemDescription: '',
      urgencyLevel: 1, // 1-一般, 2-紧急, 3-非常紧急
      expectedTime: '',
      location: '',
      contactPerson: '',
      contactPhone: ''
    },
    
    // 图片上传
    uploadedImages: [],
    
    // 紧急程度选项
    urgencyOptions: [
      { value: 1, label: '一般' },
      { value: 2, label: '紧急' },
      { value: 3, label: '非常紧急' }
    ],
    
    // 日期选择
    minDate: new Date().getTime(),
    maxDate: new Date().getTime() + 30 * 24 * 60 * 60 * 1000, // 30天后
    currentDate: new Date().getTime() + 24 * 60 * 60 * 1000, // 默认明天
    showDatePicker: false,
    
    // 表单验证
    formErrors: {
      problemDescription: '',
      contactPerson: '',
      contactPhone: ''
    },
    
    // 提交状态
    isSubmitting: false
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 获取设施ID
    if (options.id) {
      this.setData({
        facilityId: options.id
      });
      this.loadFacilityDetail(options.id);
    } else {
      wx.showToast({
        title: '设施ID无效',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载设施详情
  loadFacilityDetail: function(facilityId) {
    this.setData({ isLoading: true });
    
    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const facility = this.getMockFacilityDetail(facilityId);
      
      // 设置表单默认值
      const formData = this.data.formData;
      formData.location = facility.location;
      
      // 获取当前用户信息（模拟）
      const userInfo = this.getMockUserInfo();
      formData.contactPerson = userInfo.name;
      formData.contactPhone = userInfo.phone;
      
      this.setData({
        facility: facility,
        formData: formData,
        isLoading: false
      });
    }, 500);
  },
  
  // 获取模拟设施详情
  getMockFacilityDetail: function(facilityId) {
    const facilities = {
      '1': {
        id: '1',
        name: '小区正门监控',
        code: 'CAM-001',
        category: 'monitor',
        categoryText: '监控设施',
        location: '小区正门',
        status: 'normal',
        statusText: '正常'
      },
      '8': {
        id: '8',
        name: '1号楼单元门',
        code: 'ACC-003',
        category: 'door',
        categoryText: '门禁设施',
        location: '1号楼',
        status: 'fault',
        statusText: '故障'
      }
    };
    
    return facilities[facilityId] || facilities['1'];
  },
  
  // 获取模拟用户信息
  getMockUserInfo: function() {
    return {
      name: '张工',
      phone: '13800138000'
    };
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 表单输入处理
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新表单数据
    const formData = this.data.formData;
    formData[field] = value;
    
    this.setData({
      formData: formData
    });
    
    // 清除对应的错误提示
    if (this.data.formErrors[field]) {
      const formErrors = this.data.formErrors;
      formErrors[field] = '';
      this.setData({
        formErrors: formErrors
      });
    }
  },
  
  // 选择紧急程度
  onUrgencyChange: function(e) {
    const { value } = e.detail;
    
    const formData = this.data.formData;
    formData.urgencyLevel = value;
    
    this.setData({
      formData: formData
    });
  },
  
  // 显示日期选择器
  showDatePicker: function() {
    this.setData({
      showDatePicker: true
    });
  },
  
  // 隐藏日期选择器
  hideDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },
  
  // 确认日期选择
  confirmDatePicker: function(e) {
    const { value } = e.detail;
    
    const formData = this.data.formData;
    formData.expectedTime = dateUtil.formatDate(new Date(value));
    
    this.setData({
      currentDate: value,
      formData: formData,
      showDatePicker: false
    });
  },
  
  // 上传图片
  uploadImage: function() {
    const { uploadedImages } = this.data;
    
    // 最多上传9张图片
    const remainCount = 9 - uploadedImages.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 这里应该是上传图片到服务器
        // 目前只是添加到本地数据
        const tempFilePaths = res.tempFilePaths;
        
        this.setData({
          uploadedImages: [...uploadedImages, ...tempFilePaths]
        });
      }
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    wx.previewImage({
      current: uploadedImages[index],
      urls: uploadedImages
    });
  },
  
  // 删除图片
  deleteImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    uploadedImages.splice(index, 1);
    
    this.setData({
      uploadedImages: uploadedImages
    });
  },
  
  // 验证表单
  validateForm: function() {
    const { formData } = this.data;
    const formErrors = {
      problemDescription: '',
      contactPerson: '',
      contactPhone: ''
    };
    
    let isValid = true;
    
    // 验证问题描述
    if (!formData.problemDescription.trim()) {
      formErrors.problemDescription = '请填写问题描述';
      isValid = false;
    }
    
    // 验证联系人
    if (!formData.contactPerson.trim()) {
      formErrors.contactPerson = '请填写联系人';
      isValid = false;
    }
    
    // 验证联系电话
    if (!formData.contactPhone.trim()) {
      formErrors.contactPhone = '请填写联系电话';
      isValid = false;
    } else if (!/^1\d{10}$/.test(formData.contactPhone)) {
      formErrors.contactPhone = '请填写正确的手机号码';
      isValid = false;
    }
    
    this.setData({
      formErrors: formErrors
    });
    
    return isValid;
  },
  
  // 提交表单
  submitForm: function() {
    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }
    
    // 设置提交状态
    this.setData({
      isSubmitting: true
    });
    
    // 这里应该是提交表单到服务器
    // 目前只是模拟提交
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
})
