<!-- 失物招领组件 -->
<view class="lostfound-container">
  <!-- 筛选选项卡 -->
  <view class="filter-tabs">
    <view class="filter-tab {{lostFoundFilter === 'all' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="all">全部</view>
    <view class="filter-tab {{lostFoundFilter === 'lost' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="lost">寻物启事</view>
    <view class="filter-tab {{lostFoundFilter === 'found' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="found">招领启事</view>
  </view>

  <!-- 失物招领列表 -->
  <view class="lostfound-list">
    <view class="lostfound-item" wx:for="{{filteredLostFound}}" wx:key="id" bindtap="navigateToLostFoundDetail" data-id="{{item.id}}">
      <view class="lostfound-content">
        <view class="lostfound-header">
          <view class="lostfound-title">{{item.title}}</view>
          <view class="lostfound-tag {{item.type === 'lost' ? 'lost-tag' : 'found-tag'}}">
            {{item.type === 'lost' ? '寻找中' : '已拾到'}}
          </view>
        </view>

        <view class="lostfound-info">
          <view class="lostfound-time">
            <image class="info-icon" src="/images/icons/clock.svg" mode="aspectFit"></image>
            <text>{{item.time}}</text>
          </view>
          <view class="lostfound-location">
            <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
            <text>{{item.location}}</text>
          </view>
        </view>

        <view class="lostfound-desc">{{item.description}}</view>

        <!-- 图片预览 -->
        <view class="lostfound-images" wx:if="{{item.images.length > 0}}">
          <image 
            class="lostfound-image" 
            wx:for="{{item.images}}" 
            wx:for-item="image" 
            wx:key="index" 
            src="{{image}}" 
            mode="aspectFill"
          ></image>
        </view>

        <view class="lostfound-contact">
          <image class="contact-icon" src="/images/icons/user.svg" mode="aspectFit"></image>
          <text>联系人: {{item.contact}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredLostFound.length === 0}}">
    <image class="empty-image" src="/images/illustrations/empty-lostfound.svg" mode="aspectFit"></image>
    <view class="empty-text">暂无{{lostFoundFilter === 'lost' ? '寻物启事' : lostFoundFilter === 'found' ? '招领启事' : '失物招领信息'}}</view>
    <view class="empty-subtext">有需要可联系物业发布</view>
  </view>

  <!-- 底部安全区域，防止内容被底部选项卡遮挡 -->
  <view class="safe-bottom"></view>
</view>
