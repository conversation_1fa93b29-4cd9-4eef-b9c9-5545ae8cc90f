<!-- 巡检记录页面 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索巡检名称、位置" value="{{searchValue}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-card" animation="{{animationData}}">
    <view class="stats-row">
      <view class="stat-item">
        <view class="stat-value">{{statistics.total}}</view>
        <view class="stat-label">总巡检</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.pending}}</view>
        <view class="stat-label">待执行</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.completed}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.abnormal}}</view>
        <view class="stat-label">异常</view>
      </view>
    </view>
    <view class="stats-divider"></view>
    <view class="stats-footer">
      <view class="today-stat">今日已完成 <text class="highlight">{{statistics.todayCompleted}}</text> 次巡检</view>
    </view>
  </view>

  <!-- 日期筛选 -->
  <view class="date-filter">
    <view class="date-range">
      <view class="date-item" bindtap="showDatePicker" data-type="start">
        <text>开始：</text>
        <text class="date-value">{{dateRange.start}}</text>
      </view>
      <view class="date-separator">至</view>
      <view class="date-item" bindtap="showDatePicker" data-type="end">
        <text>结束：</text>
        <text class="date-value">{{dateRange.end}}</text>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view class="tab {{activeTab === 'pending' ? 'active' : ''}}" bindtap="switchTab" data-tab="pending">待执行</view>
    <view class="tab {{activeTab === 'completed' ? 'active' : ''}}" bindtap="switchTab" data-tab="completed">已完成</view>
    <view class="tab {{activeTab === 'abnormal' ? 'active' : ''}}" bindtap="switchTab" data-tab="abnormal">异常</view>
  </view>

  <!-- 巡检列表 -->
  <view class="inspection-list">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isLoading && isEmpty}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无巡检记录</view>
    </view>

    <!-- 巡检记录列表 -->
    <block wx:if="{{!isLoading && !isEmpty}}">
      <view class="inspection-card" wx:for="{{inspections}}" wx:key="id" bindtap="viewInspectionDetail" data-id="{{item.id}}">
        <view class="inspection-header">
          <view class="inspection-title">{{item.name}}</view>
          <view class="inspection-status {{item.status}}">
            {{item.status === 'pending' ? '待执行' : (item.status === 'completed' ? '已完成' : '异常')}}
          </view>
        </view>
        
        <view class="inspection-info">
          <view class="info-row">
            <view class="info-label">巡检位置</view>
            <view class="info-value">{{item.location}}</view>
          </view>
          <view class="info-row">
            <view class="info-label">巡检时间</view>
            <view class="info-value">{{item.date}} {{item.time}}</view>
          </view>
          <view class="info-row">
            <view class="info-label">巡检人员</view>
            <view class="info-value">{{item.inspector}}</view>
          </view>
          <view class="info-row" wx:if="{{item.status !== 'pending'}}">
            <view class="info-label">巡检结果</view>
            <view class="info-value {{item.status === 'abnormal' ? 'abnormal-text' : ''}}">
              {{item.result}}
              <text wx:if="{{item.abnormalCount > 0}}">({{item.abnormalCount}}项异常)</text>
            </view>
          </view>
        </view>
        
        <!-- 图片预览 -->
        <view class="inspection-images" wx:if="{{item.images && item.images.length > 0}}">
          <image class="inspection-image" wx:for="{{item.images}}" wx:for-item="img" wx:key="index" src="{{img}}" mode="aspectFill"></image>
        </view>
      </view>
    </block>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !isLoading}}">
      <view class="loading-indicator" wx:if="{{isLoadingMore}}"></view>
      <text wx:else bindtap="loadMore">加载更多</text>
    </view>
  </view>

  <!-- 创建新巡检按钮 -->
  <view class="create-btn" bindtap="createNewInspection">
    <view class="create-icon"></view>
  </view>
</view>
