<view class="visitor-container">
 

  <!-- 表单内容区域 -->
  <scroll-view scroll-y class="visitor-form-container">
    <!-- 快捷操作区 -->
    <view class="visitor-nav-tabs">
      <!-- 常用访客入口 -->
      <view class="visitor-nav-tab" bindtap="showFrequentVisitors">
        <image src="/images/icons/star-fill.svg" class="visitor-nav-tab-icon" />
        <text>常用访客</text>
      </view>

      <!-- 批量邀请入口 -->
      <view class="visitor-nav-tab" bindtap="navigateToBatchInvite">
        <image src="/images/icons/double-check.svg" class="visitor-nav-tab-icon" />
        <text>批量邀请</text>
      </view>
    </view>

  

    <!-- 表单区域 -->
    <form bindsubmit="submitForm">
      <!-- 访客姓名 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">访客姓名<text class="visitor-required">*</text></text>
        <input name="visitorName" class="visitor-form-input" placeholder="请输入访客姓名"
               value="{{formData.visitorName}}" bindinput="inputVisitorName" />
        <view class="visitor-error-tip" wx:if="{{errors.visitorName}}">{{errors.visitorName}}</view>
      </view>

      <!-- 访客手机号 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">手机号码<text class="visitor-required">*</text></text>
        <view class="visitor-phone-input-container">
          <input name="visitorPhone" class="visitor-form-input" placeholder="请输入手机号码"
                 type="number" maxlength="11" value="{{formData.visitorPhone}}"
                 bindinput="inputVisitorPhone" />
          <view class="visitor-paste-btn" bindtap="pastePhoneNumber">粘贴</view>
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.visitorPhone}}">{{errors.visitorPhone}}</view>
      </view>
    <!-- 车牌号码 -->
    <view class="visitor-form-item" >
        <text class="visitor-form-label">车牌号码</text>
        <view class="visitor-car-number-input-container">
          <input name="carNumber" class="visitor-form-input" placeholder="请输入车牌号码"
                 value="{{formData.carNumber}}" bindinput="inputCarNumber" />
          <view class="visitor-history-btn" bindtap="showCarNumberHistory">
            <image src="/images/icons/history.svg" class="visitor-history-icon" />
          </view>
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.carNumber}}">{{errors.carNumber}}</view>
      </view>

      <!-- 来访时间 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">来访时间<text class="visitor-required">*</text></text>
        <view class="visitor-datetime-picker" bindtap="showDateTimePicker">
          <text>{{formData.visitDate}} {{formData.visitTime}}</text>
          <image src="/images/icons/calendar.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.visitDateTime}}">{{errors.visitDateTime}}</view>
      </view>

      <!-- 滞留时长 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">滞留时长<text class="visitor-required">*</text></text>
        <view class="visitor-duration-picker" bindtap="showDurationPicker">
          <text>{{formData.duration}}小时</text>
          <image src="/images/icons/clock.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.duration}}">{{errors.duration}}</view>
      </view>

      <!-- 来访目的 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">来访目的<text class="visitor-required">*</text></text>
        <view class="visitor-purpose-picker" bindtap="showPurposePicker">
          <text>{{formData.purpose || '请选择来访目的'}}</text>
          <image src="/images/icons/arrow-down.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.purpose}}">{{errors.purpose}}</view>
      </view>

  
      <!-- 备注信息 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">备注信息</text>
        <textarea name="remarks" class="visitor-form-textarea" placeholder="请输入备注信息（选填）"
                  value="{{formData.remarks}}" bindinput="inputRemarks" />
      </view>
    </form>
  </scroll-view>

  <!-- 底部浮动步骤条 -->
  <view class="visitor-step-indicator">
    <view class="visitor-step active"></view>
    <view class="visitor-step"></view>
    <text class="visitor-step-text">步骤 1/2</text>
  </view>

  <!-- 提交按钮 -->
  <view class="visitor-submit-btn-container">
    <button class="visitor-submit-btn" bindtap="submitForm">生成访客凭证</button>
  </view>
</view>

<!-- 日期时间选择器弹窗 -->
<view class="visitor-picker-modal {{showDateTimePicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideDateTimePicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择来访时间</text>
      <view class="visitor-picker-close" bindtap="hideDateTimePicker">取消</view>
    </view>
    <view class="visitor-picker-body">
      <picker-view indicator-style="height: 50px;" style="width: 100%; height: 250px;"
                   value="{{dateTimePickerValue}}" bindchange="onDateTimePickerChange">
        <picker-view-column>
          <view wx:for="{{dateArray}}" wx:key="index" class="visitor-picker-item">{{item.label}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{hourArray}}" wx:key="index" class="visitor-picker-item">{{item}}时</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{minuteArray}}" wx:key="index" class="visitor-picker-item">{{item}}分</view>
        </picker-view-column>
      </picker-view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="confirmDateTime">确定</button>
    </view>
  </view>
</view>

<!-- 滞留时长选择器弹窗 -->
<view class="visitor-picker-modal {{showDurationPicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideDurationPicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择滞留时长</text>
      <view class="visitor-picker-close" bindtap="hideDurationPicker">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-duration-quick-select">
        <view wx:for="{{durationOptions}}" wx:key="index"
              class="visitor-duration-option {{formData.duration === item ? 'active' : ''}}"
              bindtap="selectDuration" data-duration="{{item}}">
          {{item}}小时
        </view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="confirmDuration">确定</button>
    </view>
  </view>
</view>

<!-- 来访目的选择器弹窗 -->
<view class="visitor-picker-modal {{showPurposePicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hidePurposePicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择来访目的</text>
      <view class="visitor-picker-close" bindtap="hidePurposePicker">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-purpose-options">
        <view wx:for="{{purposeOptions}}" wx:key="index"
              class="visitor-purpose-option"
              bindtap="selectPurpose" data-purpose="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 车牌号历史记录弹窗 -->
<view class="visitor-picker-modal {{showCarNumberHistory ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideCarNumberHistory"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">车牌号历史记录</text>
      <view class="visitor-picker-close" bindtap="hideCarNumberHistory">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-car-number-history">
        <view wx:if="{{carNumberHistory.length === 0}}" style="text-align: center; padding: 16px;">
          暂无历史记录
        </view>
        <view wx:for="{{carNumberHistory}}" wx:key="index"
              class="visitor-car-number-history-item"
              bindtap="selectCarNumber" data-car-number="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 常用访客选择弹窗 -->
<view class="visitor-picker-modal {{showFrequentVisitorsModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideFrequentVisitors"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择常用访客</text>
      <view class="visitor-picker-close" bindtap="hideFrequentVisitors">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-frequent-visitors">
        <view wx:if="{{frequentVisitors.length === 0}}" style="text-align: center; padding: 16px;">
          暂无常用访客
        </view>
        <view wx:for="{{frequentVisitors}}" wx:key="id"
              class="visitor-frequent-visitor-item"
              bindtap="selectFrequentVisitor" data-id="{{item.id}}">
          <view class="visitor-frequent-visitor-avatar">{{item.name[0]}}</view>
          <view class="visitor-frequent-visitor-info">
            <view class="visitor-frequent-visitor-name">{{item.name}}</view>
            <view class="visitor-frequent-visitor-phone">{{item.phone}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 隐私协议弹窗 -->
<view class="visitor-privacy-modal" wx:if="{{showPrivacyModal}}">
  <view class="visitor-privacy-content">
    <view class="visitor-privacy-header">
      <text class="visitor-privacy-title">访客登记隐私保护指引</text>
    </view>
    <view class="visitor-privacy-body">
      <view class="visitor-privacy-text">
        <text>
          尊敬的用户，欢迎使用访客登记功能。

          在使用访客登记功能前，请您仔细阅读并了解本隐私保护指引。本指引将帮助您了解以下内容：

          1. 我们收集的信息
          为了提供访客登记服务，我们需要收集以下信息：
          - 访客姓名
          - 访客手机号码
          - 来访时间和滞留时长
          - 来访目的
          - 车牌号码（如适用）
          - 备注信息（如提供）

          2. 信息的使用
          我们收集的信息将用于：
          - 生成访客凭证
          - 通知被访人
          - 安保管理
          - 统计分析

          3. 信息的保护
          我们采取严格的安全措施保护您的信息：
          - 敏感信息加密存储
          - 访问权限控制
          - 定期安全审计

          4. 信息的保留
          访客信息将在访问结束后保留30天，之后将自动删除。

          5. 您的权利
          您有权：
          - 查看和修改您登记的访客信息
          - 删除访客记录
          - 撤销对本功能的授权

          如您同意本隐私保护指引，请点击"同意并继续"开始使用访客登记功能。
        </text>
      </view>
    </view>
    <view class="visitor-privacy-footer">
      <view class="visitor-privacy-btn visitor-privacy-cancel-btn" bindtap="rejectPrivacy">不同意</view>
      <view class="visitor-privacy-btn visitor-privacy-agree-btn" bindtap="agreePrivacy">同意并继续</view>
    </view>
  </view>
</view>
