# 添加居民页面修改说明

## 修改概述

根据要求对添加居民页面进行了全面修改，包括字典数据集成、表单结构调整、房屋选择方式改进和API接口集成。

## 主要修改内容

### 1. 字典数据集成

在页面初始化时获取以下字典数据：
- `residentTag`: 人员标签字典 (使用 `resident_tag`)
- `certificateType`: 证件类型字典 (使用 `certificate_type`) 
- `residentType`: 居民住户类型字典 (使用 `resident_type`)
- `genderDict`: 性别字典 (使用 `gender`)

### 2. 表单结构调整

#### 移除的字段：
- 邮箱
- 紧急联系人
- 紧急联系人电话

#### 新增的字段：
- 证件类型选择器
- 人员标签多选框
- 备注文本域

#### 修改的字段：
- 姓名 → residentName
- 身份证号 → 证件号码 (idCardNumber)
- 出生日期 → birthday
- 性别选择改为下拉选择器
- 居民类型使用字典数据

### 3. 房屋选择方式改进

参考 `profilePackage/pages/profile/house/house` 页面的弹窗方式：
- 添加关闭按钮 (×)
- 改进房屋项显示样式
- 添加空状态提示
- 使用真实API获取房屋列表

### 4. API接口集成

#### 房屋列表API
使用 `propertyApi.getRoomList()` 获取房屋列表，数据格式化为：
```javascript
{
  id: room.id,
  fullName: `${room.buildingNumber}${room.unitNumber}${room.roomNumber}`,
  buildingNumber: room.buildingNumber,
  unitNumber: room.unitNumber,
  roomNumber: room.roomNumber
}
```

#### 添加居民API
使用 `propertyApi.addResident(params)` 提交数据，参数结构：
```javascript
{
  certificateType: "id_card",        // 证件类型字典nameEn
  communityId: "2",                  // 小区id
  idCardNumber: "110101199004070777", // 证件号码
  note: "这是备注",                   // 备注
  phone: "13685156020",              // 手机号码
  residentName: "卜测试",             // 住户名称
  residentType: "owner",             // 住户类型字典nameEn
  roomId: "",                        // 绑定的房间id
  status: "normal",                  // 状态,默认normal
  tags: "low_income,party_member",   // 标签,多选,逗号分隔
  birthday: "1990-06-06",            // 出生日期
  gender: "man"                      // 性别字典nameEn
}
```

### 5. 表单验证更新

更新了表单验证逻辑：
- 验证字段名称对应新的表单结构
- 证件号码验证根据证件类型进行
- 移除了邮箱和紧急联系人的验证

### 6. 样式更新

#### 新增样式：
- 复选框组样式 (`.checkbox-group`, `.checkbox-item`)
- 文本域样式 (`.form-textarea`)
- 弹窗关闭按钮样式 (`.modal-close`)
- 空状态样式 (`.empty-state`)

#### 修改样式：
- 弹窗头部布局改为左右分布
- 房屋选择项样式优化
- 选中状态显示为 "✓" 符号

## 文件修改列表

1. **index.js** - 主要逻辑文件
   - 添加字典数据初始化
   - 更新表单处理方法
   - 集成真实API接口
   - 更新表单验证逻辑

2. **index.wxml** - 模板文件
   - 移除联系信息部分
   - 更新表单字段结构
   - 改进房屋选择弹窗
   - 添加复选框和文本域

3. **index.wxss** - 样式文件
   - 添加复选框组样式
   - 添加文本域样式
   - 更新弹窗样式
   - 添加空状态样式

## 注意事项

1. 字典数据获取失败时会显示错误提示
2. 房屋列表为空时显示空状态提示
3. 表单提交前会进行完整性验证
4. 所有API调用都包含错误处理
5. 标签支持多选，提交时用逗号分隔
