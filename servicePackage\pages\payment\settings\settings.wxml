<!--pages/payment/settings/settings.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 设置内容 -->
  <block wx:if="{{!isLoading}}">
    <!-- 缴费提醒设置 -->
    <view class="settings-card">
      <view class="card-title">缴费提醒</view>

      <!-- 开启缴费提醒 -->
      <view class="settings-item">
        <view class="item-label">开启缴费提醒</view>
        <switch class="item-switch" checked="{{reminderEnabled}}" bindchange="toggleReminderSwitch" color="#ff8c00" />
      </view>

      <!-- 提醒天数 -->
      <view class="settings-item" bindtap="{{reminderEnabled ? 'showDaysSelector' : ''}}" style="{{!reminderEnabled ? 'opacity: 0.5;' : ''}}">
        <view class="item-label">提前提醒天数</view>
        <view class="item-value">
          <text>{{reminderDays}}天</text>
          <view class="arrow-icon"></view>
        </view>
      </view>

      <!-- 提醒方式 -->
      <view class="settings-item" style="{{!reminderEnabled ? 'opacity: 0.5;' : ''}}">
        <view class="item-label">提醒方式</view>
        <view class="item-value">
          <view class="checkbox-group">
            <view class="checkbox-item" wx:for="{{reminderMethodOptions}}" wx:key="value" bindtap="{{reminderEnabled ? 'toggleReminderMethod' : ''}}" data-index="{{index}}">
              <view class="checkbox {{item.checked ? 'checked' : ''}}"></view>
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 自动缴费设置 -->
    <view class="settings-card">
      <view class="card-title">自动缴费</view>

      <!-- 开启自动缴费 -->
      <view class="settings-item">
        <view class="item-label">开启自动缴费</view>
        <switch class="item-switch" checked="{{autoPayEnabled}}" bindchange="toggleAutoPaySwitch" color="#ff8c00" />
      </view>

      <!-- 自动缴费限额 -->
      <view class="settings-item" bindtap="{{autoPayEnabled ? 'showLimitSelector' : ''}}" style="{{!autoPayEnabled ? 'opacity: 0.5;' : ''}}">
        <view class="item-label">自动缴费限额</view>
        <view class="item-value">
          <text>¥{{autoPayLimit}}</text>
          <view class="arrow-icon"></view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="settings-item" style="{{!autoPayEnabled ? 'opacity: 0.5;' : ''}}">
        <view class="item-label">支付方式</view>
        <view class="item-value payment-methods">
          <view class="payment-method {{item.checked ? 'checked' : ''}}"
                wx:for="{{paymentMethods}}"
                wx:key="value"
                bindtap="{{autoPayEnabled ? 'selectPaymentMethod' : ''}}"
                data-value="{{item.value}}">
            <view class="payment-icon {{item.icon}}"></view>
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 银行卡信息 -->
      <view class="settings-item" wx:if="{{autoPayEnabled && paymentMethods[2].checked}}" bindtap="showBankCardSelector">
        <view class="item-label">选择银行卡</view>
        <view class="item-value">
          <view wx:if="{{selectedBankCard}}" class="bank-card-info">
            <view class="bank-icon {{selectedBankCard.icon}}"></view>
            <view class="bank-details">
              <view class="bank-name">{{selectedBankCard.bank}}</view>
              <view class="card-number">{{selectedBankCard.cardNumber}}</view>
            </view>
          </view>
          <view wx:else class="select-card">
            <text>请选择银行卡</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>

      <!-- 自动缴费说明 -->
      <view class="settings-tips" wx:if="{{autoPayEnabled}}">
        <view class="tip-icon"></view>
        <view class="tip-text">
          开启自动缴费后，系统将在费用到期前自动为您完成缴费，无需手动操作。
          <text class="highlight">单笔金额超过限额的缴费将不会自动扣款，需要您手动确认。</text>
        </view>
      </view>
    </view>
  </block>

  <!-- 提醒天数选择器弹窗 -->
  <view class="selector-modal {{showDaysSelector ? 'show' : ''}}" bindtap="hideDaysSelector">
    <view class="selector-content" catchtap="stopPropagation">
      <view class="selector-title">选择提醒天数</view>
      <view class="selector-list">
        <view class="selector-item {{reminderDays === item ? 'selected' : ''}}"
              wx:for="{{reminderDaysOptions}}"
              wx:key="*this"
              bindtap="selectReminderDays"
              data-days="{{item}}">
          <text>提前{{item}}天</text>
          <view class="check-icon" wx:if="{{reminderDays === item}}"></view>
        </view>
      </view>
      <button class="selector-cancel" bindtap="hideDaysSelector">取消</button>
    </view>
  </view>

  <!-- 自动缴费限额选择器弹窗 -->
  <view class="selector-modal {{showLimitSelector ? 'show' : ''}}" bindtap="hideLimitSelector">
    <view class="selector-content" catchtap="stopPropagation">
      <view class="selector-title">选择自动缴费限额</view>
      <view class="selector-list">
        <view class="selector-item {{autoPayLimit === item ? 'selected' : ''}}"
              wx:for="{{autoPayLimitOptions}}"
              wx:key="*this"
              bindtap="selectAutoPayLimit"
              data-limit="{{item}}">
          <text>¥{{item}}</text>
          <view class="check-icon" wx:if="{{autoPayLimit === item}}"></view>
        </view>
      </view>
      <button class="selector-cancel" bindtap="hideLimitSelector">取消</button>
    </view>
  </view>

  <!-- 银行卡选择器弹窗 -->
  <view class="selector-modal {{showBankCardSelector ? 'show' : ''}}" bindtap="hideBankCardSelector">
    <view class="selector-content" catchtap="stopPropagation">
      <view class="selector-title">选择银行卡</view>
      <view class="bank-card-list">
        <view class="bank-card-item {{selectedBankCard && selectedBankCard.id === item.id ? 'selected' : ''}}"
              wx:for="{{bankCards}}"
              wx:key="id"
              bindtap="selectBankCard"
              data-id="{{item.id}}">
          <view class="bank-card-left">
            <view class="bank-icon {{item.icon}}"></view>
            <view class="bank-details">
              <view class="bank-name">{{item.bank}}</view>
              <view class="card-type">{{item.cardType}}</view>
              <view class="card-number">{{item.cardNumber}}</view>
            </view>
          </view>
          <view class="check-icon" wx:if="{{selectedBankCard && selectedBankCard.id === item.id}}"></view>
        </view>

        <!-- 添加银行卡 -->
        <view class="add-bank-card" bindtap="addBankCard">
          <view class="add-icon"></view>
          <text>添加银行卡</text>
        </view>
      </view>
      <button class="selector-cancel" bindtap="hideBankCardSelector">取消</button>
    </view>
  </view>
</view>
