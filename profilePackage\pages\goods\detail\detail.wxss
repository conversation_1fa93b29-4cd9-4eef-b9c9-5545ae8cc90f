/* 商品详情页样式 */
.container {
  padding-bottom: 300rpx;
  background-color: #f5f5f5;
  height: 100%;
  overflow-y: auto;

}



/* 商品图片轮播 */
.goods-swiper {
  width: 100vw; /* 全屏宽度 */
  height: 75vw; /* Set height relative to viewport width (e.g., 4:3 aspect ratio) */
  margin-top: 0;
  margin-left: calc(-50vw + 50%); /* 突破容器限制，实现全屏宽度 */
  background-color: #f0f0f0; /* Add a background color for empty space */

}

.goods-swiper swiper-item {
  display: flex;
  align-items: center; /* Vertical center */
  justify-content: center; /* Horizontal center */
  overflow: hidden;
}

.goods-swiper-image {
  width: 100%;
  height: 100%;
  display: block; /* Helps with layout */
}

/* 商品基本信息 */
.goods-info {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.goods-price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.goods-price-symbol {
  font-size: 32rpx;
  color: #ff4d4f;
  margin-right: 4rpx;
}

.goods-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4d4f;
  margin-right: 16rpx;
}

.goods-original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 16rpx;
}

.goods-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.goods-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-bottom: 5rpx; /* Added for potential wrapping */
}

.type-tag {
  background-color: #fff1f0;
  color: #ff4d4f;
  border: 1rpx solid #ffccc7;
}

.goods-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

/* Styles for points information */
.points-info-section {
  margin-top: 20rpx;
  margin-bottom: 20rpx; /* Add some space before stats */
  padding: 24rpx;
  background: linear-gradient(to right, #fff7e6, #fff9ee); /* Light orange gradient background */
  border: 1rpx solid #ffcc80;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #ff8c00;
  display: flex;
  flex-direction: column;
  min-height: 40rpx; /* 确保即使没有内容也有一定高度 */
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.15);
  position: relative;
  overflow: hidden;
}

/* 添加一个装饰性元素 */
.points-info-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background-color: #ff8c00;
  border-top-left-radius: 12rpx;
  border-bottom-left-radius: 12rpx;
}

.points-deduction,
.points-reward {
  display: flex;
  align-items: center;
  line-height: 1.5;
  padding: 8rpx 0;
  position: relative;
  padding-left: 16rpx; /* 为左侧装饰元素留出空间 */
}

.points-reward {
  margin-top: 12rpx; /* Add space between deduction and reward */
  font-weight: 500; /* 加粗显示积分奖励 */
}

.points-tag {
  background-color: #ff8c00; /* Orange tag background */
  color: #fff;
  padding: 6rpx 14rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: bold;
  margin-right: 16rpx;
  white-space: nowrap; /* Prevent tag text wrap */
  box-shadow: 0 2rpx 4rpx rgba(255, 140, 0, 0.2);
}
/* End of styles for points information */

.goods-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0; /* Add separator */
}

.goods-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
}

/* 卖家信息 */
.seller-info {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.seller-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.seller-detail {
  flex: 1;
}

.seller-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.seller-location {
  font-size: 24rpx;
  color: #999;
}

.contact-btn {
  font-size: 28rpx;
  color: #ff8c00;
  background-color: #fff7e6;
  border: 1rpx solid #ff8c00;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  margin: 0;
  line-height: 1.5;
}

/* 积分抵扣 */
.point-discount {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.points-badge {
  font-size: 20rpx;
  color: #ffffff;
  background-color: #ff8c00;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.points-badge.reward {
  background-color: #52c41a;
}

.point-info {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  background-color: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.point-info-item {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 160rpx;
  color: #999;
}

.info-value {
  flex: 1;
  color: #333;
}

.point-slider-container {
  margin-top: 20rpx;
  background-color: #fff7e6;
  padding: 20rpx;
  border-radius: 8rpx;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.point-slider {
  margin: 0;
  width: 100%;
}

.point-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-top: 16rpx;
}

.point-value-text, .point-discount-text {
  font-size: 26rpx;
}

.highlight {
  color: #ff8c00;
  font-weight: 600;
  font-size: 30rpx;
}

.point-tips {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  background-color: #ffffff;
  padding: 10rpx;
  border-radius: 6rpx;
  border: 1rpx dashed #ff8c00;
}

/* 返还积分 */
.point-reward {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.reward-info {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.reward-info-item {
  display: flex;
  margin-bottom: 10rpx;
}

.reward-tips {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: #52c41a;
  background-color: #f6ffed;
  padding: 10rpx;
  border-radius: 6rpx;
  border: 1rpx dashed #52c41a;
}

/* 商品详情 */
.goods-detail {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.goods-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: none;
  overflow: visible;
  margin-top: 20rpx;
  padding-top: 10rpx;
  padding-bottom: 20rpx;
}

/* 富文本内容样式 */
.rich-content {
  font-size: 28rpx;
  line-height: 1.6;
}

.rich-content p {
  margin-bottom: 16rpx;
}

.rich-content strong, .rich-content b {
  font-weight: bold;
}

.rich-content em, .rich-content i {
  font-style: italic;
}

.rich-content u {
  text-decoration: underline;
}

/* 相似商品推荐 */
.similar-goods {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.similar-scroll {
  white-space: nowrap;
  margin: 0 -30rpx;
  padding: 0 30rpx;
}

.similar-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
}

.similar-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.similar-title {
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8rpx;
}

.similar-price {
  font-size: 28rpx;
  font-weight: 500;
  color: #ff8c00;
}

.similar-free {
  font-size: 28rpx;
  font-weight: 500;
  color: #52c41a;
}

/* 底部操作栏 */
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);
}

.footer-actions {
  display: flex;
  padding: 10rpx 0;
  margin-top: 20rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.footer-btns {
  display: flex;
  padding: 20rpx 30rpx;
}

.contact-seller-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #fff7e6;
  color: #ff8c00;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.contact-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

.buy-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff8c00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.buy-btn.disabled {
  background-color: #cccccc;
}

/* 弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 80%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-options {
  padding: 30rpx;
}

.contact-option {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.option-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
}

.option-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.option-btn {
  font-size: 24rpx;
  color: #ff8c00;
  background-color: #fff7e6;
  border: 1rpx solid #ff8c00;
  border-radius: 30rpx;
  padding: 6rpx 20rpx;
  margin: 0;
  line-height: 1.5;
}

.full-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff8c00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
}

.modal-close {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 购买确认弹窗 */
.buy-info {
  padding: 30rpx;
}

.buy-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.buy-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  padding-top: 6rpx;
}

.buy-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 积分抵扣相关样式 */
.points-switch-item {
  align-items: flex-start;
}

.points-rule {
  display: flex;
  flex-direction: column;
  margin-bottom: 10rpx;
}

.points-rule text {
  font-size: 26rpx;
  margin-bottom: 4rpx;
}

.points-slider-container {
  width: 100%;
  margin: 10rpx 0;
}

.points-slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.points-value {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.points-value text:last-child {
  color: #ff8c00;
  font-weight: 500;
}

.deduction-value {
  color: #ff8c00;
  font-weight: 500;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 32rpx;
  color: #333;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  margin: 0 2rpx;
}

.buy-item.total {
  margin-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.buy-item.total .buy-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
}

.buy-remark {
  padding: 0 30rpx 30rpx;
}

.remark-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.remark-input {
  width: 100%;
  height: 120rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
}

.buy-notice {
  padding: 0 30rpx 30rpx;
  font-size: 24rpx;
  color: #999;
}

.modal-btns {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  margin: 0;
  border-radius: 0;
}

.modal-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background-color: #ff8c00;
  color: #ffffff;
}

/* 暗黑模式 */
.darkMode, page[data-darkmode="true"] {
  background-color: #1c1c1e;
  color: #f5f5f7;
}

.darkMode .goods-info,
.darkMode .seller-info,
.darkMode .point-discount,
.darkMode .goods-detail,
.darkMode .similar-goods,
.darkMode .footer,
page[data-darkmode="true"] .goods-info,
page[data-darkmode="true"] .seller-info,
page[data-darkmode="true"] .point-discount,
page[data-darkmode="true"] .goods-detail,
page[data-darkmode="true"] .similar-goods,
page[data-darkmode="true"] .footer {
  background-color: #2c2c2e;
}

.darkMode .goods-title,
.darkMode .section-title,
.darkMode .seller-name,
page[data-darkmode="true"] .goods-title,
page[data-darkmode="true"] .section-title,
page[data-darkmode="true"] .seller-name {
  color: #f5f5f7;
}

.darkMode .goods-description,
.darkMode .stat-value,
.darkMode .similar-title,
page[data-darkmode="true"] .goods-description,
page[data-darkmode="true"] .stat-value,
page[data-darkmode="true"] .similar-title {
  color: #aeaeb2;
}

.darkMode .goods-type,
.darkMode .stat-label,
.darkMode .seller-location,
.darkMode .point-info,
page[data-darkmode="true"] .goods-type,
page[data-darkmode="true"] .stat-label,
page[data-darkmode="true"] .seller-location,
page[data-darkmode="true"] .point-info {
  color: #8e8e93;
  background-color: #3a3a3c;
}

.darkMode .goods-stats,
.darkMode .footer-actions,
page[data-darkmode="true"] .goods-stats,
page[data-darkmode="true"] .footer-actions {
  border-color: #3a3a3c;
}

.darkMode .contact-btn,
page[data-darkmode="true"] .contact-btn {
  color: #ff9500;
  background-color: rgba(255, 149, 0, 0.1);
  border-color: #ff9500;
}

.darkMode .point-value,
page[data-darkmode="true"] .point-value {
  color: #ff9500;
}

.darkMode .action-item,
page[data-darkmode="true"] .action-item {
  color: #8e8e93;
}

.darkMode .contact-seller-btn,
page[data-darkmode="true"] .contact-seller-btn {
  color: #ff9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.darkMode .buy-btn,
page[data-darkmode="true"] .buy-btn {
  background-color: #ff9500;
}

.darkMode .buy-btn.disabled,
page[data-darkmode="true"] .buy-btn.disabled {
  background-color: #3a3a3c;
}

.darkMode .modal-content,
page[data-darkmode="true"] .modal-content {
  background-color: #2c2c2e;
}

.darkMode .modal-title,
page[data-darkmode="true"] .modal-title {
  color: #f5f5f7;
  border-color: #3a3a3c;
}

.darkMode .option-label,
.darkMode .buy-label,
page[data-darkmode="true"] .option-label,
page[data-darkmode="true"] .buy-label {
  color: #8e8e93;
}

.darkMode .option-value,
.darkMode .buy-value,
page[data-darkmode="true"] .option-value,
page[data-darkmode="true"] .buy-value {
  color: #f5f5f7;
}

.darkMode .option-btn,
page[data-darkmode="true"] .option-btn {
  color: #ff9500;
  background-color: rgba(255, 149, 0, 0.1);
  border-color: #ff9500;
}

.darkMode .full-btn,
page[data-darkmode="true"] .full-btn {
  background-color: #ff9500;
}

.darkMode .modal-close,
page[data-darkmode="true"] .modal-close {
  color: #8e8e93;
  border-color: #3a3a3c;
}

.darkMode .quantity-btn,
.darkMode .quantity-input,
.darkMode .remark-input,
page[data-darkmode="true"] .quantity-btn,
page[data-darkmode="true"] .quantity-input,
page[data-darkmode="true"] .remark-input {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .buy-item.total,
page[data-darkmode="true"] .buy-item.total {
  border-color: #3a3a3c;
}

.darkMode .buy-item.total .buy-value,
page[data-darkmode="true"] .buy-item.total .buy-value {
  color: #ff9500;
}

.darkMode .buy-notice,
page[data-darkmode="true"] .buy-notice {
  color: #8e8e93;
}

.darkMode .modal-btns,
page[data-darkmode="true"] .modal-btns {
  border-color: #3a3a3c;
}

.darkMode .modal-btn.cancel,
page[data-darkmode="true"] .modal-btn.cancel {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .modal-btn.confirm,
page[data-darkmode="true"] .modal-btn.confirm {
  background-color: #ff9500;
}
