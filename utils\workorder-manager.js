// utils/workorder-manager.js
// 工单管理器工具类，封装工单相关的数据操作

const reminderManager = require('./workorder-reminder');
const workorderApi = require('./workorder-api');

// 是否使用API模式（true: 使用后端API, false: 使用本地存储）
const USE_API = false;

// 是否启用离线模式（当API请求失败时，使用本地存储）
const ENABLE_OFFLINE_MODE = true;

/**
 * 获取工单列表
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 工单列表
 */
async function getWorkOrders(params = {}) {
  if (USE_API) {
    try {
      const response = await workorderApi.getWorkOrders(params);

      // 缓存工单数据到本地存储，用于离线模式
      if (ENABLE_OFFLINE_MODE) {
        try {
          wx.setStorageSync('workOrders', response.data || []);
        } catch (error) {
          console.error('缓存工单数据失败', error);
        }
      }

      return response.data || [];
    } catch (error) {
      console.error('获取工单列表失败', error);

      // 如果启用了离线模式，则使用本地存储的数据
      if (ENABLE_OFFLINE_MODE) {
        console.log('使用离线模式获取工单列表');
        return await getWorkOrdersFromStorage(params);
      }

      throw error;
    }
  } else {
    return await getWorkOrdersFromStorage(params);
  }
}

/**
 * 从本地存储获取工单列表
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 工单列表
 */
async function getWorkOrdersFromStorage(params = {}) {
  try {
    // 从本地存储获取工单数据
    let orders = wx.getStorageSync('workOrders') || [];

    // 应用筛选条件
    if (params.status) {
      orders = orders.filter(o => o.status === params.status);
    }

    if (params.type) {
      orders = orders.filter(o => o.type === params.type);
    }

    if (params.priority) {
      orders = orders.filter(o => o.priority === params.priority);
    }

    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      orders = orders.filter(o =>
        o.id.toLowerCase().includes(keyword) ||
        o.content.title.toLowerCase().includes(keyword) ||
        (o.reporter && o.reporter.name && o.reporter.name.toLowerCase().includes(keyword))
      );
    }

    // 应用排序
    if (params.sortBy) {
      switch (params.sortBy) {
        case 'createTime':
          orders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
          break;
        case 'priority':
          const priorityWeight = { 'high': 1, 'medium': 2, 'low': 3 };
          orders.sort((a, b) => priorityWeight[a.priority] - priorityWeight[b.priority]);
          break;
        case 'status':
          const statusPriority = { 'pending': 1, 'processing': 2, 'completed': 3, 'cancelled': 4 };
          orders.sort((a, b) => statusPriority[a.status] - statusPriority[b.status]);
          break;
      }
    }

    // 应用分页
    if (params.page && params.pageSize) {
      const start = (params.page - 1) * params.pageSize;
      const end = start + params.pageSize;
      orders = orders.slice(start, end);
    }

    return orders;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取工单详情
 * @param {string} orderId 工单ID
 * @returns {Promise<Object>} 工单详情
 */
async function getWorkOrderDetail(orderId) {
  if (USE_API) {
    try {
      const response = await workorderApi.getWorkOrderDetail(orderId);

      // 缓存工单详情到本地存储，用于离线模式
      if (ENABLE_OFFLINE_MODE) {
        try {
          const orders = wx.getStorageSync('workOrders') || [];
          const index = orders.findIndex(o => o.id === orderId);

          if (index !== -1) {
            orders[index] = response;
          } else {
            orders.push(response);
          }

          wx.setStorageSync('workOrders', orders);
        } catch (error) {
          console.error('缓存工单详情失败', error);
        }
      }

      return response;
    } catch (error) {
      console.error('获取工单详情失败', error);

      // 如果启用了离线模式，则使用本地存储的数据
      if (ENABLE_OFFLINE_MODE) {
        console.log('使用离线模式获取工单详情');
        return await getWorkOrderDetailFromStorage(orderId);
      }

      throw error;
    }
  } else {
    return await getWorkOrderDetailFromStorage(orderId);
  }
}

/**
 * 从本地存储获取工单详情
 * @param {string} orderId 工单ID
 * @returns {Promise<Object>} 工单详情
 */
async function getWorkOrderDetailFromStorage(orderId) {
  try {
    const orders = wx.getStorageSync('workOrders') || [];
    const order = orders.find(o => o.id === orderId);

    if (order) {
      return order;
    } else {
      throw new Error('工单不存在');
    }
  } catch (error) {
    throw error;
  }
}

/**
 * 切换工单置顶状态
 * @param {string} orderId 工单ID
 * @returns {Promise<void>}
 */
function togglePinOrder(orderId) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        orders[index].pinned = !orders[index].pinned;
        wx.setStorageSync('workOrders', orders);
        resolve();
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 取消工单
 * @param {string} orderId 工单ID
 * @returns {Promise<void>}
 */
function cancelOrder(orderId) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        if (orders[index].status === 'pending') {
          orders[index].status = 'cancelled';
          orders[index].statusName = '已取消';

          // 添加取消记录
          const cancelRecord = {
            time: new Date().toISOString().substring(0, 19),
            action: '取消工单',
            operator: '用户',
            remark: '用户主动取消工单'
          };

          if (!orders[index].processingRecords) {
            orders[index].processingRecords = [];
          }

          orders[index].processingRecords.push(cancelRecord);

          wx.setStorageSync('workOrders', orders);
          resolve();
        } else {
          reject(new Error('只能取消待处理的工单'));
        }
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 评价工单
 * @param {string} orderId 工单ID
 * @param {number} rating 评分（1-5星）
 * @param {string} comment 评价内容
 * @param {Array<string>} images 评价图片
 * @param {boolean} isAnonymous 是否匿名
 * @returns {Promise<void>}
 */
function evaluateOrder(orderId, rating, comment, images = [], isAnonymous = false) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        if (orders[index].status === 'completed') {
          orders[index].evaluation = {
            rating,
            comment,
            images,
            isAnonymous,
            time: new Date().toISOString().substring(0, 19)
          };

          // 添加评价记录
          const evaluateRecord = {
            time: new Date().toISOString().substring(0, 19),
            action: '用户评价',
            operator: isAnonymous ? '匿名用户' : '用户',
            remark: `评分: ${rating}星, 评价: ${comment}`
          };

          if (!orders[index].processingRecords) {
            orders[index].processingRecords = [];
          }

          orders[index].processingRecords.push(evaluateRecord);

          wx.setStorageSync('workOrders', orders);
          resolve();
        } else {
          reject(new Error('只能评价已完成的工单'));
        }
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 排序工单
 * @param {Array} orders 工单列表
 * @returns {Array} 排序后的工单列表
 */
function sortWorkOrders(orders) {
  // 定义状态优先级
  const statusPriority = {
    'pending': 1,      // 待处理最优先
    'processing': 2,   // 处理中次之
    'completed': 3,    // 已完成
    'cancelled': 4     // 已取消最后
  };

  // 定义优先级权重
  const priorityWeight = {
    'high': 1,    // 高优先级
    'medium': 2,  // 中优先级
    'low': 3      // 低优先级
  };

  // 复制数组，避免修改原数组
  const sortedOrders = [...orders];

  // 排序逻辑
  sortedOrders.sort((a, b) => {
    // 1. 首先按置顶状态排序
    if (a.pinned && !b.pinned) return -1;
    if (!a.pinned && b.pinned) return 1;

    // 2. 其次按状态优先级排序
    const statusDiff = statusPriority[a.status] - statusPriority[b.status];
    if (statusDiff !== 0) return statusDiff;

    // 3. 状态相同时，按工单优先级排序
    const priorityDiff = priorityWeight[a.priority] - priorityWeight[b.priority];
    if (priorityDiff !== 0) return priorityDiff;

    // 4. 优先级相同时，按创建时间排序（新的在前）
    return new Date(b.createTime) - new Date(a.createTime);
  });

  return sortedOrders;
}

/**
 * 创建模拟工单数据（仅用于开发测试）
 * @returns {Array} 模拟工单列表
 */
function createMockWorkOrders() {
  const mockOrders = [
    {
      id: 'WO20230501001',
      type: 'repair',
      typeName: '维修',
      status: 'pending',
      statusName: '待处理',
      priority: 'high',
      priorityName: '高',
      createTime: '2023-05-01T09:30:00',
      deadline: '2023-05-04T09:30:00',
      pinned: true,
      reporter: {
        name: '张三',
        phone: '13812341234',
        type: 'resident',
        role: 'owner',
        community: '阳光小区',
        building: '2栋',
        unit: '3单元',
        room: '502室'
      },
      content: {
        title: '空调不制冷...',
        description: '空调不制冷，可能是缺氟利昂',
        images: []
      },
      repairAddress: {
        type: 'my-address',
        value: '阳光小区 2栋 3单元 502室'
      },
      processingRecords: [
        {
          time: '2023-05-01T09:30:00',
          action: '工单创建',
          operator: '系统',
          remark: '工单已创建，等待处理'
        }
      ]
    },
    {
      id: 'WO20230502001',
      type: 'complaint',
      typeName: '投诉',
      status: 'processing',
      statusName: '处理中',
      priority: 'medium',
      priorityName: '中',
      createTime: '2023-05-02T14:20:00',
      deadline: '2023-05-05T14:20:00',
      pinned: false,
      reporter: {
        name: '张三',
        phone: '13812341234',
        type: 'resident',
        role: 'owner',
        community: '阳光小区',
        building: '2栋',
        unit: '3单元',
        room: '502室'
      },
      content: {
        title: '楼上噪音太大...',
        description: '楼上装修噪音太大，影响休息',
        images: []
      },
      repairAddress: {
        type: 'my-address',
        value: '阳光小区 2栋 3单元 502室'
      },
      processingRecords: [
        {
          time: '2023-05-02T14:20:00',
          action: '工单创建',
          operator: '系统',
          remark: '工单已创建，等待处理'
        },
        {
          time: '2023-05-02T15:30:00',
          action: '开始处理',
          operator: '物业管理员',
          remark: '已联系相关业主，协调处理'
        }
      ]
    },
    {
      id: 'WO20230503001',
      type: 'suggestion',
      typeName: '建议',
      status: 'completed',
      statusName: '已完成',
      priority: 'low',
      priorityName: '低',
      createTime: '2023-05-03T10:15:00',
      deadline: '2023-05-06T10:15:00',
      pinned: false,
      reporter: {
        name: '张三',
        phone: '13812341234',
        type: 'resident',
        role: 'owner',
        community: '阳光小区',
        building: '2栋',
        unit: '3单元',
        room: '502室'
      },
      content: {
        title: '建议增加健身设施...',
        description: '建议小区增加一些健身设施，方便居民锻炼',
        images: []
      },
      repairAddress: {
        type: 'public-area',
        value: '阳光小区 中心花园'
      },
      processingRecords: [
        {
          time: '2023-05-03T10:15:00',
          action: '工单创建',
          operator: '系统',
          remark: '工单已创建，等待处理'
        },
        {
          time: '2023-05-03T11:30:00',
          action: '开始处理',
          operator: '物业管理员',
          remark: '已记录建议，安排评估'
        },
        {
          time: '2023-05-05T09:45:00',
          action: '完成工单',
          operator: '物业管理员',
          remark: '已将建议纳入小区改造计划，将在下季度实施'
        }
      ],
      evaluation: {
        rating: 5,
        comment: '物业反馈很及时，感谢采纳建议',
        images: [],
        isAnonymous: false,
        time: '2023-05-05T10:20:00'
      }
    },
    {
      id: 'WO20230504001',
      type: 'other',
      typeName: '其他',
      status: 'cancelled',
      statusName: '已取消',
      priority: 'medium',
      priorityName: '中',
      createTime: '2023-05-04T16:40:00',
      deadline: '2023-05-07T16:40:00',
      pinned: false,
      reporter: {
        name: '张三',
        phone: '13812341234',
        type: 'resident',
        role: 'owner',
        community: '阳光小区',
        building: '2栋',
        unit: '3单元',
        room: '502室'
      },
      content: {
        title: '咨询物业费缴纳...',
        description: '想咨询一下物业费的缴纳方式和截止日期',
        images: []
      },
      repairAddress: {
        type: 'my-address',
        value: '阳光小区 2栋 3单元 502室'
      },
      processingRecords: [
        {
          time: '2023-05-04T16:40:00',
          action: '工单创建',
          operator: '系统',
          remark: '工单已创建，等待处理'
        },
        {
          time: '2023-05-04T17:15:00',
          action: '取消工单',
          operator: '用户',
          remark: '用户主动取消工单'
        }
      ]
    },
    {
      id: 'WO20230505001',
      type: 'repair',
      typeName: '维修',
      status: 'completed',
      statusName: '已完成',
      priority: 'high',
      priorityName: '高',
      createTime: '2023-05-05T08:20:00',
      deadline: '2023-05-08T08:20:00',
      pinned: false,
      reporter: {
        name: '张三',
        phone: '13812341234',
        type: 'resident',
        role: 'owner',
        community: '阳光小区',
        building: '2栋',
        unit: '3单元',
        room: '502室'
      },
      content: {
        title: '水管漏水...',
        description: '厨房水管漏水，需要紧急维修',
        images: []
      },
      repairAddress: {
        type: 'my-address',
        value: '阳光小区 2栋 3单元 502室'
      },
      processingRecords: [
        {
          time: '2023-05-05T08:20:00',
          action: '工单创建',
          operator: '系统',
          remark: '工单已创建，等待处理'
        },
        {
          time: '2023-05-05T09:00:00',
          action: '开始处理',
          operator: '物业管理员',
          remark: '已安排维修人员前往处理'
        },
        {
          time: '2023-05-05T10:30:00',
          action: '完成工单',
          operator: '维修人员',
          remark: '水管已修复，漏水问题已解决'
        }
      ]
    }
  ];

  return mockOrders;
}

/**
 * 初始化模拟数据（仅用于开发测试）
 */
function initMockData() {
  try {
    // 检查是否已有工单数据
    const existingOrders = wx.getStorageSync('workOrders');
    if (!existingOrders || existingOrders.length === 0) {
      // 创建模拟数据并保存
      const mockOrders = createMockWorkOrders();
      wx.setStorageSync('workOrders', mockOrders);
      console.log('已初始化模拟工单数据');
    }

    // 初始化提醒数据
    reminderManager.initReminders();
  } catch (error) {
    console.error('初始化模拟数据失败', error);
  }
}

/**
 * 接单并开始处理工单
 * @param {string} orderId 工单ID
 * @param {string} remark 处理备注
 * @returns {Promise<Object>} 更新后的工单
 */
async function processOrder(orderId, remark) {
  if (USE_API) {
    try {
      const response = await workorderApi.processOrder(orderId, remark);

      // 缓存工单详情到本地存储，用于离线模式
      if (ENABLE_OFFLINE_MODE) {
        try {
          const orders = wx.getStorageSync('workOrders') || [];
          const index = orders.findIndex(o => o.id === orderId);

          if (index !== -1) {
            orders[index] = response;
          }

          wx.setStorageSync('workOrders', orders);
        } catch (error) {
          console.error('缓存工单详情失败', error);
        }
      }

      // 创建状态变更通知
      try {
        await reminderManager.createStatusChangeNotification(
          response.id,
          response.content.title,
          'pending', // 假设之前是待处理状态
          response.status,
          '物业管理员'
        );
      } catch (error) {
        console.error('创建状态变更通知失败', error);
      }

      // 如果有截止时间，创建截止时间提醒
      if (response.deadline) {
        try {
          await reminderManager.createDeadlineReminder(
            response.id,
            response.content.title,
            response.deadline,
            60 // 提前60分钟提醒
          );
        } catch (error) {
          console.error('创建截止时间提醒失败', error);
        }
      }

      return response;
    } catch (error) {
      console.error('处理工单失败', error);

      // 如果启用了离线模式，则使用本地存储的数据
      if (ENABLE_OFFLINE_MODE) {
        console.log('使用离线模式处理工单');
        return await processOrderFromStorage(orderId, remark);
      }

      throw error;
    }
  } else {
    return await processOrderFromStorage(orderId, remark);
  }
}

/**
 * 从本地存储处理工单
 * @param {string} orderId 工单ID
 * @param {string} remark 处理备注
 * @returns {Promise<Object>} 更新后的工单
 */
async function processOrderFromStorage(orderId, remark) {
  try {
    const orders = wx.getStorageSync('workOrders') || [];
    const index = orders.findIndex(o => o.id === orderId);

    if (index !== -1) {
      if (orders[index].status === 'pending') {
        // 保存旧状态
        const oldStatus = orders[index].status;

        // 更新工单状态
        orders[index].status = 'processing';
        orders[index].statusName = '处理中';

        // 添加处理记录
        const processRecord = {
          time: new Date().toISOString().substring(0, 19),
          action: '开始处理',
          operator: '物业管理员',
          remark: remark || '已开始处理工单'
        };

        if (!orders[index].processingRecords) {
          orders[index].processingRecords = [];
        }

        orders[index].processingRecords.push(processRecord);

        wx.setStorageSync('workOrders', orders);

        // 创建状态变更通知
        try {
          await reminderManager.createStatusChangeNotification(
            orders[index].id,
            orders[index].content.title,
            oldStatus,
            orders[index].status,
            '物业管理员'
          );
        } catch (error) {
          console.error('创建状态变更通知失败', error);
        }

        // 如果有截止时间，创建截止时间提醒
        if (orders[index].deadline) {
          try {
            await reminderManager.createDeadlineReminder(
              orders[index].id,
              orders[index].content.title,
              orders[index].deadline,
              60 // 提前60分钟提醒
            );
          } catch (error) {
            console.error('创建截止时间提醒失败', error);
          }
        }

        return orders[index];
      } else {
        throw new Error('只能处理待处理状态的工单');
      }
    } else {
      throw new Error('工单不存在');
    }
  } catch (error) {
    throw error;
  }
}

/**
 * 分配工单给指定员工
 * @param {string} orderId 工单ID
 * @param {Object} staff 员工信息
 * @param {string} remark 分配备注
 * @returns {Promise<Object>} 更新后的工单
 */
function assignOrder(orderId, staff, remark) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        if (orders[index].status === 'pending' || orders[index].status === 'processing') {
          // 更新工单状态
          orders[index].status = 'processing';
          orders[index].statusName = '处理中';
          orders[index].assignedTo = staff;

          // 添加处理记录
          const assignRecord = {
            time: new Date().toISOString().substring(0, 19),
            action: '分配工单',
            operator: '物业管理员',
            remark: remark || `已分配给${staff.name}(${staff.position})`
          };

          if (!orders[index].processingRecords) {
            orders[index].processingRecords = [];
          }

          orders[index].processingRecords.push(assignRecord);

          wx.setStorageSync('workOrders', orders);
          resolve(orders[index]);
        } else {
          reject(new Error('只能分配待处理或处理中状态的工单'));
        }
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 完成工单
 * @param {string} orderId 工单ID
 * @param {string} result 处理结果
 * @param {Array<string>} images 处理图片
 * @param {string} remark 完成备注
 * @returns {Promise<Object>} 更新后的工单
 */
function completeOrder(orderId, result, images = [], remark) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        if (orders[index].status === 'processing') {
          // 保存旧状态
          const oldStatus = orders[index].status;

          // 更新工单状态
          orders[index].status = 'completed';
          orders[index].statusName = '已完成';
          orders[index].completeResult = result;
          orders[index].completeImages = images;

          // 添加处理记录
          const completeRecord = {
            time: new Date().toISOString().substring(0, 19),
            action: '完成工单',
            operator: '物业管理员',
            remark: remark || result
          };

          if (!orders[index].processingRecords) {
            orders[index].processingRecords = [];
          }

          orders[index].processingRecords.push(completeRecord);

          wx.setStorageSync('workOrders', orders);

          // 创建状态变更通知
          reminderManager.createStatusChangeNotification(
            orders[index].id,
            orders[index].content.title,
            oldStatus,
            orders[index].status,
            '物业管理员'
          ).catch(error => {
            console.error('创建状态变更通知失败', error);
          });

          resolve(orders[index]);
        } else {
          reject(new Error('只能完成处理中状态的工单'));
        }
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 添加工单处理记录
 * @param {string} orderId 工单ID
 * @param {string} action 处理动作
 * @param {string} remark 处理备注
 * @returns {Promise<Object>} 更新后的工单
 */
function addProcessingRecord(orderId, action, remark) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        // 添加处理记录
        const record = {
          time: new Date().toISOString().substring(0, 19),
          action: action,
          operator: '物业管理员',
          remark: remark
        };

        if (!orders[index].processingRecords) {
          orders[index].processingRecords = [];
        }

        orders[index].processingRecords.push(record);

        wx.setStorageSync('workOrders', orders);
        resolve(orders[index]);
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 获取工单统计数据
 * @param {string} timeRange 时间范围（week-周, month-月, quarter-季度, year-年）
 * @returns {Promise<Object>} 统计数据
 */
function getOrderStatistics(timeRange = 'week') {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];

      // 计算各状态工单数量
      const statusCounts = {
        total: orders.length,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0
      };

      orders.forEach(order => {
        if (statusCounts[order.status] !== undefined) {
          statusCounts[order.status]++;
        }
      });

      // 计算工单类型分布
      const typeDistribution = {};
      orders.forEach(order => {
        if (!typeDistribution[order.type]) {
          typeDistribution[order.type] = {
            count: 0,
            name: order.typeName
          };
        }
        typeDistribution[order.type].count++;
      });

      // 转换为百分比
      const typePercentages = {};
      Object.keys(typeDistribution).forEach(type => {
        typePercentages[type] = {
          name: typeDistribution[type].name,
          percentage: Math.round(typeDistribution[type].count / orders.length * 100)
        };
      });

      // 模拟工单趋势数据
      let trendLabels = [];
      let trendData = [];

      if (timeRange === 'week') {
        trendLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        trendData = [5, 8, 7, 10, 6, 4, 2];
      } else if (timeRange === 'month') {
        trendLabels = ['第1周', '第2周', '第3周', '第4周'];
        trendData = [35, 42, 38, 41];
      } else if (timeRange === 'quarter') {
        trendLabels = ['1月', '2月', '3月'];
        trendData = [120, 135, 148];
      } else if (timeRange === 'year') {
        trendLabels = ['一季度', '二季度', '三季度', '四季度'];
        trendData = [350, 420, 380, 410];
      }

      // 返回统计数据
      resolve({
        statusCounts,
        typeDistribution: typePercentages,
        trend: {
          labels: trendLabels,
          data: trendData
        },
        timeEfficiency: {
          avgResponseTime: '2.5h',
          avgProcessingTime: '6.8h',
          onTimeRate: '92%'
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 批量分配工单
 * @param {Array<string>} orderIds 工单ID数组
 * @param {Object} staff 员工信息
 * @param {string} remark 分配备注
 * @returns {Promise<Array<Object>>} 更新后的工单数组
 */
function batchAssignOrders(orderIds, staff, remark) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const updatedOrders = [];
      const failedOrders = [];

      // 遍历工单ID数组
      orderIds.forEach(orderId => {
        const index = orders.findIndex(o => o.id === orderId);

        if (index !== -1) {
          if (orders[index].status === 'pending' || orders[index].status === 'processing') {
            // 更新工单状态
            orders[index].status = 'processing';
            orders[index].statusName = '处理中';
            orders[index].assignedTo = staff;

            // 添加处理记录
            const assignRecord = {
              time: new Date().toISOString().substring(0, 19),
              action: '批量分配',
              operator: '物业管理员',
              remark: remark || `已分配给${staff.name}(${staff.position})`
            };

            if (!orders[index].processingRecords) {
              orders[index].processingRecords = [];
            }

            orders[index].processingRecords.push(assignRecord);

            updatedOrders.push(orders[index]);
          } else {
            failedOrders.push({
              id: orderId,
              reason: '只能分配待处理或处理中状态的工单'
            });
          }
        } else {
          failedOrders.push({
            id: orderId,
            reason: '工单不存在'
          });
        }
      });

      // 保存更新后的工单
      wx.setStorageSync('workOrders', orders);

      resolve({
        success: updatedOrders,
        failed: failedOrders
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 批量完成工单
 * @param {Array<string>} orderIds 工单ID数组
 * @param {string} result 处理结果
 * @param {string} remark 完成备注
 * @returns {Promise<Array<Object>>} 更新后的工单数组
 */
function batchCompleteOrders(orderIds, result, remark) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const updatedOrders = [];
      const failedOrders = [];

      // 遍历工单ID数组
      orderIds.forEach(orderId => {
        const index = orders.findIndex(o => o.id === orderId);

        if (index !== -1) {
          if (orders[index].status === 'processing') {
            // 更新工单状态
            orders[index].status = 'completed';
            orders[index].statusName = '已完成';
            orders[index].completeResult = result;

            // 添加处理记录
            const completeRecord = {
              time: new Date().toISOString().substring(0, 19),
              action: '批量完成',
              operator: '物业管理员',
              remark: remark || result
            };

            if (!orders[index].processingRecords) {
              orders[index].processingRecords = [];
            }

            orders[index].processingRecords.push(completeRecord);

            updatedOrders.push(orders[index]);
          } else {
            failedOrders.push({
              id: orderId,
              reason: '只能完成处理中状态的工单'
            });
          }
        } else {
          failedOrders.push({
            id: orderId,
            reason: '工单不存在'
          });
        }
      });

      // 保存更新后的工单
      wx.setStorageSync('workOrders', orders);

      resolve({
        success: updatedOrders,
        failed: failedOrders
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 设置工单截止时间
 * @param {string} orderId 工单ID
 * @param {string} deadlineTime 截止时间
 * @returns {Promise<Object>} 更新后的工单
 */
function setOrderDeadline(orderId, deadlineTime) {
  return new Promise((resolve, reject) => {
    try {
      const orders = wx.getStorageSync('workOrders') || [];
      const index = orders.findIndex(o => o.id === orderId);

      if (index !== -1) {
        // 更新截止时间
        orders[index].deadline = deadlineTime;

        // 添加处理记录
        const record = {
          time: new Date().toISOString().substring(0, 19),
          action: '设置截止时间',
          operator: '物业管理员',
          remark: `设置工单截止时间为${deadlineTime}`
        };

        if (!orders[index].processingRecords) {
          orders[index].processingRecords = [];
        }

        orders[index].processingRecords.push(record);

        wx.setStorageSync('workOrders', orders);

        // 创建截止时间提醒
        reminderManager.createDeadlineReminder(
          orders[index].id,
          orders[index].content.title,
          deadlineTime,
          60 // 提前60分钟提醒
        ).catch(error => {
          console.error('创建截止时间提醒失败', error);
        });

        resolve(orders[index]);
      } else {
        reject(new Error('工单不存在'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 检查并发送工单提醒
 * @returns {Promise<Array>} 发送的提醒列表
 */
function checkAndSendReminders() {
  return reminderManager.checkAndSendReminders();
}

module.exports = {
  getWorkOrders,
  getWorkOrderDetail,
  togglePinOrder,
  cancelOrder,
  evaluateOrder,
  sortWorkOrders,
  initMockData,
  processOrder,
  assignOrder,
  completeOrder,
  addProcessingRecord,
  getOrderStatistics,
  batchAssignOrders,
  batchCompleteOrders,
  setOrderDeadline,
  checkAndSendReminders
};
