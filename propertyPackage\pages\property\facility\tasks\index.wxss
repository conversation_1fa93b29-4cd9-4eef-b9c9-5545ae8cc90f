/* 巡检任务列表页样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 顶部区域 */
.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 2rpx solid #ff8c00;
}

.user-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.user-status.online {
  background-color: #4CAF50;
}

.user-status.offline {
  background-color: #9e9e9e;
}

.user-details {
  flex: 1;
}

.user-greeting-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-greeting {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.user-id {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.user-position {
  font-size: 24rpx;
  color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.user-progress-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.user-progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #f0f0f0;
  border-radius: 5rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #ff8c00;
  border-radius: 5rpx;
}

.user-task-reminder {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #ff8c00;
}

.reminder-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M13.73 21a2 2 0 0 1-3.46 0' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 10rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21L16.65 16.65M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11Z' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 统计卡片 */
.statistics-card {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.statistics-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #ff8c00, #ffb74d);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  padding: 16rpx 0;
  transition: all 0.3s ease;
}

.stat-item::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: #eee;
}

.stat-item:last-child::after {
  display: none;
}

.stat-item-hover {
  background-color: rgba(255, 140, 0, 0.1);
  border-radius: 8rpx;
}

.stat-value-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  height: 80rpx;
}

.stat-value {
  font-size: 60rpx;
  font-weight: 700;
  color: #ff8c00;
  text-shadow: 0 1rpx 2rpx rgba(255, 140, 0, 0.2);
  transform: scale(1);
  transition: transform 0.2s ease;
  line-height: 1;
}

.stat-item:active .stat-value {
  transform: scale(1.1);
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  position: relative;
  font-weight: 500;
  margin-top: 4rpx;
}

.stat-label::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2rpx;
  background-color: #ff8c00;
  transition: width 0.3s ease;
}

.stat-item:active .stat-label::after {
  width: 60%;
}

/* 任务类型标签栏 */
.task-type-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 2rpx;
}

.task-type-tab {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.task-type-tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.task-type-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 任务状态标签栏 */
.task-status-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-status-tab {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.task-status-tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.task-status-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 任务列表 */
.task-list {
  padding: 0 30rpx;
}

.task-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-title-row {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.task-priority {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}

.task-priority.high {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.task-priority.medium {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.task-priority.low {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.task-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.task-type.inspection {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.task-type.maintenance {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.task-info {
  margin-bottom: 20rpx;
}

.task-info-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.task-info-item:last-child {
  margin-bottom: 0;
}

.calendar-icon, .location-icon, .facility-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.calendar-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.location-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.facility-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.task-status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.task-status.pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.task-status.processing {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.task-status.completed {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.task-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  width: 200rpx;
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #2196F3;
  border-radius: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #2196F3;
}

.task-result {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 24rpx;
  color: #666;
}

.result-container {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
}

.result-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-icon.normal {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14' stroke='%234CAF50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M22 4L12 14.01l-3-3' stroke='%234CAF50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.result-icon.abnormal {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z' stroke='%23F44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 9v4' stroke='%23F44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 17h.01' stroke='%23F44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.result-text {
  color: #4CAF50;
}

.result-text.abnormal {
  color: #F44336;
}

.view-detail {
  margin-left: 10rpx;
  color: #2196F3;
  text-decoration: underline;
}

.task-action {
  display: flex;
}

.execute-button, .continue-button {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.execute-button {
  background-color: #ff8c00;
  color: #fff;
  border: 1rpx solid #ff8c00;
}

.continue-button {
  background-color: #2196F3;
  color: #fff;
  border: 1rpx solid #2196F3;
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.execute-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 3L19 12L5 21V3Z' fill='white' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.execute-icon.inspection {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11l3 3L22 4' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.execute-icon.maintenance {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' fill='white'/%3E%3C/svg%3E");
}

.continue-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 3L19 12L5 21V3Z' fill='white' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* 添加悬停效果 */
.execute-button:active, .continue-button:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .header {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .user-info-card {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .user-avatar {
  border-color: #ff8c00;
}

.darkMode .user-greeting {
  color: #8e8e93;
}

.darkMode .user-name {
  color: #f5f5f7;
}

.darkMode .user-id {
  color: #8e8e93;
}

.darkMode .user-position {
  color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.15);
}

.darkMode .progress-text {
  color: #8e8e93;
}

.darkMode .user-progress-bar {
  background-color: #3a3a3c;
}

.darkMode .user-task-reminder {
  color: #ff9f40;
}

.darkMode .reminder-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9' stroke='%23ff9f40' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M13.73 21a2 2 0 0 1-3.46 0' stroke='%23ff9f40' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.darkMode .search-input-wrap {
  background-color: #3a3a3c;
}

.darkMode .search-input {
  color: #f5f5f7;
}

.darkMode .statistics-card {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .stat-label {
  color: #8e8e93;
}

.darkMode .task-type-tabs,
.darkMode .task-status-tabs {
  background-color: #2c2c2e;
}

.darkMode .task-status-tabs {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .task-type-tab,
.darkMode .task-status-tab {
  color: #8e8e93;
}

.darkMode .task-card {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .task-title {
  color: #f5f5f7;
}

.darkMode .task-info-item {
  color: #8e8e93;
}

.darkMode .progress-bar {
  background-color: #3a3a3c;
}

.darkMode .task-result {
  color: #8e8e93;
}

.darkMode .execute-button {
  background-color: #ff8c00;
  border-color: #ff8c00;
}

.darkMode .continue-button {
  background-color: #2196F3;
  border-color: #2196F3;
}
