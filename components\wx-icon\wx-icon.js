// components/wx-icon/wx-icon.js
Component({
  properties: {
    name: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: '#007AFF'
    },
    size: {
      type: Number,
      value: 24
    },
    bgColor: {
      type: String,
      value: '#f0f7ff'
    }
  },
  data: {
    iconType: ''
  },
  lifetimes: {
    attached: function() {
      this.updateIcon();
    }
  },
  observers: {
    'name': function() {
      this.updateIcon();
    }
  },
  methods: {
    updateIcon: function() {
      const name = this.data.name;
      let iconType = 'info';
      
      // 根据图标名称设置微信内置图标类型
      switch(name) {
        // 消息图标
        case 'message-repair':
          iconType = 'edit';
          break;
        case 'message-water':
          iconType = 'location';
          break;
        case 'message-facility':
          iconType = 'info';
          break;
          
        // 快捷服务图标
        case 'payment':
          iconType = 'success';
          break;
        case 'repair':
          iconType = 'warn';
          break;
        case 'vote':
          iconType = 'success';
          break;
        case 'goods':
          iconType = 'success';
          break;
        case 'visitor':
          iconType = 'info';
          break;
        case 'garbage':
          iconType = 'info';
          break;
        case 'service':
          iconType = 'info';
          break;
        case 'renovation':
          iconType = 'info';
          break;
        default:
          iconType = 'info';
      }
      
      this.setData({
        iconType: iconType
      });
    }
  }
})
