<!-- 实名认证页面 -->
<view class="container">
  <!-- 数据加载状态 -->
  <view class="loading-overlay" wx:if="{{dataLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText || '正在加载...'}}</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view scroll-y class="content-area">
    <!-- 进度条 -->
    <!-- <view class="card progress-card">
      <view class="card-title">
        <text class="icon-check"></text>
        <text>认证进度</text>
      </view>
      <view class="progress-track">
        <view class="progress-steps">
          <view class="step {{currentStep >= 1 ? 'active' : ''}} {{currentStep > 1 ? 'completed' : ''}}">
            <view class="step-dot">1</view>
            <view class="step-text">填写信息</view>
          </view>
          <view class="step {{currentStep >= 2 ? 'active' : ''}} {{currentStep > 2 ? 'completed' : ''}}">
            <view class="step-dot">2</view>
            <view class="step-text">审核中</view>
          </view>
          <view class="step {{currentStep >= 3 ? 'active' : ''}}">
            <view class="step-dot">3</view>
            <view class="step-text">认证完成</view>
          </view>
        </view>
      </view>
    </view> -->

    <!-- 表单区域 -->
    <view class="card form-card">
      <!-- 标题 -->
      <!-- <view class="form-title">
        <text class="icon-user"></text>
        <text>实名认证</text>
      </view> -->

      <!-- 实名认证表单 -->
      <view class="form-slide active" id="residentForm">

        <!-- 认证方式选择 - 横向布局 -->
        <!-- <view class="form-item" wx:if="{{showResidentAuth && showPropertyAuth}}">
          <view class="auth-method-selector-horizontal">
            <view class="auth-method-horizontal {{authMethod === 'resident' ? 'active' : ''}}" wx:if="{{showResidentAuth}}" bindtap="switchAuthMethod" data-method="resident">
              <view class="auth-method-text">
                <view class="auth-method-title">实名认证</view>
              </view>
            </view>
            <view class="auth-method-horizontal {{authMethod === 'property' ? 'active' : ''}}" wx:if="{{showPropertyAuth}}" bindtap="switchAuthMethod" data-method="property">
              <view class="auth-method-text">
                <view class="auth-method-title">物业员工认证</view>
              </view>
            </view>
          </view>
        </view> -->

        <!-- 单一认证类型标题 -->
        <!-- <view class="form-item" wx:if="{{!showResidentAuth || !showPropertyAuth}}">
          <view class="single-auth-title">
            <text wx:if="{{showResidentAuth}}">实名认证</text>
            <text wx:if="{{showPropertyAuth}}">物业员工认证</text>
          </view>
        </view> -->

        <!-- 实名认证表单 -->
        <view wx:if="{{authMethod === 'resident'}}">
          <!-- 姓名 -->
          <view class="form-item">
            <view class="form-label">
              <text>姓名</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{nameValid ? 'valid' : nameError ? 'error' : ''}}">
              <input type="text" placeholder="请输入您的真实姓名" value="{{name}}" bindinput="onNameInput" bindblur="validateName" />
              <view class="check-icon" wx:if="{{nameValid}}"></view>
            </view>
          </view>

          <!-- 手机号码 -->
          <view class="form-item">
            <view class="form-label">
              <text>手机号码</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{phoneValid ? 'valid' : phoneError ? 'error' : ''}}">
              <input type="number" placeholder="请输入您的手机号码" value="{{phone}}" bindinput="onPhoneInput" bindblur="validatePhone" />
              <view class="check-icon" wx:if="{{phoneValid}}"></view>
            </view>
          </view>

          <!-- 验证码 -->
          <view class="form-item" wx:if="{{!hasAuth || (pageMode === 'edit' && needVerifyCode)}}">
            <view class="form-label">
              <text>验证码</text>
              <text class="required">*</text>
            </view>
            <view class="form-input form-input-with-btn {{verifyCodeValid ? 'valid' : verifyCodeError ? 'error' : ''}}">
              <input type="number" placeholder="请输入短信验证码" value="{{verifyCode}}" bindinput="onVerifyCodeInput" bindblur="validateVerifyCode" maxlength="6" />
              <view class="verify-code-btn-inside {{codeSent ? 'sent' : ''}}" bindtap="sendVerifyCode" disabled="{{!phoneValid || codeSent}}">
                {{codeSent ? countDown + 's' : '获取验证码'}}
              </view>
              <view class="check-icon" wx:if="{{verifyCodeValid}}"></view>
            </view>
          </view>

          <!-- 身份证号码 -->
          <!-- <view class="form-item">
            <view class="form-label">
              <text>身份证号码</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{idCardValid ? 'valid' : idCardError ? 'error' : ''}}">
              <input type="idcard" placeholder="请输入您的身份证号码" value="{{idCard}}" bindinput="onIdCardInput" bindblur="validateIdCard" />
              <view class="check-icon" wx:if="{{idCardValid}}"></view>
            </view>
          </view> -->
        </view>

        <!-- 物业员工认证表单 -->
        <view wx:if="{{authMethod === 'property'}}">
          <!-- 物业员工基本信息标题 -->
          <view class="form-section-title">
            <text class="icon-property"></text>
            <text>物业员工基本信息</text>
          </view>

          <!-- 姓名 -->
          <view class="form-item">
            <view class="form-label">
              <text>姓名</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{nameValid ? 'valid' : nameError ? 'error' : ''}}">
              <input type="text" placeholder="员工名字" value="{{name}}" bindinput="onNameInput" bindblur="validateName" />
              <view class="check-icon" wx:if="{{nameValid}}"></view>
            </view>
            <!-- <view class="form-tip">请填写与您身份证上一致的姓名</view> -->
          </view>

          <!-- 手机号码 -->
          <view class="form-item">
            <view class="form-label">
              <text>手机号码</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{phoneValid ? 'valid' : phoneError ? 'error' : ''}}">
              <input type="number" placeholder="请输入手机号码" value="{{phone}}" bindinput="onPhoneInput" bindblur="validatePhone" />
              <view class="check-icon" wx:if="{{phoneValid}}"></view>
            </view>
            <!-- <view class="form-tip">用于接收认证结果通知</view> -->
          </view>

          <!-- 证件类型 -->
          <view class="form-item">
            <view class="form-label">
              <text>证件类型</text>
              <text class="required">*</text>
            </view>
            <picker bindchange="onCertificateTypeChange" value="{{certificateTypeIndex}}" range="{{certificateTypes}}" range-key="nameCn">
              <view class="form-selector {{certificateTypeValid ? 'valid' : certificateTypeError ? 'error' : ''}}">
                <text class="{{certificateType ? '' : 'placeholder'}}">{{certificateTypeIndex >= 0 ? certificateTypes[certificateTypeIndex].nameCn : '请选择证件类型'}}</text>
                <text class="arrow-icon">▼</text>
              </view>
            </picker>
            <view class="form-tip" wx:if="{{certificateTypeError}}">请选择证件类型</view>
          </view>

          <!-- 证件号码 -->
          <view class="form-item">
            <view class="form-label">
              <text>证件号码</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{idCardValid ? 'valid' : idCardError ? 'error' : ''}}">
              <input type="text" placeholder="请输入证件号码" value="{{idCard}}" bindinput="onIdCardInput" bindblur="validateIdCard" />
              <view class="check-icon" wx:if="{{idCardValid}}"></view>
            </view>
            <!-- <view class="form-tip">用于验证您的身份信息</view> -->
          </view>

          <!-- 员工编号 -->
          <view class="form-item">
            <view class="form-label">
              <text>员工编号</text>
              <text class="required">*</text>
            </view>
            <view class="form-input {{employeeIdValid ? 'valid' : employeeIdError ? 'error' : ''}}">
              <input type="text" placeholder="请输入员工编号" value="{{employeeId}}" bindinput="onEmployeeIdInput" bindblur="validateEmployeeId" />
              <view class="check-icon" wx:if="{{employeeIdValid}}"></view>
            </view>
            <!-- <view class="form-tip">请填写与公司分配的员工编号</view> -->
          </view>

          <!-- 所属部门 -->
          <view class="form-item">
            <view class="form-label">
              <text>所属部门</text>
              <text class="required">*</text>
            </view>
            <view class="form-selector {{departmentValid ? 'valid' : departmentError ? 'error' : ''}}" bindtap="showDepartmentPicker">
              <text class="{{department ? '' : 'placeholder'}}">{{department || '请选择您所属的部门'}}</text>
              <text class="arrow-icon">▼</text>
            </view>
            <!-- <view class="form-tip">例如：客服部、工程部、保安部等</view> -->
          </view>

          <!-- 职位 -->
          <view class="form-item">
            <view class="form-label">
              <text>职位</text>
              <text class="required">*</text>
            </view>
            <view class="form-selector {{positionValid ? 'valid' : positionError ? 'error' : ''}}" bindtap="showPositionPicker">
              <text class="{{position ? '' : 'placeholder'}}">{{position || '请选择您的职位'}}</text>
              <text class="arrow-icon">▼</text>
            </view>
            <!-- <view class="form-tip">例如：客服专员、维修工程师、保安队长等</view> -->
          </view>

          <!-- 工作证照片 -->
          <view class="form-item">
            <view class="form-label">
              <text>工作证照片</text>
              <text class="required">*</text>
            </view>
            <view class="work-card-upload-container">
              <view wx:if="{{!workCardPhotoPath}}" class="work-card-upload-area" bindtap="chooseWorkCardPhoto">
                <view class="work-card-upload-content">
                  <view class="work-card-upload-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="6" y="10" width="36" height="28" rx="2" stroke="#D1D5DB" stroke-width="2" fill="none"/>
                      <path d="M18 18L30 30M30 18L18 30" stroke="#D1D5DB" stroke-width="2" stroke-linecap="round"/>
                      <circle cx="24" cy="24" r="8" stroke="#FF9500" stroke-width="2" fill="none"/>
                      <path d="M24 20V28M20 24H28" stroke="#FF9500" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                  </view>
                  <view class="work-card-upload-text">点击上传工作证照片</view>
                  <view class="work-card-upload-tip">支持 JPG、PNG 格式，大小不超过 5MB</view>
                </view>
              </view>
              <view wx:else class="work-card-upload-with-image">
                <image class="work-card-uploaded-image" src="{{workCardPhotoPath}}" mode="aspectFit"></image>
                <view class="work-card-upload-overlay">
                  <view class="work-card-upload-actions">
                    <view class="work-card-upload-action" bindtap="chooseWorkCardPhoto">
                      <view class="work-card-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="white" stroke-width="2"/>
                          <circle cx="8.5" cy="8.5" r="1.5" stroke="white" stroke-width="2"/>
                          <path d="M21 15L16 10L5 21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </view>
                      <text class="work-card-action-text">重新选择</text>
                    </view>
                    <view class="work-card-upload-action" bindtap="removeWorkCardPhoto">
                      <view class="work-card-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <polyline points="3,6 5,6 21,6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M19,6V20a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6M8,6V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2V6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <line x1="10" y1="11" x2="10" y2="17" stroke="white" stroke-width="2" stroke-linecap="round"/>
                          <line x1="14" y1="11" x2="14" y2="17" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                      </view>
                      <text class="work-card-action-text">删除照片</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view class="form-tip">请上传您的工作证正面照片</view>
          </view>
        </view>

        <!-- 房屋选择 - 已移除 -->
        <!-- 根据需求，无论线上还是线下认证，都移除"我的房屋"这一项 -->
        <!-- <view class="form-item" wx:if="{{!hasAuth}}">
          <view class="form-label">
            <text>是否需要使用门禁开关</text>
       
            <view class="siwtch-btn">
            <switch checked="{{isChecked}}" bindchange="switchChange" />
          </view>
          </view>
       
        </view> -->

        <!-- 照片上传 - 仅线上认证时显示 -->
        <view class="form-item" wx:if="{{isChecked}}">
          <view class="form-label">
            <text>人脸照片</text>
            <text class="required">*</text>

          </view>
          <view class="upload-container">
            <view wx:if="{{!residentPhotoPath}}" class="upload-area-empty">
              <view class="upload-text">人脸照片</view>
              <view class="upload-placeholder">
                <!-- 移除箭头图标 -->
                <view class="upload-options">
                  <view class="upload-option" bindtap="chooseFromAlbum">
                    <view class="upload-option-icon album-icon"></view>
                    <view class="upload-option-text">从相册选择</view>
                  </view>
                  <view class="upload-option-divider"></view>
                  <view class="upload-option" bindtap="takePhoto">
                    <view class="upload-option-icon camera-icon"></view>
                    <view class="upload-option-text">拍照上传</view>
                  </view>
                </view>
              </view>
              <view class="upload-tip">支持 JPG、PNG 格式，大小不超过 5MB</view>
            </view>
            <view wx:else class="upload-area-with-image">
              <image class="uploaded-image" src="{{residentPhotoPath}}" mode="aspectFill"></image>
              <view class="upload-actions">
                <view class="upload-action" bindtap="chooseFromAlbum">
                  <view class="upload-action-icon album-icon"></view>
                  <view class="upload-action-text">重新选择</view>
                </view>
                <view class="upload-action" bindtap="takePhoto">
                  <view class="upload-action-icon camera-icon"></view>
                  <view class="upload-action-text">重新拍照</view>
                </view>
                <view class="upload-action" bindtap="removePhoto">
                  <view class="upload-action-icon delete-icon"></view>
                  <view class="upload-action-text">删除照片</view>
                </view>
              </view>
            </view>
          </view>

        </view>

        <!-- 线下认证预约时间 - 仅线下认证时显示 -->
        <!-- <view class="form-item" wx:if="{{authMethod === 'offline'}}">
          <view class="form-label">
            <text>预约时间</text>
            <text class="required">*</text>
          </view>
          <view class="form-selector {{appointmentTime ? 'valid' : appointmentError ? 'error' : ''}}" bindtap="showAppointmentSelector">
            <text class="{{appointmentTime ? '' : 'placeholder'}}">{{appointmentTime || '请选择预约时间'}}</text>
            <text class="arrow-icon">▼</text>
          </view>
          <view class="form-tip">请选择前往物业管理处办理认证的时间</view>
        </view> -->

        <!-- 线下认证备注 - 仅线下认证时显示 -->
        <!-- <view class="form-item" wx:if="{{authMethod === 'offline'}}">
          <view class="form-label">
            <text>备注信息</text>
            <text class="optional">（选填）</text>
          </view>
          <view class="form-textarea">
            <textarea placeholder="请输入其他需要说明的信息" value="{{appointmentRemark}}" bindinput="onAppointmentRemarkInput" maxlength="200"></textarea>
            <view class="textarea-counter">{{appointmentRemark.length}}/200</view>
          </view>
          <view class="form-tip">如有特殊需求，请在此说明</view>
        </view> -->
      </view>



      <!-- 提交按钮 -->
      <view class="submit-buttons">
        <!-- 认证模式按钮 -->
        <button wx:if="{{pageMode === 'auth'}}" class="submit-btn" bindtap="submitForm" loading="{{submitting}}">
          {{submitting ? '提交中...' : '提交登记'}}
        </button>

        <!-- 编辑模式按钮组 - 左右排列 -->
        <view wx:if="{{pageMode === 'edit'}}" class="edit-buttons-row">
          <button class="submit-btn edit-btn {{canSubmitEdit ? '' : 'disabled'}}" bindtap="submitForm" loading="{{submitting}}" disabled="{{!canSubmitEdit}}">
            {{submitting ? '修改中...' : '修改认证'}}
          </button>
          <!-- 注销按钮只在实名认证模式下显示 -->
          <button wx:if="{{authMethod === 'resident'}}" class="cancel-auth-btn" bindtap="showCancelAuthDialog">
            注销认证
          </button>

        </view>
      </view>

      <!-- 隐私提示 -->
      <view class="privacy-tip" >
        提交认证信息即表示您同意我们根据<text class="link" bindtap="goToPrivacyPolicy">《隐私政策》</text>收集、使用和保护您的个人信息，包括您的身份信息、房屋信息、联系方式及人脸照片等。我们将严格保护您的隐私安全，不会向无关第三方泄露您的个人信息。
      </view>
    </view>
  </scroll-view>
</view>

<!-- 房屋选择器弹窗 - 已移除 -->
<!-- 根据需求，无论线上还是线下认证，都移除"我的房屋"这一项的相关弹窗 -->



<!-- 添加房屋确认弹窗 - 已移除 -->
<!-- 根据需求，无论线上还是线下认证，都移除"我的房屋"这一项的相关弹窗 -->

<!-- 证件类型选择器弹窗已移除，改用picker组件 -->

<!-- 部门选择器弹窗 -->
<view class="selector-overlay {{showDepartmentPicker ? 'active' : ''}}" bindtap="closeDepartmentPicker">
  <view class="selector-container" catchtap="stopPropagation">
    <view class="selector-header">
      <text>选择所属部门</text>
      <text class="close-btn" bindtap="closeDepartmentPicker">关闭</text>
    </view>
    <!-- 已选择的部门显示 -->
    <view class="selected-department" wx:if="{{department}}">
      <view class="selected-label">已选择：</view>
      <view class="selected-value">{{department}}</view>
    </view>
    <view class="search-container">
      <view class="search-input-wrapper">
        <input class="search-input" placeholder="搜索部门名称" value="{{departmentSearchKeyword}}" bindinput="onDepartmentSearchInput" />
        <view class="search-icon"></view>
      </view>
    </view>
    <scroll-view scroll-y class="selector-content">
      <!-- 使用扁平化列表渲染组织结构 -->
      <block wx:for="{{flatOrgList}}" wx:key="id">
        <view class="org-item level-{{item.level}}" style="margin-left: {{item.level * 20}}px;">

          <view class="selector-item {{item.type === 'dept' ? 'dept-item' : 'company-item'}} {{item.id === selectedDepartmentId ? 'active' : ''}}"
                data-id="{{item.id}}"
                data-name="{{item.orgName}}"
                data-type="{{item.type}}"
                data-level="{{item.level}}"
                bindtap="selectDepartment">
            <view class="selector-item-content">
              <view class="selector-item-title">
                <text class="org-type-icon">{{item.type === 'company' ? '🏢' : '📁'}}</text>
                {{item.orgName}}
                <!-- 公司类型且有子级时显示展开/收起图标 -->
                <text wx:if="{{item.type === 'company' && item.children && item.children.length}}"
                      class="expand-icon {{item.expanded ? 'expanded' : ''}}">▼</text>
              </view>
            </view>
            <!-- 只有部门类型才显示对勾 -->
            <view class="selector-item-check" wx:if="{{item.type === 'dept' && item.id === selectedDepartmentId}}"></view>
          </view>
        </view>
      </block>

      <view class="empty-tip" wx:if="{{filteredDepartments.length === 0}}">
        <view class="empty-icon"></view>
        <view class="empty-text">{{departmentSearchKeyword ? '未找到相关部门' : '暂无部门数据'}}</view>
      </view>
    </scroll-view>
    <view class="selector-footer">
      <button class="confirm-btn" bindtap="confirmDepartment" disabled="{{selectedDepartmentType !== 'dept'}}">确认选择</button>
    </view>
  </view>
</view>

<!-- 职位选择器弹窗 -->
<view class="selector-overlay {{showPositionPicker ? 'active' : ''}}" bindtap="closePositionPicker">
  <view class="selector-container" catchtap="stopPropagation">
    <view class="selector-header">
      <text>选择职位</text>
      <text class="close-btn" bindtap="closePositionPicker">关闭</text>
    </view>
    <!-- 已选择的职位显示 -->
    <view class="selected-department" wx:if="{{position}}">
      <view class="selected-label">已选择：</view>
      <view class="selected-value">{{position}}</view>
    </view>
    <view class="search-container">
      <view class="search-input-wrapper">
        <input class="search-input" placeholder="搜索职位名称" value="{{positionSearchKeyword}}" bindinput="onPositionSearchInput" />
        <view class="search-icon"></view>
      </view>
    </view>
    <scroll-view scroll-y class="selector-content" bindscrolltolower="loadMorePositions">
      <view class="selector-item {{item.id === selectedPositionId ? 'active' : ''}}" wx:for="{{filteredPositions}}" wx:key="id" bindtap="selectPosition" data-id="{{item.id}}" data-name="{{item.positionName}}">
        <view class="selector-item-content">
          <view class="selector-item-title">{{item.positionName}}</view>
        </view>
        <view class="selector-item-check" wx:if="{{item.id === selectedPositionId}}"></view>
      </view>
      <view class="loading-tip" wx:if="{{positionLoading}}">
        <view class="loading-text">加载中...</view>
      </view>
      <view class="empty-tip" wx:if="{{filteredPositions.length === 0 && !positionLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">{{positionSearchKeyword ? '未找到相关职位' : '暂无职位数据'}}</view>
      </view>
      <view class="no-more-tip" wx:if="{{!positionHasMore && filteredPositions.length > 0}}">
        <view class="no-more-text">没有更多数据了</view>
      </view>
    </scroll-view>
    <view class="selector-footer">
      <button class="confirm-btn" bindtap="confirmPosition" disabled="{{!selectedPositionId}}">确认选择</button>
    </view>
  </view>
</view>

<!-- 预约时间选择器弹窗 -->
<view class="selector-overlay {{showAppointmentSelectorFlag ? 'active' : ''}}" bindtap="closeAppointmentSelector">
  <view class="selector-container" catchtap="stopPropagation">
    <view class="selector-header">
      <text>选择预约时间</text>
      <text class="close-btn" bindtap="closeAppointmentSelector">关闭</text>
    </view>
    <scroll-view scroll-y class="selector-content">
      <view class="date-selector">
        <view class="date-item {{index === selectedDateIndex ? 'active' : ''}}" wx:for="{{availableDates}}" wx:key="index" bindtap="selectDate" data-index="{{index}}">
          <view class="date-weekday">{{item.weekday}}</view>
          <view class="date-day">{{item.day}}</view>
          <view class="date-month">{{item.month}}月</view>
        </view>
      </view>
      <view class="time-selector">
        <view class="time-slot {{item.available && (selectedTimeSlot === index) ? 'active' : ''}} {{!item.available ? 'disabled' : ''}}" wx:for="{{timeSlots}}" wx:key="index" bindtap="selectTimeSlot" data-index="{{index}}" data-available="{{item.available}}">
          <view class="time-text">{{item.time}}</view>
          <view class="time-status">{{item.available ? '' : '已约满'}}</view>
        </view>
      </view>
    </scroll-view>
    <view class="selector-footer">
      <button class="confirm-btn" bindtap="confirmAppointment" disabled="{{selectedTimeSlot === null}}">确认预约</button>
    </view>
  </view>
</view>

<!-- 成功弹窗 -->
<view class="dialog-overlay {{showSuccessDialogFlag ? 'active' : ''}}" bindtap="closeSuccessDialog">
  <view class="dialog-container" catchtap="stopPropagation">
    <view class="dialog-content">
      <view class="dialog-icon success"></view>
      <view class="dialog-title">{{successTitle}}</view>
      <view class="dialog-message">{{successMessage}}</view>
    </view>
    <view class="dialog-footer single">
      <view class="dialog-btn confirm" bindtap="confirmSuccess">确定</view>
    </view>
  </view>
</view>

<!-- 注销认证声明弹窗 -->
<view class="dialog-overlay {{showCancelAuthDialog ? 'active' : ''}}" bindtap="closeCancelAuthDialog">
  <view class="dialog-container cancel-auth-dialog" catchtap="stopPropagation">
    <view class="dialog-content">
      <view class="dialog-icon warning"></view>
      <view class="dialog-title">注销认证声明</view>
      <view class="dialog-message cancel-auth-message">
        <view class="warning-text">注销认证后，您将失去以下功能权限：</view>
        <view class="permission-list">
          <view class="permission-item">• 无法使用好物相关功能（发布、购买、收藏等）</view>
          <view class="permission-item">• 无法使用房产管理功能</view>
          <view class="permission-item">• 无法使用车辆管理功能</view>
          <view class="permission-item">• 无法使用家人管理功能</view>
          <view class="permission-item">• 无法使用租客管理功能</view>
          <view class="permission-item">• 其他需要实名认证的服务功能</view>
        </view>
        <view class="confirm-text">确定要注销认证吗？</view>
      </view>
    </view>
    <view class="dialog-footer">
      <view class="dialog-btn cancel" bindtap="closeCancelAuthDialog">取消</view>
      <view class="dialog-btn confirm danger" bindtap="confirmCancelAuth">确认注销</view>
    </view>
  </view>
</view>

<!-- 隐私政策弹窗 -->
<view class="modal-mask" wx:if="{{showPrivacyModal}}" bindtap="closePrivacyModal">
  <view class="modal-container" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">隐私政策</view>
      <view class="modal-close" bindtap="closePrivacyModal">×</view>
    </view>
    <view class="modal-content">
      <view wx:if="{{privacyLoading}}" class="loading-text">加载中...</view>
      <scroll-view wx:else scroll-y="true" class="content-scroll">
        <rich-text nodes="{{privacyContent}}" class="rich-content"></rich-text>
      </scroll-view>
    </view>
    <view class="modal-footer">
      <button class="confirm-btn" bindtap="confirmPrivacyPolicy">我已阅读</button>
    </view>
  </view>
</view>