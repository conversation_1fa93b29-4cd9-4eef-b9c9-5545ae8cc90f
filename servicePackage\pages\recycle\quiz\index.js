// pages/recycle/quiz/index.js
Page({
  data: {
    darkMode: false,
    started: false,
    finished: false,
    currentIndex: 0,
    selectedOption: null,
    answered: false,
    isCorrect: false,
    score: 0,
    quizTime: 0,
    startTime: 0,
    quizHistory: [],
    questions: [
      {
        question: "以下哪种垃圾属于可回收物？",
        options: ["果皮", "废电池", "塑料瓶", "卫生纸"],
        correctOption: 2,
        explanation: "塑料瓶是可回收物，应放入蓝色垃圾桶。果皮属于厨余垃圾，废电池属于有害垃圾，卫生纸属于其他垃圾。"
      },
      {
        question: "以下哪种垃圾属于有害垃圾？",
        options: ["过期药品", "玻璃瓶", "剩饭剩菜", "纸巾"],
        correctOption: 0,
        explanation: "过期药品属于有害垃圾，应放入红色垃圾桶。玻璃瓶属于可回收物，剩饭剩菜属于厨余垃圾，纸巾属于其他垃圾。"
      },
      {
        question: "以下哪种垃圾属于厨余垃圾？",
        options: ["废纸", "灯管", "茶叶渣", "旧衣服"],
        correctOption: 2,
        explanation: "茶叶渣属于厨余垃圾，应放入绿色垃圾桶。废纸和旧衣服属于可回收物，灯管属于有害垃圾。"
      },
      {
        question: "以下哪种垃圾属于其他垃圾？",
        options: ["易拉罐", "废旧电池", "菜叶", "烟头"],
        correctOption: 3,
        explanation: "烟头属于其他垃圾，应放入灰色垃圾桶。易拉罐属于可回收物，废旧电池属于有害垃圾，菜叶属于厨余垃圾。"
      },
      {
        question: "废弃的充电宝应该投放到哪类垃圾桶？",
        options: ["可回收物", "有害垃圾", "厨余垃圾", "其他垃圾"],
        correctOption: 1,
        explanation: "废弃的充电宝含有锂电池，属于有害垃圾，应放入红色垃圾桶。"
      }
    ]
  },

  onLoad: function() {
    // 从本地存储加载历史记录
    this.loadHistory()
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 加载历史记录
  loadHistory: function() {
    const quizHistory = wx.getStorageSync('quizHistory') || []
    this.setData({
      quizHistory: quizHistory
    })
  },

  // 开始答题
  startQuiz: function() {
    // 随机打乱题目顺序
    const shuffledQuestions = this.shuffleQuestions(this.data.questions)
    
    this.setData({
      started: true,
      finished: false,
      currentIndex: 0,
      selectedOption: null,
      answered: false,
      isCorrect: false,
      score: 0,
      startTime: Date.now(),
      questions: shuffledQuestions,
      currentQuestion: shuffledQuestions[0]
    })
  },

  // 随机打乱题目顺序
  shuffleQuestions: function(questions) {
    const shuffled = [...questions]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  },

  // 选择选项
  selectOption: function(e) {
    if (this.data.answered) return
    
    const index = e.currentTarget.dataset.index
    const isCorrect = index === this.data.currentQuestion.correctOption
    
    this.setData({
      selectedOption: index,
      answered: true,
      isCorrect: isCorrect
    })
    
    if (isCorrect) {
      this.setData({
        score: this.data.score + 1
      })
    }
  },

  // 下一题
  nextQuestion: function() {
    if (this.data.currentIndex < 4) {
      const nextIndex = this.data.currentIndex + 1
      this.setData({
        currentIndex: nextIndex,
        currentQuestion: this.data.questions[nextIndex],
        selectedOption: null,
        answered: false,
        isCorrect: false
      })
    } else {
      // 计算答题时间（秒）
      const quizTime = Math.floor((Date.now() - this.data.startTime) / 1000)
      
      this.setData({
        finished: true,
        quizTime: quizTime
      })
      
      // 保存结果到历史记录
      this.saveResult()
      
      // 如果得满分，奖励10碳积分
      if (this.data.score === 5) {
        this.awardPoints()
      }
    }
  },

  // 保存结果到历史记录
  saveResult: function() {
    let quizHistory = wx.getStorageSync('quizHistory') || []
    
    // 获取当前日期
    const now = new Date()
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`
    
    // 检查今天是否已经有记录
    const todayIndex = quizHistory.findIndex(item => item.date === dateStr)
    
    if (todayIndex !== -1) {
      // 更新今天的记录
      quizHistory[todayIndex].score = this.data.score
      quizHistory[todayIndex].time = this.data.quizTime
    } else {
      // 添加新记录
      quizHistory.unshift({
        date: dateStr,
        score: this.data.score,
        time: this.data.quizTime
      })
    }
    
    // 限制历史记录数量为7条
    if (quizHistory.length > 7) {
      quizHistory = quizHistory.slice(0, 7)
    }
    
    // 保存到本地存储
    wx.setStorageSync('quizHistory', quizHistory)
    
    // 更新数据
    this.setData({
      quizHistory: quizHistory
    })
  },

  // 奖励碳积分
  awardPoints: function() {
    // 从本地存储获取当前积分
    let carbonPoints = wx.getStorageSync('carbonPoints') || 0
    
    // 增加10积分
    carbonPoints += 10
    
    // 保存回本地存储
    wx.setStorageSync('carbonPoints', carbonPoints)
    
    // 显示提示
    wx.showToast({
      title: '获得10碳积分',
      icon: 'success'
    })
  },

  // 查看答案
  reviewQuiz: function() {
    this.setData({
      finished: false,
      started: true,
      currentIndex: 0,
      currentQuestion: this.data.questions[0],
      selectedOption: this.data.questions[0].correctOption,
      answered: true,
      isCorrect: true
    })
  },

  // 返回首页
  backToHome: function() {
    wx.navigateBack()
  }
})
