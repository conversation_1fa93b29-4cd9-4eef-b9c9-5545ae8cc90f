<!--pages/payment/invoice/invoice.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 内容容器 -->
  <view class="content-container">
    <!-- 加载中状态 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 发票内容 -->
    <block wx:if="{{!isLoading && invoiceDetail}}">
      <!-- 发票预览区域 -->
      <view class="invoice-preview-container">
        <scroll-view class="invoice-scroll-view" scroll-y="true">
          <view class="invoice-canvas-container">
            <canvas type="2d" id="invoiceCanvas" class="invoice-canvas" style="width:{{canvasWidth}}rpx; height:{{canvasHeight}}rpx;"></canvas>
          </view>
        </scroll-view>
      </view>

    <!-- 发票信息卡片 -->
    <view class="invoice-info-card">
      <view class="card-title">发票信息</view>
      <view class="info-row">
        <view class="info-label">发票代码</view>
        <view class="info-value">{{invoiceDetail.invoiceCode}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">发票号码</view>
        <view class="info-value">{{invoiceDetail.invoiceNo}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">开票日期</view>
        <view class="info-value">{{invoiceDetail.date}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">金额</view>
        <view class="info-value highlight">¥{{invoiceDetail.amount}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">税额</view>
        <view class="info-value">¥{{invoiceDetail.taxAmount}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">价税合计</view>
        <view class="info-value highlight">¥{{invoiceDetail.totalAmount}}</view>
      </view>
    </view>

      <!-- 底部操作按钮 -->
      <view class="action-buttons">
        <button class="action-button save" bindtap="saveInvoiceToAlbum" disabled="{{isSaving}}">
          <view class="button-icon save-icon"></view>
          <text>{{isSaving ? '保存中...' : '保存到相册'}}</text>
        </button>
        <button class="action-button share" bindtap="showShareOptions">
          <view class="button-icon share-icon"></view>
          <text>分享</text>
        </button>
      </view>

      <!-- 保存成功提示 -->
      <view class="save-success-tip" wx:if="{{saveSuccess}}">
        <view class="tip-icon"></view>
        <view class="tip-text">发票已保存到相册</view>
      </view>
    </block>
  </view>

  <!-- 分享选项弹窗 -->
  <view class="share-options-modal {{showShareOptions ? 'show' : ''}}" bindtap="hideShareOptions">
    <view class="share-options-content" catchtap="stopPropagation">
      <view class="share-options-title">分享方式</view>
      <view class="share-options-list">
        <button class="share-option" open-type="share">
          <view class="share-option-icon wechat"></view>
          <view class="share-option-name">微信好友</view>
        </button>
        <button class="share-option" bindtap="saveInvoiceToAlbum">
          <view class="share-option-icon save"></view>
          <view class="share-option-name">保存到相册</view>
        </button>
      </view>
      <button class="share-cancel-button" bindtap="hideShareOptions">取消</button>
    </view>
  </view>
</view>
