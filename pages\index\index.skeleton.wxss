/*
此文件为开发者工具生成，生成时间: 2025/5/28上午10:48:18

在 D:\BuWorkSpace\物业侧小程序开发（副本）\miniprogram\pages\index\index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-text-16-6667-545 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 43.4783rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-opacity {
    opacity: 0 !important;
  }
.sk-text-16-6667-710 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 46.1957rpx;
    position: relative !important;
  }
.sk-text-16-6667-851 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.7609rpx;
    position: relative !important;
  }
.sk-text-16-6667-884 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.7609rpx;
    position: relative !important;
  }
.sk-text-16-6667-733 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.7609rpx;
    position: relative !important;
  }
.sk-text-8-3333-972 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-738 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-891 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-207 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-802 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-755 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-747 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-8-3333-310 {
    background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
    background-size: 100% 30.4348rpx;
    position: relative !important;
  }
.sk-text-16-6667-557 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.7609rpx;
    position: relative !important;
  }
.sk-text-16-6667-796 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 59.7826rpx;
    position: relative !important;
  }
.sk-text-16-6667-691 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 40.7609rpx;
    position: relative !important;
  }
.sk-button {
    color: #EFEFEF !important;
    background: #EFEFEF !important;
    border: none !important;
    box-shadow: none !important;
  }
.sk-image {
    background: #EFEFEF !important;
  }
.sk-pseudo::before, .sk-pseudo::after {
      background: #EFEFEF !important;
      background-image: none !important;
      color: transparent !important;
      border-color: transparent !important;
    }
.sk-pseudo-rect::before, .sk-pseudo-rect::after {
      border-radius: 0 !important;
    }
.sk-pseudo-circle::before, .sk-pseudo-circle::after {
      border-radius: 50% !important;
    }
.sk-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
  }
