<!--居民管理主页-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="title">居民管理</view>
    <view class="subtitle">高效管理社区居民信息</view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-card" animation="{{animationData}}">
    <!-- <view class="stats-header">
      <view class="stats-more" bindtap="navigateToStatistics">查看详细统计</view>
    </view> -->
    <view class="stats-content">
      <view class="stats-item">
        <view class="stats-value">{{statistics.totalResidents}}</view>
        <view class="stats-label">总人数</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{pendingList.length}}</view>
        <view class="stats-label">待审核</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{statistics.todayNew}}</view>
        <view class="stats-label">今日新增</view>
      </view>
    </view>
  </view>

  <!-- 快速操作区 -->
  <view class="quick-actions" animation="{{animationData}}">
    <view class="section-title">快速操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="navigateToReview">
        <view class="action-icon review-icon"></view>
        <view class="action-label">信息审核</view>
      </view>
      <view class="action-item" bindtap="navigateToSearch">
        <view class="action-icon search-icon"></view>
        <view class="action-label">居民查询</view>
      </view>
      <view class="action-item" bindtap="navigateToAdd">
        <view class="action-icon add-icon"></view>
        <view class="action-label">添加居民</view>
      </view>
      <view class="action-item" bindtap="navigateToStatistics">
        <view class="action-icon stats-icon"></view>
        <view class="action-label">数据统计</view>
      </view>
    </view>
  </view>

  <!-- 最近审核 -->
  <view class="recent-activities" animation="{{animationData}}">
    <view class="section-title">
      <text>最近审核</text>
      <text class="more-link" bindtap="navigateToReview">查看全部</text>
    </view>
    <view class="activity-list" wx:if="{{pendingList.length > 0}}">
      <view class="activity-item" wx:for="{{pendingList}}" wx:key="id" bindtap="navigateToReviewDetail" data-item="{{item}}">
        <view class="activity-icon {{item.type === 'resident_room' ? 'icon-house' : 'icon-vehicle'}}"></view>
        <view class="activity-content">
          <view class="activity-text">{{item.residentName}} 提交了{{item.typeText}}</view>
          <view class="activity-detail">{{item.addressText}} </view>
          <view class="activity-detail" wx:if="{{item.type=='resident_room'}}">{{item.residentTypeName}} </view>
          <view class="activity-time">{{item.createTime}}</view>
        </view>
        <view class="activity-arrow"></view>
      </view>
    </view>
    <view class="empty-state" wx:else>
      <text>暂无待审核项目</text>
    </view>
  </view>
</view>
