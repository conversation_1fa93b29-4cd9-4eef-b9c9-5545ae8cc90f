/* 房屋详情页面样式 */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px 20px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 房屋卡片样式 */
.house-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.house-header {
  padding: 20px;
  background: linear-gradient(135deg, #FF9500, #FF7800);
  color: white;
}

.house-address {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.house-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  height: 22px;
  min-width: 40px;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.2);
}

.status-tag.verified {
  background-color: rgba(52, 199, 89 );
  
}

.status-tag.unverified {
  background-color:	  #DC143C;
}

.house-info-list {
  padding: 0 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 500;
}

/* 认证提示 */
.verify-tip {
  background: #FFF9E6;
  border-radius: 12px;
  padding: 16px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.tip-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-content {
  flex: 1;
  font-size: 14px;
  color: #8A6D3B;
}

.tip-link {
  color: #FF9500;
  font-weight: 500;
  display: inline;
}

/* 按钮样式 */
.button-group {
  margin-top: 20px;
}

.action-button {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  border: none;
  transition: all 0.2s ease;
}

.primary-button {
  background: #FF9500;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.2);
}

.secondary-button {
  background: #F2F2F7;
  color: #FF9500;
  border: 1px solid #FF9500;
}

.secondary-button:active {
  transform: scale(0.98);
  background: #FFF5E6;
}

.secondary-button[disabled] {
  color: #999;
  border-color: #ccc;
  background: #F2F2F7;
}

.danger-button {
  background: #FF3B30;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.2);
}

.danger-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.2);
}

/* 确认弹窗样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.confirm-dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.confirm-dialog {
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}

.confirm-dialog-overlay.active .confirm-dialog {
  transform: scale(1);
}

.confirm-dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.confirm-dialog-icon {
  width: 56px;
  height: 56px;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.confirm-dialog-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.confirm-dialog-message {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  line-height: 1.4;
}

.confirm-dialog-buttons {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.confirm-dialog-button {
  flex: 1;
  padding: 14px 0;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.2s;
}

.confirm-dialog-button:first-child {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.confirm-dialog-button.secondary {
  color: #8e8e93;
}

.confirm-dialog-button.primary {
  color: #FF3B30;
  font-weight: 600;
}

.confirm-dialog-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 成功弹窗样式 */
.success-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.success-dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.success-dialog {
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}

.success-dialog-overlay.active .success-dialog {
  transform: scale(1);
}

.success-dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.success-dialog-icon {
  width: 56px;
  height: 56px;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.success-dialog-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.success-dialog-message {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  line-height: 1.4;
}

.success-dialog-buttons {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.success-dialog-button {
  flex: 1;
  padding: 14px 0;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.2s;
}

.success-dialog-button.primary {
  color: #FF9500;
  font-weight: 600;
}

.success-dialog-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}
