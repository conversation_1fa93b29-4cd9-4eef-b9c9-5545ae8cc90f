# 房屋选择完整逻辑实现

## 实现概述

完全按照我的房屋页面的弹窗逻辑和样式实现了房屋选择功能，包括三步选择流程：楼栋 → 单元/房间 → 房间。

## 选择流程

### 第一步：选择楼栋
- 显示所有楼栋的网格列表
- 支持搜索筛选楼栋
- 点击楼栋后进入下一步

### 第二步：选择单元/房间（混合）
- 根据楼栋下的房间情况，智能显示：
  - **有单元的房间**：按单元分组显示
  - **无单元的房间**：直接显示房间
- 支持两种选择：
  - 点击单元：进入该单元的房间选择
  - 点击房间：直接选中该房间（无单元）

### 第三步：选择房间（仅当选择了单元）
- 显示选中单元下的所有房间
- 点击房间完成选择

## 数据结构

### 基础数据
```javascript
{
  buildings: [],           // 楼栋列表
  units: [],              // 单元/房间混合列表
  rooms: [],              // 房间列表
  filteredBuildings: [], // 筛选后的楼栋
  filteredUnits: [],     // 筛选后的单元/房间
  filteredRooms: []      // 筛选后的房间
}
```

### 选中状态
```javascript
{
  selectedBuildingId: '',    // 选中楼栋ID
  selectedBuildingName: '',  // 选中楼栋名称
  selectedUnitNumber: '',    // 选中单元号
  selectedRoomId: null,      // 选中房间ID
  selectedRoomName: '',      // 选中房间名称
  canSubmit: false          // 是否可以提交
}
```

### 单元/房间混合数据结构
```javascript
// 单元类型
{
  type: 'unit',
  unitNumber: '1单元',
  displayName: '1单元',
  rooms: [房间列表]
}

// 房间类型（无单元）
{
  type: 'room',
  id: 'room_id',
  roomNumber: '101',
  displayName: '101'
}
```

## 关键方法

### 数据加载
```javascript
loadBuildings()           // 加载楼栋列表
loadRooms(buildingId)     // 加载房间并处理单元分组
```

### 选择逻辑
```javascript
selectBuilding(e)         // 选择楼栋
selectUnit(e)            // 选择单元或房间（混合）
selectRoom(e)            // 选择房间
```

### 导航控制
```javascript
backToBuilding()         // 返回楼栋选择
backToUnit()            // 返回单元选择
checkCanSubmit()        // 检查是否可提交
```

### 搜索筛选
```javascript
filterBuildings(keyword) // 筛选楼栋
filterUnits(keyword)     // 筛选单元/房间
filterRooms(keyword)     // 筛选房间
```

## 界面状态

### 步骤控制
```javascript
currentStep: 'building' | 'unit' | 'room'
searchPlaceholder: '搜索楼栋' | '搜索单元' | '搜索房间'
```

### 已选信息显示
- 显示已选楼栋，可重新选择
- 显示已选单元，可重新选择
- 显示已选房间

### 空状态处理
- 楼栋为空：显示"暂无楼栋信息"
- 搜索无结果：显示"未找到匹配的xxx"
- 单元/房间为空：显示"该楼栋暂无单元/房间信息"

## 样式特点

### 弹窗样式
- 底部弹出，圆角设计
- 最大高度80vh，内容自适应
- 流畅的滑入滑出动画

### 网格布局
- 4列网格布局，响应式设计
- 选中状态蓝色高亮
- 统一的项目高度和间距

### 已选信息
- 浅灰色背景区域
- 重新选择按钮
- 清晰的层级显示

## 交互逻辑

### 选择流程
1. **楼栋选择**：点击楼栋 → 加载该楼栋的房间 → 进入单元/房间选择
2. **单元选择**：点击单元 → 进入该单元的房间选择
3. **房间选择**：点击房间 → 完成选择，可以提交

### 返回逻辑
- 从房间选择返回单元选择：保持楼栋选择，清空房间选择
- 从单元/房间选择返回楼栋选择：清空所有选择

### 提交条件
- 必须选择了具体的房间（selectedRoomId不为空）
- 按钮状态根据canSubmit动态变化

## 错误处理

### 数据加载失败
- 显示错误提示
- 保持当前状态不变

### 网络异常
- 显示"网络异常，请重试"
- 不影响已选择的数据

### 选择验证
- 提交时检查是否选择了房间
- 显示相应的提示信息

## 性能优化

### 数据处理
- 单元分组在数据加载时一次性处理
- 搜索筛选使用本地数据，响应快速

### 状态管理
- 最小化数据更新范围
- 合理使用setData批量更新

### 内存管理
- 及时清理不需要的数据
- 重置时清空所有相关数组
