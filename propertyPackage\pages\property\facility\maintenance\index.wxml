<!--设施维修/保养记录页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">维修/保养记录</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 维修/保养表单 -->
  <block wx:if="{{!isLoading && facility}}">
    <!-- 设施信息 -->
    <view class="facility-info">
      <view class="facility-name">{{facility.name}}</view>
      <view class="facility-meta">
        <view class="facility-code">编号: {{facility.code}}</view>
        <view class="facility-status {{facility.status}}">{{facility.statusText}}</view>
      </view>
      <view class="facility-location">
        <view class="location-icon"></view>
        <text>{{facility.location}}</text>
      </view>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 工作类型 -->
      <view class="form-group">
        <view class="form-label">工作类型</view>
        <view class="form-control">
          <view class="work-type-selector">
            <view 
              class="work-type-option {{formData.workType === item.value ? 'active' : ''}}" 
              wx:for="{{workTypeOptions}}" 
              wx:key="value" 
              bindtap="onWorkTypeChange" 
              data-value="{{item.value}}"
            >
              {{item.label}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 处理描述 -->
      <view class="form-group">
        <view class="form-label required">处理描述</view>
        <view class="form-control">
          <textarea 
            class="form-textarea {{formErrors.description ? 'error' : ''}}" 
            placeholder="请详细描述处理过程和结果" 
            value="{{formData.description}}" 
            bindinput="onInputChange" 
            data-field="description"
            maxlength="500"
          ></textarea>
          <view class="error-message" wx:if="{{formErrors.description}}">{{formErrors.description}}</view>
        </view>
      </view>
      
      <!-- 耗材使用 -->
      <view class="form-group">
        <view class="form-label">耗材使用</view>
        <view class="form-control">
          <textarea 
            class="form-textarea" 
            placeholder="请填写使用的耗材和数量" 
            value="{{formData.materialsUsed}}" 
            bindinput="onInputChange" 
            data-field="materialsUsed"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      
      <!-- 费用 -->
      <view class="form-group">
        <view class="form-label">费用（元）</view>
        <view class="form-control">
          <input 
            class="form-input" 
            placeholder="请输入费用" 
            value="{{formData.cost}}" 
            bindinput="onInputChange" 
            data-field="cost"
            type="digit"
          />
        </view>
      </view>
      
      <!-- 图片上传 -->
      <view class="form-group">
        <view class="form-label">完成图片</view>
        <view class="form-control">
          <view class="upload-container">
            <view class="upload-list">
              <view class="upload-item" wx:for="{{uploadedImages}}" wx:key="index">
                <image class="upload-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
                <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}"></view>
              </view>
              <view class="upload-button" bindtap="uploadImage" wx:if="{{uploadedImages.length < 9}}">
                <view class="upload-icon"></view>
                <text>上传图片</text>
              </view>
            </view>
            <view class="upload-tip">最多上传9张图片，可拍照或从相册选择</view>
          </view>
        </view>
      </view>
      
      <!-- 操作人 -->
      <view class="form-group">
        <view class="form-label required">操作人</view>
        <view class="form-control">
          <input 
            class="form-input {{formErrors.operatorName ? 'error' : ''}}" 
            placeholder="请输入操作人姓名" 
            value="{{formData.operatorName}}" 
            bindinput="onInputChange" 
            data-field="operatorName"
          />
          <view class="error-message" wx:if="{{formErrors.operatorName}}">{{formErrors.operatorName}}</view>
        </view>
      </view>
      
      <!-- 联系电话 -->
      <view class="form-group">
        <view class="form-label required">联系电话</view>
        <view class="form-control">
          <input 
            class="form-input {{formErrors.operatorPhone ? 'error' : ''}}" 
            placeholder="请输入联系电话" 
            value="{{formData.operatorPhone}}" 
            bindinput="onInputChange" 
            data-field="operatorPhone"
            type="number"
            maxlength="11"
          />
          <view class="error-message" wx:if="{{formErrors.operatorPhone}}">{{formErrors.operatorPhone}}</view>
        </view>
      </view>
      
      <!-- 完成日期 -->
      <view class="form-group">
        <view class="form-label">完成日期</view>
        <view class="form-control">
          <view class="form-input date-input" bindtap="showDatePicker">
            <text>{{formData.completionDate || '请选择完成日期'}}</text>
            <view class="calendar-icon"></view>
          </view>
        </view>
      </view>
      
      <!-- 完成时间 -->
      <view class="form-group">
        <view class="form-label">完成时间</view>
        <view class="form-control">
          <view class="form-input time-input" bindtap="showTimePicker">
            <text>{{formData.completionTime || '请选择完成时间'}}</text>
            <view class="time-icon"></view>
          </view>
        </view>
      </view>
      
      <!-- 设施状态 -->
      <view class="form-group">
        <view class="form-label">处理后状态</view>
        <view class="form-control">
          <view class="status-selector">
            <view 
              class="status-option {{formData.statusAfter === item.value ? 'active' : ''}}" 
              wx:for="{{statusOptions}}" 
              wx:key="value" 
              bindtap="onStatusChange" 
              data-value="{{item.value}}"
              style="{{formData.statusAfter === item.value ? 'background-color:' + item.color + '10; color:' + item.color + ';' : ''}}"
            >
              {{item.label}}
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-container">
      <button 
        class="submit-button {{isSubmitting ? 'disabled' : ''}}" 
        bindtap="submitForm" 
        disabled="{{isSubmitting}}"
      >
        {{isSubmitting ? '提交中...' : '提交记录'}}
      </button>
    </view>
  </block>
  
  <!-- 日期选择器弹窗 -->
  <view class="date-picker-popup {{showDatePicker ? 'active' : ''}}">
    <view class="date-picker-mask" bindtap="hideDatePicker"></view>
    <view class="date-picker-container">
      <view class="date-picker-header">
        <view class="date-picker-cancel" bindtap="hideDatePicker">取消</view>
        <view class="date-picker-title">选择日期</view>
        <view class="date-picker-confirm" bindtap="confirmDatePicker">确定</view>
      </view>
      <picker-view 
        class="date-picker" 
        value="{{currentDate}}" 
        bindchange="onDateChange"
      >
        <picker-view-column>
          <!-- 日期选项 -->
        </picker-view-column>
      </picker-view>
    </view>
  </view>
  
  <!-- 时间选择器弹窗 -->
  <view class="time-picker-popup {{showTimePicker ? 'active' : ''}}">
    <view class="time-picker-mask" bindtap="hideTimePicker"></view>
    <view class="time-picker-container">
      <view class="time-picker-header">
        <view class="time-picker-cancel" bindtap="hideTimePicker">取消</view>
        <view class="time-picker-title">选择时间</view>
        <view class="time-picker-confirm" bindtap="confirmTimePicker">确定</view>
      </view>
      <picker-view 
        class="time-picker" 
        value="{{currentTime}}" 
        bindchange="onTimeChange"
      >
        <picker-view-column>
          <!-- 时间选项 -->
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>
