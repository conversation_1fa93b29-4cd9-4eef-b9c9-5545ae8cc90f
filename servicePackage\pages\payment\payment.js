// payment.js
const payment = require('@/api/paymentApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    loading: true,
    selectedCommunity: '',
    paymentObject: null, // 缴费业主信息
    paymentList: [], // 缴费账单列表
    totalAmount: 0, // 总金额
    allSelected: true,
    selectedItems: [], // 已选择的缴费项目ID

    // 支付相关
    paymentMethod: 'wechat_pay',
    showSuccessModal: false,
    showConfirmModal: false,
    showMoreMenu: false,
    paymentMethodsExpanded: false,
    isProcessing: false,

    // 字典数据
    paymentBillStatusDict: [], // 缴费账单状态字典
    paymentDetailStatusDict: [], // 缴费明细状态字典
    payTypeDict: [], // 支付类型字典

    // 支付方式选项
    currentPaymentMethod: {
      id: 'wechat_pay',
      name: '微信支付',
      icon: 'icon-wechat',
      iconClass: 'wechat-icon'
    },
    paymentMethods: [
      {
        id: 'wechat_pay',
        name: '微信支付',
        icon: 'icon-wechat',
        iconClass: 'wechat-icon'
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: 'icon-alipay',
        iconClass: 'alipay-icon'
      }
    ]
  },

  onLoad: function () {
    this.initDictData()
    this.checkCommunitySelection()
    this.loadPaymentData()
  },

  onShow: function () {
    this.checkCommunitySelection()
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const paymentBillStatusDict = util.getDictByNameEn('property_payment_bill_status')[0].children
      const paymentDetailStatusDict = util.getDictByNameEn('property_payment_detail_status')[0].children
      const payTypeDict = util.getDictByNameEn('pay_type')[0].children

      this.setData({
        paymentBillStatusDict,
        paymentDetailStatusDict,
        payTypeDict
      })
    } catch (error) {
      console.error('初始化字典数据失败:', error)
    }
  },

  // 检查社区选择
  checkCommunitySelection: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity') || {}

    if (!selectedCommunity.id) {
      wx.showToast({
        title: '请先选择社区',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return false
    }

    this.setData({
      selectedCommunity
    })
    return true
  },

  // 加载缴费数据
  loadPaymentData: function() {
    if (!this.checkCommunitySelection()) return

    this.setData({ loading: true })

    // 并行获取缴费账单和业主信息
    Promise.all([
      this.getPaymentList(),
      this.getPaymentObject()
    ]).then(() => {
      this.setData({ loading: false })
      this.updateSelectedItems()
    }).catch(error => {
      console.error('加载缴费数据失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      })
    })
  },

  // 获取缴费账单列表
  getPaymentList: function() {
    const params = {
      pageNum: 1,
      pageSize: 500,
      status: "wait_pay", // 待支付
      communityId: this.data.selectedCommunity.id
    }

    console.log('获取缴费账单参数:', params)

    return payment.queryMyPaymentList(params).then(res => {
      console.log('缴费账单返回数据:', res)

      if (res && res.list) {
        // 处理缴费账单数据
        const paymentList = res.list.map(item => {
          // 解析paymentItemSnapshot JSON字符串
          let paymentItemSnapshot = {}
          try {
            paymentItemSnapshot = JSON.parse(item.paymentItemSnapshot || '{}')
          } catch (error) {
            console.error('解析paymentItemSnapshot失败:', error)
          }

          // 获取支付周期显示文本
          const billingCycleText = this.getBillingCycleText(paymentItemSnapshot.billingCycle)

          return {
            ...item,
            paymentItemSnapshot,
            paymentItemName: paymentItemSnapshot.paymentItemName || '未知费用',
            billingCycleText: billingCycleText,
            formattedCreateTime: this.formatTime(item.createTime),
            formattedBillDate: this.formatTime(item.billDate),
            selected: true // 默认全选
          }
        })

        // 默认选中所有项目
        const selectedItems = paymentList.map(item => item.id)

        this.setData({
          paymentList,
          selectedItems
        })

        console.log('处理后的缴费账单:', paymentList)
      } else {
        this.setData({
          paymentList: [],
          selectedItems: []
        })
      }
    })
  },

  // 获取缴费业主信息
  getPaymentObject: function() {
    const params = {
      communityId: this.data.selectedCommunity.id
    }

    console.log('获取缴费业主信息参数:', params)

    return payment.getPaymentObject(params).then(res => {
      console.log('缴费业主信息返回数据:', res)

      this.setData({
        paymentObject: res || null
      })
    })
  },

  // 获取支付周期显示文本
  getBillingCycleText: function(billingCycle) {
    const cycleMap = {
      'monthly': '月度',
      'quarterly': '季度',
      'yearly': '年度'
    }
    return cycleMap[billingCycle] || billingCycle
  },

  // 格式化时间
  formatTime: function(timeStr) {
    if (!timeStr) return ''

    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  },

  // 更新已选项目列表和总金额
  updateSelectedItems: function() {
    const { paymentList, selectedItems } = this.data

    // 计算总金额
    let totalAmount = 0
    paymentList.forEach(item => {
      if (selectedItems.includes(item.id)) {
        totalAmount += parseFloat(item.payAmount || 0)
      }
    })

    // 检查是否全选
    const allSelected = selectedItems.length === paymentList.length && paymentList.length > 0

    this.setData({
      totalAmount: totalAmount.toFixed(2),
      allSelected
    })
  },

  stopPropagation: function () {
    // 阻止事件冒泡
  },

  // 切换全选/全不选
  toggleSelectAll: function() {
    const { paymentList, allSelected } = this.data
    let selectedItems = []

    if (!allSelected) {
      // 全选
      selectedItems = paymentList.map(item => item.id)
    }
    // 如果当前是全选状态，则取消全选，selectedItems保持为空数组

    this.setData({
      selectedItems
    })

    // 更新总金额
    this.updateSelectedItems()
  },

  // 切换单个项目的选中状态
  toggleSelectItem: function(e) {
    const id = e.currentTarget.dataset.id
    const { selectedItems } = this.data

    let newSelectedItems = [...selectedItems]
    const index = newSelectedItems.indexOf(id)

    if (index === -1) {
      // 添加选择
      newSelectedItems.push(id)
    } else {
      // 取消选择
      newSelectedItems.splice(index, 1)
    }

    this.setData({
      selectedItems: newSelectedItems
    })

    // 更新总金额
    this.updateSelectedItems()
  },

  // 切换支付方式展开/收起
  togglePaymentMethodsExpand: function() {
    this.setData({
      paymentMethodsExpanded: !this.data.paymentMethodsExpanded
    })
  },

  // 选择支付方式
  selectPaymentMethod: function (e) {
    const method = e.currentTarget.dataset.method
    const currentMethod = this.data.paymentMethods.find(item => item.id === method)

    this.setData({
      paymentMethod: method,
      currentPaymentMethod: currentMethod,
      paymentMethodsExpanded: false
    })
  },

  // 显示更多菜单
  showMoreMenu: function() {
    this.setData({
      showMoreMenu: true
    })
  },

  // 关闭更多菜单
  closeMoreMenu: function() {
    this.setData({
      showMoreMenu: false
    })
  },

  // 切换自动扣款
  toggleAutoPayment: function() {
    this.setData({
      autoPayment: !this.data.autoPayment,
      showMoreMenu: false
    })

    wx.showToast({
      title: this.data.autoPayment ? '已开启自动扣款' : '已关闭自动扣款',
      icon: 'none'
    })
  },

  // 跳转到缴费记录
  navigateToPaymentHistory: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/history/history'
    })
  },

  // 跳转到缴费设置
  navigateToPaymentSettings: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析
  navigateToPaymentAnalysis: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/analysis/analysis'
    })
  },

  // 查看缴费详情
  viewPaymentDetail: function(id) {
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  },

  // 查看电子发票
  downloadInvoice: function() {
    // 跳转到发票预览页面，使用默认ID 1001
    wx.navigateTo({
      url: '/servicePackage/pages/payment/invoice/invoice?id=1001'
    })
  },

  // 分享发票
  shareInvoice: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 确认支付
  confirmPayment: function () {
    // 检查是否有选中的项目
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请至少选择一个缴费项目',
        icon: 'none'
      })
      return
    }

    // 显示确认弹窗
    this.setData({
      showConfirmModal: true
    })
  },

  // 关闭确认弹窗
  closeConfirmModal: function() {
    this.setData({
      showConfirmModal: false
    })
  },

  // 处理支付
  processPayment: function() {
    const { selectedItems, paymentList, paymentMethod } = this.data

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择缴费项目',
        icon: 'none'
      })
      return
    }

    // 关闭确认弹窗
    this.setData({
      showConfirmModal: false,
      isProcessing: true
    })

    // 显示加载中
    wx.showLoading({
      title: '支付处理中...',
      mask: true
    })

    // TODO: 这里应该调用真实的支付接口
    // 目前先模拟支付过程
    setTimeout(() => {
      wx.hideLoading()

      // 显示支付成功弹窗
      this.setData({
        showSuccessModal: true,
        isProcessing: false
      })

      // 刷新数据
      this.loadPaymentData()

    }, 1500)
  },

  closeSuccessModal: function () {
    this.setData({
      showSuccessModal: false
    })
  },

  // 查看缴费详情
  viewPaymentDetail: function (e) {
    const id = e.currentTarget.dataset.id

    // 跳转到缴费详情页
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  }
})
