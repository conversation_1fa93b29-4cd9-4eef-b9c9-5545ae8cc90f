// payment.js

Page({
  data: {
    selectedCommunity: '',
    userName: '',
    userPhone: '',
    houseInfo: '2栋3单元1801',
    daysRemaining: 5,
    isProcessing: false,
    paymentMethod: 'wechat',
    showSuccessModal: false,
    showAlreadyPaidModal: false,
    showConfirmModal: false,
    showMoreMenu: false,
    paymentMethodsExpanded: false,
    autoPayment: false,
    lastPaymentDate: '',
    lastSyncTime: '2023-12-26 18:30',
    totalAmount: '1,258.50',
    allSelected: true,
    selectedItems: [],
    currentPaymentMethod: {
      id: 'wechat',
      name: '微信支付',
      icon: 'icon-wechat',
      iconClass: 'wechat-icon'
    },
    feeItems: [
      {
        id: 1,
        name: '物业管理费',
        period: '2023年10月-12月',
        amount: '¥720.00',
        icon: 'icon-property',
        iconClass: 'property-icon',
        selected: true
      },
      {
        id: 2,
        name: '地下停车费',
        period: '2023年第四季度',
        amount: '¥450.00',
        icon: 'icon-parking',
        iconClass: 'parking-icon',
        selected: true
      },
      {
        id: 3,
        name: '水电费',
        period: '2023年11月',
        amount: '¥88.50',
        icon: 'icon-utility',
        iconClass: 'utility-icon',
        selected: true
      }
    ],
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: 'icon-wechat',
        iconClass: 'wechat-icon'
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: 'icon-alipay',
        iconClass: 'alipay-icon'
      },
      {
        id: 'bank',
        name: '银行卡',
        icon: 'icon-bank',
        iconClass: 'bank-icon'
      }
    ]
  },

  onLoad: function () {
    this.checkCommunitySelection()
    this.checkUserInfo()
    this.checkPaymentStatus()
    this.updateSelectedItems()

    // 计算剩余天数
    const dueDate = new Date('2023-12-31')
    const today = new Date()
    const diffTime = dueDate - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    this.setData({
      daysRemaining: diffDays > 0 ? diffDays : 0
    })
  },

  onShow: function () {
    this.checkCommunitySelection()
    this.checkUserInfo()
  },

  // 更新已选项目列表和总金额
  updateSelectedItems: function() {
    const selectedItems = this.data.feeItems.filter(item => item.selected)

    // 计算总金额
    let totalAmount = 0
    selectedItems.forEach(item => {
      // 从金额字符串中提取数字
      const amount = parseFloat(item.amount.replace('¥', '').replace(',', ''))
      totalAmount += amount
    })

    // 格式化总金额
    const formattedTotal = totalAmount.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })

    this.setData({
      selectedItems,
      totalAmount: formattedTotal
    })
  },

  checkCommunitySelection: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity') || ''

    this.setData({
      selectedCommunity
    })
  },

  checkUserInfo: function () {
    const userName = wx.getStorageSync('userName') || ''
    const userPhone = wx.getStorageSync('userPhone') || ''

    this.setData({
      userName,
      userPhone: userPhone ? userPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
    })
  },

  checkPaymentStatus: function () {
    // 检查是否已经支付
    const isPaid = wx.getStorageSync('propertyFeesPaid') === 'true'

    if (isPaid) {
      // 如果已经支付，显示已缴费提示
      setTimeout(() => {
        this.showAlreadyPaidDialog()
      }, 500)
    }

    // 更新最后支付日期
    const paidDate = wx.getStorageSync('propertyFeesPaidDate')
    if (paidDate) {
      const date = new Date(paidDate)
      const formattedDate = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
      this.setData({
        lastPaymentDate: formattedDate
      })
    }
  },

  showAlreadyPaidDialog: function () {
    this.setData({
      showAlreadyPaidModal: true
    })
  },

  closeAlreadyPaidModal: function () {
    this.setData({
      showAlreadyPaidModal: false
    })

    // 返回上一页
    wx.navigateBack()
  },

  stopPropagation: function () {
    // 阻止事件冒泡
  },

  // 切换全选/全不选
  toggleSelectAll: function() {
    const allSelected = !this.data.allSelected

    // 更新所有项目的选中状态
    const feeItems = this.data.feeItems.map(item => {
      return {
        ...item,
        selected: allSelected
      }
    })

    this.setData({
      allSelected,
      feeItems
    })

    // 更新已选项目和总金额
    this.updateSelectedItems()
  },

  // 切换单个项目的选中状态
  toggleSelectItem: function(e) {
    const id = e.currentTarget.dataset.id

    // 更新项目的选中状态
    const feeItems = this.data.feeItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        }
      }
      return item
    })

    // 检查是否全部选中
    const allSelected = feeItems.every(item => item.selected)

    this.setData({
      feeItems,
      allSelected
    })

    // 更新已选项目和总金额
    this.updateSelectedItems()
  },

  // 切换支付方式展开/收起
  togglePaymentMethodsExpand: function() {
    this.setData({
      paymentMethodsExpanded: !this.data.paymentMethodsExpanded
    })
  },

  // 选择支付方式
  selectPaymentMethod: function (e) {
    const method = e.currentTarget.dataset.method
    const currentMethod = this.data.paymentMethods.find(item => item.id === method)

    this.setData({
      paymentMethod: method,
      currentPaymentMethod: currentMethod,
      paymentMethodsExpanded: false
    })
  },

  // 显示更多菜单
  showMoreMenu: function() {
    this.setData({
      showMoreMenu: true
    })
  },

  // 关闭更多菜单
  closeMoreMenu: function() {
    this.setData({
      showMoreMenu: false
    })
  },

  // 切换自动扣款
  toggleAutoPayment: function() {
    this.setData({
      autoPayment: !this.data.autoPayment,
      showMoreMenu: false
    })

    wx.showToast({
      title: this.data.autoPayment ? '已开启自动扣款' : '已关闭自动扣款',
      icon: 'none'
    })
  },

  // 跳转到缴费记录
  navigateToPaymentHistory: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/history/history'
    })
  },

  // 跳转到缴费设置
  navigateToPaymentSettings: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析
  navigateToPaymentAnalysis: function() {
    this.setData({
      showMoreMenu: false
    })

    wx.navigateTo({
      url: '/servicePackage/pages/payment/analysis/analysis'
    })
  },

  // 查看缴费详情
  viewPaymentDetail: function(id) {
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  },

  // 查看电子发票
  downloadInvoice: function() {
    // 跳转到发票预览页面，使用默认ID 1001
    wx.navigateTo({
      url: '/servicePackage/pages/payment/invoice/invoice?id=1001'
    })
  },

  // 分享发票
  shareInvoice: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 确认支付
  confirmPayment: function () {
    // 检查是否有选中的项目
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请至少选择一个缴费项目',
        icon: 'none'
      })
      return
    }

    // 显示确认弹窗
    this.setData({
      showConfirmModal: true
    })
  },

  // 关闭确认弹窗
  closeConfirmModal: function() {
    this.setData({
      showConfirmModal: false
    })
  },

  // 处理支付
  processPayment: function() {
    // 关闭确认弹窗
    this.setData({
      showConfirmModal: false,
      isProcessing: true
    })

    // 显示加载中
    wx.showLoading({
      title: '支付处理中...',
      mask: true
    })

    // 模拟支付过程
    setTimeout(() => {
      wx.hideLoading()

      // 显示支付成功弹窗
      this.setData({
        showSuccessModal: true,
        isProcessing: false
      })

      // 保存支付状态
      wx.setStorageSync('propertyFeesPaid', 'true')
      wx.setStorageSync('propertyFeesPaidDate', new Date().toISOString())

    }, 1500)
  },

  closeSuccessModal: function () {
    this.setData({
      showSuccessModal: false
    })

    // 返回上一页
    wx.navigateBack()
  },

  // 从支付成功弹窗查看缴费详情
  viewPaymentDetail: function (e) {
    const id = e.currentTarget.dataset.id

    this.setData({
      showSuccessModal: false
    })

    // 跳转到缴费详情页
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  },

  // 测试功能：重置支付状态（开发调试用）
  resetPaymentStatus: function () {
    wx.removeStorageSync('propertyFeesPaid')
    wx.removeStorageSync('propertyFeesPaidDate')

    wx.showToast({
      title: '支付状态已重置',
      icon: 'none'
    })

    setTimeout(() => {
      wx.redirectTo({
        url: '/servicePackage/pages/payment/payment'
      })
    }, 1500)
  }
})
