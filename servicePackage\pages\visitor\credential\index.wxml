<view class="visitor-container">

  <!-- 凭证内容区域 -->
  <scroll-view scroll-y class="visitor-credential-container">


    <!-- 小区名称 -->
    <view class="visitor-community-name">
      <text>{{communityName}}</text>

    </view>



    <view class="visitor-car-number-container" wx:if="{{visitorData.carNumber}}">
      <view class="visitor-car-number">
        <view class="car-number-prefix">
          <text>粤</text>
        </view>
        <view class="car-number-content">
          <text>{{visitorData.carNumber}}</text>
        </view>
      </view>
      <view class="car-number-label">车牌号码</view>
    </view>

    <!-- 凭证信息区 -->
    <view class="visitor-credential-info-card">
      <view class="visitor-card-header">
        <view class="visitor-card-header-left">
          <image src="/images/icons/visitor-info.svg" class="visitor-card-icon" />
          <text class="visitor-card-title">访客信息</text>
        </view>
        <view class="visitor-card-header-right">
          <view class="visitor-status-badge {{visitorData.status}}">
            <text>{{statusText}}</text>
          </view>
          <view class="favorite-icon {{visitorData.isUsual ? 'active' : ''}}" catchtap="toggleFavorite">
            <image src="/images/icons/star.svg" class="star-icon"></image>
          </view>
        </view>
      </view>
      <view class="visitor-card-content">
        <view class="visitor-info-row">
          <text class="visitor-info-label">访客姓名</text>
          <text class="visitor-info-value">{{visitorData.visitorName}}</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">手机号码</text>
          <text class="visitor-info-value">{{visitorData.phone}}</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">车牌号码</text>
          <text class="visitor-info-value">{{visitorData.vehicleNumber?visitorData.vehicleNumber:''}}</text>
        </view>
        
        <view class="visitor-info-row">
          <text class="visitor-info-label">来访时间</text>
          <text class="visitor-info-value">{{visitorData.visitTime}}</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">滞留时长</text>
          <text class="visitor-info-value">{{visitorData.stayDuration}}小时</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">来访目的</text>
          <text class="visitor-info-value">{{visitorData.purpose}}</text>
        </view>
         <view class="visitor-info-row">
          <text class="visitor-info-label">来访备注</text>
          <text class="visitor-info-value">{{visitorData.note}}</text>
        </view>
      </view>
    </view>

    <view class="visitor-credential-info-card">
      <view class="visitor-card-header">
        <image src="/images/icons/host-info.svg" class="visitor-card-icon" />
        <text class="visitor-card-title">被访人信息</text>
      </view>
      <view class="visitor-card-content">
        <view class="visitor-info-row">
          <text class="visitor-info-label">被访人</text>
          <text class="visitor-info-value">{{visitorData.residentName}}</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">手机号码</text>
          <text class="visitor-info-value">{{visitorData.residentPhone}}</text>
        </view>
        <view class="visitor-info-row">
          <text class="visitor-info-label">被访地址</text>
          <text class="visitor-info-value">{{visitorData.residentAddress?visitorData.residentAddress:''}}</text>
        </view>
      </view>
    </view>

    <!-- 二维码展示区 -->
    <view class="visitor-qrcode-container">
      <view class="visitor-qrcode-wrapper">
        <!-- 二维码图片 - 只在未失效时显示 -->
        <image
          wx:if="{{!showExpiredTip}}"
          src="{{imagePath}}"
          bindtap="imgYu"
          mode="aspectFit"
          style="width: 380rpx;height: 380rpx;">
        </image>

        <!-- 失效时显示的占位内容 -->
        <view wx:if="{{showExpiredTip}}" class="qrcode-expired-placeholder">
          <image src="/images/icons/expired.svg" class="expired-icon"></image>
          <text class="expired-text">凭证已失效</text>
        </view>

        <canvas
          catchtouchstart="XXX"
          catchtouchend="XXX"
          catchtouchmove="XXX"
          style="opacity: 0;position:absolute;left:-1000rpx;width: 422rpx;height: 386rpx;"
          canvas-id="mycanvas">
        </canvas>
      </view>



      <view class="visitor-current-time">
        <text>{{currentTime}}</text>
      </view>

      <!-- 失效提示 -->
      <view class="visitor-expired-tip" wx:if="{{showExpiredTip}}">
        <text>已失效</text>
      </view>

      <!-- 使用提示 (物业人员扫码进入时隐藏) -->
      <view class="visitor-usage-tip {{communityType === 'smart' ? 'smart' : 'normal'}}" wx:if="{{!fromScan}}">
        <image src="{{communityType === 'smart' ? '/images/icons/gate.svg' : '/images/icons/guard.svg'}}" class="usage-tip-icon"></image>
        <text>{{usageTip}}</text>
      </view>

      <!-- 核销按钮 (仅在扫码进入且满足条件时显示) -->
      <view class="visitor-verify-section" wx:if="{{showVerifyButton}}">
        <button class="visitor-verify-btn {{isVerifying ? 'verifying' : ''}}"
                bindtap="verifyVisitor"
                disabled="{{isVerifying}}">
          <image src="/images/icons/check-circle.svg" class="verify-icon" wx:if="{{!isVerifying}}"></image>
          <image src="/images/icons/loading.svg" class="verify-icon loading" wx:if="{{isVerifying}}"></image>
          <text>{{isVerifying ? '核销中...' : '核销访客'}}</text>
        </button>
        <view class="verify-tip" wx:if="{{!verifySuccess}}">
          <text>确认访客身份后点击核销</text>
        </view>
        <view class="verify-success" wx:if="{{verifySuccess}}">
          <image src="/images/icons/check-circle-fill.svg" class="success-icon"></image>
          <text>核销成功</text>
        </view>
      </view>

      <!-- 操作图标区 (物业人员扫码进入时隐藏) -->
      <view class="visitor-action-icons" wx:if="{{!fromScan}}">
        <view class="action-icon-row">
          <view class="action-icon-item" bindtap="saveCredential">
            <view class="action-icon-circle save">
              <image src="/images/icons/save.svg" class="action-icon"></image>
            </view>
            <text class="action-icon-text">保存</text>
          </view>

          <view class="action-icon-item" bindtap="showShareOptions">
            <view class="action-icon-circle share">
              <image src="/images/icons/share.svg" class="action-icon"></image>
            </view>
            <text class="action-icon-text">分享</text>
          </view>

          <view class="action-icon-item" bindtap="showCancelConfirm" wx:if="{{visitorData.status === 'wait_visit'}}">
            <view class="action-icon-circle cancel">
              <image src="/images/icons/cancel.svg" class="action-icon"></image>
            </view>
            <text class="action-icon-text">作废</text>
          </view>
        </view>

        <view class="action-icon-row" wx:if="{{visitorData.status === 'wait_visit'}}">
          <view class="action-icon-item" bindtap="showExtendOptions">
            <view class="action-icon-circle extend">
              <image src="/images/icons/extend.svg" class="action-icon"></image>
            </view>
            <text class="action-icon-text">延期</text>
          </view>

          <view class="action-icon-item" bindtap="activateNow" wx:if="{{!isActive}}">
            <view class="action-icon-circle activate">
              <image src="/images/icons/activate.svg" class="action-icon"></image>
            </view>
            <text class="action-icon-text">立即生效</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>


</view>

<!-- 分享选项弹窗 -->
<view class="visitor-share-modal {{showShareModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideShareModal"></view>
  <view class="visitor-modal-content">
    <view class="visitor-modal-header">
      <text class="visitor-modal-title">分享访客凭证</text>
      <view class="visitor-modal-close" bindtap="hideShareModal">
        <image src="/images/icons/close.svg" class="visitor-close-icon" />
      </view>
    </view>
    <view class="visitor-share-options">
      <view class="visitor-share-option" bindtap="shareToWechat">
        <view class="visitor-share-icon wechat">
          <image src="/images/icons/wechat.svg" class="visitor-option-icon" />
        </view>
        <text class="visitor-option-name">微信好友</text>
      </view>
      <view class="visitor-share-option" bindtap="copyLink">
        <view class="visitor-share-icon link">
          <image src="/images/icons/link.svg" class="visitor-option-icon" />
        </view>
        <text class="visitor-option-name">复制链接</text>
      </view>
    </view>
  </view>
</view>

<!-- 延期选项弹窗 -->
<view class="visitor-share-modal {{showExtendModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideExtendModal"></view>
  <view class="visitor-modal-content">
    <view class="visitor-modal-header">
      <text class="visitor-modal-title">延长访客有效期</text>
      <view class="visitor-modal-close" bindtap="hideExtendModal">
        <image src="/images/icons/close.svg" class="visitor-close-icon" />
      </view>
    </view>
    <view class="visitor-modal-body">
      <view class="extend-options">
        <view class="extend-option {{extendDuration === 1 ? 'active' : ''}}" bindtap="selectExtendDuration" data-duration="1">
          <text class="extend-option-value">1小时</text>
        </view>
        <view class="extend-option {{extendDuration === 2 ? 'active' : ''}}" bindtap="selectExtendDuration" data-duration="2">
          <text class="extend-option-value">2小时</text>
        </view>
        <view class="extend-option {{extendDuration === 4 ? 'active' : ''}}" bindtap="selectExtendDuration" data-duration="4">
          <text class="extend-option-value">4小时</text>
        </view>
        <view class="extend-option {{extendDuration === 8 ? 'active' : ''}}" bindtap="selectExtendDuration" data-duration="8">
          <text class="extend-option-value">8小时</text>
        </view>
      </view>
      <view class="extend-info">
        <text>当前过期时间: {{expireTime}}</text>
        <text wx:if="{{newExpireTime}}">延期后过期时间: {{newExpireTime}}</text>
      </view>
    </view>
    <view class="visitor-modal-footer">
      <button class="visitor-modal-btn visitor-modal-cancel-btn" bindtap="hideExtendModal">取消</button>
      <button class="visitor-modal-btn visitor-modal-confirm-btn" bindtap="confirmExtend">确认延期</button>
    </view>
  </view>
</view>

<!-- 网络错误提示 -->
<view class="network-error-toast {{showNetworkError ? 'show' : ''}}">
  <view class="network-error-content">
    <image src="/images/icons/error.svg" class="error-icon"></image>
    <text>{{networkErrorMsg}}</text>
    <button class="retry-btn" bindtap="retryOperation">重试</button>
  </view>
</view>