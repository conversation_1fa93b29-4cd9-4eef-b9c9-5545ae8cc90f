/**
 * 工单API服务类
 * 提供工单管理模块所需的所有API接口
 */

const apiService = require('./api-service');

// API路径
const API_PATHS = {
  // 工单相关
  WORK_ORDERS: '/api/workorders',
  WORK_ORDER_DETAIL: '/api/workorders/{id}',
  WORK_ORDER_PROCESS: '/api/workorders/{id}/process',
  WORK_ORDER_ASSIGN: '/api/workorders/{id}/assign',
  WORK_ORDER_COMPLETE: '/api/workorders/{id}/complete',
  WORK_ORDER_CANCEL: '/api/workorders/{id}/cancel',
  WORK_ORDER_RECORD: '/api/workorders/{id}/records',
  WORK_ORDER_STATISTICS: '/api/workorders/statistics',
  WORK_ORDER_BATCH_ASSIGN: '/api/workorders/batch/assign',
  WORK_ORDER_BATCH_COMPLETE: '/api/workorders/batch/complete',
  WORK_ORDER_DEADLINE: '/api/workorders/{id}/deadline',
  
  // 员工相关
  STAFF_LIST: '/api/staff',
  
  // 文件上传
  UPLOAD_IMAGE: '/api/upload/image'
};

/**
 * 替换URL中的路径参数
 * @param {string} url 包含路径参数的URL
 * @param {Object} params 路径参数对象
 * @returns {string} 替换后的URL
 */
function replacePathParams(url, params) {
  let result = url;
  
  Object.keys(params).forEach(key => {
    result = result.replace(`{${key}}`, params[key]);
  });
  
  return result;
}

/**
 * 获取工单列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 工单列表和分页信息
 */
function getWorkOrders(params = {}) {
  return apiService.get(API_PATHS.WORK_ORDERS, params);
}

/**
 * 获取工单详情
 * @param {string} id 工单ID
 * @returns {Promise<Object>} 工单详情
 */
function getWorkOrderDetail(id) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_DETAIL, { id });
  return apiService.get(url);
}

/**
 * 切换工单置顶状态
 * @param {string} id 工单ID
 * @returns {Promise<Object>} 更新后的工单
 */
function togglePinOrder(id) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_DETAIL, { id });
  return apiService.put(url, { togglePin: true });
}

/**
 * 取消工单
 * @param {string} id 工单ID
 * @param {string} reason 取消原因
 * @returns {Promise<Object>} 更新后的工单
 */
function cancelOrder(id, reason) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_CANCEL, { id });
  return apiService.post(url, { reason });
}

/**
 * 接单并开始处理工单
 * @param {string} id 工单ID
 * @param {string} remark 处理备注
 * @returns {Promise<Object>} 更新后的工单
 */
function processOrder(id, remark) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_PROCESS, { id });
  return apiService.post(url, { remark });
}

/**
 * 分配工单给指定员工
 * @param {string} id 工单ID
 * @param {Object} staff 员工信息
 * @param {string} remark 分配备注
 * @returns {Promise<Object>} 更新后的工单
 */
function assignOrder(id, staff, remark) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_ASSIGN, { id });
  return apiService.post(url, { staffId: staff.id, remark });
}

/**
 * 完成工单
 * @param {string} id 工单ID
 * @param {string} result 处理结果
 * @param {Array<string>} images 处理图片
 * @param {string} remark 完成备注
 * @returns {Promise<Object>} 更新后的工单
 */
function completeOrder(id, result, images = [], remark) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_COMPLETE, { id });
  return apiService.post(url, { result, images, remark });
}

/**
 * 添加工单处理记录
 * @param {string} id 工单ID
 * @param {string} action 处理动作
 * @param {string} remark 处理备注
 * @returns {Promise<Object>} 更新后的工单
 */
function addProcessingRecord(id, action, remark) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_RECORD, { id });
  return apiService.post(url, { action, remark });
}

/**
 * 获取工单统计数据
 * @param {string} timeRange 时间范围（week-周, month-月, quarter-季度, year-年）
 * @returns {Promise<Object>} 统计数据
 */
function getOrderStatistics(timeRange = 'week') {
  return apiService.get(API_PATHS.WORK_ORDER_STATISTICS, { timeRange });
}

/**
 * 批量分配工单
 * @param {Array<string>} orderIds 工单ID数组
 * @param {Object} staff 员工信息
 * @param {string} remark 分配备注
 * @returns {Promise<Object>} 批量分配结果
 */
function batchAssignOrders(orderIds, staff, remark) {
  return apiService.post(API_PATHS.WORK_ORDER_BATCH_ASSIGN, {
    orderIds,
    staffId: staff.id,
    remark
  });
}

/**
 * 批量完成工单
 * @param {Array<string>} orderIds 工单ID数组
 * @param {string} result 处理结果
 * @param {string} remark 完成备注
 * @returns {Promise<Object>} 批量完成结果
 */
function batchCompleteOrders(orderIds, result, remark) {
  return apiService.post(API_PATHS.WORK_ORDER_BATCH_COMPLETE, {
    orderIds,
    result,
    remark
  });
}

/**
 * 设置工单截止时间
 * @param {string} id 工单ID
 * @param {string} deadlineTime 截止时间
 * @returns {Promise<Object>} 更新后的工单
 */
function setOrderDeadline(id, deadlineTime) {
  const url = replacePathParams(API_PATHS.WORK_ORDER_DEADLINE, { id });
  return apiService.post(url, { deadline: deadlineTime });
}

/**
 * 上传工单图片
 * @param {string} filePath 图片路径
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise<Object>} 上传结果
 */
function uploadOrderImage(filePath, onProgress) {
  return apiService.uploadFile(
    API_PATHS.UPLOAD_IMAGE,
    filePath,
    'image',
    { type: 'workorder' },
    { onProgress }
  );
}

/**
 * 获取员工列表
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 员工列表
 */
function getStaffList(params = {}) {
  return apiService.get(API_PATHS.STAFF_LIST, params);
}

// 导出模块
module.exports = {
  getWorkOrders,
  getWorkOrderDetail,
  togglePinOrder,
  cancelOrder,
  processOrder,
  assignOrder,
  completeOrder,
  addProcessingRecord,
  getOrderStatistics,
  batchAssignOrders,
  batchCompleteOrders,
  setOrderDeadline,
  uploadOrderImage,
  getStaffList
};
