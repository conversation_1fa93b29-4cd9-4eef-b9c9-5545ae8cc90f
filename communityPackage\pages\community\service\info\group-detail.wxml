<!--社区兴趣群详情页面-->
<view class="container">

  <!-- 群组基本信息 -->
  <view class="group-header">
    <view class="group-name">{{group.name}}</view>
    <view class="group-stats">
      <view class="group-members">
        <image class="stats-icon" src="/images/icons/user-group.svg" mode="aspectFit"></image>
        <text>{{group.memberCount}}人</text>
      </view>
      <view class="group-created">
        <image class="stats-icon" src="/images/icons/clock.svg" mode="aspectFit"></image>
        <text>{{group.createdTime}}</text>
      </view>
    </view>
  </view>

  <!-- 群组详情卡片 -->
  <info-card title="群组介绍">
    <view class="group-description">{{group.description}}</view>

    <view class="group-activities" wx:if="{{group.activities && group.activities.length > 0}}">
      <view class="activities-title">近期活动</view>
      <view class="activity-item" wx:for="{{group.activities}}" wx:key="index">
        <view class="activity-date">{{item.date}}</view>
        <view class="activity-content">
          <view class="activity-title">{{item.title}}</view>
          <view class="activity-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
  </info-card>

  <!-- 群主信息卡片 -->
  <info-card title="群主信息">
    <view class="owner-info">
      <image class="owner-avatar" src="{{group.ownerAvatar}}" mode="aspectFill"></image>
      <view class="owner-details">
        <view class="owner-name">{{group.ownerName}}</view>
        <view class="owner-intro">{{group.ownerIntro}}</view>
      </view>
    </view>
  </info-card>

  <!-- 入群方式卡片 -->
  <info-card title="入群方式">
    <view class="join-description">{{group.joinDescription}}</view>

    <block wx:if="{{group.joinMethod === 'contact'}}">
      <view class="contact-info">
        <view class="contact-label">联系群主微信</view>
        <view class="contact-value">{{group.contactInfo}}</view>
        <button class="copy-btn" bindtap="copyContact">复制微信号</button>
      </view>
    </block>

    <block wx:if="{{group.joinMethod === 'qrcode' && group.qrcode}}">
      <view class="qrcode-container">
        <view class="qrcode-label">扫描下方二维码加入</view>
        <image class="qrcode-image" src="{{group.qrcode}}" mode="aspectFit" bindtap="previewQrcode"></image>
        <view class="qrcode-tip">点击二维码可查看大图</view>
      </view>
    </block>
  </info-card>

  <!-- 注意事项 -->
  <view class="notice-card">
    <view class="notice-title">
      <image class="notice-icon" src="/images/icons/info.svg" mode="aspectFit"></image>
      <text>注意事项</text>
    </view>
    <view class="notice-content">
      <text>本平台仅为社区兴趣群提供信息展示，不对群内活动负责。请自行甄别信息真实性，注意个人信息安全。</text>
    </view>
  </view>
</view>
