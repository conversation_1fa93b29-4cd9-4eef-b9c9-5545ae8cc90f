/* components/virtual-list/index.wxss */

/* 虚拟列表容器 */
.virtual-list-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 虚拟列表 */
.virtual-list {
  width: 100%;
  height: 100%;
}

/* 列表容器 */
.list-container {
  width: 100%;
  position: relative;
}

/* 列表项 */
.list-item {
  width: 100%;
  box-sizing: border-box;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #ff8c00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 空列表提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.empty-icon-default {
  width: 64px;
  height: 64px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'%3E%3C/path%3E%3Cpolyline points='13 2 13 9 20 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
