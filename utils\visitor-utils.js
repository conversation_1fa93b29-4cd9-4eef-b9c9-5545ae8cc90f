// visitor-utils.js
// 访客工具类

const VisitorUtils = {

  /**
   * 生成访客ID
   * @returns {string} 访客ID
   */
  generateVisitorId: function() {
    return 'V' + Date.now().toString();
  },

  /**
   * 生成防伪签名
   * @param {string} id 访客ID
   * @param {number} timestamp 时间戳
   * @returns {string} 签名
   */
  generateSignature: function(id, timestamp) {
    // 实际应用中应使用更安全的签名算法
    // 这里仅作示例
    const key = 'visitor_secret_key';
    return this.md5(`${id}_${timestamp}_${key}`);
  },

  /**
   * 简单的MD5实现（仅作示例，实际应用中应使用更安全的库）
   * @param {string} str 输入字符串
   * @returns {string} MD5哈希
   */
  md5: function(str) {
    // 实际应用中应使用更安全的MD5库
    // 这里仅返回一个模拟的哈希值
    return `md5_${str}_${Math.random().toString(36).substring(2, 10)}`;
  },

  /**
   * AES加密（仅作示例，实际应用中应使用更安全的库）
   * @param {string} text 明文
   * @returns {string} 密文
   */
  encrypt: function(text) {
    // 实际应用中应使用更安全的加密库
    // 这里仅返回一个模拟的加密结果
    return `encrypted_${text}`;
  },

  /**
   * AES解密（仅作示例，实际应用中应使用更安全的库）
   * @param {string} text 密文
   * @returns {string} 明文
   */
  decrypt: function(text) {
    // 实际应用中应使用更安全的解密库
    // 这里仅返回一个模拟的解密结果
    if (text.startsWith('encrypted_')) {
      return text.substring(10);
    }
    return text;
  }
};

module.exports = VisitorUtils;
