/* pages/renovation/create/create.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
  background-color: #fff;
  position: relative;
}

.progress-bar-container {
  position: absolute;
  top: 60rpx;
  left: 60rpx;
  right: 60rpx;
  height: 4rpx;
  background-color: #E5E5EA;
  z-index: 1;
}

.progress-bar-active {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: #FF9800;
  transition: right 0.3s ease;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-circle {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #E5E5EA;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.step-label {
  font-size: 24rpx;
  color: #8E8E93;
  white-space: nowrap;
}

.step-active .step-circle {
  background-color: #FF9800;
  color: white;
}

.step-active .step-label {
  color: #FF9800;
  font-weight: 500;
}

/* 表单区域 */
.form-section {
  margin: 24rpx 0;
  background-color: white;
  padding: 32rpx;
}

.form-section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #FF3B30;
  margin-left: 8rpx;
}

.form-field {
  position: relative;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #F9F9F9;
  box-sizing: border-box;
}

.form-picker {
  width: 100%;
  height: 88rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #F9F9F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.placeholder {
  color: #8E8E93;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: white;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.next-button {
  width: 100%;
  height: 88rpx;
  background-color: #FF9800;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.next-button[disabled] {
  background-color: #E5E5EA;
  color: #8E8E93;
}