<!--设施添加/编辑页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">{{isEditMode ? '编辑设施' : '添加设施'}}</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 表单内容 -->
  <view class="form-container" wx:if="{{!isLoading}}">
    <!-- 核心信息 -->
    <view class="form-section">
      <view class="section-title">核心信息</view>

      <!-- 设施名称 -->
      <view class="form-group">
        <view class="form-label required">设施名称</view>
        <view class="form-control">
          <input
            class="form-input {{formErrors.name ? 'error' : ''}}"
            placeholder="请输入设施名称"
            value="{{formData.name}}"
            bindinput="onInputChange"
            data-field="name"
          />
          <view class="error-message" wx:if="{{formErrors.name}}">{{formErrors.name}}</view>
        </view>
      </view>

      <!-- 设施编号 -->
      <view class="form-group">
        <view class="form-label required">设施编号</view>
        <view class="form-control">
          <input
            class="form-input {{formErrors.code ? 'error' : ''}}"
            placeholder="请输入设施编号"
            value="{{formData.code}}"
            bindinput="onInputChange"
            data-field="code"
          />
          <view class="error-message" wx:if="{{formErrors.code}}">{{formErrors.code}}</view>
        </view>
      </view>

      <!-- 设施分类 -->
      <view class="form-group">
        <view class="form-label">设施分类</view>
        <view class="form-control">
          <picker
            mode="selector"
            range="{{categoryOptions}}"
            range-key="label"
            value="{{0}}"
            bindchange="onCategoryChange"
          >
            <view class="form-input picker-input">
              <text wx:if="{{formData.category === 'monitor'}}">监控设施</text>
              <text wx:elif="{{formData.category === 'door'}}">门禁设施</text>
              <text wx:elif="{{formData.category === 'fire'}}">消防设施</text>
              <text wx:elif="{{formData.category === 'light'}}">照明设施</text>
              <text wx:elif="{{formData.category === 'elevator'}}">电梯设施</text>
              <text wx:elif="{{formData.category === 'water'}}">给排水设施</text>
              <text wx:elif="{{formData.category === 'hvac'}}">暖通设施</text>
              <text wx:elif="{{formData.category === 'other'}}">其他设施</text>
              <text wx:else>监控设施</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
      </view>

      <!-- 设施位置 -->
      <view class="form-group">
        <view class="form-label required">设施位置</view>
        <view class="form-control">
          <view
            class="form-input location-input {{formErrors.location ? 'error' : ''}}"
            bindtap="showLocationPicker"
          >
            <text>{{formData.location || '请选择设施位置'}}</text>
            <view class="location-picker-icon"></view>
          </view>
          <view class="error-message" wx:if="{{formErrors.location}}">{{formErrors.location}}</view>
        </view>
      </view>

      <!-- 设施状态 -->
      <view class="form-group">
        <view class="form-label">设施状态</view>
        <view class="form-control">
          <view class="status-selector">
            <view
              class="status-option {{formData.status === item.value ? 'active' : ''}}"
              wx:for="{{statusOptions}}"
              wx:key="value"
              bindtap="onStatusChange"
              data-value="{{item.value}}"
              style="{{formData.status === item.value ? 'background-color:' + item.color + '10; color:' + item.color + ';' : ''}}"
            >
              {{item.label}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">设施图片</view>
      <view class="upload-container">
        <view class="upload-list">
          <view class="upload-item" wx:for="{{uploadedImages}}" wx:key="index">
            <image class="upload-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}"></view>
          </view>
          <view class="upload-button" bindtap="uploadImage" wx:if="{{uploadedImages.length < 9}}">
            <view class="upload-icon"></view>
            <text>上传图片</text>
          </view>
        </view>
        <view class="upload-tip">最多上传9张图片，可拍照或从相册选择</view>
      </view>
    </view>

    <!-- 扩展信息 -->
    <view class="form-section">
      <view class="section-title">扩展信息</view>

      <!-- 品牌 -->
      <view class="form-group">
        <view class="form-label">品牌</view>
        <view class="form-control">
          <input
            class="form-input"
            placeholder="请输入设施品牌"
            value="{{formData.brand}}"
            bindinput="onInputChange"
            data-field="brand"
          />
        </view>
      </view>

      <!-- 型号 -->
      <view class="form-group">
        <view class="form-label">型号</view>
        <view class="form-control">
          <input
            class="form-input"
            placeholder="请输入设施型号"
            value="{{formData.model}}"
            bindinput="onInputChange"
            data-field="model"
          />
        </view>
      </view>

      <!-- 供应商 -->
      <view class="form-group">
        <view class="form-label">供应商</view>
        <view class="form-control">
          <input
            class="form-input"
            placeholder="请输入供应商"
            value="{{formData.supplier}}"
            bindinput="onInputChange"
            data-field="supplier"
          />
        </view>
      </view>

      <!-- 联系电话 -->
      <view class="form-group">
        <view class="form-label">联系电话</view>
        <view class="form-control">
          <input
            class="form-input"
            placeholder="请输入联系电话"
            value="{{formData.contactPhone}}"
            bindinput="onInputChange"
            data-field="contactPhone"
            type="number"
            maxlength="11"
          />
        </view>
      </view>

      <!-- 安装日期 -->
      <view class="form-group">
        <view class="form-label">安装日期</view>
        <view class="form-control">
          <view class="form-input date-input" bindtap="showDatePicker" data-type="installDate">
            <text>{{formData.installDate || '请选择安装日期'}}</text>
            <view class="calendar-icon"></view>
          </view>
        </view>
      </view>

      <!-- 保修期至 -->
      <view class="form-group">
        <view class="form-label">保修期至</view>
        <view class="form-control">
          <view class="form-input date-input" bindtap="showDatePicker" data-type="warrantyEndDate">
            <text>{{formData.warrantyEndDate || '请选择保修期结束日期'}}</text>
            <view class="calendar-icon"></view>
          </view>
        </view>
      </view>

      <!-- 负责人 -->
      <view class="form-group">
        <view class="form-label required">负责人</view>
        <view class="form-control">
          <input
            class="form-input {{formErrors.responsiblePerson ? 'error' : ''}}"
            placeholder="请输入负责人"
            value="{{formData.responsiblePerson}}"
            bindinput="onInputChange"
            data-field="responsiblePerson"
          />
          <view class="error-message" wx:if="{{formErrors.responsiblePerson}}">{{formErrors.responsiblePerson}}</view>
        </view>
      </view>

      <!-- 设施描述 -->
      <view class="form-group">
        <view class="form-label">设施描述</view>
        <view class="form-control">
          <textarea
            class="form-textarea"
            placeholder="请输入设施描述"
            value="{{formData.description}}"
            bindinput="onInputChange"
            data-field="description"
            maxlength="500"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 二维码/NFC绑定 -->
    <view class="form-section">
      <view class="section-title">二维码/NFC绑定</view>
      <view class="qrcode-bind-container">
        <view class="qrcode-bind-button" bindtap="showQRCodeBind">
          <view class="qrcode-icon"></view>
          <text>{{qrCodeValue ? '已绑定二维码' : '绑定二维码'}}</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <button
        class="submit-button {{isSubmitting ? 'disabled' : ''}}"
        bindtap="submitForm"
        disabled="{{isSubmitting}}"
      >
        {{isSubmitting ? '提交中...' : (isEditMode ? '保存修改' : '添加设施')}}
      </button>
    </view>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="date-picker-popup {{showDatePicker ? 'active' : ''}}">
    <view class="date-picker-mask" bindtap="hideDatePicker"></view>
    <view class="date-picker-container">
      <view class="date-picker-header">
        <view class="date-picker-cancel" bindtap="hideDatePicker">取消</view>
        <view class="date-picker-title">选择日期</view>
        <view class="date-picker-confirm" bindtap="confirmDatePicker">确定</view>
      </view>
      <picker-view
        class="date-picker"
        value="{{[0, 0, 0]}}"
        bindchange="onDateChange"
      >
        <picker-view-column>
          <!-- 日期选项 -->
        </picker-view-column>
      </picker-view>
    </view>
  </view>

  <!-- 位置选择器弹窗 -->
  <view class="location-picker-popup {{showLocationPicker ? 'active' : ''}}">
    <view class="location-picker-mask" bindtap="hideLocationPicker"></view>
    <view class="location-picker-container">
      <view class="location-picker-header">
        <view class="location-picker-cancel" bindtap="hideLocationPicker">取消</view>
        <view class="location-picker-title">选择位置</view>
        <view class="location-picker-confirm" bindtap="confirmLocationPicker">确定</view>
      </view>
      <view class="location-picker-content">
        <map
          class="location-map"
          latitude="{{latitude}}"
          longitude="{{longitude}}"
          markers="{{markers}}"
          bindmarkertap="markerTap"
          bindtap="mapTap"
          show-location
        ></map>
        <view class="location-tip">点击地图选择设施位置</view>
      </view>
    </view>
  </view>

  <!-- 二维码绑定弹窗 -->
  <view class="qrcode-bind-popup {{showQRCodeBindDialog ? 'active' : ''}}">
    <view class="qrcode-bind-mask" bindtap="hideQRCodeBind"></view>
    <view class="qrcode-bind-dialog">
      <view class="qrcode-bind-title">绑定二维码</view>
      <view class="qrcode-bind-content">
        <view class="qrcode-bind-tip">扫描设施二维码进行绑定，绑定后可通过扫码快速查看设施信息</view>
        <button class="qrcode-scan-button" bindtap="scanQRCode">
          <view class="scan-icon"></view>
          <text>扫描二维码</text>
        </button>
      </view>
      <view class="qrcode-bind-footer">
        <button class="qrcode-bind-cancel" bindtap="hideQRCodeBind">取消</button>
      </view>
    </view>
  </view>
</view>
