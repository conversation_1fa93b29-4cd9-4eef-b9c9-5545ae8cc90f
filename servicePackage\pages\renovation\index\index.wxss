/* pages/renovation/index/index.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 80rpx;
  box-sizing: border-box;
  position: relative;
}

/* 搜索框 */
.search-container {
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-sizing: border-box;
  width: 100%;
}

.search-bar {
  position: relative;
  height: 76rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-input {
  width: 100%;
  height: 76rpx;
  border-radius: 20rpx;
  border: none;
  background-color: rgba(118, 118, 128, 0.12);
  padding: 0 72rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  text-align: left;
}

.search-icon {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #8E8E93;
}

.clear-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #8E8E93;
}

/* 分段控制器 */
.segmented-control {
  margin: 16rpx 32rpx 32rpx;
  display: flex;
  height: 72rpx;
  border-radius: 16rpx;
  background-color: rgba(118, 118, 128, 0.12);
  padding: 4rpx;
  position: relative;
}

.segment {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #8E8E93;
  position: relative;
  z-index: 2;
}

.segment-active {
  color: #333;
}

.segment-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  border-radius: 12rpx;
  background-color: white;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 1;
  transition: transform 0.3s ease;
}

/* 申请列表 */
.application-list {
  padding: 0 32rpx;
}

.application-card {
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.application-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.application-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.application-status {
  padding: 8rpx 20rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.status-approved {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.status-rejected {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.status-completed {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.application-info {
  padding: 32rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.application-footer {
  padding: 24rpx 32rpx;
  background-color: #F2F2F7;
  display: flex;
  justify-content: flex-end;
}

.app-btn {
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

.btn-outline {
  border: 1rpx solid #FF9800;
  color: #FF9800;
  background-color: transparent;
  margin-right: 16rpx;
}

.btn-primary {
  background-color: #FF9800;
  color: white;
  border: none;
}

/* 无数据提示 */
.empty-state {
  padding: 40rpx 32rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #8E8E93;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-description {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 20rpx;
}

/* 新建申请按钮 */
.create-btn {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 100rpx;
  background-color: #FF9800;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.3);
  z-index: 10;
}

.create-btn-icon {
  margin-right: 16rpx;
  font-size: 40rpx;
  font-weight: 400;
}