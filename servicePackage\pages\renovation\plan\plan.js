// pages/renovation/plan/plan.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    formData: {
      areas: [],
      otherAreaDesc: '',
      items: [],
      otherItemDesc: '',
      description: '',
      hasBearingWall: null,
      bearingWallDesc: ''
    },
    // 添加布尔值直接控制选中状态
    areaSelected: {
      livingRoom: false,
      bedroom: false,
      kitchen: false,
      bathroom: false,
      balcony: false,
      other: false
    },
    itemSelected: {
      wall: false,
      floor: false,
      ceiling: false,
      water: false,
      electric: false,
      furniture: false,
      other: false
    },
    isFormValid: false,
    basicInfo: {},
    forceUpdate: Date.now(), // 添加强制更新变量
    forceRefresh: Date.now(), // 额外的刷新变量
    lastUpdated: Date.now(), // 最后更新时间
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时，重新检查表单有效性
    this.checkFormValidity();
    console.log('页面显示时的数据:', this.data.formData);

    // 同步布尔值选中状态
    const areaSelected = {
      livingRoom: this.data.formData.areas.includes('livingRoom'),
      bedroom: this.data.formData.areas.includes('bedroom'),
      kitchen: this.data.formData.areas.includes('kitchen'),
      bathroom: this.data.formData.areas.includes('bathroom'),
      balcony: this.data.formData.areas.includes('balcony'),
      other: this.data.formData.areas.includes('other')
    };

    const itemSelected = {
      wall: this.data.formData.items.includes('wall'),
      floor: this.data.formData.items.includes('floor'),
      ceiling: this.data.formData.items.includes('ceiling'),
      water: this.data.formData.items.includes('water'),
      electric: this.data.formData.items.includes('electric'),
      furniture: this.data.formData.items.includes('furniture'),
      other: this.data.formData.items.includes('other')
    };

    // 强制刷新视图
    this.setData({
      areaSelected: areaSelected,
      itemSelected: itemSelected,
      forceRefresh: Date.now(),
      lastUpdated: Date.now()
    });

    console.log('页面显示时同步后的布尔值状态:', areaSelected);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取上一步保存的基本信息
    const basicInfo = wx.getStorageSync('renovationFormData') || {};

    // 获取之前保存的施工方案信息（如果有）
    let planInfo = wx.getStorageSync('renovationPlanData') || {
      areas: [],
      otherAreaDesc: '',
      items: [],
      otherItemDesc: '',
      description: '',
      hasBearingWall: null,
      bearingWallDesc: ''
    };

    // 确保areas和items是数组
    if (!Array.isArray(planInfo.areas)) {
      planInfo.areas = [];
      console.warn('areas不是数组，已初始化为空数组');
    }
    if (!Array.isArray(planInfo.items)) {
      planInfo.items = [];
      console.warn('items不是数组，已初始化为空数组');
    }

    // 强制转换为数组（以防万一）
    planInfo.areas = Array.isArray(planInfo.areas) ? planInfo.areas : [];
    planInfo.items = Array.isArray(planInfo.items) ? planInfo.items : [];

    console.log('初始化数据:', planInfo);
    console.log('areas类型:', typeof planInfo.areas, '是否数组:', Array.isArray(planInfo.areas));
    console.log('items类型:', typeof planInfo.items, '是否数组:', Array.isArray(planInfo.items));

    // 初始化布尔值选中状态
    const areaSelected = {
      livingRoom: planInfo.areas.includes('livingRoom'),
      bedroom: planInfo.areas.includes('bedroom'),
      kitchen: planInfo.areas.includes('kitchen'),
      bathroom: planInfo.areas.includes('bathroom'),
      balcony: planInfo.areas.includes('balcony'),
      other: planInfo.areas.includes('other')
    };

    const itemSelected = {
      wall: planInfo.items.includes('wall'),
      floor: planInfo.items.includes('floor'),
      ceiling: planInfo.items.includes('ceiling'),
      water: planInfo.items.includes('water'),
      electric: planInfo.items.includes('electric'),
      furniture: planInfo.items.includes('furniture'),
      other: planInfo.items.includes('other')
    };

    this.setData({
      basicInfo: basicInfo,
      formData: planInfo,
      areaSelected: areaSelected,
      itemSelected: itemSelected
    }, () => {
      console.log('页面加载后的数据:', this.data.formData);
      console.log('areas类型:', typeof this.data.formData.areas, '是否数组:', Array.isArray(this.data.formData.areas));
      console.log('items类型:', typeof this.data.formData.items, '是否数组:', Array.isArray(this.data.formData.items));

      // 测试布尔值选中状态
      console.log('客厅选中状态:', this.data.areaSelected.livingRoom);
      console.log('厨房选中状态:', this.data.areaSelected.kitchen);
      console.log('墙面处理选中状态:', this.data.itemSelected.wall);
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 通用的选项切换函数 - 使用布尔值控制
   */
  toggleOption(e) {
    console.log('选项点击:', e.currentTarget.dataset);
    const { type, value } = e.currentTarget.dataset;

    // 确定当前选中状态
    const selectedKey = type === 'areas' ? 'areaSelected' : 'itemSelected';
    const currentSelected = this.data[selectedKey][value];
    console.log(`${value}当前选中状态:`, currentSelected);

    // 切换选中状态
    const newSelected = !currentSelected;
    console.log(`${value}新选中状态:`, newSelected);

    // 准备更新数据
    const updateData = {};

    // 更新布尔值状态
    updateData[`${selectedKey}.${value}`] = newSelected;

    // 同步更新数组
    let newArray = [...this.data.formData[type]];

    if (newSelected) {
      // 如果新状态是选中，则添加到数组
      if (!newArray.includes(value)) {
        newArray.push(value);
      }
    } else {
      // 如果新状态是取消选中，则从数组中移除
      newArray = newArray.filter(item => item !== value);

      // 如果取消选中"其他"，则清空相关说明
      if (value === 'other') {
        if (type === 'areas') {
          updateData['formData.otherAreaDesc'] = '';
        } else if (type === 'items') {
          updateData['formData.otherItemDesc'] = '';
        }
      }
    }

    // 更新数组
    updateData[`formData.${type}`] = newArray;

    // 添加时间戳强制更新
    updateData.lastUpdated = Date.now();

    // 更新数据
    this.setData(updateData, () => {
      console.log(`${value}更新后选中状态:`, this.data[selectedKey][value]);
      console.log(`更新后的${type}数组:`, this.data.formData[type]);

      // 检查表单有效性
      this.checkFormValidity();

      // 额外的强制刷新
      setTimeout(() => {
        this.setData({
          forceRefresh: Date.now()
        });
      }, 50);
    });
  },

  /**
   * 其他区域说明输入
   */
  onOtherAreaInput(e) {
    // 创建整个formData的副本
    const newFormData = JSON.parse(JSON.stringify(this.data.formData));
    newFormData.otherAreaDesc = e.detail.value;

    // 更新整个formData对象
    this.setData({
      formData: newFormData
    }, () => {
      // 检查表单有效性
      this.checkFormValidity();
    });
  },

  /**
   * 切换承重墙选择
   */
  toggleBearingWall(e) {
    console.log('承重墙选择:', e.currentTarget.dataset.value);
    const value = e.currentTarget.dataset.value;

    // 创建整个formData的副本
    const newFormData = JSON.parse(JSON.stringify(this.data.formData));

    // 更新承重墙选择
    newFormData.hasBearingWall = value;

    // 如果选择"否"，则清空承重墙改造说明
    if (value === false) {
      newFormData.bearingWallDesc = '';
      console.log('已清空承重墙改造说明');
    }

    // 更新整个formData对象
    this.setData({
      formData: newFormData
    }, () => {
      console.log('承重墙选择更新为:', this.data.formData.hasBearingWall);
      // 检查表单有效性
      this.checkFormValidity();
    });
  },

  /**
   * 其他项目说明输入
   */
  onOtherItemInput(e) {
    // 创建整个formData的副本
    const newFormData = JSON.parse(JSON.stringify(this.data.formData));
    newFormData.otherItemDesc = e.detail.value;

    // 更新整个formData对象
    this.setData({
      formData: newFormData
    }, () => {
      // 检查表单有效性
      this.checkFormValidity();
    });
  },

  /**
   * 施工详情输入
   */
  onDescriptionInput(e) {
    // 创建整个formData的副本
    const newFormData = JSON.parse(JSON.stringify(this.data.formData));
    newFormData.description = e.detail.value;

    // 更新整个formData对象
    this.setData({
      formData: newFormData
    }, () => {
      // 检查表单有效性
      this.checkFormValidity();
    });
  },



  /**
   * 承重墙改造说明输入
   */
  onBearingWallDescInput(e) {
    // 创建整个formData的副本
    const newFormData = JSON.parse(JSON.stringify(this.data.formData));
    newFormData.bearingWallDesc = e.detail.value;

    // 更新整个formData对象
    this.setData({
      formData: newFormData
    }, () => {
      // 检查表单有效性
      this.checkFormValidity();
    });
  },

  /**
   * 检查表单有效性
   */
  checkFormValidity() {
    const formData = this.data.formData;

    console.log('检查表单有效性，当前数据:', formData);

    // 确保areas和items是数组
    if (!Array.isArray(formData.areas) || !Array.isArray(formData.items)) {
      console.error('areas或items不是数组:', formData.areas, formData.items);
      console.log('areas类型:', typeof formData.areas);
      console.log('items类型:', typeof formData.items);

      // 尝试修复数组问题
      const fixedFormData = {...formData};
      if (!Array.isArray(fixedFormData.areas)) {
        fixedFormData.areas = [];
        console.warn('已修复areas为空数组');
      }
      if (!Array.isArray(fixedFormData.items)) {
        fixedFormData.items = [];
        console.warn('已修复items为空数组');
      }

      this.setData({
        formData: fixedFormData,
        isFormValid: false,
        lastUpdated: Date.now() // 强制更新
      });
      return;
    }

    // 检查必填字段
    const areasValid = formData.areas.length > 0;
    const itemsValid = formData.items.length > 0;
    const descriptionValid = formData.description.length > 0;
    const bearingWallValid = formData.hasBearingWall !== null;

    console.log('区域有效性:', areasValid, '项目有效性:', itemsValid);
    console.log('描述有效性:', descriptionValid, '承重墙选择有效性:', bearingWallValid);

    let isValid = areasValid && itemsValid && descriptionValid && bearingWallValid;

    // 如果选择了"其他"区域，则必须填写其他区域说明
    const otherAreaSelected = formData.areas.includes('other');
    const otherAreaDescValid = !otherAreaSelected || (otherAreaSelected && formData.otherAreaDesc);
    if (!otherAreaDescValid) {
      console.log('选择了其他区域但未填写说明');
      isValid = false;
    }

    // 如果选择了"其他"项目，则必须填写其他项目说明
    const otherItemSelected = formData.items.includes('other');
    const otherItemDescValid = !otherItemSelected || (otherItemSelected && formData.otherItemDesc);
    if (!otherItemDescValid) {
      console.log('选择了其他项目但未填写说明');
      isValid = false;
    }

    // 如果选择了"是"涉及承重墙改造，则必须填写承重墙改造说明
    const bearingWallDescValid = formData.hasBearingWall !== true || (formData.hasBearingWall === true && formData.bearingWallDesc);
    if (!bearingWallDescValid) {
      console.log('选择了涉及承重墙但未填写说明');
      isValid = false;
    }

    console.log('表单有效性:', isValid);
    console.log('其他区域说明有效性:', otherAreaDescValid);
    console.log('其他项目说明有效性:', otherItemDescValid);
    console.log('承重墙说明有效性:', bearingWallDescValid);

    this.setData({
      isFormValid: isValid,
      lastUpdated: Date.now() // 强制更新
    });
  },

  /**
   * 返回上一步
   */
  goToPrevStep() {
    // 保存当前表单数据
    wx.setStorageSync('renovationPlanData', this.data.formData);

    // 返回上一页
    wx.navigateBack();
  },

  /**
   * 进入下一步
   */
  goToNextStep() {
    // 检查表单有效性
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    // 保存表单数据
    wx.setStorageSync('renovationPlanData', this.data.formData);

    // 如果涉及承重墙改造，显示警告提示
    if (this.data.formData.hasBearingWall) {
      wx.showModal({
        title: '重要提示',
        content: '您的装修方案涉及承重墙改造，请确保已经获得专业的结构评估报告，并在材料上传环节提供相关证明文件。',
        confirmText: '我已了解',
        confirmColor: '#FF9800',
        success: (res) => {
          if (res.confirm) {
            // 跳转到下一步
            wx.navigateTo({
              url: '/servicePackage/pages/renovation/materials/materials'
            });
          }
        }
      });
    } else {
      // 跳转到下一步
      wx.navigateTo({
        url: '/servicePackage/pages/renovation/materials/materials'
      });
    }
  }
})