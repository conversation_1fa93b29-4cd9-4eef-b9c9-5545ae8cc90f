/* 访客核销页面样式 */
.visitor-verify-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 标签页样式 */
.tab-header {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
  color: #666;
}

.tab-item.active {
  color: #ff8c00;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #ff8c00;
  border-radius: 3rpx;
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

/* 扫码核销样式 */
.scan-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.scan-icon-container {
  width: 200rpx;
  height: 200rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.scan-icon {
  width: 120rpx;
  height: 120rpx;
}

.scan-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.scan-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}

.scan-button {
  width: 80%;
  height: 88rpx;
  background-color: #ff8c00;
  color: #fff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.button-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

/* 搜索核销样式 */
.search-container {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.search-box {
  flex: 1;
  height: 80rpx;
  background-color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
  border: 1rpx solid #eee;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon image {
  width: 32rpx;
  height: 32rpx;
}

.search-button {
  width: 160rpx;
  height: 80rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 搜索结果样式 */
.search-results {
  padding: 0 30rpx;
}

.result-header {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.result-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.result-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.result-item:last-child {
  border-bottom: none;
}

.visitor-info {
  flex: 1;
}

.visitor-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.visitor-phone {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.visitor-details {
  margin-bottom: 8rpx;
}

.visitor-purpose {
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
}

.visitor-time {
  font-size: 24rpx;
  color: #999;
}

.visitor-note {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.visitor-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  align-self: flex-start;
}

/* 访客状态样式 */
.status-wait_visit {
  background-color: #fff5e6;
  color: #ff8c00;
}

.status-visited {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-expired {
  background-color: #fff2e8;
  color: #fa8c16;
}

.status-canceled {
  background-color: #fff1f0;
  color: #f5222d;
}

.arrow-right {
  display: flex;
  align-items: center;
}

.arrow-right image {
  width: 32rpx;
  height: 32rpx;
}

/* 加载更多样式 */
.load-more {
  padding: 30rpx;
  text-align: center;
}

.loading {
  color: #999;
  font-size: 28rpx;
}

.load-more-text {
  color: #ccc;
  font-size: 26rpx;
}

.no-more {
  padding: 30rpx;
  text-align: center;
  color: #ccc;
  font-size: 26rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 无搜索结果样式 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.no-data-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.no-results text {
  font-size: 28rpx;
  color: #999;
}

/* 加载中样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 28rpx;
  color: #999;
}

/* 核验弹窗样式 */
.verify-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.verify-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.verify-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.modal-body {
  padding: 30rpx;
}

.verify-info {
  margin-bottom: 30rpx;
}

.verify-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.verify-item {
  display: flex;
  margin-bottom: 16rpx;
}

.verify-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.verify-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.verify-tip {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.verify-tip text {
  font-size: 24rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin: 0 15rpx;
}

.modal-cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.modal-confirm-btn {
  background-color: #ff8c00;
  color: #fff;
}
