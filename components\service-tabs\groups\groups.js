// 社区群组组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    groups: [
      {
        id: 1,
        name: '晨跑健身小组',
        description: '每天早上6:30在小区中央广场集合，一起晨跑健身，享受健康生活。',
        memberCount: 28,
        ownerName: '张先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      },
      {
        id: 2,
        name: '读书分享会',
        description: '每周日下午3点在社区活动室举行读书分享会，交流读书心得，共同成长。',
        memberCount: 15,
        ownerName: '李女士',
        ownerAvatar: 'https://randomuser.me/api/portraits/women/44.jpg'
      },
      {
        id: 3,
        name: '棋牌娱乐群',
        description: '喜欢棋牌游戏的邻居们快来加入，周末一起娱乐放松。',
        memberCount: 32,
        ownerName: '王先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/68.jpg'
      },
      {
        id: 4,
        name: '社区志愿者',
        description: '关注社区公益，参与志愿服务，共建美好家园。',
        memberCount: 45,
        ownerName: '赵女士',
        ownerAvatar: 'https://randomuser.me/api/portraits/women/22.jpg'
      },
      {
        id: 5,
        name: '宠物之家',
        description: '宠物饲养交流，遛狗活动组织，宠物用品分享。',
        memberCount: 38,
        ownerName: '刘先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/42.jpg'
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 导航到群组详情页
    navigateToGroupDetail: function(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/communityPackage/pages/community/service/info/group-detail?id=${id}`,
        fail: (err) => {
          console.error('导航失败:', err)
          if (err.errMsg && err.errMsg.indexOf('webviewId') > -1) {
            wx.redirectTo({
              url: `/communityPackage/pages/community/service/info/group-detail?id=${id}`
            })
          }
        }
      })
    }
  }
})
