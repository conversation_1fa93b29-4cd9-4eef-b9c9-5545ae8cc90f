// community.js
const util = require('../../../utils/util.js')

Page({
  data: {
    events: [
      {
        id: 1,
        title: '中秋团圆晚会',
        description: '共度中秋佳节，邻里同欢。活动包括猜灯谜、品月饼、文艺表演等多种形式，欢迎社区居民踊跃参与。',
        date: '09-29',
        participants: 42,
        image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      },
      {
        id: 2,
        title: '社区健康讲座',
        description: '邀请三甲医院专家主讲，内容包括常见疾病预防和健康生活方式，适合所有年龄段居民参加。',
        date: '10-05',
        participants: 28,
        image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      },
      {
        id: 3,
        title: '亲子运动会',
        description: '为增进亲子关系，特举办此次运动会。活动包括亲子接力、趣味游戏等多个环节，适合3-12岁儿童及其家长参加。',
        date: '10-12',
        participants: 35,
        image: 'https://images.unsplash.com/photo-1527529482837-4698179dc6ce?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      },
      {
        id: 4,
        title: '社区读书会',
        description: '每月一次的读书分享活动，本月主题为经典文学作品赏析，欢迎文学爱好者参加。',
        date: '10-08',
        participants: 22,
        image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      }
    ]
  },

  navigateToEventDetail: function (e) {
    const event = e.currentTarget.dataset.event
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: `/pages/community/event-detail/event-detail?id=${event.id}`
      })
    } else {
      util.showAuthModal()
    }
  }
})
