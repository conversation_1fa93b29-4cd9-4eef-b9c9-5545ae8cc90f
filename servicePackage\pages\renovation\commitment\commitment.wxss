/* pages/renovation/commitment/commitment.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
  background-color: #fff;
  position: relative;
}

.progress-bar-container {
  position: absolute;
  top: 60rpx;
  left: 60rpx;
  right: 60rpx;
  height: 4rpx;
  background-color: #E5E5EA;
  z-index: 1;
}

.progress-bar-active {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: #FF9800;
  transition: right 0.3s ease;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-circle {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #E5E5EA;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.step-label {
  font-size: 24rpx;
  color: #8E8E93;
  white-space: nowrap;
}

.step-active .step-circle {
  background-color: #FF9800;
  color: white;
}

.step-active .step-label {
  color: #FF9800;
  font-weight: 500;
}

.step-complete .step-circle {
  background-color: #FF9800;
  color: white;
}

.step-complete .step-label {
  color: #333;
  font-weight: 500;
}

/* 承诺书内容 */
.commitment-section {
  margin: 24rpx 32rpx;
  background-color: white;
  padding: 32rpx;
  border-radius: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.commitment-content {
  height: 400rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 32rpx;
  background-color: #F9F9F9;
  width: auto;
  margin: 0 auto;
}

.commitment-header {
  text-align: center;
  margin-bottom: 32rpx;
  width: 100%;
}

.commitment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.commitment-subtitle {
  font-size: 28rpx;
  color: #8E8E93;
}

.commitment-text {
  margin-bottom: 32rpx;
  text-align: left;
}

.paragraph-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin: 24rpx 0 16rpx;
}

.paragraph {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
  text-align: justify;
}

.commitment-footer {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx dashed #E5E5EA;
}

.signature-row {
  display: flex;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.signature-name, .signature-date {
  margin-left: 16rpx;
  font-weight: 500;
}

/* 签名区域 */
.signature-section {
  margin: 24rpx 32rpx;
  background-color: white;
  padding: 32rpx;
  border-radius: 16rpx;
}

.signature-description {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 24rpx;
}

.signature-pad-container {
  position: relative;
  width: 100%;
  height: 240rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: white;
}

.signature-pad {
  width: 100%;
  height: 100%;
  background-color: white;
  position: relative;
  z-index: 1;
  touch-action: none; /* 防止触摸事件被系统处理 */
}

.signature-actions {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
}

.clear-button {
  padding: 8rpx 24rpx;
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  font-size: 24rpx;
  border-radius: 32rpx;
  border: none;
  line-height: 1.5;
}

/* 同意条款 */
.agreement-section {
  margin: 24rpx 32rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 8rpx;
  border: 1rpx solid #C7C7CC;
  margin-right: 16rpx;
  position: relative;
  background-color: white;
}

.checkbox-item.checked .checkbox-icon {
  background-color: #FF9800;
  border-color: #FF9800;
}

.checkbox-item.checked .checkbox-icon::after {
  content: '';
  position: absolute;
  top: 8rpx;
  left: 12rpx;
  width: 8rpx;
  height: 16rpx;
  border: solid white;
  border-width: 0 3rpx 3rpx 0;
  transform: rotate(45deg);
}

.agreement-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: white;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: space-between;
}

.back-button {
  width: 30%;
  height: 88rpx;
  background-color: #F2F2F7;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.submit-button {
  width: 65%;
  height: 88rpx;
  background-color: #FF9800;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.submit-button[disabled] {
  background-color: #E5E5EA;
  color: #8E8E93;
}