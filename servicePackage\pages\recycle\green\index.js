// pages/recycle/green/index.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    // Banner口号列表
    slogans: [
      "垃圾分类，绿色生活",
      "保护环境，从我做起",
      "减少碳排放，共建美好家园",
      "循环利用，珍惜资源",
      "分类投放，让地球更美好"
    ],
    // 当前显示的slogan索引
    currentSloganIndex: 0,
    // 当前选中的垃圾分类Tab索引
    currentTypeIndex: 0,
    // 当前展开的混淆项索引，-1表示都不展开
    currentConfusionIndex: -1,
    // 各个区域的展开状态
    isKnowledgeSectionExpanded: false,
    isConfusionSectionExpanded: false,
    isCommunityExpanded: false,
    areAllConfusionItemsExpanded: false,
    // 清运时间详情的显示状态
    isScheduleDetailVisible: false,
    // 碳减排树的显示状态
    isCarbonTreeVisible: false,
    // 加载状态
    isLoading: true,
    recycleTypes: [
      {
        id: 'recyclable',
        name: '可回收物',
        color: '#2196f3',
        icon: 'recyclable',
        examples: ['纸张', '塑料', '金属', '玻璃', '织物'],
        description: '可回收物是指适宜回收和资源利用的废弃物，主要包括废纸、塑料、玻璃、金属和织物等。',
        requirements: '投放要求：轻投轻放，清洁干燥，避免污染。纸张应保持平整，塑料瓶、罐子等应清空内容物并压扁。'
      },
      {
        id: 'hazardous',
        name: '有害垃圾',
        color: '#f44336',
        icon: 'hazardous',
        examples: ['电池', '灯管', '药品', '油漆', '化妆品'],
        description: '有害垃圾是指对人体健康或自然环境造成直接或潜在危害的废弃物。',
        requirements: '投放要求：轻放轻放，保持包装或包裹完好，单独投放，不要混入其他垃圾。'
      },
      {
        id: 'kitchen',
        name: '厨余垃圾',
        color: '#4caf50',
        icon: 'kitchen',
        examples: ['食物残渣', '果皮', '茶叶', '骨头', '花卉'],
        description: '厨余垃圾是指日常生活中产生的食物类废物，包括食物残渣、果皮等易腐烂的有机物质。',
        requirements: '投放要求：沥干水分，去除包装物，有包装物的厨余垃圾应将包装物取出后分类投放。'
      },
      {
        id: 'other',
        name: '其他垃圾',
        color: '#9e9e9e',
        icon: 'other',
        examples: ['卫生纸', '尿不湿', '烟头', '陶瓷', '污染物'],
        description: '其他垃圾是指除可回收物、有害垃圾、厨余垃圾以外的其他生活废弃物。',
        requirements: '投放要求：尽量沥干水分，难以辨别类别的生活垃圾投入其他垃圾容器内。'
      }
    ],
    // 容易混淆的垃圾分类项目 - 简化版
    confusionItems: [
      {
        title: '纸巾vs卫生纸',
        content: '纸巾和卫生纸都是不可回收的其他垃圾，因为它们的纤维已被破坏，无法再利用。',
        types: ['other', 'other']
      },
      {
        title: '塑料袋vs塑料瓶',
        content: '干净的塑料瓶是可回收物；而污染的塑料袋属于其他垃圾，干净的可回收。',
        types: ['recyclable', 'recyclable']
      },
      {
        title: '果壳vs果核',
        content: '果壳和果核都属于厨余垃圾，可以被生物降解，制成肥料。',
        types: ['kitchen', 'kitchen']
      }
    ],
    // 清运时间表
    currentDate: '',
    schedules: [
      {
        type: '可回收物',
        time: '每周一、周四 09:00-11:00',
        color: '#2196f3'
      },
      {
        type: '厨余垃圾',
        time: '每天 08:00-10:00, 18:00-20:00',
        color: '#4caf50'
      },
      {
        type: '有害垃圾',
        time: '每周三 14:00-16:00',
        color: '#f44336'
      },
      {
        type: '其他垃圾',
        time: '每天 08:00-22:00',
        color: '#9e9e9e'
      }
    ],
    // 用户环保数据
    userStats: {
      points: 75,
      recycleCount: 8,
      carbonReduction: 12.5
    },
    // 用户徽章
    userBadges: [
      {
        id: 'beginner',
        name: '初级环保者',
        unlocked: true
      },
      {
        id: 'recycler',
        name: '回收达人',
        unlocked: false
      },
      {
        id: 'expert',
        name: '分类专家',
        unlocked: false
      },
      {
        id: 'carbon',
        name: '减碳先锋',
        unlocked: false
      },
      {
        id: 'master',
        name: '环保大师',
        unlocked: false
      },
      {
        id: 'leader',
        name: '社区领袖',
        unlocked: false
      }
    ],
    searchKeyword: '',
    searchResults: []
  },

  onLoad: function() {
    // 设置当前日期
    const today = new Date()
    const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    this.setData({
      currentDate: formattedDate
    })

    // 模拟加载过程
    setTimeout(() => {
      this.setData({
        isLoading: false
      })
    }, 1000)

    // 启动slogan轮播定时器
    this.startSloganInterval()
  },

  // 启动slogan轮播定时器
  startSloganInterval: function() {
    // 每3秒切换一次slogan
    this.sloganTimer = setInterval(() => {
      const nextIndex = (this.data.currentSloganIndex + 1) % this.data.slogans.length
      this.setData({
        currentSloganIndex: nextIndex
      })
    }, 3000)
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    if (this.sloganTimer) {
      clearInterval(this.sloganTimer)
    }
  },

  onHide: function() {
    // 页面隐藏时清除定时器
    if (this.sloganTimer) {
      clearInterval(this.sloganTimer)
    }
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 重新启动slogan轮播定时器
    this.startSloganInterval()
  },

  // 导航到分类指南页面
  navigateToGuide: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/guide/index'
    })
  },

  // 导航到投放地图页面
  navigateToMap: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/map/index'
    })
  },

  // 导航到积分中心页面
  navigateToPoints: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/contribution/index'
    })
  },

  // 导航到社区互动页面
  navigateToCommunity: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  // 显示垃圾分类详情
  showTypeDetail: function(e) {
    const typeId = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/servicePackage/pages/recycle/guide/detail?type=${typeId}`
    })
  },

  // 搜索垃圾分类
  searchWaste: function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    if (keyword) {
      // 模拟搜索结果
      const results = [
        {
          name: '塑料瓶',
          type: 'recyclable',
          typeName: '可回收物',
          typeColor: '#2196f3'
        },
        {
          name: '废电池',
          type: 'hazardous',
          typeName: '有害垃圾',
          typeColor: '#f44336'
        },
        {
          name: '香蕉皮',
          type: 'kitchen',
          typeName: '厨余垃圾',
          typeColor: '#4caf50'
        },
        {
          name: '卫生纸',
          type: 'other',
          typeName: '其他垃圾',
          typeColor: '#9e9e9e'
        }
      ].filter(item => item.name.includes(keyword))

      this.setData({
        searchResults: results
      })
    } else {
      this.setData({
        searchResults: []
      })
    }
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      searchResults: []
    })
  },

  // 拍照识别垃圾分类 - 直接跳转到AI拍照识别页面
  takePhotoForRecognition: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/camera/index'
    })
  },

  // 导航到预约回收页面
  navigateToAppointment: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/appointment/index'
    })
  },

  // 导航到我的预约页面
  navigateToMyAppointments: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/my-appointments/index'
    })
  },

  // 切换垃圾分类Tab
  switchTypeTab: function(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentTypeIndex: index
    })
  },

  // 切换混淆项的展开/折叠状态
  toggleConfusionItem: function(e) {
    const index = e.currentTarget.dataset.index
    // 如果点击的是当前已展开的项，则折叠；否则展开点击的项
    const newIndex = this.data.currentConfusionIndex === index ? -1 : index
    this.setData({
      currentConfusionIndex: newIndex
    })
  },

  // 切换知识区域的展开/折叠状态
  toggleKnowledgeSection: function() {
    this.setData({
      isKnowledgeSectionExpanded: !this.data.isKnowledgeSectionExpanded
    })
  },

  // 切换混淆区域的展开/折叠状态
  toggleConfusionSection: function() {
    this.setData({
      isConfusionSectionExpanded: !this.data.isConfusionSectionExpanded
    })
  },

  // 切换社区互动区域的展开/折叠状态
  toggleCommunitySection: function() {
    this.setData({
      isCommunityExpanded: !this.data.isCommunityExpanded
    })
  },

  // 切换所有混淆项的展开/折叠状态
  toggleExpandAllConfusionItems: function() {
    this.setData({
      areAllConfusionItemsExpanded: !this.data.areAllConfusionItemsExpanded
    })
  },

  // 显示清运时间详情
  showScheduleDetail: function() {
    this.setData({
      isScheduleDetailVisible: true
    })
  },

  // 隐藏清运时间详情
  hideScheduleDetail: function() {
    this.setData({
      isScheduleDetailVisible: false
    })
  },

  // 显示碳减排树
  showCarbonTree: function() {
    this.setData({
      isCarbonTreeVisible: true
    })
  },

  // 隐藏碳减排树
  hideCarbonTree: function() {
    this.setData({
      isCarbonTreeVisible: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡
    return false
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 开始语音搜索
  startVoiceSearch: function() {
    wx.showToast({
      title: '长按说话中...',
      icon: 'none',
      duration: 1500
    })
  },

  // 结束语音搜索
  endVoiceSearch: function() {
    wx.showToast({
      title: '语音识别中...',
      icon: 'loading',
      duration: 1500
    })

    // 模拟语音识别结果
    setTimeout(() => {
      this.setData({
        searchKeyword: '塑料瓶'
      })
      this.searchWaste({
        detail: {
          value: '塑料瓶'
        }
      })
    }, 1500)
  }
})
