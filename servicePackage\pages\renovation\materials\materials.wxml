<!--pages/renovation/materials/materials.wxml-->
<view class="container">
  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="progress-bar-container">
      <view class="progress-bar-active" style="right: calc(25% + 40px);"></view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">基本信息</view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">施工方案</view>
    </view>
    <view class="step step-active">
      <view class="step-circle">3</view>
      <view class="step-label">材料上传</view>
    </view>
    <view class="step">
      <view class="step-circle">4</view>
      <view class="step-label">承诺签署</view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="hint-section">
    <view class="hint-title">
      <text class="hint-icon">!</text>
      <text>材料上传说明</text>
    </view>
    <view class="hint-content">
      <text>请上传装修所需的材料清单、设计图纸等相关资料，以便物业审核。如涉及承重墙改造，请务必上传专业的结构评估报告。</text>
    </view>
  </view>

  <!-- 上传区域 -->
  <view class="upload-section">
    <view class="section-title">装修设计图纸</view>
    <view class="upload-description">请上传装修设计图纸，包括平面图、立面图等（必传）</view>

    <view class="file-grid">
      <view class="file-item" wx:for="{{designFiles}}" wx:key="index">
        <image src="{{item.path}}" mode="aspectFill"></image>
        <view class="file-delete" catchtap="deleteFile" data-type="design" data-index="{{index}}">×</view>
      </view>

      <view class="upload-button" bindtap="chooseImage" data-type="design" wx:if="{{designFiles.length < 5}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">上传图片</view>
      </view>
    </view>
  </view>

  <view class="upload-section">
    <view class="section-title">材料清单</view>
    <view class="upload-description">请上传装修材料清单，包括品牌、型号等信息（必传）</view>

    <view class="file-grid">
      <view class="file-item" wx:for="{{materialFiles}}" wx:key="index">
        <image src="{{item.path}}" mode="aspectFill"></image>
        <view class="file-delete" catchtap="deleteFile" data-type="material" data-index="{{index}}">×</view>
      </view>

      <view class="upload-button" bindtap="chooseImage" data-type="material" wx:if="{{materialFiles.length < 5}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">上传图片</view>
      </view>
    </view>
  </view>

  <view class="upload-section" wx:if="{{hasBearingWall}}">
    <view class="section-title">结构评估报告</view>
    <view class="upload-description">您的装修方案涉及承重墙改造，请上传专业的结构评估报告（必传）</view>

    <view class="file-grid">
      <view class="file-item" wx:for="{{reportFiles}}" wx:key="index">
        <image src="{{item.path}}" mode="aspectFill"></image>
        <view class="file-delete" catchtap="deleteFile" data-type="report" data-index="{{index}}">×</view>
      </view>

      <view class="upload-button" bindtap="chooseImage" data-type="report" wx:if="{{reportFiles.length < 3}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">上传图片</view>
      </view>
    </view>
  </view>

  <view class="upload-section">
    <view class="section-title">其他资料</view>
    <view class="upload-description">可上传其他相关资料（选传）</view>

    <view class="file-grid">
      <view class="file-item" wx:for="{{otherFiles}}" wx:key="index">
        <image src="{{item.path}}" mode="aspectFill"></image>
        <view class="file-delete" catchtap="deleteFile" data-type="other" data-index="{{index}}">×</view>
      </view>

      <view class="upload-button" bindtap="chooseImage" data-type="other" wx:if="{{otherFiles.length < 5}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">上传图片</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="back-button" bindtap="goToPrevStep">上一步</button>
    <button class="next-button" bindtap="goToNextStep" disabled="{{!isFormValid}}">下一步</button>
  </view>
</view>