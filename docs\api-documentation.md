# 智慧物业API接口文档

## 概述
- **API版本**: 1.0
- **服务器地址**: http://**********:8080
- **认证方式**: Authorization Header (Bearer Token)
- **文档生成时间**: 基于Swagger OpenAPI 3.1.0规范

## 用户端接口 (users-api)

### 1. 认证模块

#### 1.1 获取授权令牌
- **接口路径**: `POST /users-api/v1/auth/token`
- **功能描述**: 用户端通过微信授权码获取访问令牌
- **认证要求**: 无需认证
- **请求参数**:
  - `code` (string, 必填): 微信授权码
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/auth/token',
    method: 'POST',
    data: {
      code: 'wx_auth_code_from_login'
    }
  });
  ```
- **响应数据结构**: `ResponseEntityMemberAccessToken`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "refresh_token_string",
      "memberDetail": {
        "id": 3,
        "openid": "o7hun7a9uuv1WTOSj6XlTtnz5M4U",
        "unionid": null,
        "userName": "小明",
        "nickName": null,
        "avatarUrl": null,
        "phone": "13685156020",
        "gender": "man",
        "birthday": "1990-06-05T23:00:00",
        "role": "tourist",
        "residentId": 1
      }
    }
  }
  ```

#### 1.2 用户实名认证
- **接口路径**: `POST /users-api/v1/auth/real-name`
- **功能描述**: 用户端提交实名认证信息
- **认证要求**: 需要Authorization Token
- **请求体**: `RealNameCreateDTO`
  ```json
  {
    "residentName": "张三",
    "certificateType": "ID_CARD",
    "idCardNumber": "110101199001011234",
    "phone": "13800138000",
    "codeKey": "sms_code_key",
    "code": "123456",
    "photo": "/uploads/id_photo.jpg"
  }
  ```
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/auth/real-name',
    method: 'POST',
    header: {
      'Authorization': wx.getStorageSync('access_token'),
      'Content-Type': 'application/json'
    },
    data: {
      residentName: '张三',
      certificateType: 'ID_CARD',
      idCardNumber: '110101199001011234',
      phone: '13800138000',
      codeKey: 'sms_code_key',
      code: '123456',
      photo: '/uploads/id_photo.jpg'
    }
  });
  ```
- **响应数据结构**: `ResponseEntityLong`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": 123456
  }
  ```

### 2. 车辆管理

#### 2.1 查询我的车辆
- **接口路径**: `GET /users-api/v1/community/vehicle`
- **功能描述**: 通过ID查询我的车辆信息
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `id` (integer, 必填): 小区车辆ID
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/community/vehicle',
    method: 'GET',
    data: { id: 123 },
    header: {
      'Authorization': wx.getStorageSync('access_token')
    }
  });
  ```
- **响应数据结构**: `ResponseEntityCommunityVehicle`
  ```json
{
  "errorMessage": "string",
  "code": 1073741824,
  "data": {
    "id": 9007199254740991,
    "plateNumber": "string",
    "plateColor": "string",
    "parkingType": "string",
    "parkingNumber": "string",
    "validBeginTime": "2025-05-28T00:54:52.493Z",
    "validEndTime": "2025-05-28T00:54:52.493Z",
    "note": "string",
    "status": "string",
    "mainUse": true,
    "media": "string",
    "createTime": "2025-05-28T00:54:52.493Z",
    "updateTime": "2025-05-28T00:54:52.493Z",
    "residentId": 9007199254740991,
    "communityId": 9007199254740991
  }
}
  ```

#### 2.2 新增我的车辆
- **接口路径**: `POST /users-api/v1/community/vehicle`
- **功能描述**: 新增小区我的车辆
- **认证要求**: 需要Authorization Token
- **请求参数**: `communityVehicle` (CommunityVehicle对象)
{
  "id": 9007199254740991,
  "plateNumber": "string",
  "plateColor": "string",
  "parkingType": "string",
  "parkingNumber": "string",
  "validBeginTime": "2025-05-28T00:55:18.255Z",
  "validEndTime": "2025-05-28T00:55:18.255Z",
  "note": "string",
  "status": "string",
  "mainUse": true,
  "media": "string",
  "createTime": "2025-05-28T00:55:18.255Z",
  "updateTime": "2025-05-28T00:55:18.255Z",
  "residentId": 9007199254740991,
  "communityId": 9007199254740991
}
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/community/vehicle',
    method: 'POST',
    data: {
      plateNumber: '京A12345',
      vehicleType: '小型汽车',
      ownerName: '张三',
      ownerPhone: '13800138000'
    },
    header: {
      'Authorization': wx.getStorageSync('access_token')
    }
  });
  ```
- **响应数据结构**: `ResponseEntityLong`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": 123456
  }
  ```

#### 2.3 编辑我的车辆
- **接口路径**: `PUT /users-api/v1/community/vehicle`
- **功能描述**: 编辑我的车辆信息
- **认证要求**: 需要Authorization Token
- **请求参数**: `communityVehicle` (CommunityVehicle对象)
{
  "id": 9007199254740991,
  "plateNumber": "string",
  "plateColor": "string",
  "parkingType": "string",
  "parkingNumber": "string",
  "validBeginTime": "2025-05-28T00:55:53.350Z",
  "validEndTime": "2025-05-28T00:55:53.350Z",
  "note": "string",
  "status": "string",
  "mainUse": true,
  "media": "string",
  "createTime": "2025-05-28T00:55:53.350Z",
  "updateTime": "2025-05-28T00:55:53.350Z",
  "residentId": 9007199254740991,
  "communityId": 9007199254740991
}
- **响应数据结构**: `ResponseEntityBoolean`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": true
  }
  ```

#### 2.4 删除我的车辆
- **接口路径**: `DELETE /users-api/v1/community/vehicle`
- **功能描述**: 删除我的车辆
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `id` (integer, 必填): 小区车辆ID
- **响应数据结构**: `ResponseEntityBoolean`

#### 2.5 分页查询我的车辆
- **接口路径**: `GET /users-api/v1/community/vehicle/page`
- **功能描述**: 通过分页查询我的车辆列表
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `pageNum` (integer, 可选, 默认1): 页码
  - `pageSize` (integer, 可选, 默认10): 每页数量
  - `type` (string, 可选): 类型
  - `masterType` (string, 可选): 主类型
  - `masterId` (string, 可选): 主ID
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/community/vehicle/page',
    method: 'GET',
    data: {
      pageNum: 1,
      pageSize: 10
    },
    header: {
      'Authorization': wx.getStorageSync('access_token')
    }
  });
  ```
- **响应数据结构**: `ResponseEntityPageInfoCommunityVehicle`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": {
      "total": 50,
      "list": [
        {
          "id": 9007199254740991,
        "plateNumber": "string",
        "plateColor": "string",
        "parkingType": "string",
        "parkingNumber": "string",
        "validBeginTime": "2025-05-28T00:49:56.576Z",
        "validEndTime": "2025-05-28T00:49:56.576Z",
        "note": "string",
        "status": "string",
        "mainUse": true,
        "media": "string",
        "createTime": "2025-05-28T00:49:56.576Z",
        "updateTime": "2025-05-28T00:49:56.576Z",
        "residentId": 9007199254740991,
        "communityId": 9007199254740991
        }
      ],
      "pageNum": 1,
      "pageSize": 10,
      "pages": 5,
      "isFirstPage": true,
      "isLastPage": false,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
  ```

### 3. 房屋管理

#### 3.1 查询我的房屋
- **接口路径**: `GET /users-api/v1/community/building`
- **功能描述**: 通过ID查询我的房屋信息
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `id` (integer, 必填): 小区房屋ID
- **响应数据结构**: `ResponseEntityCommunityBuilding`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": {
      "id": 456,
      "buildingNumber": "1号楼",
      "unitNumber": "1单元",
      "floorNumber": "10层",
      "roomNumber": "1001",
      "area": 120.5,
      "createTime": "2023-12-21T10:30:00",
      "updateTime": "2023-12-21T10:30:00"
    }
  }
  ```

#### 3.2 新增我的房屋
- **接口路径**: `POST /users-api/v1/community/building`
- **功能描述**: 新增我的房屋
- **认证要求**: 需要Authorization Token
- **请求参数**: `communityBuilding` (CommunityBuilding对象)
{
  "id": 9007199254740991,
  "buildingNumber": "string",
  "type": "string",
  "lng": 0.1,//已选小区的经纬度吧
  "lat": 0.1,
  "alt": 0.1,
  "expandData": "string",
  "createTime": "2025-05-27T02:42:22.764Z",
  "updateTime": "2025-05-27T02:42:22.764Z",
  "note": "string",
  "sort": 排序,
  "communityId": '已选小区的id',
  "parentId": 0,
  "ancestors": "string"
}

- **响应数据结构**: `ResponseEntityLong`

#### 3.3 删除我的房屋
- **接口路径**: `DELETE /users-api/v1/community/building`
- **功能描述**: 删除我的房屋
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `id` (integer, 必填): 小区房屋ID
- **响应数据结构**: `ResponseEntityBoolean`

#### 3.4 分页查询我的房屋
- **接口路径**: `GET /users-api/v1/community/building/page`
- **功能描述**: 通过分页查询我的房屋列表
- **认证要求**: 需要Authorization Token
- **响应数据结构**: `ResponseEntityPageInfoCommunityBuilding`

### 4. 小区信息

#### 4.1 分页查询小区信息
- **接口路径**: `GET /users-api/v1/community/page`
- **功能描述**: 通过分页查询小区信息
- **认证要求**: 需要Authorization Token
- **响应数据结构**: `ResponseEntityPageInfoCommunity`
  ```json
{
    "errorMessage": null,
    "code": 0,
    "data": {
        "total": 1,
        "list": [
            {
                "id": "2",
                "communityName": "网信科技",
                "note": "",
                "lng": 0.0,
                "lat": 0.0,
                "address": "邦宁",
                "expandData": "",
                "sort": 0,
                "createTime": "2025-05-27 10:02:05",
                "updateTime": null,
                "orgId": "7091961583521234948"
            }
        ],
        "pageNum": 1,
        "pageSize": 1,
        "size": 1,
        "startRow": 0,
        "endRow": 0,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
    }
}
  ```

### 5. 图文内容

#### 5.1 查询图文
- **接口路径**: `GET /users-api/v1/imagetext`
- **功能描述**: 通过ID查询图文内容
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `id` (integer, 必填): 图文ID
- **响应数据结构**: `ResponseEntityImagetext`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": {
      "id": 789,
      "title": "小区公告",
      "content": "图文内容详情...",
      "imageUrl": "/uploads/image.jpg",
      "type": "notice",
      "masterType": "community",
      "masterId": "1",
      "createTime": "2023-12-21T10:30:00",
      "updateTime": "2023-12-21T10:30:00"
    }
  }
  ```

#### 5.2 分页查询图文
- **接口路径**: `GET /users-api/v1/imagetext/page`
- **功能描述**: 通过分页查询图文内容
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `pageNum` (integer, 可选, 默认1): 页码
  - `pageSize` (integer, 可选, 默认10): 每页数量
  - `type` (string, 可选): 类型
  - `masterType` (string, 可选): 主类型
  - `masterId` (string, 可选): 主ID
pageNum
integer($int32)
(query)
1
pageSize
integer($int32)
(query)
10
type
string
(query)
banner //banner轮播图 
masterType
string
(query)
masterType
masterId
string
(query)



- **响应数据结构**: `ResponseEntityPageInfoImagetext`
{
  "errorMessage": null,
  "code": 0,
  "data": {
    "total": 1,
    "list": [
      {
        "id": "2",
        "title": "轮播图1",
        "imageUrl": "assets/1c385998af46416f9f901f5c850d47a5.jpeg",
        "content": null,
        "link": "https://www.baidu.com",
        "sort": "1",
        "publisherUserId": null,
        "type": "banner",
        "createTime": null,
        "updateTime": null,
        "masterIds": null,
        "masterType": null,
        "extendData": null
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "size": 1,
    "startRow": 1,
    "endRow": 1,
    "pages": 1,
    "prePage": 0,
    "nextPage": 0,
    "isFirstPage": true,
    "isLastPage": true,
    "hasPreviousPage": false,
    "hasNextPage": false,
    "navigatePages": 8,
    "navigatepageNums": [
      1
    ],
    "navigateFirstPage": 1,
    "navigateLastPage": 1
  }
}


### 6. 字典查询

#### 6.1 根据代码查询字典
- **接口路径**: `GET /users-api/v1/dict/search`
- **功能描述**: 根据字典代码查询字典项
- **认证要求**: 需要Authorization Token
- **请求参数**:
  - `nameEn` (string, 必填): 字典代码
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/users-api/v1/dict/search',
    method: 'GET',
    data: { nameEn: 'vehicle_type' },
    header: {
      'Authorization': wx.getStorageSync('access_token')
    }
  });
  ```
- **响应数据结构**: `ResponseEntityListDict`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": [
      {
        "id": 1,
        "nameEn": "small_car",
        "nameCn": "小型汽车",
        "cssClass": "car-small",
        "parentId": 0,
        "sort": 1,
        "note": "小型汽车类型",
        "createTime": "2023-12-21T10:30:00",
        "updateTime": "2023-12-21T10:30:00"
      }
    ]
  }
  ```

## 公共模块接口 (common-api)

### 1. 短信服务

#### 1.1 获取验证码
- **接口路径**: `POST /common-api/v1/sms/code`
- **功能描述**: 通过手机号获取短信验证码
- **认证要求**: 无需认证
- **请求参数**:
  - `phone` (string, 必填): 手机号
- **请求示例**:
  ```javascript
  wx.request({
    url: 'http://**********:8080/common-api/v1/sms/code',
    method: 'POST',
    data: {
      phone: '13800138000'
    }
  });
  ```
- **响应数据结构**: `ResponseEntityString`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": "验证码发送成功"
  }
  ```

### 2. 文件服务

#### 2.1 文件上传
- **接口路径**: `POST /common-api/v1/file/upload`
- **功能描述**: 上传文件到服务器
- **认证要求**: 需要Authorization Token
- **请求类型**: multipart/form-data
- **请求参数**:
  - `file` (binary, 必填): 要上传的文件
- **请求示例**:
  ```javascript
  wx.chooseImage({
    count: 1,
    success: (res) => {
      wx.uploadFile({
        url: 'http://**********:8080/common-api/v1/file/upload',
        filePath: res.tempFilePaths[0],
        name: 'file',
        header: {
          'Authorization': wx.getStorageSync('access_token')
        },
        success: (uploadRes) => {
          const data = JSON.parse(uploadRes.data);
          console.log('文件上传成功，路径：', data.data);
        }
      });
    }
  });
  ```
- **响应数据结构**: `ResponseEntityString`
  ```json
  {
    "code": 0,
    "errorMessage": null,
    "data": "/uploads/2023/12/21/file_20231221103000.jpg"
  }
  ```

## 响应状态码说明

- `0`: 操作成功
- `1`: 操作失败
- `401`: 未授权，需要重新登录
- `403`: 权限不足
- `404`: 请求的资源不存在
- `500`: 服务器内部错误

## 数据模型定义

### 基础响应结构

#### ResponseEntity (通用响应结构)
```json
{
  "code": 0,
  "errorMessage": null,
  "data": "具体数据内容"
}
```

#### PageInfo (分页信息结构)
```json
{
  "total": 100,
  "list": [],
  "pageNum": 1,
  "pageSize": 10,
  "pages": 10,
  "size": 10,
  "startRow": 1,
  "endRow": 10,
  "isFirstPage": true,
  "isLastPage": false,
  "hasPreviousPage": false,
  "hasNextPage": true,
  "navigatePages": 8,
  "navigatepageNums": [1, 2, 3, 4, 5, 6, 7, 8],
  "navigateFirstPage": 1,
  "navigateLastPage": 8
}
```

### 用户相关模型

#### Member (用户信息)
```json
{
  "id": 3,
  "openid": "o7hun7a9uuv1WTOSj6XlTtnz5M4U",
  "unionid": null,
  "userName": "小明",
  "nickName": null,
  "avatarUrl": null,
  "phone": "13685156020",
  "gender": "man",
  "birthday": "1990-06-05T23:00:00",
  "role": "tourist",
  "residentId": 1,
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

#### MemberAccessToken (登录令牌响应)
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_string",
  "memberDetail": {
    // Member对象结构
  }
}
```

#### RealNameCreateDTO (实名认证请求)
```json
{
  "residentName": "张三",
  "certificateType": "ID_CARD",
  "idCardNumber": "110101199001011234",
  "phone": "13800138000",
  "codeKey": "sms_code_key",
  "code": "123456",
  "photo": "/uploads/id_photo.jpg"
}
```

### 小区相关模型

#### Community (小区信息)
```json
{
  "id": 1,
  "communityName": "阳光花园",
  "address": "北京市朝阳区xxx路xxx号",
  "description": "高档住宅小区",
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

#### CommunityBuilding (房屋信息)
```json
{
  "id": 456,
  "buildingNumber": "1号楼",
  "unitNumber": "1单元",
  "floorNumber": "10层",
  "roomNumber": "1001",
  "area": 120.5,
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

#### CommunityVehicle (车辆信息)
```json
{
  "id": 123,
  "plateNumber": "京A12345",
  "vehicleType": "小型汽车",
  "ownerName": "张三",
  "ownerPhone": "13800138000",
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

### 内容相关模型

#### Imagetext (图文内容)
```json
{
  "id": 789,
  "title": "小区公告",
  "content": "图文内容详情...",
  "imageUrl": "/uploads/image.jpg",
  "type": "notice",
  "masterType": "community",
  "masterId": "1",
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

#### Dict (字典项)
```json
{
  "id": 1,
  "nameEn": "small_car",
  "nameCn": "小型汽车",
  "cssClass": "car-small",
  "parentId": 0,
  "sort": 1,
  "note": "小型汽车类型",
  "createTime": "2023-12-21T10:30:00",
  "updateTime": "2023-12-21T10:30:00"
}
```

## 使用示例

### 1. 完整的用户登录流程
```javascript
// 1. 获取微信授权码并登录
wx.login({
  success: (res) => {
    const code = res.code;

    // 2. 调用获取令牌接口
    wx.request({
      url: 'http://**********:8080/users-api/v1/auth/token',
      method: 'POST',
      data: { code: code },
      success: (tokenRes) => {
        if (tokenRes.data.code === 0) {
          // 3. 保存令牌和用户信息
          wx.setStorageSync('access_token', tokenRes.data.data.accessToken);
          wx.setStorageSync('refresh_token', tokenRes.data.data.refreshToken);
          wx.setStorageSync('userInfo', tokenRes.data.data.memberDetail);

          console.log('登录成功', tokenRes.data.data.memberDetail);
        } else {
          console.error('登录失败', tokenRes.data.errorMessage);
        }
      },
      fail: (err) => {
        console.error('网络错误', err);
      }
    });
  }
});
```

### 2. 完整的实名认证流程
```javascript
// 1. 发送验证码
function sendSmsCode(phone) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'http://**********:8080/common-api/v1/sms/code',
      method: 'POST',
      data: { phone: phone },
      success: (res) => {
        if (res.data.code === 0) {
          resolve(res.data.data);
        } else {
          reject(res.data.errorMessage);
        }
      },
      fail: reject
    });
  });
}

// 2. 上传身份证照片
function uploadIdPhoto(filePath) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'http://**********:8080/common-api/v1/file/upload',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': wx.getStorageSync('access_token')
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data);
        if (data.code === 0) {
          resolve(data.data);
        } else {
          reject(data.errorMessage);
        }
      },
      fail: reject
    });
  });
}

// 3. 提交实名认证
function submitRealNameAuth(authData) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'http://**********:8080/users-api/v1/auth/real-name',
      method: 'POST',
      header: {
        'Authorization': wx.getStorageSync('access_token'),
        'Content-Type': 'application/json'
      },
      data: authData,
      success: (res) => {
        if (res.data.code === 0) {
          resolve(res.data.data);
        } else {
          reject(res.data.errorMessage);
        }
      },
      fail: reject
    });
  });
}

// 4. 完整认证流程
async function completeRealNameAuth() {
  try {
    // 发送验证码
    await sendSmsCode('13800138000');
    console.log('验证码发送成功');

    // 选择并上传照片
    const chooseResult = await new Promise((resolve, reject) => {
      wx.chooseImage({
        count: 1,
        success: resolve,
        fail: reject
      });
    });

    const photoPath = await uploadIdPhoto(chooseResult.tempFilePaths[0]);
    console.log('照片上传成功', photoPath);

    // 提交认证
    const authData = {
      residentName: '张三',
      certificateType: 'ID_CARD',
      idCardNumber: '110101199001011234',
      phone: '13800138000',
      codeKey: 'sms_code_key',
      code: '123456',
      photo: photoPath
    };

    const result = await submitRealNameAuth(authData);
    console.log('实名认证提交成功', result);

  } catch (error) {
    console.error('认证流程失败', error);
  }
}
```

### 3. 车辆管理示例
```javascript
// 车辆管理类
class VehicleManager {
  // 获取车辆列表
  static async getVehicleList(pageNum = 1, pageSize = 10) {
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://**********:8080/users-api/v1/community/vehicle/page',
        method: 'GET',
        data: { pageNum, pageSize },
        header: {
          'Authorization': wx.getStorageSync('access_token')
        },
        success: (res) => {
          if (res.data.code === 0) {
            resolve(res.data.data);
          } else {
            reject(res.data.errorMessage);
          }
        },
        fail: reject
      });
    });
  }

  // 添加车辆
  static async addVehicle(vehicleData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://**********:8080/users-api/v1/community/vehicle',
        method: 'POST',
        data: vehicleData,
        header: {
          'Authorization': wx.getStorageSync('access_token')
        },
        success: (res) => {
          if (res.data.code === 0) {
            resolve(res.data.data);
          } else {
            reject(res.data.errorMessage);
          }
        },
        fail: reject
      });
    });
  }

  // 删除车辆
  static async deleteVehicle(vehicleId) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://**********:8080/users-api/v1/community/vehicle',
        method: 'DELETE',
        data: { id: vehicleId },
        header: {
          'Authorization': wx.getStorageSync('access_token')
        },
        success: (res) => {
          if (res.data.code === 0) {
            resolve(res.data.data);
          } else {
            reject(res.data.errorMessage);
          }
        },
        fail: reject
      });
    });
  }
}

// 使用示例
async function manageVehicles() {
  try {
    // 获取车辆列表
    const vehicleList = await VehicleManager.getVehicleList(1, 10);
    console.log('车辆列表', vehicleList);

    // 添加新车辆
    const newVehicle = {
      plateNumber: '京A12345',
      vehicleType: '小型汽车',
      ownerName: '张三',
      ownerPhone: '13800138000'
    };
    const vehicleId = await VehicleManager.addVehicle(newVehicle);
    console.log('车辆添加成功', vehicleId);

  } catch (error) {
    console.error('车辆管理失败', error);
  }
}
```

## 错误处理

### 常见错误码
- `0`: 操作成功
- `1`: 操作失败（业务逻辑错误）
- `401`: Token过期或无效，需要重新登录
- `403`: 权限不足，无法访问该资源
- `404`: 请求的资源不存在
- `500`: 服务器内部错误

### 统一错误处理封装
```javascript
// API请求封装
class ApiRequest {
  static baseUrl = 'http://**********:8080';

  static request(options) {
    return new Promise((resolve, reject) => {
      // 添加默认配置
      const defaultOptions = {
        header: {
          'Authorization': wx.getStorageSync('access_token'),
          'Content-Type': 'application/json'
        },
        timeout: 10000
      };

      const requestOptions = Object.assign({}, defaultOptions, options);
      requestOptions.url = this.baseUrl + options.url;

      wx.request({
        ...requestOptions,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 0) {
              // 请求成功
              resolve(res.data.data);
            } else {
              // 业务错误
              this.handleBusinessError(res.data);
              reject(new Error(res.data.errorMessage || '业务处理失败'));
            }
          } else {
            // HTTP状态码错误
            this.handleHttpError(res.statusCode);
            reject(new Error(`HTTP错误: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          // 网络错误
          this.handleNetworkError(err);
          reject(err);
        }
      });
    });
  }

  // 处理业务错误
  static handleBusinessError(data) {
    console.error('业务错误:', data.errorMessage);
    wx.showToast({
      title: data.errorMessage || '操作失败',
      icon: 'error',
      duration: 2000
    });
  }

  // 处理HTTP错误
  static handleHttpError(statusCode) {
    switch (statusCode) {
      case 401:
        console.log('Token过期，需要重新登录');
        wx.removeStorageSync('access_token');
        wx.removeStorageSync('userInfo');
        wx.redirectTo({
          url: '/pages/auth/login/login'
        });
        break;
      case 403:
        wx.showToast({
          title: '权限不足',
          icon: 'error'
        });
        break;
      case 404:
        wx.showToast({
          title: '请求的资源不存在',
          icon: 'error'
        });
        break;
      case 500:
        wx.showToast({
          title: '服务器内部错误',
          icon: 'error'
        });
        break;
      default:
        wx.showToast({
          title: `网络错误: ${statusCode}`,
          icon: 'error'
        });
    }
  }

  // 处理网络错误
  static handleNetworkError(err) {
    console.error('网络错误:', err);
    wx.showToast({
      title: '网络连接失败',
      icon: 'error',
      duration: 2000
    });
  }
}

// 使用示例
async function getUserVehicles() {
  try {
    const vehicles = await ApiRequest.request({
      url: '/users-api/v1/community/vehicle/page',
      method: 'GET',
      data: { pageNum: 1, pageSize: 10 }
    });
    console.log('车辆列表:', vehicles);
    return vehicles;
  } catch (error) {
    console.error('获取车辆列表失败:', error);
    throw error;
  }
}
```

## 开发注意事项

### 1. 认证和授权
- **Token管理**: 除了登录和获取验证码接口外，其他接口都需要在请求头中携带 Authorization Token
- **Token刷新**: 当收到401错误时，应该清除本地Token并引导用户重新登录
- **权限控制**: 不同用户角色可能有不同的接口访问权限

### 2. 数据格式规范
- **统一响应格式**: 所有接口返回格式为 `{code: number, data: any, errorMessage?: string}`
- **时间格式**: 使用 ISO 8601 格式 (YYYY-MM-DDTHH:mm:ss)
- **分页参数**: pageNum 从 1 开始，pageSize 默认为 10
- **字符编码**: 统一使用 UTF-8 编码

### 3. 文件上传规范
- **请求格式**: 使用 multipart/form-data 格式
- **文件字段名**: 统一使用 'file'
- **文件大小限制**: 建议单个文件不超过 10MB
- **支持格式**: 图片文件支持 jpg、png、gif 等格式

### 4. 分页查询规范
- **分页参数**: pageNum（页码，从1开始）、pageSize（每页数量，默认10）
- **响应结构**: 包含 total、list、pageNum、pageSize、pages 等分页信息
- **性能优化**: 建议每页数量不超过 50 条记录

## 开发建议

### 1. API封装建议
```javascript
// 建议创建专门的API模块
// api/userApi.js
export const userApi = {
  // 登录
  login: (code) => ApiRequest.request({
    url: '/users-api/v1/auth/token',
    method: 'POST',
    data: { code }
  }),

  // 实名认证
  realNameAuth: (authData) => ApiRequest.request({
    url: '/users-api/v1/auth/real-name',
    method: 'POST',
    data: authData
  })
};

// api/vehicleApi.js
export const vehicleApi = {
  // 获取车辆列表
  getList: (params) => ApiRequest.request({
    url: '/users-api/v1/community/vehicle/page',
    method: 'GET',
    data: params
  }),

  // 添加车辆
  add: (vehicleData) => ApiRequest.request({
    url: '/users-api/v1/community/vehicle',
    method: 'POST',
    data: vehicleData
  })
};
```

### 2. 缓存策略建议
```javascript
// 缓存管理器
class CacheManager {
  static cache = new Map();

  // 设置缓存（带过期时间）
  static set(key, data, expireTime = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      expireTime: Date.now() + expireTime
    });
  }

  // 获取缓存
  static get(key) {
    const item = this.cache.get(key);
    if (item && Date.now() < item.expireTime) {
      return item.data;
    }
    this.cache.delete(key);
    return null;
  }

  // 清除缓存
  static clear(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
}

// 使用缓存的API调用
async function getDictWithCache(nameEn) {
  const cacheKey = `dict_${nameEn}`;
  let dictData = CacheManager.get(cacheKey);

  if (!dictData) {
    dictData = await ApiRequest.request({
      url: '/users-api/v1/dict/search',
      method: 'GET',
      data: { nameEn }
    });
    CacheManager.set(cacheKey, dictData, 10 * 60 * 1000); // 缓存10分钟
  }

  return dictData;
}
```

### 3. 错误重试机制
```javascript
// 带重试的API请求
class RetryApiRequest extends ApiRequest {
  static async requestWithRetry(options, maxRetries = 3) {
    let lastError;

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.request(options);
      } catch (error) {
        lastError = error;

        // 只对网络错误进行重试，业务错误不重试
        if (error.message.includes('HTTP错误') || error.message.includes('网络')) {
          console.log(`请求失败，第${i + 1}次重试...`);
          await this.delay(1000 * (i + 1)); // 递增延迟
        } else {
          throw error; // 业务错误直接抛出
        }
      }
    }

    throw lastError;
  }

  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 4. Loading状态管理
```javascript
// Loading管理器
class LoadingManager {
  static loadingCount = 0;

  static show(title = '加载中...') {
    if (this.loadingCount === 0) {
      wx.showLoading({ title });
    }
    this.loadingCount++;
  }

  static hide() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      wx.hideLoading();
    }
  }
}

// 带Loading的API请求
async function apiWithLoading(apiCall, loadingTitle) {
  try {
    LoadingManager.show(loadingTitle);
    return await apiCall();
  } finally {
    LoadingManager.hide();
  }
}

// 使用示例
async function loadVehicleList() {
  return await apiWithLoading(
    () => vehicleApi.getList({ pageNum: 1, pageSize: 10 }),
    '加载车辆列表...'
  );
}
```

## 总结

本文档基于Swagger OpenAPI 3.1.0规范生成，涵盖了智慧物业系统的用户端和公共模块接口。开发时请注意：

1. **严格按照接口规范进行开发**，确保请求参数和响应数据格式正确
2. **做好错误处理和用户体验优化**，包括Loading状态、错误提示等
3. **合理使用缓存和重试机制**，提升应用性能和稳定性
4. **遵循RESTful API设计原则**，保持接口的一致性和可维护性

如有疑问或需要更新，请参考最新的Swagger文档：http://**********:8080/swagger-ui/index.html
