// pages/property/workorder/stats/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const util = require('@/utils/util.js');
import * as echarts from "@/components/ec-canvas/echarts";

Page({
  data: {
    darkMode: false,
    isLoading: true,
    activeTab: 'overview', // 当前活动标签页：overview, type, trend
    timeRange: 'week', // 时间范围：week, month, quarter, year
    timeLevel: 1, // 时间级别：1-近7天, 2-近30天, 3-本季度, 4-本年度

    // 统计数据
    statistics: {
      statusCounts: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
        accepted: 0,
        complete: 0,
        wait_process: 0,
        cancel: 0
      },
      typeDistribution: {
        repair: 0,
        complaint: 0,
        suggestion: 0,
        other: 0,
        total: 0
      },
      trend: {
        labels: [],
        data: []
      },
      timeEfficiency: {
        avgCompleteTime: '暂无数据',
        avgResponseTime: '暂无数据',
        onTimeCompleteRate: '暂无数据'
      }
    },

    // 字典数据
    workOrderStatusDict: [],
    workOrderTypeDict: [],

    // 计算结果
    completionRate: 0,
    processingRate: 0,
    cancelRate: 0,
    typeDistributionData: [],

    // ECharts配置
    typeDistributionChart: {
      lazyLoad: true
    },
    trendChart: {
      lazyLoad: true
    }
  },

  onLoad: function () {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单统计分析'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载统计数据
    this.loadStatistics();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadStatistics().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 初始化字典数据
  initDictData: function () {
    try {
      const workOrderStatusDict = util.getDictByNameEn('work_order_status')[0].children;
      const workOrderTypeDict = util.getDictByNameEn('work_order_type')[0].children;

      this.setData({
        workOrderStatusDict,
        workOrderTypeDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载统计数据
  loadStatistics: function () {
    this.setData({ isLoading: true });

    const params = {
      timeLevel: this.data.timeLevel,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    // 并行请求三个统计接口
    const promises = [
      workOrderApi.getPropertyStatusCount(params), // 概览统计
      workOrderApi.getPropertyTypeCount(params),   // 类型分布统计
      workOrderApi.getPropertyDataCount(params),    // 趋势分析统计
      workOrderApi.getPropertyValidityCount(params)    // 趋势分析统计
    ];

    return Promise.all(promises)
      .then(([statusData, typeData, trendData, validityData]) => {
        console.log('状态统计:', statusData);
        console.log('类型统计:', typeData);
        console.log('趋势统计:', trendData);
        console.log('处理时效:', validityData);

        // 提取实际数据
        const statusCounts = statusData || {};
        const typeDistribution = typeData || {};

        // 处理趋势数据 - 确保正确提取数据数组
        const trendDataArray = trendData.data || trendData || [];
        const trendLabels = this.generateTrendLabels(this.data.timeLevel, trendDataArray);

        // 计算各种比率
        const completionRate = this.calculateCompletionRate(statusCounts);
        const processingRate = this.calculateProcessingRate(statusCounts);
        const cancelRate = this.calculateCancelRate(statusCounts);

        const typeDistributionData = this.getTypeDistributionData(typeDistribution);

        // 计算趋势数据总和 - 确保正确处理数据结构
        let trendTotal = 0;
        if (trendDataArray && trendDataArray.length > 0) {
          trendTotal = trendDataArray.reduce((sum, item) => {
            // 处理不同的数据结构
            const value = typeof item === 'object' ? (item.value || 0) : (typeof item === 'number' ? item : 0);
            return sum + value;
          }, 0);
        }

        this.setData({
          statistics: {
            statusCounts: statusCounts,
            typeDistribution: typeDistribution,
            trend: {
              labels: trendLabels,
              data: trendDataArray
            },
            // 处理时效数据暂时使用默认值，等待后台接口完善
            timeEfficiency: {
              avgCompleteTime: (validityData.avgCompleteTime / 60).toFixed(2) + '分钟',
              avgResponseTime: (validityData.avgResponseTime / 60).toFixed(2) + '分钟',
              onTimeCompleteRate: validityData.onTimeCompleteRate + '%'
            }
          },
          // 计算结果放到data中供WXML使用
          completionRate: completionRate,
          processingRate: processingRate,
          cancelRate: cancelRate,
          typeDistributionData: typeDistributionData,
          trendTotal: trendTotal,
          isLoading: false
        });

        // 根据当前标签页初始化对应图表
        if (this.data.activeTab === 'type') {
          this.initTypeDistributionChart();
        } else if (this.data.activeTab === 'trend') {
          this.initTrendChart();
        }
      })
      .catch(error => {
        console.error('加载统计数据失败', error);
        this.setData({ isLoading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });

    // 根据切换的标签页初始化对应图表
    if (tab === 'type' && this.data.typeDistributionData.length > 0) {
      setTimeout(() => {
        this.initTypeDistributionChart();
      }, 100);
    } else if (tab === 'trend' && this.data.statistics.trend.data.length > 0) {
      setTimeout(() => {
        this.initTrendChart();
      }, 100);
    }
  },

  // 切换时间范围
  switchTimeRange: function (e) {
    const range = e.currentTarget.dataset.range;

    if (range !== this.data.timeRange) {
      // 映射时间范围到时间级别
      const timeLevelMap = {
        'week': 1,    // 本周
        'month': 2,   // 本月
        'quarter': 3, // 本季度
        'year': 4     // 本年度
      };

      this.setData({
        timeRange: range,
        timeLevel: timeLevelMap[range],
        isLoading: true
      });

      // 重新加载统计数据
      this.loadStatistics();
    }
  },

  // 生成趋势图标签
  generateTrendLabels: function (timeLevel, trendData) {
    console.log('生成趋势标签 - timeLevel:', timeLevel, 'trendData:', trendData);
    const labels = [];

    switch (timeLevel) {
      case 1: // 本周（周一到周日）
        const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        // 根据实际数据长度生成标签，通常是7天
        for (let i = 0; i < Math.min(trendData.length, 7); i++) {
          labels.push(weekDays[i]);
        }
        break;
      case 2: // 本月（根据实际数据长度生成日期标签）
        for (let i = 1; i <= trendData.length; i++) {
          labels.push(i + '日');
        }
        break;
      case 3: // 本季度（根据实际数据长度生成月份标签）
        const currentMonth = new Date().getMonth() + 1;
        const quarterStart = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        for (let i = 0; i < Math.min(trendData.length, 3); i++) {
          labels.push((quarterStart + i) + '月');
        }
        break;
      case 4: // 本年度（根据实际数据长度生成季度标签）
        const quarterLabels = ['第1季度', '第2季度', '第3季度', '第4季度'];
        for (let i = 0; i < Math.min(trendData.length, 4); i++) {
          labels.push(quarterLabels[i]);
        }
        break;
      default:
        // 如果没有匹配的时间级别，使用groupName作为标签
        trendData.forEach(item => {
          labels.push(item.groupName);
        });
    }

    console.log('生成的标签:', labels);
    return labels;
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },


  // 获取状态显示文本
  getStatusText: function (status) {
    const statusDict = this.data.workOrderStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 获取类型显示文本
  getTypeText: function (type) {
    const typeDict = this.data.workOrderTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === type);
    return typeItem ? typeItem.nameCn : type;
  },

  // 获取类型分布数据
  getTypeDistributionData: function (typeDistribution) {
    const typeData = typeDistribution || this.data.statistics.typeDistribution;
    const result = [];

    // 遍历类型数据，排除total字段
    Object.keys(typeData).forEach(key => {
      if (key !== 'total' && typeData[key] > 0) {
        result.push({
          type: key,
          name: this.getTypeText(key),
          value: typeData[key],
          percentage: typeData.total > 0 ? Math.round((typeData[key] / typeData.total) * 100) : 0
        });
      }
    });

    return result;
  },


  // 计算完成率
  calculateCompletionRate: function (statusCounts) {
      
    const counts = statusCounts;
    const { complete, total } = counts;
    if (!total || total === 0) return 0;
    return Math.round((complete / total) * 100);
  },



  // 计算处理中比率
  calculateProcessingRate: function (statusCounts) {
      
    const { processing, total } = statusCounts;
    if (total === 0) return 0;
    return Math.round((processing / total) * 100);
  },

  // 计算取消比率
  calculateCancelRate: function (statusCounts) {
      
    const { cancel, total } = statusCounts;
    if (total === 0) return 0;
    return Math.round((cancel / total) * 100);
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 初始化类型分布图表
  initTypeDistributionChart: function() {
    const typeData = this.data.typeDistributionData;
    if (!typeData || typeData.length === 0) {
      console.log('没有类型分布数据，跳过图表初始化');
      return;
    }

    console.log('初始化类型分布图表，数据:', typeData);
    this.loadTypeDistributionChart(typeData);
  },

  // 加载类型分布图表
  loadTypeDistributionChart: function(data) {
    // 绑定组件（ec-canvas标签的id）
    let ec_canvas = this.selectComponent('#typeDistributionChart');
    if (!ec_canvas) {
      console.error('无法找到类型分布图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr // 解决模糊显示的问题
      });

      // 准备饼图数据
      const pieData = data.map(item => ({
        name: item.name,
        value: item.value
      }));

      // ECharts配置选项
      const option = {
        title: {
          text: '工单类型分布',
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        grid: {
          bottom: 80 // 为图例预留足够的底部空间
        },
        legend: {
          orient: 'horizontal',
          bottom: 20, // 图例距离底部的距离
          left: 'center', // 图例居中对齐
          itemGap: 20, // 增加图例项之间的间距
          itemWidth: 15, // 设置图例标记的宽度
          itemHeight: 10, // 设置图例标记的高度
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
          }
        },
        series: [
          {
            name: '工单类型',
            type: 'pie',
            radius: ['35%', '60%'], // 缩小饼图
            center: ['50%', '35%'], // 将饼图向上移动并居中
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: pieData,
            itemStyle: {
              borderRadius: 5,
              borderColor: '#fff',
              borderWidth: 2
            }
          }
        ],
        color: ['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe']
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 初始化趋势分析图表
  initTrendChart: function() {
    const trendData = this.data.statistics.trend.data;
    const trendLabels = this.data.statistics.trend.labels;

    console.log('初始化趋势图表 - 原始数据:', trendData);
    console.log('初始化趋势图表 - 标签:', trendLabels);

    if (!trendData || trendData.length === 0) {
      console.log('没有趋势分析数据，跳过图表初始化');
      return;
    }

    console.log('开始加载趋势分析图表');
    this.loadTrendChart(trendData, trendLabels);
  },

  // 加载趋势分析图表
  loadTrendChart: function(data, labels) {
    // 绑定组件（ec-canvas标签的id）
    let ec_canvas = this.selectComponent('#trendChart');
    if (!ec_canvas) {
      console.error('无法找到趋势分析图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr // 解决模糊显示的问题
      });

      // 准备柱状图数据 - 确保正确处理数据结构
      const chartData = data.map(item => {
        if (typeof item === 'object') {
          return item.value || item.count || 0;
        } else if (typeof item === 'number') {
          return item;
        } else {
          return 0;
        }
      });

      console.log('图表数据:', chartData, '标签:', labels);

      // ECharts配置选项
      const option = {
        title: {
          text: '工单趋势分析',
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0];
            return `${param.name}工单数量: ${param.value}`;
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: labels,
          axisLabel: {
            fontSize: 12,
            color: '#666',
            interval: 0,
            rotate: labels.length > 7 ? 45 : 0
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          minInterval: 1, // 确保Y轴刻度为整数
          axisLabel: {
            fontSize: 12,
            color: '#666',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '工单数量',
            type: 'bar',
            data: chartData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#4f46e5'
                  },
                  {
                    offset: 1,
                    color: '#7c3aed'
                  }
                ]
              },
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#6366f1'
                    },
                    {
                      offset: 1,
                      color: '#8b5cf6'
                    }
                  ]
                }
              }
            },
            barWidth: '60%'
          }
        ]
      };

      chart.setOption(option);
      return chart;
    });
  }
});
