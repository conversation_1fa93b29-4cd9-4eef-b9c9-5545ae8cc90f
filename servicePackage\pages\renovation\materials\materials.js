// pages/renovation/materials/materials.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    designFiles: [],
    materialFiles: [],
    reportFiles: [],
    otherFiles: [],
    hasBearingWall: false,
    isFormValid: false,
    basicInfo: {},
    planInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取上一步保存的基本信息和施工方案
    const basicInfo = wx.getStorageSync('renovationFormData') || {};
    const planInfo = wx.getStorageSync('renovationPlanData') || {};

    // 获取之前保存的材料信息（如果有）
    const materialsInfo = wx.getStorageSync('renovationMaterialsData') || {
      designFiles: [],
      materialFiles: [],
      reportFiles: [],
      otherFiles: []
    };

    // 检查是否涉及承重墙改造
    const hasBearingWall = planInfo.hasBearingWall || false;

    this.setData({
      basicInfo: basicInfo,
      planInfo: planInfo,
      hasBearingWall: hasBearingWall,
      designFiles: materialsInfo.designFiles,
      materialFiles: materialsInfo.materialFiles,
      reportFiles: materialsInfo.reportFiles,
      otherFiles: materialsInfo.otherFiles
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 选择图片
   */
  chooseImage(e) {
    const type = e.currentTarget.dataset.type;
    let maxCount = 5;

    // 根据不同类型设置不同的最大数量
    if (type === 'report') {
      maxCount = 3;
    }

    // 计算还可以选择的图片数量
    const currentCount = this.data[`${type}Files`].length;
    const count = maxCount - currentCount;

    if (count <= 0) {
      wx.showToast({
        title: '已达到最大上传数量',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 将选择的图片添加到对应类型的文件数组中
        const tempFilePaths = res.tempFilePaths;
        const tempFiles = res.tempFiles;

        const newFiles = tempFilePaths.map((path, index) => {
          return {
            path: path,
            size: tempFiles[index].size,
            name: `${type}_${Date.now()}_${index}.jpg`
          };
        });

        const filesKey = `${type}Files`;
        const updatedFiles = [...this.data[filesKey], ...newFiles];

        this.setData({
          [filesKey]: updatedFiles
        });

        // 检查表单有效性
        this.checkFormValidity();
      }
    });
  },

  /**
   * 删除文件
   */
  deleteFile(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;

    const filesKey = `${type}Files`;
    const files = [...this.data[filesKey]];

    // 删除指定索引的文件
    files.splice(index, 1);

    this.setData({
      [filesKey]: files
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 检查表单有效性
   */
  checkFormValidity() {
    // 检查必填项
    const isValid =
      this.data.designFiles.length > 0 &&
      this.data.materialFiles.length > 0 &&
      (!this.data.hasBearingWall || this.data.reportFiles.length > 0);

    this.setData({
      isFormValid: isValid
    });
  },

  /**
   * 返回上一步
   */
  goToPrevStep() {
    // 保存当前表单数据
    this.saveMaterialsData();

    // 返回上一页
    wx.navigateBack();
  },

  /**
   * 进入下一步
   */
  goToNextStep() {
    // 检查表单有效性
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请上传必要的材料',
        icon: 'none'
      });
      return;
    }

    // 保存表单数据
    this.saveMaterialsData();

    // 跳转到下一步
    wx.navigateTo({
      url: '/servicePackage/pages/renovation/commitment/commitment'
    });
  },

  /**
   * 保存材料数据
   */
  saveMaterialsData() {
    const materialsData = {
      designFiles: this.data.designFiles,
      materialFiles: this.data.materialFiles,
      reportFiles: this.data.reportFiles,
      otherFiles: this.data.otherFiles
    };

    wx.setStorageSync('renovationMaterialsData', materialsData);
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时保存数据
    this.saveMaterialsData();
  }
})