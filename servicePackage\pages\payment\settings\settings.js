// pages/payment/settings/settings.js
const util = require('@/utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,

    // 缴费提醒设置
    reminderEnabled: false,
    reminderDays: 3,
    reminderDaysOptions: [1, 2, 3, 5, 7, 10, 15],
    reminderMethodOptions: [
      { value: 'wechat', label: '微信通知', checked: true },
      { value: 'sms', label: '短信通知', checked: false }
    ],

    // 自动缴费设置
    autoPayEnabled: false,
    autoPayLimit: 500,
    autoPayLimitOptions: [100, 200, 300, 500, 1000, 2000],

    // 支付方式
    paymentMethods: [
      { value: 'wechat', label: '微信支付', icon: 'wechat', checked: true },
      { value: 'alipay', label: '支付宝', icon: 'alipay', checked: false },
      { value: 'bank', label: '银行卡', icon: 'bank', checked: false }
    ],

    // 选择的银行卡
    selectedBankCard: null,

    // 银行卡列表
    bankCards: [
      {
        id: '1',
        bank: '中国工商银行',
        cardType: '储蓄卡',
        cardNumber: '6222 **** **** 1234',
        icon: 'icbc'
      },
      {
        id: '2',
        bank: '招商银行',
        cardType: '信用卡',
        cardNumber: '5187 **** **** 5678',
        icon: 'cmb'
      }
    ],

    // 弹窗控制
    showBankCardSelector: false,
    showDaysSelector: false,
    showLimitSelector: false,

    // 授权状态
    subscriptionAuthorized: false
  },

  onLoad: function () {
    this.loadSettings()
  },

  // 加载设置
  loadSettings: function () {
    this.setData({
      isLoading: true
    })

    // 模拟加载数据
    setTimeout(() => {
      // 从本地存储获取设置
      const reminderEnabled = wx.getStorageSync('reminderEnabled') || false
      const reminderDays = wx.getStorageSync('reminderDays') || 3
      const autoPayEnabled = wx.getStorageSync('autoPayEnabled') || false
      const autoPayLimit = wx.getStorageSync('autoPayLimit') || 500
      const selectedPaymentMethod = wx.getStorageSync('selectedPaymentMethod') || 'wechat'
      const selectedBankCardId = wx.getStorageSync('selectedBankCardId')

      // 更新支付方式选中状态
      const paymentMethods = this.data.paymentMethods.map(method => {
        return {
          ...method,
          checked: method.value === selectedPaymentMethod
        }
      })

      // 获取选中的银行卡
      let selectedBankCard = null
      if (selectedBankCardId) {
        selectedBankCard = this.data.bankCards.find(card => card.id === selectedBankCardId) || null
      }

      // 检查订阅消息授权状态
      this.checkSubscriptionAuth()

      this.setData({
        reminderEnabled,
        reminderDays,
        autoPayEnabled,
        autoPayLimit,
        paymentMethods,
        selectedBankCard,
        isLoading: false
      })
    }, 1000)
  },

  // 检查订阅消息授权状态
  checkSubscriptionAuth: function () {
    wx.getSetting({
      withSubscriptions: true,
      success: (res) => {
        if (res.subscriptionsSetting && res.subscriptionsSetting.mainSwitch) {
          this.setData({
            subscriptionAuthorized: true
          })
        } else {
          this.setData({
            subscriptionAuthorized: false
          })
        }
      }
    })
  },

  // 切换缴费提醒开关
  toggleReminderSwitch: function (e) {
    const enabled = e.detail.value

    if (enabled && !this.data.subscriptionAuthorized) {
      // 如果开启提醒但未授权，则请求授权
      this.requestSubscriptionAuth(() => {
        this.setData({
          reminderEnabled: true
        })
        wx.setStorageSync('reminderEnabled', true)
      })
    } else {
      this.setData({
        reminderEnabled: enabled
      })
      wx.setStorageSync('reminderEnabled', enabled)
    }
  },

  // 请求订阅消息授权
  requestSubscriptionAuth: function (callback) {
    wx.requestSubscribeMessage({
      tmplIds: ['your-template-id-here'], // 替换为实际的模板ID
      success: (res) => {
        if (res['your-template-id-here'] === 'accept') {
          this.setData({
            subscriptionAuthorized: true
          })
          if (callback) callback()
        } else {
          wx.showModal({
            title: '提示',
            content: '需要您授权接收缴费提醒通知',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (res) => {
                    if (res.authSetting['scope.subscribeMessage']) {
                      this.setData({
                        subscriptionAuthorized: true
                      })
                      if (callback) callback()
                    }
                  }
                })
              }
            }
          })
        }
      }
    })
  },

  // 显示提醒天数选择器
  showDaysSelector: function () {
    this.setData({
      showDaysSelector: true
    })
  },

  // 隐藏提醒天数选择器
  hideDaysSelector: function () {
    this.setData({
      showDaysSelector: false
    })
  },

  // 选择提醒天数
  selectReminderDays: function (e) {
    const days = parseInt(e.currentTarget.dataset.days)
    this.setData({
      reminderDays: days,
      showDaysSelector: false
    })
    wx.setStorageSync('reminderDays', days)
  },

  // 切换提醒方式
  toggleReminderMethod: function (e) {
    const index = e.currentTarget.dataset.index
    const reminderMethodOptions = this.data.reminderMethodOptions

    // 更新选中状态
    reminderMethodOptions[index].checked = !reminderMethodOptions[index].checked

    this.setData({
      reminderMethodOptions
    })

    // 保存设置
    wx.setStorageSync('reminderMethodOptions', reminderMethodOptions)
  },

  // 切换自动缴费开关
  toggleAutoPaySwitch: function (e) {
    const enabled = e.detail.value

    if (enabled) {
      // 如果开启自动缴费，检查是否有选择支付方式
      const hasSelectedMethod = this.data.paymentMethods.some(method => method.checked)

      if (!hasSelectedMethod) {
        wx.showToast({
          title: '请先选择支付方式',
          icon: 'none'
        })
        this.setData({
          autoPayEnabled: false
        })
        return
      }

      // 如果选择了银行卡支付但未选择具体银行卡
      const bankSelected = this.data.paymentMethods.find(method => method.value === 'bank' && method.checked)
      if (bankSelected && !this.data.selectedBankCard) {
        wx.showToast({
          title: '请先选择银行卡',
          icon: 'none'
        })
        this.setData({
          autoPayEnabled: false
        })
        return
      }
    }

    this.setData({
      autoPayEnabled: enabled
    })
    wx.setStorageSync('autoPayEnabled', enabled)
  },

  // 显示自动缴费限额选择器
  showLimitSelector: function () {
    this.setData({
      showLimitSelector: true
    })
  },

  // 隐藏自动缴费限额选择器
  hideLimitSelector: function () {
    this.setData({
      showLimitSelector: false
    })
  },

  // 选择自动缴费限额
  selectAutoPayLimit: function (e) {
    const limit = parseInt(e.currentTarget.dataset.limit)
    this.setData({
      autoPayLimit: limit,
      showLimitSelector: false
    })
    wx.setStorageSync('autoPayLimit', limit)
  },

  // 选择支付方式
  selectPaymentMethod: function (e) {
    const value = e.currentTarget.dataset.value

    // 更新支付方式选中状态
    const paymentMethods = this.data.paymentMethods.map(method => {
      return {
        ...method,
        checked: method.value === value
      }
    })

    this.setData({
      paymentMethods
    })

    // 如果选择了银行卡支付但未选择具体银行卡
    if (value === 'bank' && !this.data.selectedBankCard) {
      this.showBankCardSelector()
    }

    // 保存设置
    wx.setStorageSync('selectedPaymentMethod', value)
  },

  // 显示银行卡选择器
  showBankCardSelector: function () {
    this.setData({
      showBankCardSelector: true
    })
  },

  // 隐藏银行卡选择器
  hideBankCardSelector: function () {
    this.setData({
      showBankCardSelector: false
    })
  },

  // 选择银行卡
  selectBankCard: function (e) {
    const id = e.currentTarget.dataset.id
    const selectedBankCard = this.data.bankCards.find(card => card.id === id)

    this.setData({
      selectedBankCard,
      showBankCardSelector: false
    })

    // 保存设置
    wx.setStorageSync('selectedBankCardId', id)
  },

  // 添加银行卡
  addBankCard: function () {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  }
})
