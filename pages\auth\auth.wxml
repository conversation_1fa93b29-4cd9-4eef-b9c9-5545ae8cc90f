<!--auth.wxml-->
<view class="container">
  <view class="auth-header">
    <view class="auth-title">实名认证</view>
    <view class="auth-subtitle">完成认证后可使用全部功能</view>
  </view>
  
  <view class="auth-form">
    <view class="form-group">
      <view class="form-label">姓名</view>
      <input class="form-input" type="text" placeholder="请输入您的真实姓名" bindinput="inputName" value="{{name}}"/>
    </view>
    
    <view class="form-group">
      <view class="form-label">身份证号</view>
      <input class="form-input" type="idcard" placeholder="请输入您的身份证号码" bindinput="inputIdCard" value="{{idCard}}"/>
    </view>
    
    <view class="form-group">
      <view class="form-label">手机号</view>
      <input class="form-input" type="number" placeholder="请输入您的手机号码" bindinput="inputPhone" value="{{phone}}"/>
    </view>
    
    <view class="form-group">
      <view class="form-label">验证码</view>
      <view class="verification-code-group">
        <input class="form-input verification-input" type="number" placeholder="请输入验证码" bindinput="inputVerificationCode" value="{{verificationCode}}"/>
        <button class="verification-btn {{codeSent ? 'sent' : ''}}" bindtap="sendVerificationCode" disabled="{{codeSent}}">
          {{codeSent ? countDown + 's后重新获取' : '获取验证码'}}
        </button>
      </view>
    </view>
    
    <view class="form-group">
      <view class="form-label">房屋信息</view>
      <view class="house-info-selector" bindtap="selectHouse">
        <text>{{houseInfo || '请选择您的房屋信息'}}</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="agreement-group">
      <checkbox-group bindchange="checkboxChange">
        <label class="checkbox-label">
          <checkbox value="agree" checked="{{agreed}}"/>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" bindtap="showAgreement">《用户服务协议》</text>
          <text class="agreement-text">和</text>
          <text class="agreement-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
        </label>
      </checkbox-group>
    </view>
  </view>
  
  <view class="auth-footer">
    <button class="auth-btn {{canSubmit ? '' : 'disabled'}}" bindtap="submitAuth" disabled="{{!canSubmit}}">提交认证</button>
    <view class="auth-tips">认证信息仅用于业主身份验证，我们将严格保护您的隐私</view>
  </view>
</view>

<!-- 房屋选择弹窗 -->
<view class="house-selector-modal {{showHouseSelector ? 'show' : ''}}">
  <view class="house-selector-content">
    <view class="house-selector-header">
      <view class="house-selector-title">选择房屋</view>
      <view class="house-selector-close" bindtap="closeHouseSelector">×</view>
    </view>
    <view class="house-selector-body">
      <view class="house-item {{selectedHouse === index ? 'selected' : ''}}" 
            wx:for="{{houseList}}" 
            wx:key="id" 
            bindtap="selectHouseItem" 
            data-index="{{index}}">
        <view class="house-name">{{item.name}}</view>
        <view class="house-address">{{item.address}}</view>
      </view>
    </view>
    <view class="house-selector-footer">
      <button class="house-selector-btn" bindtap="confirmHouseSelection">确认选择</button>
    </view>
  </view>
</view>
