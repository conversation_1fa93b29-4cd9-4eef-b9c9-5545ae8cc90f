# WebSocket与Token管理集成方案

## 🎯 **解决的问题**

1. **Token过期检测缺失**：WebSocket管理器没有检测token过期时间
2. **重连机制有限**：只有5次重连，且只在正常断线时触发
3. **锁屏场景未处理**：用户锁屏后没有重连机制
4. **协作机制不完善**：request.js和WebSocket管理器缺乏协调

## 🛠 **解决方案架构**

### **1. Token生命周期管理增强**

#### request.js增强
- ✅ 添加token过期时间检测
- ✅ 登录/刷新token时保存过期时间
- ✅ 提供token剩余时间查询
- ✅ 添加WebSocket通知机制

#### 新增功能
```javascript
// 检查token是否过期（提前5分钟判断）
isTokenExpired()

// 获取token剩余有效时间
getTokenRemainingTime()

// 通知WebSocket管理器token状态变化
notifyWebSocketTokenChange()
```

### **2. WebSocket管理器增强**

#### 新增功能
- ✅ Token过期时间检测
- ✅ 小程序生命周期监听
- ✅ 无限重连机制（移除5次限制）
- ✅ 前台后台切换处理

#### 核心改进
```javascript
// Token过期检测
isTokenExpired(token, expireTime)

// 小程序生命周期处理
setupAppLifecycleListeners()
handleAppShow()  // 前台显示时重连
handleAppHide()  // 后台隐藏时保持连接

// 无限重连（重置计数器）
startReconnect() // 移除最大次数限制
```

### **3. 协作机制**

#### Token状态同步
1. **登录成功** → 通知WebSocket → 建立连接
2. **Token刷新** → 通知WebSocket → 重新连接
3. **Token过期** → WebSocket断开 → 等待新token
4. **Token清除** → WebSocket断开 → 进入等待状态

#### 生命周期协调
1. **小程序启动** → 检查token → 建立连接
2. **进入后台** → 保持连接
3. **回到前台** → 检查token → 重连（如需要）
4. **长时间后台** → 系统断开 → 前台时自动重连

## 🧪 **测试场景**

### **1. Token过期场景**
```
测试步骤：
1. 正常使用小程序，建立WebSocket连接
2. 等待token接近过期时间（或手动修改过期时间）
3. 观察WebSocket是否自动断开
4. 触发API请求，观察token是否自动刷新
5. 观察WebSocket是否重新连接

预期结果：
- Token过期前5分钟WebSocket自动断开
- API请求触发token刷新
- Token刷新后WebSocket自动重连
```

### **2. 锁屏场景**
```
测试步骤：
1. 正常使用小程序，建立WebSocket连接
2. 锁屏手机，等待较长时间（10-30分钟）
3. 解锁手机，重新打开小程序
4. 观察WebSocket连接状态

预期结果：
- 小程序回到前台时自动检查连接状态
- 如果连接断开且token有效，自动重连
- 如果token过期，等待API请求触发刷新后重连
```

### **3. 长时间使用场景**
```
测试步骤：
1. 长时间保持小程序在前台
2. 观察WebSocket连接状态
3. 观察重连机制是否正常工作

预期结果：
- 连接断开时自动重连
- 重连次数不受5次限制
- 重连计数器定期重置
```

### **4. 网络切换场景**
```
测试步骤：
1. 在WiFi环境下建立连接
2. 切换到移动网络
3. 观察连接状态和重连机制

预期结果：
- 网络切换时连接可能断开
- 自动检测并重连
- 重连成功后恢复正常功能
```

## 📊 **监控和调试**

### **日志输出**
- Token状态检查日志
- 连接状态变化日志
- 重连尝试日志
- 生命周期事件日志

### **关键日志示例**
```
检查token状态: {hasToken: true, tokenChanged: false, isTokenExpired: false}
小程序从后台进入前台
处理小程序前台显示
小程序前台显示，尝试重新连接
开始第 1 次重连
重连成功
Token刷新成功
通知WebSocket管理器token已更新
```

## 🎯 **预期效果**

1. **无缝连接体验**：用户无需关心连接状态，系统自动维护
2. **Token自动管理**：过期检测和刷新完全自动化
3. **生命周期适配**：适应小程序前台后台切换
4. **网络适应性**：网络变化时自动重连
5. **长期稳定性**：长时间使用不会因重连次数限制而断开

## 🔧 **配置说明**

### **Token过期缓冲时间**
```javascript
const bufferTime = 5 * 60 * 1000; // 5分钟
```

### **重连策略**
```javascript
// 指数退避算法
baseDelay: 1000ms
maxDelay: 30000ms
无最大重连次数限制
```

### **检查间隔**
```javascript
tokenCheckInterval: 30000ms // 30秒检查一次token状态
```
