// pages/payment/analysis/analysis.js
const util = require('@/utils/util.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,
    
    // 图表相关
    canvasWidth: 0,
    canvasHeight: 0,
    pixelRatio: 2,
    
    // 时间范围选择
    timeRanges: ['近3个月', '近6个月', '近12个月'],
    selectedTimeRange: 0, // 默认选择近3个月
    
    // 图表类型选择
    chartTypes: ['支出趋势', '支出类型', '同比分析'],
    selectedChartType: 0, // 默认选择支出趋势
    
    // 数据统计
    statistics: {
      totalAmount: 0,
      averageAmount: 0,
      maxAmount: 0,
      maxAmountMonth: '',
      paymentCount: 0
    },
    
    // 支出趋势数据
    trendData: [],
    
    // 支出类型数据
    typeData: [],
    
    // 同比分析数据
    comparisonData: {
      current: [],
      previous: []
    },
    
    // 节约建议
    savingTips: []
  },

  onLoad: function () {
    this.setStatusBarHeight()
    this.setCanvasSize()
    this.loadAnalysisData()
  },
  
  // 设置状态栏高度
  setStatusBarHeight: function () {
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      pixelRatio: systemInfo.pixelRatio
    })
  },
  
  // 设置画布大小
  setCanvasSize: function () {
    const systemInfo = wx.getSystemInfoSync()
    const canvasWidth = systemInfo.windowWidth
    const canvasHeight = 220
    
    this.setData({
      canvasWidth,
      canvasHeight
    })
  },
  
  // 加载分析数据
  loadAnalysisData: function () {
    this.setData({
      isLoading: true
    })
    
    // 模拟加载数据
    setTimeout(() => {
      // 根据选择的时间范围和图表类型生成数据
      this.generateAnalysisData()
      
      this.setData({
        isLoading: false
      })
      
      // 绘制图表
      this.drawChart()
    }, 1000)
  },
  
  // 生成分析数据
  generateAnalysisData: function () {
    const timeRange = this.getTimeRangeMonths()
    const chartType = this.data.chartTypes[this.data.selectedChartType]
    
    // 生成支出趋势数据
    const trendData = this.generateTrendData(timeRange)
    
    // 生成支出类型数据
    const typeData = this.generateTypeData()
    
    // 生成同比分析数据
    const comparisonData = this.generateComparisonData(timeRange)
    
    // 计算统计数据
    const statistics = this.calculateStatistics(trendData)
    
    // 生成节约建议
    const savingTips = this.generateSavingTips(typeData, trendData)
    
    this.setData({
      trendData,
      typeData,
      comparisonData,
      statistics,
      savingTips
    })
  },
  
  // 获取时间范围对应的月数
  getTimeRangeMonths: function () {
    const ranges = [3, 6, 12]
    return ranges[this.data.selectedTimeRange]
  },
  
  // 生成支出趋势数据
  generateTrendData: function (months) {
    const data = []
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth()
    
    for (let i = months - 1; i >= 0; i--) {
      const month = (currentMonth - i + 12) % 12
      const year = currentYear - Math.floor((i - currentMonth) / 12)
      const monthName = `${year}年${month + 1}月`
      
      // 生成随机金额，但保持一定的趋势性
      let amount = 0
      
      // 物业费（每季度）
      if (month % 3 === 0) {
        amount += 720
      }
      
      // 停车费（每季度）
      if (month % 3 === 0) {
        amount += 450
      }
      
      // 水电费（每月，有波动）
      const baseUtility = 80
      const seasonFactor = Math.sin((month / 12) * Math.PI * 2) * 30 // 季节性波动
      amount += baseUtility + seasonFactor + Math.random() * 20
      
      // 四舍五入到2位小数
      amount = Math.round(amount * 100) / 100
      
      data.push({
        month: monthName,
        amount: amount
      })
    }
    
    return data
  },
  
  // 生成支出类型数据
  generateTypeData: function () {
    return [
      { name: '物业费', value: 2880, percentage: 60 },
      { name: '停车费', value: 1800, percentage: 30 },
      { name: '水电费', value: 480, percentage: 10 }
    ]
  },
  
  // 生成同比分析数据
  generateComparisonData: function (months) {
    const currentData = []
    const previousData = []
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth()
    
    for (let i = 0; i < months; i++) {
      const month = (currentMonth - months + i + 1 + 12) % 12
      const monthName = `${month + 1}月`
      
      // 当前年度数据
      const currentAmount = 500 + Math.random() * 300
      currentData.push({
        month: monthName,
        amount: Math.round(currentAmount * 100) / 100
      })
      
      // 上一年度数据（略高一些，表示今年有节约）
      const previousAmount = currentAmount * (1 + Math.random() * 0.2)
      previousData.push({
        month: monthName,
        amount: Math.round(previousAmount * 100) / 100
      })
    }
    
    return {
      current: currentData,
      previous: previousData
    }
  },
  
  // 计算统计数据
  calculateStatistics: function (trendData) {
    let totalAmount = 0
    let maxAmount = 0
    let maxAmountMonth = ''
    
    trendData.forEach(item => {
      totalAmount += item.amount
      
      if (item.amount > maxAmount) {
        maxAmount = item.amount
        maxAmountMonth = item.month
      }
    })
    
    const paymentCount = trendData.length
    const averageAmount = paymentCount > 0 ? Math.round((totalAmount / paymentCount) * 100) / 100 : 0
    
    return {
      totalAmount: Math.round(totalAmount * 100) / 100,
      averageAmount,
      maxAmount,
      maxAmountMonth,
      paymentCount
    }
  },
  
  // 生成节约建议
  generateSavingTips: function (typeData, trendData) {
    const tips = [
      '物业费可以选择年付方式，享受95折优惠',
      '夏季空调使用建议：温度设置在26℃左右，每提高1℃可节约6%的用电量',
      '冬季采暖建议：温度设置在18-20℃之间，每降低1℃可节约约10%的能耗',
      '使用节水型龙头和花洒，可以减少30%-50%的用水量',
      '选择LED灯具替换传统灯泡，可节约80%的照明用电',
      '充分利用自然光，减少不必要的照明使用',
      '洗衣机满载运行，可以节约用水和用电',
      '定期检查水管是否漏水，避免水费浪费'
    ]
    
    // 根据数据情况，选择合适的建议
    const selectedTips = []
    
    // 如果物业费占比高，添加物业费相关建议
    if (typeData[0].percentage > 50) {
      selectedTips.push(tips[0])
    }
    
    // 根据季节添加相应建议
    const now = new Date()
    const month = now.getMonth()
    
    if (month >= 5 && month <= 8) {
      // 夏季
      selectedTips.push(tips[1])
    } else if (month >= 10 || month <= 2) {
      // 冬季
      selectedTips.push(tips[2])
    }
    
    // 添加通用节约建议
    selectedTips.push(tips[3])
    selectedTips.push(tips[4])
    
    return selectedTips
  },
  
  // 绘制图表
  drawChart: function () {
    const chartType = this.data.selectedChartType
    
    if (chartType === 0) {
      this.drawTrendChart()
    } else if (chartType === 1) {
      this.drawTypeChart()
    } else if (chartType === 2) {
      this.drawComparisonChart()
    }
  },
  
  // 绘制支出趋势图表
  drawTrendChart: function () {
    const query = wx.createSelectorQuery()
    query.select('#analysisCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')
          
          // 设置画布大小
          const dpr = this.data.pixelRatio
          canvas.width = this.data.canvasWidth * dpr
          canvas.height = this.data.canvasHeight * dpr
          ctx.scale(dpr, dpr)
          
          // 清空画布
          ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)
          
          // 绘制趋势图
          this.renderTrendChart(ctx)
        }
      })
  },
  
  // 绘制支出类型图表
  drawTypeChart: function () {
    const query = wx.createSelectorQuery()
    query.select('#analysisCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')
          
          // 设置画布大小
          const dpr = this.data.pixelRatio
          canvas.width = this.data.canvasWidth * dpr
          canvas.height = this.data.canvasHeight * dpr
          ctx.scale(dpr, dpr)
          
          // 清空画布
          ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)
          
          // 绘制饼图
          this.renderPieChart(ctx)
        }
      })
  },
  
  // 绘制同比分析图表
  drawComparisonChart: function () {
    const query = wx.createSelectorQuery()
    query.select('#analysisCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')
          
          // 设置画布大小
          const dpr = this.data.pixelRatio
          canvas.width = this.data.canvasWidth * dpr
          canvas.height = this.data.canvasHeight * dpr
          ctx.scale(dpr, dpr)
          
          // 清空画布
          ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)
          
          // 绘制对比图
          this.renderComparisonChart(ctx)
        }
      })
  },
  
  // 渲染趋势图
  renderTrendChart: function (ctx) {
    const data = this.data.trendData
    const width = this.data.canvasWidth
    const height = this.data.canvasHeight
    const padding = 40
    const chartWidth = width - padding * 2
    const chartHeight = height - padding * 2
    
    // 找出最大值，用于计算比例
    let maxValue = 0
    data.forEach(item => {
      if (item.amount > maxValue) {
        maxValue = item.amount
      }
    })
    maxValue = Math.ceil(maxValue / 100) * 100 // 向上取整到百位
    
    // 绘制坐标轴
    ctx.beginPath()
    ctx.strokeStyle = '#ccc'
    ctx.lineWidth = 1
    
    // X轴
    ctx.moveTo(padding, height - padding)
    ctx.lineTo(width - padding, height - padding)
    
    // Y轴
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height - padding)
    ctx.stroke()
    
    // 绘制X轴标签
    ctx.textAlign = 'center'
    ctx.textBaseline = 'top'
    ctx.fillStyle = '#666'
    ctx.font = '12px sans-serif'
    
    const barWidth = chartWidth / data.length * 0.6
    const barSpacing = chartWidth / data.length
    
    data.forEach((item, index) => {
      const x = padding + barSpacing * (index + 0.5)
      ctx.fillText(item.month.substring(5), x, height - padding + 10)
    })
    
    // 绘制Y轴标签
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    
    for (let i = 0; i <= 5; i++) {
      const y = height - padding - (chartHeight / 5) * i
      const value = (maxValue / 5) * i
      ctx.fillText('¥' + value.toFixed(0), padding - 10, y)
      
      // 绘制网格线
      ctx.beginPath()
      ctx.strokeStyle = '#eee'
      ctx.moveTo(padding, y)
      ctx.lineTo(width - padding, y)
      ctx.stroke()
    }
    
    // 绘制柱状图
    data.forEach((item, index) => {
      const x = padding + barSpacing * (index + 0.5) - barWidth / 2
      const barHeight = (item.amount / maxValue) * chartHeight
      const y = height - padding - barHeight
      
      // 渐变填充
      const gradient = ctx.createLinearGradient(x, y, x, height - padding)
      gradient.addColorStop(0, '#ff8c00')
      gradient.addColorStop(1, '#ff6b00')
      
      ctx.fillStyle = gradient
      ctx.fillRect(x, y, barWidth, barHeight)
      
      // 显示数值
      ctx.fillStyle = '#333'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'bottom'
      ctx.fillText('¥' + item.amount.toFixed(0), x + barWidth / 2, y - 5)
    })
  },
  
  // 渲染饼图
  renderPieChart: function (ctx) {
    const data = this.data.typeData
    const width = this.data.canvasWidth
    const height = this.data.canvasHeight
    const centerX = width / 2
    const centerY = height / 2
    const radius = Math.min(centerX, centerY) - 60
    
    // 颜色数组
    const colors = ['#ff8c00', '#2196f3', '#4caf50', '#9c27b0', '#f44336']
    
    // 绘制饼图
    let startAngle = 0
    data.forEach((item, index) => {
      const endAngle = startAngle + (item.percentage / 100) * Math.PI * 2
      
      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, startAngle, endAngle)
      ctx.closePath()
      
      ctx.fillStyle = colors[index % colors.length]
      ctx.fill()
      
      // 计算标签位置
      const labelAngle = startAngle + (endAngle - startAngle) / 2
      const labelRadius = radius * 0.7
      const labelX = centerX + Math.cos(labelAngle) * labelRadius
      const labelY = centerY + Math.sin(labelAngle) * labelRadius
      
      // 绘制标签
      ctx.fillStyle = '#fff'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.font = 'bold 14px sans-serif'
      ctx.fillText(item.percentage + '%', labelX, labelY)
      
      startAngle = endAngle
    })
    
    // 绘制图例
    const legendX = 60
    const legendY = height - 80
    const legendSpacing = 30
    
    data.forEach((item, index) => {
      const y = legendY + index * legendSpacing
      
      // 绘制颜色方块
      ctx.fillStyle = colors[index % colors.length]
      ctx.fillRect(legendX, y, 16, 16)
      
      // 绘制文本
      ctx.fillStyle = '#333'
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.font = '14px sans-serif'
      ctx.fillText(`${item.name}: ¥${item.value.toFixed(0)} (${item.percentage}%)`, legendX + 24, y + 8)
    })
  },
  
  // 渲染对比图
  renderComparisonChart: function (ctx) {
    const currentData = this.data.comparisonData.current
    const previousData = this.data.comparisonData.previous
    const width = this.data.canvasWidth
    const height = this.data.canvasHeight
    const padding = 40
    const chartWidth = width - padding * 2
    const chartHeight = height - padding * 2
    
    // 找出最大值，用于计算比例
    let maxValue = 0
    currentData.concat(previousData).forEach(item => {
      if (item.amount > maxValue) {
        maxValue = item.amount
      }
    })
    maxValue = Math.ceil(maxValue / 100) * 100 // 向上取整到百位
    
    // 绘制坐标轴
    ctx.beginPath()
    ctx.strokeStyle = '#ccc'
    ctx.lineWidth = 1
    
    // X轴
    ctx.moveTo(padding, height - padding)
    ctx.lineTo(width - padding, height - padding)
    
    // Y轴
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height - padding)
    ctx.stroke()
    
    // 绘制X轴标签
    ctx.textAlign = 'center'
    ctx.textBaseline = 'top'
    ctx.fillStyle = '#666'
    ctx.font = '12px sans-serif'
    
    const pointSpacing = chartWidth / (currentData.length - 1)
    
    currentData.forEach((item, index) => {
      const x = padding + pointSpacing * index
      ctx.fillText(item.month, x, height - padding + 10)
    })
    
    // 绘制Y轴标签
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    
    for (let i = 0; i <= 5; i++) {
      const y = height - padding - (chartHeight / 5) * i
      const value = (maxValue / 5) * i
      ctx.fillText('¥' + value.toFixed(0), padding - 10, y)
      
      // 绘制网格线
      ctx.beginPath()
      ctx.strokeStyle = '#eee'
      ctx.moveTo(padding, y)
      ctx.lineTo(width - padding, y)
      ctx.stroke()
    }
    
    // 绘制当前年度折线
    ctx.beginPath()
    ctx.strokeStyle = '#ff8c00'
    ctx.lineWidth = 2
    
    currentData.forEach((item, index) => {
      const x = padding + pointSpacing * index
      const y = height - padding - (item.amount / maxValue) * chartHeight
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    
    ctx.stroke()
    
    // 绘制上一年度折线
    ctx.beginPath()
    ctx.strokeStyle = '#2196f3'
    ctx.lineWidth = 2
    
    previousData.forEach((item, index) => {
      const x = padding + pointSpacing * index
      const y = height - padding - (item.amount / maxValue) * chartHeight
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    
    ctx.stroke()
    
    // 绘制数据点
    currentData.forEach((item, index) => {
      const x = padding + pointSpacing * index
      const y = height - padding - (item.amount / maxValue) * chartHeight
      
      ctx.beginPath()
      ctx.fillStyle = '#ff8c00'
      ctx.arc(x, y, 4, 0, Math.PI * 2)
      ctx.fill()
    })
    
    previousData.forEach((item, index) => {
      const x = padding + pointSpacing * index
      const y = height - padding - (item.amount / maxValue) * chartHeight
      
      ctx.beginPath()
      ctx.fillStyle = '#2196f3'
      ctx.arc(x, y, 4, 0, Math.PI * 2)
      ctx.fill()
    })
    
    // 绘制图例
    const legendX = width - 180
    const legendY = padding + 20
    
    // 当前年度
    ctx.beginPath()
    ctx.strokeStyle = '#ff8c00'
    ctx.lineWidth = 2
    ctx.moveTo(legendX, legendY)
    ctx.lineTo(legendX + 30, legendY)
    ctx.stroke()
    
    ctx.beginPath()
    ctx.fillStyle = '#ff8c00'
    ctx.arc(legendX + 15, legendY, 4, 0, Math.PI * 2)
    ctx.fill()
    
    ctx.fillStyle = '#333'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    ctx.font = '14px sans-serif'
    ctx.fillText('当前年度', legendX + 40, legendY)
    
    // 上一年度
    ctx.beginPath()
    ctx.strokeStyle = '#2196f3'
    ctx.lineWidth = 2
    ctx.moveTo(legendX, legendY + 25)
    ctx.lineTo(legendX + 30, legendY + 25)
    ctx.stroke()
    
    ctx.beginPath()
    ctx.fillStyle = '#2196f3'
    ctx.arc(legendX + 15, legendY + 25, 4, 0, Math.PI * 2)
    ctx.fill()
    
    ctx.fillStyle = '#333'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    ctx.font = '14px sans-serif'
    ctx.fillText('上一年度', legendX + 40, legendY + 25)
  },
  
  // 切换时间范围
  changeTimeRange: function (e) {
    const index = e.currentTarget.dataset.index
    
    this.setData({
      selectedTimeRange: index
    })
    
    this.loadAnalysisData()
  },
  
  // 切换图表类型
  changeChartType: function (e) {
    const index = e.currentTarget.dataset.index
    
    this.setData({
      selectedChartType: index
    })
    
    this.loadAnalysisData()
  },
  
  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  }
})
