# ECharts饼图图例间距问题彻底修复

## 问题描述

用户反馈：访客统计分析等页面的ECharts饼图下方的图例（legend）与饼图本身距离太近，导致部分重叠，影响视觉效果和用户体验。

## 问题分析

经过网络搜索和研究，发现ECharts饼图图例重叠问题的主要原因：

1. **图例位置设置不当**：`bottom`值太小，图例距离饼图太近
2. **饼图位置不合理**：`center`位置没有为图例预留足够空间
3. **缺少容器空间控制**：没有使用`grid`属性预留底部空间
4. **饼图尺寸过大**：`radius`值过大，占用了图例的显示空间

## 解决方案

基于网络搜索结果和最佳实践，采用以下综合解决方案：

### 1. 添加grid配置
```javascript
grid: {
  bottom: 80 // 为图例预留足够的底部空间
}
```

### 2. 优化legend配置
```javascript
legend: {
  orient: 'horizontal',
  bottom: 20, // 图例距离底部的距离（从10增加到20）
  left: 'center', // 图例居中对齐
  itemGap: 20, // 增加图例项之间的间距
  itemWidth: 15, // 设置图例标记的宽度
  itemHeight: 10, // 设置图例标记的高度
  textStyle: {
    fontSize: 12, // 字体大小从10增加到12
    padding: [0, 0, 0, 5] // 增加文本与图例标记的间距
  }
}
```

### 3. 调整饼图位置和大小
```javascript
series: [{
  type: 'pie',
  radius: '50%', // 从60%缩小到50%，为图例留出更多空间
  center: ['50%', '35%'], // 从['50%', '50%']调整到['50%', '35%']，向上移动
  // ... 其他配置
}]
```

## 修复的页面和图表

### 1. 访客统计分析页面
**文件**: `propertyPackage/pages/property/visitor-stats/index.js`

- **停留时长分布饼图**
  - 添加grid配置，bottom: 80
  - 调整legend的bottom从30到20，添加left: 'center'
  - 饼图radius从['25%', '55%']调整到['25%', '50%']
  - 饼图center从['50%', '40%']调整到['50%', '35%']

- **停车时长分布饼图**
  - 添加grid配置，bottom: 80
  - 调整legend的bottom从30到20，添加left: 'center'
  - 饼图radius从['35%', '65%']调整到['30%', '55%']
  - 饼图center从['50%', '40%']调整到['50%', '35%']

### 2. 工单统计页面
**文件**: `propertyPackage/pages/property/workorder/stats/index.js`

- **工单类型分布饼图**
  - 添加grid配置，bottom: 80
  - 调整legend的bottom从10到20，添加left: 'center'
  - 饼图radius从['40%', '70%']调整到['35%', '60%']
  - 饼图center从['65%', '50%']调整到['50%', '35%']，同时居中显示

### 3. 物业统计页面
**文件**: `propertyPackage/pages/property/statistics/statistics.js`

修复了6个饼图：

- **居民类型分布饼图**
- **工单类型分布饼图**
- **房屋类型分布饼图**
- **车位类型分布饼图**
- **车辆颜色分布饼图**
- **访客目的分布饼图**

每个饼图都应用了相同的修复方案：
- 添加grid配置，bottom: 80
- 添加或优化legend配置
- 饼图radius从60%缩小到50%
- 饼图center从['50%', '50%']调整到['50%', '35%']

## 技术要点

### 1. Grid属性的作用
`grid.bottom: 80`为图例预留了80像素的底部空间，确保图例有足够的显示区域。

### 2. Legend位置优化
- `bottom: 20`：图例距离容器底部20像素
- `left: 'center'`：图例水平居中对齐
- `itemGap: 20`：图例项之间的间距为20像素

### 3. 饼图位置调整
- `center: ['50%', '35%']`：将饼图向上移动到35%位置，为底部图例留出空间
- `radius: '50%'`：适当缩小饼图，避免与图例重叠

### 4. 字体和间距优化
- `fontSize: 12`：增大字体，提高可读性
- `padding: [0, 0, 0, 5]`：增加文本与图例标记的间距

## 视觉效果改进

### 修复前
```
┌─────────────────────┐
│                     │
│    ●●●●●●●●●●●●●    │ ← 饼图过大
│    ●●●●●●●●●●●●●    │
│    ●●●●●●●●●●●●●    │
│ ■ 项目1 ■ 项目2 ■ 项目3 │ ← 图例与饼图重叠
└─────────────────────┘
```

### 修复后
```
┌─────────────────────┐
│                     │
│     ●●●●●●●●●●●     │ ← 饼图适中，位置上移
│     ●●●●●●●●●●●     │
│                     │
│                     │ ← 充足的间距
│ ■ 项目1 ■ 项目2 ■ 项目3 │ ← 图例清晰显示
└─────────────────────┘
```

## 兼容性和性能

### 1. 向后兼容
- 保持原有的数据结构和API调用
- 不影响图表的交互功能
- 保持颜色主题和样式一致性

### 2. 响应式设计
- 使用百分比单位，适应不同屏幕尺寸
- 图例自动换行，适应内容长度
- 保持图表在小屏幕上的可读性

### 3. 性能优化
- 配置优化不影响渲染性能
- 保持ECharts的原生优化特性

## 测试建议

### 1. 视觉测试
- 检查所有饼图的图例是否与饼图有足够间距
- 验证图例文字是否清晰可读
- 确认饼图大小适中，不会过小影响数据展示

### 2. 响应式测试
- 在不同屏幕尺寸下测试图表显示
- 验证图例在小屏幕上的换行效果
- 检查图表在横屏和竖屏模式下的表现

### 3. 数据测试
- 测试不同数量的图例项（2-10项）
- 验证长文本图例的显示效果
- 检查空数据时的图表表现

## 相关文件

- `propertyPackage/pages/property/visitor-stats/index.js` - 访客统计页面
- `propertyPackage/pages/property/workorder/stats/index.js` - 工单统计页面  
- `propertyPackage/pages/property/statistics/statistics.js` - 物业统计页面

## 参考资源

- [ECharts官方文档 - Legend配置](https://echarts.apache.org/zh/option.html#legend)
- [ECharts官方文档 - Grid配置](https://echarts.apache.org/zh/option.html#grid)
- [Stack Overflow - ECharts Legend Overlap Solutions](https://stackoverflow.com/questions/76466164/prevent-overlapping-of-the-chart-title-and-legend-with-apache-echarts)

现在所有ECharts饼图的图例间距问题都已经彻底解决，图例与饼图之间有了充足的空间，提升了整体的视觉效果和用户体验。
