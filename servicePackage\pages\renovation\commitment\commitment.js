// pages/renovation/commitment/commitment.js
const app = getApp();
const dateUtil = require('../../../../utils/dateUtil.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    basicInfo: {},
    planInfo: {},
    materialsInfo: {},
    currentDate: '',
    isAgreed: false,
    isFormValid: false,
    hasSigned: false,
    signaturePoints: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取上一步保存的基本信息、施工方案和材料信息
    const basicInfo = wx.getStorageSync('renovationFormData') || {};
    const planInfo = wx.getStorageSync('renovationPlanData') || {};
    const materialsInfo = wx.getStorageSync('renovationMaterialsData') || {};

    // 获取当前日期
    const currentDate = dateUtil.formatDate(new Date());

    this.setData({
      basicInfo: basicInfo,
      planInfo: planInfo,
      materialsInfo: materialsInfo,
      currentDate: currentDate
    });

    // 初始化签名画布
    this.initSignaturePad();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 初始化签名画布
   */
  initSignaturePad() {
    const ctx = wx.createCanvasContext('signaturePad');

    // 设置画布背景为白色
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, 1000, 1000);

    // 设置画笔样式
    ctx.setStrokeStyle('#000000');
    ctx.setLineWidth(4);
    ctx.setLineCap('round');
    ctx.setLineJoin('round');

    // 保存上下文引用
    this.ctx = ctx;
    ctx.draw();

    // 获取画布信息以适应不同设备
    const query = wx.createSelectorQuery();
    query.select('.signature-pad').boundingClientRect(rect => {
      this.canvasWidth = rect.width;
      this.canvasHeight = rect.height;
    }).exec();
  },

  /**
   * 处理触摸开始事件
   */
  handleTouchStart(e) {
    const x = e.touches[0].x;
    const y = e.touches[0].y;

    // 保存起始点
    this.lastX = x;
    this.lastY = y;

    // 初始化点数组，但不再每次都更新整个数组
    if (!this.data.signaturePoints.length) {
      this.setData({
        signaturePoints: [[x, y]]
      });
    } else {
      // 只添加到内部数组，不触发视图更新
      this.data.signaturePoints.push([x, y]);
    }

    // 获取画布上下文
    this.ctx = wx.createCanvasContext('signaturePad');
    this.ctx.setStrokeStyle('#000000');
    this.ctx.setLineWidth(4);
    this.ctx.setLineCap('round');
    this.ctx.setLineJoin('round');
  },
 
  /**
   * 处理触摸移动事件
   */
  handleTouchMove(e) {
    const x = e.touches[0].x;
    const y = e.touches[0].y;

    // 只绘制新的线段，不重新绘制所有点
    this.ctx.beginPath();
    this.ctx.moveTo(this.lastX, this.lastY);
    this.ctx.lineTo(x, y);
    this.ctx.stroke();
    this.ctx.draw(true);

    // 更新最后的点
    this.lastX = x;
    this.lastY = y;

    // 添加到点数组，但不触发视图更新
    this.data.signaturePoints.push([x, y]);
  },

  /**
   * 处理触摸结束事件
   */
  handleTouchEnd(e) {
    // 如果有签名点，则表示已签名
    if (this.data.signaturePoints.length > 2) {
      this.setData({
        hasSigned: true
      });

      // 检查表单有效性
      this.checkFormValidity();
    }

    // 释放上下文引用，减少内存占用
    this.ctx = null;
  },

  /**
   * 清除签名
   */
  clearSignature() {
    this.setData({
      signaturePoints: [],
      hasSigned: false
    });

    // 重新初始化画布
    this.initSignaturePad();

    // 检查表单有效性
    this.checkFormValidity();

    // 显示反馈
    wx.showToast({
      title: '已清除签名',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 切换同意条款
   */
  toggleAgreement() {
    this.setData({
      isAgreed: !this.data.isAgreed
    });

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 检查表单有效性
   */
  checkFormValidity() {
    const isValid = this.data.hasSigned && this.data.isAgreed;

    this.setData({
      isFormValid: isValid
    });
  },

  /**
   * 返回上一步
   */
  goToPrevStep() {
    wx.navigateBack();
  },

  /**
   * 提交申请
   */
  submitApplication() {
    // 检查表单有效性
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请签名并同意承诺书',
        icon: 'none'
      });
      return;
    }

    // 保存签名图片
    wx.canvasToTempFilePath({
      canvasId: 'signaturePad',
      success: (res) => {
        const signatureImage = res.tempFilePath;

        // 模拟提交申请
        wx.showLoading({
          title: '提交中...',
          mask: true
        });

        // 模拟网络请求延迟
        setTimeout(() => {
          wx.hideLoading();

          // 清除表单数据
          wx.removeStorageSync('renovationFormData');
          wx.removeStorageSync('renovationPlanData');
          wx.removeStorageSync('renovationMaterialsData');

          // 生成申请ID
          const applicationId = 'RN' + dateUtil.formatTime(new Date(), 'YYYYMMDDHHmmss');

          // 跳转到状态页面
          wx.redirectTo({
            url: `/servicePackage/pages/renovation/status/status?id=${applicationId}&new=1`
          });
        }, 1500);
      },
      fail: (err) => {
        console.error('保存签名失败', err);
        wx.showToast({
          title: '保存签名失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})