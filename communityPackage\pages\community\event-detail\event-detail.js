// event-detail.js
const util = require('../../../../utils/util.js')
// 引入page-lifecycle.js只是为了确保它被加载
require('../../../../utils/page-lifecycle.js')

Page({
  data: {
    event: {
      id: 0,
      title: '',
      image: '',
      time: '',
      location: '',
      participants: 0,
      maxParticipants: 0,
      status: '',
      description: '',
      notices: []
    },
    showJoinModal: false,
    darkMode: false
  },

  onLoad: function(options) {
    // 获取活动ID
    const id = options.id

    // 模拟获取活动详情
    this.getEventDetail(id)

    // 检查用户是否已报名
    this.checkEnrollmentStatus(id)
  },

  // 检查用户是否已报名该活动
  checkEnrollmentStatus: function(id) {
    // 从本地存储获取已报名活动ID列表
    const enrolledIds = wx.getStorageSync('userEnrolledEvents') || []

    // 检查当前活动是否在已报名列表中
    const isEnrolled = enrolledIds.includes(parseInt(id))

    if (isEnrolled) {
      this.setData({
        'event.isEnrolled': true
      })
    }
  },

  onShow: function() {
    // 页面生命周期会自动处理暗黑模式
    // 这里不需要额外的代码
  },

  getEventDetail: function(id) {
    // 模拟活动数据
    const events = [
      {
        id: 1,
        title: '中秋团圆晚会',
        image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        time: '2023年09月29日 19:00-21:00',
        location: '小区中央花园',
        participants: 42,
        maxParticipants: 100,
        status: '报名中',
        description: '中秋节是中国传统的团圆节日，为增进邻里感情，共度佳节，特举办此次中秋团圆晚会。活动内容丰富多彩，包括猜灯谜、品月饼、文艺表演等多种形式，欢迎社区居民踊跃参与。',
        notices: [
          '请提前10分钟到达活动现场',
          '活动现场提供免费月饼和饮料',
          '请自觉维护现场秩序，活动结束后带走个人垃圾',
          '如遇恶劣天气，活动将延期举行，具体时间另行通知'
        ]
      },
      {
        id: 2,
        title: '社区健康讲座',
        image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        time: '2023年10月05日 14:00-16:00',
        location: '小区会议室',
        participants: 28,
        maxParticipants: 50,
        status: '报名中',
        description: '为提高社区居民健康意识，特邀请三甲医院专家举办健康讲座。讲座内容包括常见疾病预防和健康生活方式，适合所有年龄段居民参加。讲座结束后，专家将现场解答居民健康问题。',
        notices: [
          '请提前10分钟到达活动现场',
          '活动现场提供免费茶水',
          '可携带个人健康问题，专家将在讲座后解答',
          '请保持安静，不要在讲座过程中大声喧哗'
        ]
      },
      {
        id: 3,
        title: '亲子运动会',
        image: 'https://images.unsplash.com/photo-1527529482837-4698179dc6ce?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        time: '2023年10月12日 09:00-12:00',
        location: '小区运动场',
        participants: 35,
        maxParticipants: 40,
        status: '报名已满',
        description: '为增进亲子关系，特举办此次运动会。活动包括亲子接力、趣味游戏等多个环节，适合3-12岁儿童及其家长参加。活动结束后将颁发参与奖和优胜奖。',
        notices: [
          '请提前20分钟到达活动现场进行签到',
          '请穿着运动服装和运动鞋参加',
          '活动现场提供免费饮用水',
          '请家长全程陪同孩子参与活动，确保安全'
        ]
      },
      {
        id: 4,
        title: '社区读书会',
        image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        time: '2023年10月08日 19:30-21:30',
        location: '小区图书室',
        participants: 22,
        maxParticipants: 30,
        status: '报名中',
        description: '每月一次的读书分享活动，本月主题为经典文学作品赏析。参与者可以分享自己喜爱的经典文学作品，交流阅读心得。欢迎文学爱好者参加。',
        notices: [
          '请提前10分钟到达活动现场',
          '可携带自己喜爱的书籍参加',
          '每人分享时间不超过10分钟',
          '请保持安静，尊重他人发言'
        ]
      }
    ]

    // 查找对应ID的活动
    const event = events.find(item => item.id == id)

    if (event) {
      this.setData({
        event
      })
    } else {
      wx.showToast({
        title: '未找到活动信息',
        icon: 'none'
      })
    }
  },

  joinEvent: function() {
    if (this.data.event.status !== '报名中') {
      return
    }

    // 获取活动ID
    const activityId = this.data.event.id

    // 获取用户已报名的活动ID列表
    let enrolledIds = wx.getStorageSync('userEnrolledEvents') || []

    // 检查是否已报名
    if (enrolledIds.includes(activityId)) {
      wx.showToast({
        title: '您已报名此活动',
        icon: 'none'
      })
      return
    }

    // 添加到已报名列表
    enrolledIds.push(activityId)

    // 保存到本地存储
    wx.setStorageSync('userEnrolledEvents', enrolledIds)

    // 显示报名成功弹窗
    this.setData({
      showJoinModal: true,
      'event.isEnrolled': true
    })

    // 更新参与人数
    this.setData({
      'event.participants': this.data.event.participants + 1
    })

    // 如果人数已满，更新状态
    if (this.data.event.participants >= this.data.event.maxParticipants) {
      this.setData({
        'event.status': '报名已满'
      })
    }
  },

  closeJoinModal: function() {
    this.setData({
      showJoinModal: false
    })
  },

  stopPropagation: function(e) {
    // 阻止事件冒泡
  },

  onShareAppMessage: function() {
    // 使用微信原生分享方式
    return {
      title: this.data.event.title,
      path: '/pages/community/event-detail/event-detail?id=' + this.data.event.id,
      imageUrl: this.data.event.image
    }
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    return {
      title: this.data.event.title,
      query: 'id=' + this.data.event.id,
      imageUrl: this.data.event.image
    }
  }
})
