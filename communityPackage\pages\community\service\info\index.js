// 社区信息展示页面
const app = getApp()
const navigator = require('../../../../utils/navigator')

Page({
  data: {
    activeTab: 'groups', // 'groups' 或 'lostfound'
    lostFoundFilter: 'all', // 'all', 'lost' 或 'found'
    groups: [
      {
        id: 1,
        name: '晨跑健身小组',
        description: '每天早上6:30在小区中央广场集合，一起晨跑健身，享受健康生活。',
        memberCount: 28,
        ownerName: '张先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      },
      {
        id: 2,
        name: '读书分享会',
        description: '每周日下午3点在社区活动室举行读书分享会，交流读书心得，共同成长。',
        memberCount: 15,
        ownerName: '李女士',
        ownerAvatar: 'https://randomuser.me/api/portraits/women/44.jpg'
      },
      {
        id: 3,
        name: '棋牌娱乐群',
        description: '喜欢棋牌游戏的邻居们快来加入，周末一起娱乐放松。',
        memberCount: 32,
        ownerName: '王先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/68.jpg'
      }
    ],
    lostFound: [
      {
        id: 1,
        title: '寻找黑色钱包',
        type: 'lost',
        time: '2023-06-15 18:30',
        location: '小区中央广场',
        description: '昨天傍晚在中央广场散步时不慎遗失黑色钱包一个，内有身份证、银行卡等重要证件，如有拾到请联系物业。',
        images: [
          'https://img.freepik.com/free-photo/black-wallet_74190-4488.jpg'
        ]
      },
      {
        id: 2,
        title: '拾到一串钥匙',
        type: 'found',
        time: '2023-06-16 09:15',
        location: '3号楼电梯口',
        description: '今早在3号楼电梯口拾到一串钥匙，有房门钥匙和车钥匙，失主请联系物业认领。',
        images: [
          'https://img.freepik.com/free-photo/house-keys_144627-12773.jpg'
        ]
      },
      {
        id: 3,
        title: '寻找儿童玩具车',
        type: 'lost',
        time: '2023-06-14 16:45',
        location: '小区儿童游乐场',
        description: '孩子在游乐场玩耍时丢失了一辆红色遥控玩具车，是孩子很喜欢的玩具，如有拾到请联系物业。',
        images: [
          'https://img.freepik.com/free-photo/red-toy-car_144627-15043.jpg'
        ]
      }
    ],
    filteredLostFound: [] // 根据筛选条件过滤后的失物招领列表
  },

  onLoad: function() {
    // 初始化筛选后的失物招领列表
    this.filterLostFound()
  },

  onShow: function() {
    // 页面显示时的逻辑
    // 清除所有导航节流标记
    navigator.clearThrottles();
  },

  // 切换选项卡
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 切换失物招领筛选条件
  switchLostFoundFilter: function(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      lostFoundFilter: filter
    })
    this.filterLostFound()
  },

  // 根据筛选条件过滤失物招领列表
  filterLostFound: function() {
    const { lostFound, lostFoundFilter } = this.data
    let filtered = lostFound

    if (lostFoundFilter === 'lost') {
      filtered = lostFound.filter(item => item.type === 'lost')
    } else if (lostFoundFilter === 'found') {
      filtered = lostFound.filter(item => item.type === 'found')
    }

    this.setData({
      filteredLostFound: filtered
    })
  },

  // 导航到兴趣群详情页
  navigateToGroupDetail: function(e) {
    const id = e.currentTarget.dataset.id
    navigator.navigateTo(`/communityPackage/pages/community/service/info/group-detail?id=${id}`);
  },

  // 导航到失物招领详情页
  navigateToLostFoundDetail: function(e) {
    const id = e.currentTarget.dataset.id
    navigator.navigateTo(`/communityPackage/pages/community/service/info/lostfound-detail?id=${id}`);
  },

  // 预览图片
  previewImage: function(e) {
    const { urls, current } = e.currentTarget.dataset
    wx.previewImage({
      urls,
      current
    })
  }
})
