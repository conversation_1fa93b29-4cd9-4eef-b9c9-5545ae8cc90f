<!-- 添加车辆页面 -->
<view class="container">
  <!-- 内容区域 -->
  <scroll-view scroll-y class="content-area">
    <form>
      <!-- 基本信息 -->
      <view class="form-group-title">基本信息</view>
      <view class="form-group">
        <view class="form-item">
          <view class="form-label">车牌号码<text class="required-mark">*</text></view>
        </view>
        <view class="form-item plate-item">
          <view class="plate-box-container {{isNewEnergyPlate ? 'new-energy' : ''}}">
            <view class="plate-char-box {{plateChars[0] ? '' : 'empty'}} {{currentPlateIndex === 0 ? 'active' : ''}}" data-index="0" bindtap="openPlateSelector">
              {{plateChars[0] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[1] ? '' : 'empty'}} {{currentPlateIndex === 1 ? 'active' : ''}}" data-index="1" bindtap="openPlateSelector">
              {{plateChars[1] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[2] ? '' : 'empty'}} {{currentPlateIndex === 2 ? 'active' : ''}}" data-index="2" bindtap="openPlateSelector">
              {{plateChars[2] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[3] ? '' : 'empty'}} {{currentPlateIndex === 3 ? 'active' : ''}}" data-index="3" bindtap="openPlateSelector">
              {{plateChars[3] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[4] ? '' : 'empty'}} {{currentPlateIndex === 4 ? 'active' : ''}}" data-index="4" bindtap="openPlateSelector">
              {{plateChars[4] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[5] ? '' : 'empty'}} {{currentPlateIndex === 5 ? 'active' : ''}}" data-index="5" bindtap="openPlateSelector">
              {{plateChars[5] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[6] ? '' : 'empty'}} {{currentPlateIndex === 6 ? 'active' : ''}}" data-index="6" bindtap="openPlateSelector">
              {{plateChars[6] || ''}}
            </view>
            <view class="plate-char-box {{plateChars[7] ? '' : 'empty'}} {{currentPlateIndex === 7 ? 'active' : ''}}" data-index="7" bindtap="openPlateSelector">
              {{plateChars[7] || ''}}
            </view>
          </view>
          <view class="plate-hint" wx:if="{{isNewEnergyPlate}}">检测到新能源车牌</view>
        </view>
        <view class="form-item">
          <view class="form-label">车辆颜色</view>
          <view class="color-display {{selectedColor ? '' : 'empty'}}" bindtap="openColorSelector">
            <block wx:if="{{selectedColor}}">
              <view class="color-dot" style="background-color: {{colorValue}};"></view>
              {{selectedColor}}
            </block>
            <block wx:else>请选择车辆颜色</block>
          </view>
          <view class="select-arrow">›</view>
        </view>
      </view>

      <!-- 停车信息 -->
      <view class="form-group-title">停车信息</view>
      <view class="form-group">
        <view class="form-item">
          <view class="form-label">车位类型<text class="required-mark">*</text></view>
          <picker bindchange="onParkingTypeChange" value="{{parkingTypeIndex}}" range="{{parkingTypeOptions}}">
            <view class="picker-display">
              {{parkingType}}
              <view class="select-arrow">›</view>
            </view>
          </picker>
        </view>
        <view class="form-item">
          <view class="form-label">停车位</view>
          <input type="text" class="form-input" placeholder="请输入停车位（选填）" bindinput="onParkingSpaceInput" value="{{parkingNumber}}"/>
        </view>
        <view class="form-item">
          <view class="form-label">主要用车</view>
          <switch checked="{{mainUse}}" bindchange="onPrimaryVehicleChange" color="#007AFF"/>
        </view>
      </view>

      <!-- 车辆证件 -->
      <view class="form-group-title">车辆证件</view>
      <view class="form-group">
        <view class="form-item upload-item">
          <view class="form-label">行驶证照片</view>
          <view class="upload-container">
            <view wx:if="{{!drivingLicenseUrl}}" class="upload-area-empty">
              <view class="upload-text">行驶证照片</view>
              <view class="upload-placeholder">
                <view class="upload-options">
                  <view class="upload-option" bindtap="chooseFromAlbum">
                    <view class="upload-option-icon album-icon"></view>
                    <view class="upload-option-text">从相册选择</view>
                  </view>
                  <view class="upload-option-divider"></view>
                  <view class="upload-option" bindtap="takePhoto">
                    <view class="upload-option-icon camera-icon"></view>
                    <view class="upload-option-text">拍照上传</view>
                  </view>
                </view>
              </view>
              <view class="upload-tip">支持 JPG、PNG 格式，大小不超过 5MB</view>
            </view>
            <view wx:else class="upload-area-with-image">
              <image class="uploaded-image" src="{{drivingLicenseUrl}}" mode="aspectFill"></image>
              <view class="upload-actions">
                <view class="upload-action" bindtap="chooseFromAlbum">
                  <view class="upload-action-icon album-icon"></view>
                  <view class="upload-action-text">重新选择</view>
                </view>
                <view class="upload-action" bindtap="takePhoto">
                  <view class="upload-action-icon camera-icon"></view>
                  <view class="upload-action-text">重新拍照</view>
                </view>
                <view class="upload-action" bindtap="removeDrivingLicense">
                  <view class="upload-action-icon delete-icon"></view>
                  <view class="upload-action-text">删除照片</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </form>
  </scroll-view>



  <!-- 提交按钮容器 -->
  <view class="submit-button-container">
    <button class="submit-button {{canSubmit ? '' : 'disabled'}}" disabled="{{!canSubmit}}" bindtap="submitForm">{{submitButtonText}}</button>
  </view>

  <!-- 车牌选择器弹窗 -->
  <view class="plate-selector-modal {{showPlateSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="plate-selector-content">
      <view class="plate-selector-header">
        <view class="close-button" bindtap="closePlateSelector">×</view>
        <view class="plate-selector-title">{{plateSelectorTitle}}</view>
        <view class="confirm-button" bindtap="confirmPlateNumber">确认</view>
      </view>



      <view class="plate-selector-body">
        <!-- 省份选择 -->
        <view class="plate-selector-section {{currentStep === 1 ? '' : 'hidden'}}">
          <!-- 显示当前车牌号码 -->
          <view class="current-plate-preview {{isNewEnergyPlate ? 'new-energy' : ''}}">
            <view class="plate-preview-box {{plateChars[0] ? '' : 'empty'}} {{currentPlateIndex === 0 ? 'active' : ''}}">
              {{plateChars[0] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[1] ? '' : 'empty'}} {{currentPlateIndex === 1 ? 'active' : ''}}">
              {{plateChars[1] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[2] ? '' : 'empty'}} {{currentPlateIndex === 2 ? 'active' : ''}}">
              {{plateChars[2] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[3] ? '' : 'empty'}} {{currentPlateIndex === 3 ? 'active' : ''}}">
              {{plateChars[3] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[4] ? '' : 'empty'}} {{currentPlateIndex === 4 ? 'active' : ''}}">
              {{plateChars[4] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[5] ? '' : 'empty'}} {{currentPlateIndex === 5 ? 'active' : ''}}">
              {{plateChars[5] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[6] ? '' : 'empty'}} {{currentPlateIndex === 6 ? 'active' : ''}}">
              {{plateChars[6] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[7] ? '' : 'empty'}} {{currentPlateIndex === 7 ? 'active' : ''}}">
              {{plateChars[7] || ''}}
            </view>
          </view>
          <view class="plate-options">
            <view class="plate-option {{plateChars[0] === '粤' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="粤">粤</view>
            <view class="plate-option {{plateChars[0] === '京' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="京">京</view>
            <view class="plate-option {{plateChars[0] === '沪' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="沪">沪</view>
            <view class="plate-option {{plateChars[0] === '津' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="津">津</view>
            <view class="plate-option {{plateChars[0] === '渝' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="渝">渝</view>
            <view class="plate-option {{plateChars[0] === '冀' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="冀">冀</view>
            <view class="plate-option {{plateChars[0] === '豫' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="豫">豫</view>
            <view class="plate-option {{plateChars[0] === '云' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="云">云</view>
            <view class="plate-option {{plateChars[0] === '辽' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="辽">辽</view>
            <view class="plate-option {{plateChars[0] === '黑' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="黑">黑</view>
            <view class="plate-option {{plateChars[0] === '湘' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="湘">湘</view>
            <view class="plate-option {{plateChars[0] === '皖' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="皖">皖</view>
            <view class="plate-option {{plateChars[0] === '鲁' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="鲁">鲁</view>
            <view class="plate-option {{plateChars[0] === '新' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="新">新</view>
            <view class="plate-option {{plateChars[0] === '苏' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="苏">苏</view>
            <view class="plate-option {{plateChars[0] === '浙' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="浙">浙</view>
            <view class="plate-option {{plateChars[0] === '赣' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="赣">赣</view>
            <view class="plate-option {{plateChars[0] === '鄂' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="鄂">鄂</view>
            <view class="plate-option {{plateChars[0] === '桂' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="桂">桂</view>
            <view class="plate-option {{plateChars[0] === '甘' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="甘">甘</view>
            <view class="plate-option {{plateChars[0] === '晋' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="晋">晋</view>
            <view class="plate-option {{plateChars[0] === '蒙' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="蒙">蒙</view>
            <view class="plate-option {{plateChars[0] === '陕' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="陕">陕</view>
            <view class="plate-option {{plateChars[0] === '吉' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="吉">吉</view>
            <view class="plate-option {{plateChars[0] === '闽' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="闽">闽</view>
            <view class="plate-option {{plateChars[0] === '贵' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="贵">贵</view>
            <view class="plate-option {{plateChars[0] === '青' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="青">青</view>
            <view class="plate-option {{plateChars[0] === '藏' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="藏">藏</view>
            <view class="plate-option {{plateChars[0] === '川' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="川">川</view>
            <view class="plate-option {{plateChars[0] === '宁' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="宁">宁</view>
            <view class="plate-option {{plateChars[0] === '琼' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="琼">琼</view>
          </view>
        </view>

        <!-- 城市代码选择 -->
        <view class="plate-selector-section {{currentStep === 2 ? '' : 'hidden'}}">
          <!-- 显示当前车牌号码 -->
          <view class="current-plate-preview {{isNewEnergyPlate ? 'new-energy' : ''}}">
            <view class="plate-preview-box {{plateChars[0] ? '' : 'empty'}} {{currentPlateIndex === 0 ? 'active' : ''}}">
              {{plateChars[0] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[1] ? '' : 'empty'}} {{currentPlateIndex === 1 ? 'active' : ''}}">
              {{plateChars[1] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[2] ? '' : 'empty'}} {{currentPlateIndex === 2 ? 'active' : ''}}">
              {{plateChars[2] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[3] ? '' : 'empty'}} {{currentPlateIndex === 3 ? 'active' : ''}}">
              {{plateChars[3] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[4] ? '' : 'empty'}} {{currentPlateIndex === 4 ? 'active' : ''}}">
              {{plateChars[4] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[5] ? '' : 'empty'}} {{currentPlateIndex === 5 ? 'active' : ''}}">
              {{plateChars[5] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[6] ? '' : 'empty'}} {{currentPlateIndex === 6 ? 'active' : ''}}">
              {{plateChars[6] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[7] ? '' : 'empty'}} {{currentPlateIndex === 7 ? 'active' : ''}}">
              {{plateChars[7] || ''}}
            </view>
          </view>
          <view class="plate-options">
            <view class="plate-option {{plateChars[1] === 'A' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="A">A</view>
            <view class="plate-option {{plateChars[1] === 'B' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="B">B</view>
            <view class="plate-option {{plateChars[1] === 'C' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="C">C</view>
            <view class="plate-option {{plateChars[1] === 'D' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="D">D</view>
            <view class="plate-option {{plateChars[1] === 'E' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="E">E</view>
            <view class="plate-option {{plateChars[1] === 'F' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="F">F</view>
            <view class="plate-option {{plateChars[1] === 'G' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="G">G</view>
            <view class="plate-option {{plateChars[1] === 'H' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="H">H</view>
            <view class="plate-option {{plateChars[1] === 'J' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="J">J</view>
            <view class="plate-option {{plateChars[1] === 'K' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="K">K</view>
            <view class="plate-option {{plateChars[1] === 'L' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="L">L</view>
            <view class="plate-option {{plateChars[1] === 'M' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="M">M</view>
            <view class="plate-option {{plateChars[1] === 'N' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="N">N</view>
            <view class="plate-option {{plateChars[1] === 'P' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="P">P</view>
            <view class="plate-option {{plateChars[1] === 'Q' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="Q">Q</view>
            <view class="plate-option {{plateChars[1] === 'R' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="R">R</view>
            <view class="plate-option {{plateChars[1] === 'S' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="S">S</view>
            <view class="plate-option {{plateChars[1] === 'T' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="T">T</view>
            <view class="plate-option {{plateChars[1] === 'U' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="U">U</view>
            <view class="plate-option {{plateChars[1] === 'V' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="V">V</view>
            <view class="plate-option {{plateChars[1] === 'W' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="W">W</view>
            <view class="plate-option {{plateChars[1] === 'X' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="X">X</view>
            <view class="plate-option {{plateChars[1] === 'Y' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="Y">Y</view>
            <view class="plate-option {{plateChars[1] === 'Z' ? 'selected' : ''}}"
                  bindtap="inputPlateChar" data-char="Z">Z</view>
          </view>
        </view>

        <!-- 车牌号码输入 -->
        <view class="plate-selector-section {{currentStep === 3 ? '' : 'hidden'}}">
          <!-- 显示当前车牌号码 -->
          <view class="current-plate-preview {{isNewEnergyPlate ? 'new-energy' : ''}}">
            <view class="plate-preview-box {{plateChars[0] ? '' : 'empty'}} {{currentPlateIndex === 0 ? 'active' : ''}}">
              {{plateChars[0] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[1] ? '' : 'empty'}} {{currentPlateIndex === 1 ? 'active' : ''}}">
              {{plateChars[1] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[2] ? '' : 'empty'}} {{currentPlateIndex === 2 ? 'active' : ''}}">
              {{plateChars[2] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[3] ? '' : 'empty'}} {{currentPlateIndex === 3 ? 'active' : ''}}">
              {{plateChars[3] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[4] ? '' : 'empty'}} {{currentPlateIndex === 4 ? 'active' : ''}}">
              {{plateChars[4] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[5] ? '' : 'empty'}} {{currentPlateIndex === 5 ? 'active' : ''}}">
              {{plateChars[5] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[6] ? '' : 'empty'}} {{currentPlateIndex === 6 ? 'active' : ''}}">
              {{plateChars[6] || ''}}
            </view>
            <view class="plate-preview-box {{plateChars[7] ? '' : 'empty'}} {{currentPlateIndex === 7 ? 'active' : ''}}">
              {{plateChars[7] || ''}}
            </view>
          </view>
          <view class="plate-options">
            <view class="plate-option" bindtap="inputPlateChar" data-char="1">1</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="2">2</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="3">3</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="4">4</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="5">5</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="6">6</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="7">7</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="8">8</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="9">9</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="0">0</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="A">A</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="B">B</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="C">C</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="D">D</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="E">E</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="F">F</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="G">G</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="H">H</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="J">J</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="K">K</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="L">L</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="M">M</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="N">N</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="P">P</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="Q">Q</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="R">R</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="S">S</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="T">T</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="U">U</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="V">V</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="W">W</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="X">X</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="Y">Y</view>
            <view class="plate-option" bindtap="inputPlateChar" data-char="Z">Z</view>
            <view class="plate-option function" bindtap="backspacePlateChar">删除</view>
            <view class="plate-option function" bindtap="clearPlateChar">清空</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-selector-modal {{showColorSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="color-selector-content">
      <view class="color-selector-header">
        <view class="color-selector-title">选择车辆颜色</view>
        <view class="close-button" bindtap="closeColorSelector">×</view>
      </view>
      <view class="color-options">
        <view class="color-option {{selectedColor === item.name ? 'selected' : ''}}"
              wx:for="{{availableColors}}" wx:key="code"
              bindtap="selectColor" data-color="{{item.name}}" data-value="{{item.value}}">
          <view class="color-dot" style="background-color: {{item.value}};"></view>
          <text>{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>




</view>
