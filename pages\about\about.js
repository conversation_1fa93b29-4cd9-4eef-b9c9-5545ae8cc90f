// about.js
const imagetext = require('../../api/imagetext.js')

Page({
  data: {
    aboutContent: '',
    loading: true,
    contactInfo: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.netrust.cn'
    }
  },

  onLoad: function() {
    this.loadAboutUsContent();
  },

  // 加载关于我们内容
  loadAboutUsContent: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 获取关于我们内容
    imagetext.getImagetextList({
      type: 'about_us',
      pageNum: 1,
      pageSize: 1
    }).then(res => {
      wx.hideLoading();

      if (res && res.list && res.list.length > 0) {
        const aboutUs = res.list[0]; // 取最新的第一个

        this.setData({
          aboutContent: aboutUs.content || '智慧物业小程序是一款为小区业主提供便捷物业服务的应用。通过本应用，您可以在线缴纳物业费、报修、查看社区活动、接收物业通知等，享受全方位的智能物业服务体验。',
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('获取关于我们内容失败:', error);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  callPhone: function() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone.replace(/-/g, ''),
      success: function() {
        console.log('拨打电话成功')
      },
      fail: function() {
        console.log('拨打电话失败')
      }
    })
  }
})
