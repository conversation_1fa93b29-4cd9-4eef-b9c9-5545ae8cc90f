# 数据统计页面API集成说明

## 修改概述

本次修改将statistics页面从模拟数据改为真实API接口数据，并将图表库从wxCharts改为ECharts实现。

## 主要修改内容

### 1. API接口集成

#### 运营概览数据接口
- `propertyApi.getTodayWorkOrderStatistics()` - 今日待办工单统计
- `propertyApi.getTodayVisitorStatistics()` - 今日访客统计  
- `propertyApi.getPersonCount()` - 居民总人数和今日新增

#### 分类快照数据接口
- `propertyApi.getResidentStatistics()` - 居民统计数据
- `propertyApi.getRoomDataStatistics()` - 房屋数据统计
- `propertyApi.getVehicleDataStatistics()` - 车辆数据统计
- `workOrderApi.getPropertyStatusCount()` - 工单概览统计
- `workOrderApi.getPropertyTypeCount()` - 工单类型分布统计
- `propertyApi.getVisitorStatistics()` - 访客统计数据

### 2. 时间级别参数

使用timeLevel参数替代原来的timeRange：
- 0: 今日
- 1: 本周  
- 2: 本月
- 3: 本季度
- 4: 本年度

### 3. 图表库更换

将所有图表从wxCharts改为ECharts实现：
- 饼图：居民类型分布、工单类型分布、房屋类型分布、车位类型分布、车辆颜色分布、访客目的分布
- 柱状图：工单状态分布、楼栋房屋分布、访客时段分布

### 4. 数据字典映射

使用`util.getDictByNameEn()`方法获取字典数据：
- `resident_type` - 居民类型字典
- `work_order_type` - 工单类型字典
- `room_type` - 房屋类型字典

### 5. 界面更新

- 将"认证率"改为"今日新增"
- 更新数据绑定字段名称
- 添加时间筛选器（今日/本周/本月/本季度/本年度）
- 使用ec-canvas组件替代canvas组件

## 文件修改列表

1. `statistics.js` - 主要逻辑文件，集成API接口和ECharts
2. `statistics.wxml` - 模板文件，更新数据绑定和组件
3. `statistics.json` - 配置文件，添加ec-canvas组件引用
4. `statistics.wxss` - 样式文件，更新图表样式

## 注意事项

1. 设施统计暂时不对接接口，保持原有显示
2. 所有API请求都需要传入communityId参数
3. 图表数据需要根据API返回结构进行格式化
4. ECharts组件需要正确的CSS布局才能正常显示
