<!--goods.wxml-->
<view class="container page-bottom-safe-area {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="page-header">
    <view class="page-title">周边好物</view>
    <!-- 切换列表样式按钮 -->
    <view class="layout-toggle-btn" bindtap="toggleListStyle">
      <view class="layout-icon {{listStyle === 'grid' ? 'list-icon' : 'grid-icon'}}"></view>
    </view>
  </view>



  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="14"></icon>
      <input class="search-input" placeholder="搜索好物" bindinput="onSearchInput" value="{{searchKeyword}}" />
      <icon wx:if="{{searchKeyword}}" class="clear-icon" type="clear" size="14" bindtap="clearSearch"></icon>
    </view>
  </view>

  <!-- 分类导航 - 使用字典数据 -->
  <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
    <view class="category-nav">
      <!-- 全部选项 -->
      <view class="category-item {{currentCategory === 'all' ? 'active' : ''}}" bindtap="switchCategory" data-category="all">
        全部
      </view>
      <!-- 字典分类选项 -->
      <view class="category-item {{currentCategory === item.nameEn ? 'active' : ''}}" wx:for="{{categories}}" wx:key="id" bindtap="switchCategory" data-category="{{item.nameEn}}">
        {{item.nameCn}}
      </view>
    </view>
  </scroll-view>

  <!-- 筛选和排序 -->
  <view class="filter-bar">
    <view class="filter-item {{sortType === 'newest' ? 'active' : ''}}" bindtap="switchSort" data-sort="newest">最新</view>
    <view class="filter-item {{sortType === 'amount-asc' ? 'active' : ''}}" bindtap="switchSort" data-sort="amount-asc">价格↑</view>
    <view class="filter-item {{sortType === 'amount-desc' ? 'active' : ''}}" bindtap="switchSort" data-sort="amount-desc">价格↓</view>
    <!-- 类型筛选 -->
    <view class="filter-item {{currentType === 'all' ? 'active' : ''}}" bindtap="switchType" data-type="all">全部类型</view>
    <view class="filter-item {{currentType === item.nameEn ? 'active' : ''}}" wx:for="{{typeOptions}}" wx:key="nameEn" bindtap="switchType" data-type="{{item.nameEn}}">
      {{item.nameCn}}
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="goods-list {{listStyle === 'grid' ? 'grid-style' : ''}}">
    <view class="goods-card {{listStyle === 'grid' ? 'grid-item' : ''}}" wx:for="{{filteredGoods}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="card-container">
        <image class="goods-image" src="{{item.firstImage}}" mode="aspectFill"></image>
        <view class="goods-info">
          <rich-text class="goods-name" nodes="{{item.title || item.stuffDescribe}}"></rich-text>
          <!-- <view >{{}}</view> -->

          <view class="goods-tag-row">
            <view class="goods-condition-tag {{item.isFreeType?'goods-free-tag':''}}">{{item.typeName}}</view>
            <view class="goods-price">¥{{item.isFreeType ? '0' : item.amount}}</view>
        
          </view>
       
          <view class="goods-user-avatar">
            <image wx:if="{{item.sellerAvatarUrl}}" class="user-avatar" src="{{apiUrl}}{{item.sellerAvatarUrl}}" mode="aspectFill">
            </image>
            <view wx:else class="user-avatar-default"></view>
            <view style="margin-left: 16rpx;">{{item.userName}}</view>
          </view>
          <view class="goods-meta">
            <view class="goods-location">{{item.address}}</view>

          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 无结果提示 -->
  <view class="no-result" wx:if="{{filteredGoods.length === 0}}">
    <icon class="no-result-icon" type="info" size="40" color="#ccc"></icon>
    <view class="no-result-text">暂无相关商品</view>
  </view>

  <!-- 发布按钮 -->
  <view class="publish-btn" bindtap="navigateToPublish">
    <text class="plus-icon">+</text>
  </view>
</view>