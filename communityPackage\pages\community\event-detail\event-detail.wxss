/* event-detail.wxss */
/* 暗黑模式样式 */
.darkMode, page[data-darkmode="true"] {
  background-color: #1c1c1e;
  color: #f5f5f7;
}

.darkMode .event-content, page[data-darkmode="true"] .event-content {
  background-color: #1c1c1e;
}

.darkMode .event-title, page[data-darkmode="true"] .event-title {
  color: #f5f5f7;
}

.darkMode .event-info, page[data-darkmode="true"] .event-info {
  background: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .info-icon, page[data-darkmode="true"] .info-icon {
  background: #3a3a3c;
  color: #ff9500;
}

.darkMode .info-label, page[data-darkmode="true"] .info-label {
  color: #8e8e93;
}

.darkMode .info-value, page[data-darkmode="true"] .info-value {
  color: #f5f5f7;
}

.darkMode .description-title, page[data-darkmode="true"] .description-title,
.darkMode .notice-title, page[data-darkmode="true"] .notice-title {
  color: #f5f5f7;
}

.darkMode .description-content, page[data-darkmode="true"] .description-content,
.darkMode .notice-item, page[data-darkmode="true"] .notice-item {
  color: #aeaeb2;
}

.darkMode .event-footer, page[data-darkmode="true"] .event-footer {
  background: #2c2c2e;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .join-modal-content, page[data-darkmode="true"] .join-modal-content {
  background: #2c2c2e;
}

.darkMode .join-modal-title, page[data-darkmode="true"] .join-modal-title {
  color: #f5f5f7;
}

.darkMode .join-modal-desc, page[data-darkmode="true"] .join-modal-desc {
  color: #aeaeb2;
}

.darkMode .join-modal-tips, page[data-darkmode="true"] .join-modal-tips {
  color: #8e8e93;
}

/* SVG图标样式 */
.icon-calendar-detail, .icon-location-detail, .icon-people-detail, .icon-share-detail, .icon-success-detail {
  width: 48rpx;
  height: 48rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.icon-location-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.icon-people-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

.icon-share-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8'%3E%3C/path%3E%3Cpolyline points='16 6 12 2 8 6'%3E%3C/polyline%3E%3Cline x1='12' y1='2' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

.icon-success-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

/* 暗黑模式下SVG图标样式 */
.darkMode .icon-calendar-detail, page[data-darkmode="true"] .icon-calendar-detail,
.darkMode .icon-location-detail, page[data-darkmode="true"] .icon-location-detail,
.darkMode .icon-people-detail, page[data-darkmode="true"] .icon-people-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.darkMode .icon-location-detail, page[data-darkmode="true"] .icon-location-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.darkMode .icon-people-detail, page[data-darkmode="true"] .icon-people-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

.darkMode .icon-share-detail, page[data-darkmode="true"] .icon-share-detail {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238e8e93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8'%3E%3C/path%3E%3Cpolyline points='16 6 12 2 8 6'%3E%3C/polyline%3E%3Cline x1='12' y1='2' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
}


.container {
  padding-bottom: 120rpx;
}

.event-image {
  height: 400rpx;
  background-color: #E5E5EA;
  background-size: cover;
  background-position: center;
}

.event-content {
  padding: 40rpx;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.event-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.event-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #999;
}

.event-status.active {
  background: #e8f5e9;
  color: #4caf50;
}

.event-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.info-item {
  display: flex;
  margin-bottom: 30rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  color: #ff8c00;
}

.info-text {
  flex: 1;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 32rpx;
  color: #333;
}

.event-description, .event-notice {
  margin-bottom: 40rpx;
  width: 100%;
}

.description-title, .notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.description-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: normal;
  word-wrap: break-word;
  text-align: justify;
  display: block;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
  max-height: none;
}

.notice-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.event-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  background: none;
  margin: 0;
  padding: 0;
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

.share-btn::after {
  border: none;
}

.join-btn {
  flex: 1;
  height: 80rpx;
  background: #ff8c00;
  color: white;
  font-size: 32rpx;
  border-radius: 40rpx;
  margin: 0 0 0 20rpx;
}

.join-btn.disabled {
  background: #ccc;
  color: #fff;
}

/* 报名成功弹窗 */
.join-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.join-modal.show {
  opacity: 1;
  visibility: visible;
}

.join-modal-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.join-modal.show .join-modal-content {
  transform: scale(1);
}

.join-modal-icon {
  width: 120rpx;
  height: 120rpx;
  background: #e8f5e9;
  color: #4caf50;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-bottom: 30rpx;
}

.join-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.join-modal-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 10rpx;
}

.join-modal-tips {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.join-modal-btn {
  width: 100%;
  height: 80rpx;
  background: #ff8c00;
  color: white;
  font-size: 32rpx;
  border-radius: 40rpx;
}

