# 访客核销页面实现说明

## 功能概述

实现了物业工作台访客核销页面的搜索核销功能，支持智能搜索、分页加载和状态显示。

## 主要功能

### 1. 智能搜索功能

#### 输入类型自动判断
```javascript
// 智能解析搜索关键词
parseSearchKeyword: function(keyword) {
  // 判断是否为纯数字（手机号）
  const isPhone = /^\d+$/.test(keyword);
  
  if (isPhone) {
    // 纯数字，作为手机号搜索
    return { phone: keyword };
  } else {
    // 包含字符，作为姓名搜索
    return { visitorName: keyword };
  }
}
```

#### 搜索逻辑
- **纯数字输入**: 自动识别为手机号，使用`phone`参数搜索
- **包含字符输入**: 自动识别为姓名，使用`visitorName`参数搜索
- **实时搜索**: 点击搜索按钮触发API请求

### 2. 分页功能

#### 下拉刷新
```javascript
onPullDownRefresh: function() {
  if (this.data.activeTab === 'search') {
    this.loadVisitorList(true);
  } else {
    wx.stopPullDownRefresh();
  }
}
```

#### 上拉加载更多
```javascript
onReachBottom: function() {
  if (this.data.activeTab === 'search' && this.data.hasMore && !this.data.loadingMore) {
    this.loadVisitorList(false);
  }
}
```

#### 分页参数
```javascript
const params = {
  pageNum: refresh ? 1 : this.data.pageNum,
  pageSize: this.data.pageSize,
  communityId: wx.getStorageSync('selectedCommunity').id,
  ...searchParams // visitorName 或 phone
};
```

### 3. API接口调用

#### 接口方法
```javascript
visitorsApi.getPropertyVisitorList(params)
```

#### 请求参数
```javascript
{
  pageNum: 1,           // 页码
  pageSize: 10,         // 每页数量
  communityId: "2",     // 小区ID
  visitorName: "张三",  // 访客姓名（字符输入时）
  phone: "13800138000"  // 手机号（数字输入时）
}
```

#### 响应数据结构
```javascript
{
  "total": 2,
  "list": [
    {
      "id": "20",
      "visitorName": "不妨可",
      "phone": "13685156020",
      "vehicleNumber": null,
      "note": "神大夫",
      "purpose": "维修服务",
      "stayDuration": 2,
      "timeUnit": "hours",
      "visitTime": "2025-07-08 11:38:00",
      "createTime": "2025-07-08 11:08:40",
      "updateTime": null,
      "residentId": "118",
      "communityId": "2",
      "status": "wait_visit",
      "isUsual": false,
      "verifyBy": null
    }
  ],
  "pageNum": 1,
  "pageSize": 500,
  "hasNextPage": false
}
```

### 4. 状态显示

#### 字典数据
使用`visitor_status`字典来显示访客状态的中文名称：

```javascript
// 初始化字典数据
const visitorStatusDict = util.getDictByNameEn('visitor_status')[0].children || [];

// 格式化访客状态
formatVisitorStatus: function(status) {
  const statusDict = this.data.visitorStatusDict;
  const statusItem = statusDict.find(item => item.nameEn === status);
  return statusItem ? statusItem.nameCn : status;
}
```

#### 状态样式
```css
.status-wait_visit {
  background-color: #fff5e6;
  color: #ff8c00;
}

.status-visited {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-expired {
  background-color: #fff2e8;
  color: #fa8c16;
}

.status-canceled {
  background-color: #fff1f0;
  color: #f5222d;
}
```

### 5. 界面显示

#### 访客信息显示
```xml
<view class="visitor-info">
  <view class="visitor-name">{{item.visitorName}}</view>
  <view class="visitor-phone">{{item.phone}}</view>
  <view class="visitor-details">
    <text class="visitor-purpose">{{item.purpose}}</text>
    <text class="visitor-time">来访时间: {{item.visitTime}}</text>
  </view>
  <view class="visitor-note" wx:if="{{item.note}}">
    <text>备注: {{item.note}}</text>
  </view>
</view>
<view class="visitor-status status-{{item.status}}">
  <text>{{item.statusText}}</text>
</view>
```

#### 加载状态显示
- **加载更多**: 显示"加载中..."
- **没有更多**: 显示"没有更多数据了"
- **上拉提示**: 显示"上拉加载更多"

### 6. 数据处理

#### 数据预处理
```javascript
// 为每个访客项添加状态文本
const processedList = res.list.map(item => ({
  ...item,
  statusText: this.formatVisitorStatus(item.status)
}));
```

#### 分页状态管理
```javascript
{
  pageNum: 1,           // 当前页码
  pageSize: 10,         // 每页数量
  total: 0,             // 总数量
  hasMore: true,        // 是否有更多数据
  loadingMore: false,   // 是否正在加载更多
  searchResults: []     // 搜索结果列表
}
```

## 使用流程

1. **输入搜索关键词**: 在搜索框输入访客姓名或手机号
2. **自动类型判断**: 系统自动判断输入类型（姓名/手机号）
3. **点击搜索**: 触发API请求，获取第一页数据
4. **查看结果**: 显示访客列表，包含姓名、手机、目的、时间、状态等信息
5. **下拉刷新**: 重新获取最新数据
6. **上拉加载**: 获取更多数据
7. **点击访客**: 进入访客详情或核销操作

## 错误处理

- **网络异常**: 显示"获取访客列表失败"提示
- **数据格式错误**: 显示相应错误信息
- **字典加载失败**: 显示"数据加载失败"提示
- **无搜索结果**: 显示"未找到相关访客"

## 性能优化

- **防重复加载**: 加载过程中防止重复请求
- **状态管理**: 合理管理加载状态，避免界面卡顿
- **数据缓存**: 分页数据累加显示，提升用户体验
