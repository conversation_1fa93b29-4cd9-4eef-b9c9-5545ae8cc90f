# 完善信息页面用户名字段实现

## 问题描述

在完善信息界面 `profilePackage/pages/profile/complete-info/complete-info` 中，需要正确实现用户名字段的功能：

- 用户名字段应该使用 `userName` 而不是 `realName`
- 需要独立的用户名输入、验证和保存逻辑
- 区分真实姓名（realName）和用户名（userName）两个不同的字段

## 实现内容

### 1. JavaScript数据结构
```javascript
data: {
  // 基本信息
  name: '', // 显示用的姓名（来自realName）
  phone: '',

  // 可编辑信息
  realName: '', // 真实姓名
  userName: '', // 用户名

  // 验证状态
  realNameValid: false,
  realNameError: false,
  userNameValid: false,
  userNameError: false,
}
```

### 2. 用户名相关方法
```javascript
// 用户名输入处理
onUserNameInput: function (e) {
  const userName = e.detail.value
  this.setData({ userName })
  this.validateUserName()
},

// 用户名验证
validateUserName: function () {
  const userName = this.data.userName.trim()
  const isValid = userName.length >= 2 && userName.length <= 20
  this.setData({
    userNameValid: isValid,
    userNameError: userName.length > 0 && !isValid
  })
  return isValid
}
```

### 3. WXML模板
```xml
<!-- 用户名输入框 -->
<view class="form-input {{userNameValid ? 'valid' : userNameError ? 'error' : ''}}">
  <input type="text"
         placeholder="请输入用户名"
         value="{{userName}}"
         bindinput="onUserNameInput"
         bindblur="validateUserName"
         maxlength="20" />
  <view class="check-icon" wx:if="{{userNameValid}}"></view>
</view>
```

## 数据流程

### 1. 数据加载
```javascript
loadUserInfo: function () {
  const userInfo = wx.getStorageSync('userInfo') || {}
  this.setData({
    name: userInfo.realName || '', // 基本信息显示
    realName: userInfo.realName || '', // 真实姓名
    userName: userInfo.userName || '', // 用户名
    // ... 其他字段
  })
}
```

### 2. 数据验证
- **真实姓名验证**：`validaterealName()` - 验证realName字段
- **用户名验证**：`validateUserName()` - 验证userName字段
- **验证规则**：2-20个字符，支持中文、英文、数字

### 3. 数据提交
```javascript
const submitData = {
  realName: this.data.realName, // 真实姓名
  userName: this.data.userName, // 用户名
  avatarUrl: this.data.avatarServerPath,
  gender: this.data.gender,
  birthday: this.data.birthday,
  certificateType: this.data.certificateType,
  idCardNumber: this.data.idCardNumber
}
```

### 4. 本地存储更新
```javascript
const updatedUserInfo = {
  ...userInfo,
  realName: this.data.realName,
  userName: this.data.userName,
  // ... 其他字段
}
wx.setStorageSync('userInfo', updatedUserInfo)
```

## 字段区别

| 字段 | 用途 | 显示位置 | 验证方法 |
|------|------|----------|----------|
| `realName` | 真实姓名 | 基本信息展示区域 | `validaterealName()` |
| `userName` | 用户名 | 用户名输入框 | `validateUserName()` |

## 相关文件

- `profilePackage/pages/profile/complete-info/complete-info.js` - 数据处理逻辑
- `profilePackage/pages/profile/complete-info/complete-info.wxml` - 页面模板
- `api/userApi.js` - 用户信息相关API接口

## 注意事项

1. **字段区分**：realName和userName是两个不同的字段，各有独立的验证逻辑
2. **数据一致性**：确保WXML中的数据绑定与JavaScript中的字段名称一致
3. **验证规则**：两个字段都要求2-20个字符
4. **存储格式**：两个字段都存储在userInfo对象中
5. **显示逻辑**：基本信息区域显示realName，用户名输入框显示userName
