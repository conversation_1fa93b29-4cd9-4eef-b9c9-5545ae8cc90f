# 完善信息页面用户名字段修复

## 问题描述

在完善信息界面 `profilePackage/pages/profile/complete-info/complete-info` 中，发现用户名字段的数据绑定不一致：

- **JavaScript文件**：使用 `realName` 字段存储和处理用户名数据
- **WXML文件**：使用 `{{userName}}` 进行数据绑定

这导致用户名输入框无法正确显示和更新数据。

## 修复内容

### 修复前
```xml
<!-- WXML中使用了错误的字段名 -->
<input type="text" placeholder="请输入用户名" value="{{userName}}" bindinput="onrealNameInput" bindblur="validaterealName" maxlength="20" />
```

### 修复后
```xml
<!-- 修正为正确的字段名 -->
<input type="text" placeholder="请输入用户名" value="{{realName}}" bindinput="onrealNameInput" bindblur="validaterealName" maxlength="20" />
```

## 数据流程确认

### JavaScript数据结构
```javascript
data: {
  realName: '', // 用户名字段
  // ... 其他字段
}
```

### 相关方法
- `onrealNameInput()` - 处理用户名输入
- `validaterealName()` - 验证用户名格式
- `loadUserInfo()` - 从userInfo加载realName数据
- `saveInfo()` - 提交时使用realName字段

### 数据来源和存储
- **加载时**：从 `wx.getStorageSync('userInfo').realName` 获取
- **保存时**：提交 `realName` 字段到 `userApi.supplementUserInfo()`
- **本地存储**：更新 `userInfo.realName` 字段

## 验证结果

修复后的数据绑定流程：
1. 页面加载时，从userInfo中读取realName并显示在输入框
2. 用户输入时，通过onrealNameInput更新realName字段
3. 实时验证用户名格式并显示验证状态
4. 保存时，将realName字段提交到服务器

## 相关文件

- `profilePackage/pages/profile/complete-info/complete-info.js` - 数据处理逻辑
- `profilePackage/pages/profile/complete-info/complete-info.wxml` - 页面模板（已修复）
- `api/userApi.js` - 用户信息相关API接口

## 注意事项

1. **字段一致性**：确保WXML中的数据绑定字段与JavaScript中的data字段名称一致
2. **数据验证**：用户名验证逻辑要求2-20个字符
3. **存储格式**：用户名存储在userInfo.realName字段中
4. **显示逻辑**：基本信息展示区域使用name字段（从userInfo.realName获取）
