// pages/property/facility/tasks/execute/index.js
const dateUtil = require('../../../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,
    
    // 任务ID
    taskId: '',
    
    // 任务信息
    task: null,
    
    // 当前巡检点
    currentPointIndex: 0,
    
    // 巡检点列表
    inspectionPoints: [],
    
    // 当前巡检点信息
    currentPoint: null,
    
    // 检查项结果
    checkResults: {},
    
    // 异常描述
    abnormalDescription: '',
    
    // 图片上传
    uploadedImages: [],
    
    // 是否显示异常报告表单
    showAbnormalForm: false,
    
    // 是否显示确认对话框
    showConfirmDialog: false,
    confirmDialogType: '', // 'next', 'complete'
    
    // 提交状态
    isSubmitting: false,
    
    // 扫码状态
    scanSuccess: false
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 获取任务ID
    if (options.id) {
      this.setData({
        taskId: options.id
      });
      this.loadTaskDetail(options.id);
    } else {
      wx.showToast({
        title: '任务ID无效',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载任务详情
  loadTaskDetail: function(taskId) {
    this.setData({ isLoading: true });
    
    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const task = this.getMockTaskDetail(taskId);
      const inspectionPoints = this.getMockInspectionPoints(taskId);
      
      // 初始化检查项结果
      const checkResults = {};
      inspectionPoints.forEach((point, pointIndex) => {
        point.checkItems.forEach((item, itemIndex) => {
          const key = `${pointIndex}_${itemIndex}`;
          checkResults[key] = {
            result: '',
            images: []
          };
        });
      });
      
      // 设置当前巡检点
      const currentPointIndex = task.progress ? Math.floor(task.progress / (100 / inspectionPoints.length)) : 0;
      const currentPoint = inspectionPoints[currentPointIndex];
      
      this.setData({
        task: task,
        inspectionPoints: inspectionPoints,
        currentPointIndex: currentPointIndex,
        currentPoint: currentPoint,
        checkResults: checkResults,
        isLoading: false
      });
    }, 500);
  },
  
  // 获取模拟任务详情
  getMockTaskDetail: function(taskId) {
    const tasks = {
      '1': {
        id: '1',
        title: '小区监控设备巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'pending',
        statusText: '待执行',
        date: '2023-11-15',
        time: '09:00-10:00',
        location: '小区各监控点位',
        facilityCount: 12,
        description: '检查小区各监控设备运行状态，确保画面清晰，存储正常',
        assignee: '张工'
      },
      '2': {
        id: '2',
        title: '消防设备月度巡检',
        type: 'inspection',
        typeText: '巡检',
        status: 'processing',
        statusText: '进行中',
        date: '2023-11-15',
        time: '14:00-16:00',
        location: '小区公共区域',
        facilityCount: 25,
        description: '检查小区消防栓、灭火器、烟感等设备状态',
        assignee: '张工',
        progress: 60
      }
    };
    
    return tasks[taskId] || tasks['1'];
  },
  
  // 获取模拟巡检点列表
  getMockInspectionPoints: function(taskId) {
    const points = {
      '1': [
        {
          id: '1_1',
          name: '小区正门监控',
          code: 'CAM-001',
          location: '小区正门',
          qrCode: 'CAM-001',
          checkItems: [
            { id: '1_1_1', name: '摄像头外观', description: '检查摄像头外观是否完好，无破损' },
            { id: '1_1_2', name: '画面质量', description: '检查监控画面是否清晰，无卡顿' },
            { id: '1_1_3', name: '存储状态', description: '检查存储设备是否正常工作，存储空间是否充足' }
          ]
        },
        {
          id: '1_2',
          name: '小区后门监控',
          code: 'CAM-002',
          location: '小区后门',
          qrCode: 'CAM-002',
          checkItems: [
            { id: '1_2_1', name: '摄像头外观', description: '检查摄像头外观是否完好，无破损' },
            { id: '1_2_2', name: '画面质量', description: '检查监控画面是否清晰，无卡顿' },
            { id: '1_2_3', name: '存储状态', description: '检查存储设备是否正常工作，存储空间是否充足' }
          ]
        },
        {
          id: '1_3',
          name: '地下车库入口监控',
          code: 'CAM-003',
          location: '地下车库入口',
          qrCode: 'CAM-003',
          checkItems: [
            { id: '1_3_1', name: '摄像头外观', description: '检查摄像头外观是否完好，无破损' },
            { id: '1_3_2', name: '画面质量', description: '检查监控画面是否清晰，无卡顿' },
            { id: '1_3_3', name: '存储状态', description: '检查存储设备是否正常工作，存储空间是否充足' }
          ]
        }
      ],
      '2': [
        {
          id: '2_1',
          name: '1号楼消防栓',
          code: 'FIRE-001',
          location: '1号楼一层',
          qrCode: 'FIRE-001',
          checkItems: [
            { id: '2_1_1', name: '外观检查', description: '检查消防栓外观是否完好，无破损' },
            { id: '2_1_2', name: '水压检查', description: '检查水压是否正常' },
            { id: '2_1_3', name: '阀门检查', description: '检查阀门是否灵活，无锈蚀' }
          ]
        },
        {
          id: '2_2',
          name: '2号楼消防栓',
          code: 'FIRE-002',
          location: '2号楼一层',
          qrCode: 'FIRE-002',
          checkItems: [
            { id: '2_2_1', name: '外观检查', description: '检查消防栓外观是否完好，无破损' },
            { id: '2_2_2', name: '水压检查', description: '检查水压是否正常' },
            { id: '2_2_3', name: '阀门检查', description: '检查阀门是否灵活，无锈蚀' }
          ]
        },
        {
          id: '2_3',
          name: '小区中央广场灭火器',
          code: 'FIRE-003',
          location: '小区中央广场',
          qrCode: 'FIRE-003',
          checkItems: [
            { id: '2_3_1', name: '外观检查', description: '检查灭火器外观是否完好，无破损' },
            { id: '2_3_2', name: '压力检查', description: '检查压力表指针是否在绿色区域' },
            { id: '2_3_3', name: '有效期检查', description: '检查灭火器是否在有效期内' }
          ]
        }
      ]
    };
    
    return points[taskId] || points['1'];
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 选择检查结果
  selectCheckResult: function(e) {
    const { pointIndex, itemIndex, result } = e.currentTarget.dataset;
    const key = `${pointIndex}_${itemIndex}`;
    
    const checkResults = this.data.checkResults;
    checkResults[key].result = result;
    
    this.setData({
      checkResults: checkResults
    });
    
    // 如果选择异常，显示异常表单
    if (result === 'abnormal') {
      this.setData({
        showAbnormalForm: true,
        currentAbnormalKey: key
      });
    }
  },
  
  // 关闭异常表单
  closeAbnormalForm: function() {
    this.setData({
      showAbnormalForm: false,
      abnormalDescription: '',
      uploadedImages: []
    });
  },
  
  // 异常描述输入
  onAbnormalInput: function(e) {
    this.setData({
      abnormalDescription: e.detail.value
    });
  },
  
  // 上传图片
  uploadImage: function() {
    const { uploadedImages } = this.data;
    
    // 最多上传9张图片
    const remainCount = 9 - uploadedImages.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 这里应该是上传图片到服务器
        // 目前只是添加到本地数据
        const tempFilePaths = res.tempFilePaths;
        
        this.setData({
          uploadedImages: [...uploadedImages, ...tempFilePaths]
        });
      }
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    wx.previewImage({
      current: uploadedImages[index],
      urls: uploadedImages
    });
  },
  
  // 删除图片
  deleteImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    uploadedImages.splice(index, 1);
    
    this.setData({
      uploadedImages: uploadedImages
    });
  },
  
  // 提交异常表单
  submitAbnormalForm: function() {
    const { abnormalDescription, uploadedImages, currentAbnormalKey, checkResults } = this.data;
    
    // 验证异常描述
    if (!abnormalDescription.trim()) {
      wx.showToast({
        title: '请填写异常描述',
        icon: 'none'
      });
      return;
    }
    
    // 更新检查结果
    checkResults[currentAbnormalKey].description = abnormalDescription;
    checkResults[currentAbnormalKey].images = uploadedImages;
    
    this.setData({
      checkResults: checkResults,
      showAbnormalForm: false,
      abnormalDescription: '',
      uploadedImages: []
    });
    
    wx.showToast({
      title: '异常信息已记录',
      icon: 'success'
    });
  },
  
  // 扫码确认
  scanQRCode: function() {
    wx.scanCode({
      success: (res) => {
        const { currentPoint } = this.data;
        
        // 验证扫码结果是否匹配当前巡检点
        if (res.result === currentPoint.qrCode) {
          this.setData({
            scanSuccess: true
          });
          
          wx.showToast({
            title: '扫码成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '二维码不匹配',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 验证当前巡检点是否完成
  validateCurrentPoint: function() {
    const { currentPointIndex, currentPoint, checkResults } = this.data;
    
    // 检查是否所有检查项都有结果
    let isValid = true;
    currentPoint.checkItems.forEach((item, itemIndex) => {
      const key = `${currentPointIndex}_${itemIndex}`;
      if (!checkResults[key].result) {
        isValid = false;
      }
    });
    
    // 检查是否已扫码确认
    if (!this.data.scanSuccess) {
      isValid = false;
      wx.showToast({
        title: '请扫码确认',
        icon: 'none'
      });
    }
    
    return isValid;
  },
  
  // 显示确认对话框
  showConfirm: function(e) {
    const { type } = e.currentTarget.dataset;
    
    // 验证当前巡检点是否完成
    if (!this.validateCurrentPoint()) {
      wx.showToast({
        title: '请完成所有检查项',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      showConfirmDialog: true,
      confirmDialogType: type
    });
  },
  
  // 关闭确认对话框
  closeConfirmDialog: function() {
    this.setData({
      showConfirmDialog: false
    });
  },
  
  // 确认操作
  confirmAction: function() {
    const { confirmDialogType } = this.data;
    
    if (confirmDialogType === 'next') {
      this.nextPoint();
    } else if (confirmDialogType === 'complete') {
      this.completeTask();
    }
    
    this.setData({
      showConfirmDialog: false
    });
  },
  
  // 下一个巡检点
  nextPoint: function() {
    const { currentPointIndex, inspectionPoints } = this.data;
    
    // 检查是否是最后一个巡检点
    if (currentPointIndex >= inspectionPoints.length - 1) {
      wx.showToast({
        title: '已是最后一个巡检点',
        icon: 'none'
      });
      return;
    }
    
    // 更新当前巡检点
    const nextIndex = currentPointIndex + 1;
    const nextPoint = inspectionPoints[nextIndex];
    
    this.setData({
      currentPointIndex: nextIndex,
      currentPoint: nextPoint,
      scanSuccess: false
    });
    
    // 更新任务进度
    const progress = Math.floor((nextIndex + 1) / inspectionPoints.length * 100);
    const task = this.data.task;
    task.progress = progress;
    task.status = 'processing';
    task.statusText = '进行中';
    
    this.setData({
      task: task
    });
  },
  
  // 完成任务
  completeTask: function() {
    // 设置提交状态
    this.setData({
      isSubmitting: true
    });
    
    // 这里应该是提交任务结果到服务器
    // 目前只是模拟提交
    setTimeout(() => {
      // 更新任务状态
      const task = this.data.task;
      task.status = 'completed';
      task.statusText = '已完成';
      task.progress = 100;
      task.completedTime = dateUtil.formatTime(new Date()).substring(0, 5); // 只取小时和分钟
      task.result = '正常';
      
      this.setData({
        task: task,
        isSubmitting: false
      });
      
      wx.showToast({
        title: '任务已完成',
        icon: 'success'
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
})
