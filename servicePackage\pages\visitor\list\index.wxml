<view class="container">
  <!-- 筛选区域 -->
  <view class="filter-section">
    <!-- 状态筛选标签栏 -->
    <scroll-view scroll-x class="status-tabs">
      <view class="status-tab {{currentFilter === item.nameEn ? 'active' : ''}}"
            wx:for="{{filterTabs}}" wx:key="nameEn"
            bindtap="setFilter" data-filter="{{item.nameEn}}">{{item.nameCn}}</view>
    </scroll-view>

    <!-- 搜索框 -->
    <view class="search-box">
      <image src="/images/icons/search.svg" class="search-icon" />
      <input type="text" class="search-input" placeholder="搜索访客姓名/手机号"
             value="{{searchKeyword}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-btn" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="/images/icons/clear.svg" class="clear-icon" />
      </view>
    </view>
  </view>

  <!-- 访客列表 -->
  <scroll-view scroll-y class="visitor-list" bindscrolltolower="loadMoreVisitors"
               scroll-into-view="{{scrollIntoView}}" scroll-with-animation
               refresher-enabled refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onPullDownRefresh">

    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-container" wx:elif="{{visitors.length === 0}}">
      <image src="/images/icons/empty-visitor.svg" class="empty-icon" />
      <text class="empty-text">{{searchKeyword ? '未找到相关访客' : '暂无访客记录'}}</text>
      <button class="create-btn" bindtap="navigateToRegistration" wx:if="{{!searchKeyword}}">添加访客</button>
    </view>

    <!-- 访客列表内容 -->
    <block wx:else>
      <!-- 今天 -->
      <view id="group-today" class="visitor-group" wx:if="{{visitorGroups.today.length > 0}}">
        <view class="group-header">
          <text class="group-title">今天</text>
          <text class="group-count">{{visitorGroups.today.length}}位访客</text>
        </view>
        <view class="visitor-cards">
          <view class="visitor-card" wx:for="{{visitorGroups.today}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <view class="visitor-card-header">
              <view class="visitor-info-left">
                <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
                <view class="visitor-basic-info">
                  <view class="visitor-name">{{item.visitorName}}</view>
                  <view class="visitor-time">{{item.visitTime}}</view>
                </view>
              </view>
              <view class="visitor-status">
                <view class="status-tag status-{{item.status}}">{{statusTextMap[item.status]}}</view>
                <view class="favorite-icon {{item.isUsual ? 'active' : ''}}" catchtap="toggleFavorite" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="star-icon"></image>
                </view>
              </view>
            </view>

            <view class="visitor-card-body">
              <view class="visitor-details">
                <view class="visitor-end-time" wx:if="{{item.endTime}}">结束时间：{{item.endTime}}</view>
                <view class="visitor-note" wx:if="{{item.note}}">备注：{{item.note}}</view>
                <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              </view>
            </view>

            <view class="visitor-card-actions">
              <button class="action-btn view-btn" catchtap="viewCredential" data-id="{{item.id}}">
                查看凭证
              </button>
              <button class="action-btn share-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                分享
              </button>
              <button class="action-btn extend-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                延期
              </button>
              <button class="action-btn delete-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 明天 -->
      <view id="group-tomorrow" class="visitor-group" wx:if="{{visitorGroups.tomorrow.length > 0}}">
        <view class="group-header">
          <text class="group-title">明天</text>
          <text class="group-count">{{visitorGroups.tomorrow.length}}位访客</text>
        </view>
        <view class="visitor-cards">
          <view class="visitor-card" wx:for="{{visitorGroups.tomorrow}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <view class="visitor-card-header">
              <view class="visitor-info-left">
                <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
                <view class="visitor-basic-info">
                  <view class="visitor-name">{{item.visitorName}}</view>
                  <view class="visitor-time">{{item.visitTime}}</view>
                </view>
              </view>
              <view class="visitor-status">
                <view class="status-tag status-{{item.status}}">{{statusTextMap[item.status]}}</view>
                <view class="favorite-icon {{item.isUsual ? 'active' : ''}}" catchtap="toggleFavorite" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="star-icon"></image>
                </view>
              </view>
            </view>

            <view class="visitor-card-body">
              <view class="visitor-details">
                <view class="visitor-end-time" wx:if="{{item.endTime}}">结束时间：{{item.endTime}}</view>
                <view class="visitor-note" wx:if="{{item.note}}">备注：{{item.note}}</view>
                <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              </view>
            </view>

            <view class="visitor-card-actions">
              <button class="action-btn view-btn" catchtap="viewCredential" data-id="{{item.id}}">
                查看凭证
              </button>
              <button class="action-btn share-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                分享
              </button>
              <button class="action-btn extend-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                延期
              </button>
              <button class="action-btn delete-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 最近7天 -->
      <view id="group-week" class="visitor-group" wx:if="{{visitorGroups.week.length > 0}}">
        <view class="group-header">
          <text class="group-title">最近7天</text>
          <text class="group-count">{{visitorGroups.week.length}}位访客</text>
        </view>
        <view class="visitor-cards">
          <view class="visitor-card" wx:for="{{visitorGroups.week}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <view class="visitor-card-header">
              <view class="visitor-info-left">
                <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
                <view class="visitor-basic-info">
                  <view class="visitor-name">{{item.visitorName}}</view>
                  <view class="visitor-time">{{item.visitTime}}</view>
                </view>
              </view>
              <view class="visitor-status">
                <view class="status-tag status-{{item.status}}">{{statusTextMap[item.status]}}</view>
                <view class="favorite-icon {{item.isUsual ? 'active' : ''}}" catchtap="toggleFavorite" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="star-icon"></image>
                </view>
              </view>
            </view>

            <view class="visitor-card-body">
              <view class="visitor-details">
                <view class="visitor-end-time" wx:if="{{item.endTime}}">结束时间：{{item.endTime}}</view>
                <view class="visitor-note" wx:if="{{item.note}}">备注：{{item.note}}</view>
                <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              </view>
            </view>

            <view class="visitor-card-actions">
              <button class="action-btn view-btn" catchtap="viewCredential" data-id="{{item.id}}">
                查看凭证
              </button>
              <button class="action-btn share-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                分享
              </button>
              <button class="action-btn extend-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                延期
              </button>
              <button class="action-btn delete-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 更早 -->
      <view id="group-earlier" class="visitor-group" wx:if="{{visitorGroups.earlier.length > 0}}">
        <view class="group-header">
          <text class="group-title">更早</text>
          <text class="group-count">{{visitorGroups.earlier.length}}位访客</text>
        </view>
        <view class="visitor-cards">
          <view class="visitor-card" wx:for="{{visitorGroups.earlier}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
            <view class="visitor-card-header">
              <view class="visitor-info-left">
                <view class="visitor-avatar {{item.status}}">{{item.visitorName[0]}}</view>
                <view class="visitor-basic-info">
                  <view class="visitor-name">{{item.visitorName}}</view>
                  <view class="visitor-time">{{item.visitTime}}</view>
                </view>
              </view>
              <view class="visitor-status">
                <view class="status-tag status-{{item.status}}">{{statusTextMap[item.status]}}</view>
                <view class="favorite-icon {{item.isUsual ? 'active' : ''}}" catchtap="toggleFavorite" data-id="{{item.id}}">
                  <image src="/images/icons/star.svg" class="star-icon"></image>
                </view>
              </view>
            </view>

            <view class="visitor-card-body">
              <view class="visitor-details">
                <view class="visitor-end-time" wx:if="{{item.endTime}}">结束时间：{{item.endTime}}</view>
                <view class="visitor-note" wx:if="{{item.note}}">备注：{{item.note}}</view>
                <view class="visitor-vehicle" wx:if="{{item.vehicleNumber}}">车牌：{{item.vehicleNumber}}</view>
              </view>
            </view>

            <view class="visitor-card-actions">
              <button class="action-btn view-btn" catchtap="viewCredential" data-id="{{item.id}}">
                查看凭证
              </button>
              <button class="action-btn share-btn" catchtap="shareVisitor" data-id="{{item.id}}">
                分享
              </button>
              <button class="action-btn extend-btn" catchtap="showExtendOptions" data-id="{{item.id}}" wx:if="{{item.status === 'wait_visit'}}">
                延期
              </button>
              <button class="action-btn delete-btn" catchtap="deleteVisitor" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 分页指示器 -->
      <view class="pagination" wx:if="{{visitors.length > 0}}">
        <view class="load-more" wx:if="{{hasMoreVisitors}}">
          <view class="loading-spinner" wx:if="{{loadingMore}}"></view>
          <text wx:if="{{loadingMore}}">加载更多...</text>
          <text wx:else>上拉加载更多</text>
        </view>
        <view class="load-more" wx:else>
          <text>已加载全部访客</text>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 悬浮添加按钮 -->
  <view class="add-btn" bindtap="navigateToRegistration">
    <image src="/images/icons/add.svg" class="add-icon" />
  </view>
</view>

<!-- 延期选项弹窗 -->
<view class="extend-modal {{showExtendModal ? 'show' : ''}}">
  <view class="modal-mask" bindtap="hideExtendModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">调整访客时间</text>
      <view class="modal-close" bindtap="hideExtendModal">
        <image src="/images/icons/close.svg" class="close-icon" />
      </view>
    </view>
    <view class="modal-body">
      <view class="extend-option" bindtap="extendVisitor" data-hours="1">
        <text>延长1小时</text>
      </view>
      <view class="extend-option" bindtap="extendVisitor" data-hours="2">
        <text>延长2小时</text>
      </view>
      <view class="extend-option" bindtap="extendVisitor" data-hours="4">
        <text>延长4小时</text>
      </view>
    </view>
  </view>
</view>
