/* repair.wxss */
.container {
  padding: 32rpx;
  padding-bottom: 120rpx;
  background-color: #F2F2F7; /* iOS背景色 */
}

.form-card {
  background: white;
  border-radius: 28rpx; /* iOS卡牌圆角14px */
  padding: 32rpx; /* iOS卡牌内边16px */
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.form-card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  border-radius: 10rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  font-size: 34rpx; /* iOS标题字号17px */
  font-weight: 600;
  color: #000000; /* iOS主要文本颜色 */
}

.required {
  color: #FF3B30;
  margin-left: 8rpx;
}

/* 工单类型选择器 */
.type-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx; /* iOS按钮圆角12px */
  border: 2rpx solid #E5E5EA;
  background-color: #FFFFFF;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* iOS过渡动画 */
}

.type-option.active {
  background-color: rgba(255, 140, 0, 0.08);
  border-color: #FF8C00;
  transform: translateY(-2rpx); /* iOS选中效果微升 */
}

.type-option:active {
  transform: scale(0.96); /* iOS点击反馈 */
}

.type-icon {
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标样式已移至全局样式表 */

.icon-arrow-down.expanded {
  transform: rotate(180deg);
}

.type-label {
  font-size: 26rpx;
  color: #000000;
  font-weight: 500;
}

/* 问题描述 */
.textarea-wrapper {
  background-color: #F5F5F7;
  border-radius: 20rpx; /* iOS文本域圆角10px */
  padding: 24rpx 30rpx;
  border: 2rpx solid #E5E5EA; /* iOS文本域边框 */
  margin-bottom: 32rpx;
}

.form-textarea {
  width: 100%;
  height: 240rpx;
  font-size: 30rpx; /* iOS正文字号15px */
  color: #000000; /* iOS主要文本颜色 */
  line-height: 1.5;
}

/* 图片上传 */
.image-upload-section {
  margin-top: 16rpx;
}

.section-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}

.label-tip {
  font-size: 26rpx; /* iOS辅助文本13px */
  color: #8E8E93; /* iOS次要文本颜色 */
  margin-left: 16rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.upload-button {
  width: 200rpx;
  height: 200rpx;
  background-color: #F5F5F7;
  border-radius: 20rpx; /* iOS图片上传按钮圆角10px */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #C7C7CC; /* iOS虚线边框 */
}

.upload-icon {
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #8E8E93;
}

.image-item {
  width: 190rpx;
  height: 190rpx;
  border-radius: 20rpx; /* iOS图片项目圆角10px */
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); /* iOS阴影效果 */
}

.image-item image {
  width: 100%;
  height: 100%;
}

.delete-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx; /* iOS圆形图标 */
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.delete-icon-inner {
  font-size: 28rpx;
  font-weight: bold;
}

.upload-tip {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #8E8E93;
}

.tip-icon {
  margin-right: 8rpx;
  display: flex;
  align-items: center;
}

.tip-text {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 地址类型选择器 */
.address-type-selector {
  margin-bottom: 20rpx;
}

.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-option {
  display: flex;
  align-items: center;
}

.radio-button {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.radio-button.selected {
  border-color: #FF8C00;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #FF8C00;
}

.radio-label {
  font-size: 28rpx;
  color: #333;
}

/* 我的房屋地址 */
.my-address-container {
  background-color: #F5F5F7;
  border-radius: 20rpx;
  padding: 24rpx 30rpx;
}

.my-address-display {
  font-size: 28rpx;
  color: #333;
}

/* 公共区域地址 */
.public-area-container {
  margin-bottom: 16rpx;
}

.address-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #F5F5F7;
  border-radius: 20rpx; /* iOS输入框圆角10px */
  padding: 0 30rpx;
  position: relative;
  border: 2rpx solid #E5E5EA; /* iOS输入框边框 */
}

.address-input {
  flex: 1;
  height: 88rpx; /* iOS输入框高44px */
  font-size: 30rpx; /* iOS正文字号15px */
  color: #000000; /* iOS主要文本颜色 */
}

.location-btn {
  width: 80rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-tip {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

/* 报修人信息 */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-arrow {
  transition: transform 0.3s;
}

.header-arrow.expanded {
  transform: rotate(180deg);
}

/* 物业人员专用输入区域 */
.reporter-input-section {
  margin-top: 16rpx;
}

.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.input-field {
  background-color: #F5F5F7;
  border-radius: 20rpx;
  padding: 0 30rpx;
  height: 88rpx;
  font-size: 30rpx;
  color: #000000;
  border: 2rpx solid #E5E5EA;
  width: 100%;
  box-sizing: border-box;
}

.picker-field {
  background-color: #F5F5F7;
  border-radius: 20rpx;
  padding: 0 30rpx;
  height: 88rpx;
  font-size: 30rpx;
  color: #000000;
  border: 2rpx solid #E5E5EA;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s;
}

.collapsible-content.expanded {
  max-height: 500rpx;
}

.reporter-basic-info {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-top: 1rpx solid #E5E5EA;
  margin-top: 16rpx;
}

.reporter-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: rgba(255, 140, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.reporter-details {
  flex: 1;
}

.reporter-name {
  font-size: 34rpx; /* iOS标题字号17px */
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.reporter-type {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 用户详细信息 */
.reporter-detail-info {
  margin-top: 24rpx;
}

.info-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.info-item {
  flex: 1;
  background-color: #F5F5F7;
  border-radius: 20rpx;
  padding: 20rpx;
}

.info-block {
  background-color: #F5F5F7;
  border-radius: 20rpx;
  padding: 20rpx;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 30rpx;
  color: #000000;
  font-weight: 500;
}

/* 提交按钮 */
.submit-btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 100rpx; /* iOS按钮高50px */
  background: #FF8C00; /* 主品牌色，橙色 */
  color: white;
  font-size: 34rpx; /* iOS标题字号17px */
  font-weight: 600;
  border-radius: 24rpx; /* iOS按钮圆角12px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s; /* iOS过渡效果 */
}

.submit-btn text {
  margin-left: 8rpx;
}

.submit-btn:active {
  transform: scale(0.98); /* iOS点击反馈 */
  background: #E67E00; /* 橙色按钮点击颜色变深 */
}

.submit-btn.disabled {
  background: #ccc;
  color: #fff;
}

/* 加载中弹窗 */
.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  width: 240rpx;
  height: 240rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-top: 4rpx solid white;
  border-radius: 50%;
  margin-bottom: 30rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: white;
}

/* 提示弹窗 */
.alert-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.alert-modal.show {
  opacity: 1;
  visibility: visible;
}

.alert-content {
  width: 560rpx;
  background: white;
  border-radius: 28rpx; /* iOS弹窗圆角14px */
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15); /* iOS弹窗阴影 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0 0;
}

.alert-modal.show .alert-content {
  transform: scale(1);
}

.alert-icon {
  margin-bottom: 16rpx;
}

.alert-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 16rpx;
}

.alert-message {
  font-size: 30rpx;
  color: #666;
  text-align: center;
  padding: 0 40rpx;
  margin-bottom: 32rpx;
}

.alert-footer {
  width: 100%;
  border-top: 1rpx solid #E5E5EA;
}

.alert-btn {
  height: 100rpx;
  font-size: 34rpx; /* iOS按钮文本17px */
  color: #FF8C00; /* 主品牌色，橙色 */
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: transparent;
  transition: all 0.2s; /* iOS过渡效果 */
}

.alert-btn:active {
  background-color: rgba(255, 140, 0, 0.1); /* 橙色按钮点击背景 */
}
