// rules.js
const app = getApp();

Page({
  data: {
    darkMode: false,
    ruleCategories: [
      { id: 'earn', name: '获取规则' },
      { id: 'use', name: '使用规则' },
      { id: 'validity', name: '有效期规则' },
      { id: 'faq', name: '常见问题' }
    ],
    currentCategory: 'earn',
    expandedRules: {}, // 记录展开状态的对象
    tooltipVisible: {}, // 记录提示可见状态的对象
    rules: {
      earn: [
        {
          title: '日常任务',
          content: '1. 每日签到：5积分/次\n2. 浏览社区公告：1积分/次（每日上限10积分）\n3. 浏览社区活动：1积分/次（每日上限5积分）\n4. 发布社区帖子：5积分/次（每日上限15积分）\n5. 回复/评论社区帖子：2积分/次（每日上限10积分）',
          tooltip: '连续签到7天额外奖励30积分，连续签到30天额外奖励150积分',
          important: true
        },
        {
          title: '物业服务任务',
          content: '1. 提交报事报修申请：15积分/次（每日上限15积分）\n2. 报事报修评价：5积分/次（每日上限5积分）\n3. 按时缴纳物业费：30积分/次\n4. 提前10天以上缴费：额外10积分/次',
          tooltip: '按时缴费是指在缴费截止日期前完成缴费',
          important: true
        },
        {
          title: '一次性任务',
          content: '1. 完善个人资料：20积分\n2. 绑定手机号：10积分\n3. 绑定房屋信息：50积分',
          tooltip: '一次性任务只能获取一次积分奖励'
        },
        {
          title: '消费返积分',
          content: '消费1元返1积分，积分四舍五入到整数',
          tooltip: '不同会员等级可能有不同的返积分比例'
        }
      ],
      use: [
        {
          title: '积分兑换',
          content: '1. 积分可在积分商城兑换商品\n2. 积分可抵扣部分商品金额\n3. 不同会员等级积分抵扣比例不同',
          tooltip: '普通会员：100积分=1元\n银卡会员：100积分=1元\n金卡会员：90积分=1元\n钻石会员：80积分=1元',
          important: true
        },
        {
          title: '抵扣限制',
          content: '1. 部分特殊商品不支持积分抵扣\n2. 单笔订单最高可使用积分抵扣订单金额的一定比例',
          tooltip: '普通会员：最高可抵扣20%\n银卡会员：最高可抵扣25%\n金卡会员：最高可抵扣30%\n钻石会员：最高可抵扣35%'
        },
        {
          title: '兑换权益',
          content: '1. 普通会员：积分商城兑换基础权益\n2. 银卡会员及以上：积分商城兑换全部权益',
          tooltip: '部分高价值商品和服务仅限银卡会员及以上等级兑换'
        }
      ],
      validity: [
        {
          title: '积分有效期',
          content: '1. 积分自获取日起有效期为1年\n2. 即将过期的积分会在"我的积分"页面提醒',
          tooltip: '积分到期前一个月会收到过期提醒',
          important: true
        },
        {
          title: '积分消耗规则',
          content: '积分消耗遵循"先进先出"原则，优先使用即将过期的积分',
          tooltip: '系统会自动优先使用最早获得的积分'
        },
        {
          title: '积分清零',
          content: '过期积分将自动失效并从账户中扣除',
          tooltip: '建议定期查看积分明细，及时使用即将过期的积分'
        }
      ],
      faq: [
        {
          title: '如何查看我的积分？',
          content: '在小程序底部菜单点击"积分"即可查看您的积分余额和明细',
          tooltip: '积分明细页面可以查看积分的获取和使用记录'
        },
        {
          title: '积分可以转赠吗？',
          content: '目前积分暂不支持转赠功能',
          tooltip: '未来可能会开放积分转赠功能，敬请期待'
        },
        {
          title: '为什么我的积分没有到账？',
          content: '积分一般在完成任务后即时到账，如有延迟，请稍后刷新页面查看。如长时间未到账，请联系客服',
          tooltip: '部分任务可能需要系统审核后才会发放积分'
        },
        {
          title: '如何提高我的会员等级？',
          content: '会员等级根据您的积分总量自动判定，获得更多积分即可提升等级',
          tooltip: '普通会员：0-999积分\n银卡会员：1000-4999积分\n金卡会员：5000-9999积分\n钻石会员：10000+积分',
          important: true
        }
      ]
    }
  },

  onLoad: function() {
    // 获取暗黑模式设置
    this.setData({
      darkMode: app.globalData.darkMode
    });

    // 初始化展开状态，默认第一个规则展开
    const expandedRules = {};
    const currentCategory = this.data.currentCategory;
    if (this.data.rules[currentCategory] && this.data.rules[currentCategory].length > 0) {
      expandedRules[`${currentCategory}_0`] = true;
    }

    this.setData({ expandedRules });
  },

  // 切换规则分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;

    // 重置展开状态，默认第一个规则展开
    const expandedRules = {};
    if (this.data.rules[category] && this.data.rules[category].length > 0) {
      expandedRules[`${category}_0`] = true;
    }

    this.setData({
      currentCategory: category,
      expandedRules: expandedRules,
      tooltipVisible: {} // 重置提示可见状态
    });
  },

  // 切换规则展开状态
  toggleRule: function(e) {
    const { category, index } = e.currentTarget.dataset;
    const key = `${category}_${index}`;
    const expandedRules = { ...this.data.expandedRules };

    expandedRules[key] = !expandedRules[key];

    this.setData({
      expandedRules: expandedRules
    });
  },

  // 显示/隐藏提示
  toggleTooltip: function(e) {
    const { category, index } = e.currentTarget.dataset;
    const key = `${category}_${index}`;
    const tooltipVisible = { ...this.data.tooltipVisible };

    // 关闭其他所有提示
    Object.keys(tooltipVisible).forEach(k => {
      tooltipVisible[k] = false;
    });

    // 切换当前提示的可见状态
    tooltipVisible[key] = !tooltipVisible[key];

    this.setData({
      tooltipVisible: tooltipVisible
    });
  },

  // 关闭提示
  closeTooltip: function() {
    this.setData({
      tooltipVisible: {}
    });
  },

  // 联系客服
  contactSupport: function() {
    wx.showToast({
      title: '正在连接客服...',
      icon: 'loading',
      duration: 1000,
      success: () => {
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/messages/messages'
          });
        }, 1000);
      }
    });
  }
})
