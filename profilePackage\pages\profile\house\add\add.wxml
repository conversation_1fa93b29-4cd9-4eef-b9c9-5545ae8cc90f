<!-- 添加房屋页面 -->
<view class="container">


  <!-- 内容区域 -->
  <scroll-view scroll-y class="content-area">
    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 楼栋选择 -->
      <view class="form-row">
        <view class="form-label">楼栋</view>
        <view class="form-control">
          <picker
            mode="selector"
            range="{{buildingPickerRange}}"
            range-key="buildingNumber"
            value="{{selectedBuildingIndex}}"
            bindchange="onBuildingPickerChange"
            disabled="{{buildingsLoading}}"
            wx:if="{{!buildingsLoading && buildings.length > 0}}"
          >
            <view class="picker-content {{selectedBuildingIndex === -1 ? 'placeholder' : ''}}">
              <text>{{selectedBuildingIndex === -1 ? '请选择楼栋' : buildingPickerRange[selectedBuildingIndex].buildingNumber}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>

          <!-- 楼栋加载状态 -->
          <view class="loading-text" wx:if="{{buildingsLoading}}">正在加载楼栋信息...</view>

          <!-- 楼栋空状态 -->
          <view class="empty-text" wx:if="{{!buildingsLoading && buildings.length === 0}}">
            暂无可选楼栋
            <button class="retry-button" bindtap="loadBuildings">重新加载</button>
          </view>
        </view>
      </view>

      <!-- 房间选择 -->
      <view class="form-row">
        <view class="form-label">房间</view>
        <view class="form-control">
          <picker
            mode="selector"
            range="{{roomPickerRange}}"
            range-key="roomNumber"
            value="{{selectedRoomIndex}}"
            bindchange="onRoomPickerChange"
            disabled="{{roomsLoading || !selectedBuildingId}}"
            wx:if="{{selectedBuildingId && !roomsLoading && rooms.length > 0}}"
          >
            <view class="picker-content {{selectedRoomIndex === -1 ? 'placeholder' : ''}}">
              <text>{{selectedRoomIndex === -1 ? '请选择房间' : roomPickerRange[selectedRoomIndex].roomNumber}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>

          <!-- 房间加载状态 -->
          <view class="loading-text" wx:if="{{selectedBuildingId && roomsLoading}}">正在加载房间信息...</view>

          <!-- 房间空状态 -->
          <view class="empty-text" wx:if="{{selectedBuildingId && !roomsLoading && rooms.length === 0}}">
            该楼栋暂无可选房间
            <button class="retry-button" bindtap="loadRooms">重新加载</button>
          </view>

          <!-- 未选择楼栋提示 -->
          <view class="placeholder-text" wx:if="{{!selectedBuildingId}}">请先选择楼栋</view>
        </view>
      </view>

    </view>

    <!-- 居住身份选择区域 - 移动到下方 -->
    <view class="form-section" wx:if="{{selectedRoomId}}">
      <view class="section-header">
        <view class="section-title">居住身份</view>
      </view>

      <view class="resident-type-container">
        <view class="resident-type-tags">
          <view class="resident-tag {{selectedResidentType === 'owner' ? 'selected' : ''}}"
                bindtap="selectResidentType" data-residenttype="owner">
            <view class="tag-text">业主</view>
            <view class="tag-desc">房屋所有者</view>
          </view>
          <view class="resident-tag {{selectedResidentType === 'tenant' ? 'selected' : ''}}"
                bindtap="selectResidentType" data-residenttype="tenant">
            <view class="tag-text">租户</view>
            <view class="tag-desc">房屋租赁者</view>
          </view>
          <view class="resident-tag {{selectedResidentType === 'family' ? 'selected' : ''}}"
                bindtap="selectResidentType" data-residenttype="family">
            <view class="tag-text">家庭成员</view>
            <view class="tag-desc">业主或租户的家属</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 未选择房间提示 -->
    <view class="form-section" wx:if="{{!selectedRoomId}}">
      <view class="placeholder-section">
        <view class="placeholder-text">请先选择房间后再选择居住身份</view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="submit-button-container" wx:if="{{selectedResidentType}}">
      <button type="button" class="submit-button {{canSubmit ? '' : 'disabled'}}" disabled="{{!canSubmit}}" bindtap="submitForm">{{submitButtonText}}</button>
    </view>
  </scroll-view>

  <!-- 加载指示器 -->
  <view class="loading-overlay {{isSubmitting ? 'active' : ''}}">
    <view class="loading-spinner">
      <view class="spinner"></view>
      <view class="loading-text">{{submitButtonText}}中...</view>
    </view>
  </view>

  <!-- 成功提示弹窗 -->
  <view class="confirm-dialog-overlay {{showSuccessDialog ? 'active' : ''}}">
    <view class="confirm-dialog">
      <view class="confirm-dialog-content">
        <view class="confirm-dialog-icon">
          <icon type="success" size="32" color="#34C759"></icon>
        </view>
        <view class="confirm-dialog-title">{{mode === 'edit' ? '修改成功' : '添加成功'}}</view>
        <view class="confirm-dialog-message">房屋信息已成功{{mode === 'edit' ? '修改' : '添加到您的账户'}}</view>
      </view>
      <view class="confirm-dialog-buttons">
        <view class="confirm-dialog-button primary" bindtap="confirmSuccess">确定</view>
      </view>
    </view>
  </view>
</view>

