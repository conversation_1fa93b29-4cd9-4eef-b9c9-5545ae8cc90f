<!--我的预约页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- Banner内容 -->
  <view class="banner-content">
    <view class="banner-desc">查看您的废品回收预约记录</view>
  </view>

  <!-- 预约记录列表 -->
  <view class="appointment-list" wx:if="{{appointments.length > 0}}">
    <view class="appointment-item" wx:for="{{appointments}}" wx:key="id" bindtap="viewAppointmentDetail" data-id="{{item.id}}">
      <view class="appointment-header">
        <view class="appointment-status {{item.status === '待上门' ? 'pending' : (item.status === '已完成' ? 'completed' : 'canceled')}}">{{item.status}}</view>
        <view class="appointment-date">{{item.date}}</view>
      </view>
      <view class="appointment-content">
        <view class="appointment-info">
          <view class="info-item">
            <view class="info-label">预约编号：</view>
            <view class="info-value">{{item.orderNumber}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">废品类型：</view>
            <view class="info-value">{{item.wasteTypes}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">预约时间：</view>
            <view class="info-value">{{item.appointmentTime}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">回收地址：</view>
            <view class="info-value address">{{item.address}}</view>
          </view>
        </view>
        <view class="appointment-actions">
          <view class="action-btn detail-btn">查看详情</view>
          <view class="action-btn cancel-btn" wx:if="{{item.status === '待上门'}}" catchtap="cancelAppointment" data-id="{{item.id}}">取消预约</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{appointments.length === 0}}">
    <view class="empty-icon"></view>
    <view class="empty-text">暂无预约记录</view>
    <view class="empty-action" bindtap="navigateToAppointment">立即预约废品回收</view>
  </view>

  <!-- 预约详情弹窗 -->
  <view class="detail-modal {{showDetailModal ? 'visible' : ''}}" bindtap="hideDetailModal">
    <view class="detail-content" catchtap="stopPropagation">
      <view class="detail-header">
        <view class="detail-title">预约详情</view>
        <view class="detail-close" bindtap="hideDetailModal">×</view>
      </view>

      <view class="detail-body">
        <view class="detail-status">
          <view class="status-icon {{currentAppointment.status === '待上门' ? 'pending' : (currentAppointment.status === '已完成' ? 'completed' : 'canceled')}}"></view>
          <view class="status-text">{{currentAppointment.status}}</view>
        </view>

        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="detail-item">
            <view class="detail-label">预约编号：</view>
            <view class="detail-value">{{currentAppointment.orderNumber}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">创建时间：</view>
            <view class="detail-value">{{currentAppointment.createTime}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">预约时间：</view>
            <view class="detail-value">{{currentAppointment.appointmentTime}}</view>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">联系信息</view>
          <view class="detail-item">
            <view class="detail-label">联系人：</view>
            <view class="detail-value">{{currentAppointment.name}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">联系电话：</view>
            <view class="detail-value">{{currentAppointment.phone}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">回收地址：</view>
            <view class="detail-value">{{currentAppointment.address}}</view>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">废品信息</view>
          <view class="detail-item">
            <view class="detail-label">废品类型：</view>
            <view class="detail-value">{{currentAppointment.wasteTypes}}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">预估重量：</view>
            <view class="detail-value">{{currentAppointment.weight}} kg</view>
          </view>
          <view class="detail-item" wx:if="{{currentAppointment.remark}}">
            <view class="detail-label">备注信息：</view>
            <view class="detail-value">{{currentAppointment.remark}}</view>
          </view>
        </view>

        <view class="detail-photos" wx:if="{{currentAppointment.photos && currentAppointment.photos.length > 0}}">
          <view class="section-title">废品照片</view>
          <view class="photo-list">
            <image
              class="photo-item"
              wx:for="{{currentAppointment.photos}}"
              wx:key="*this"
              src="{{item}}"
              mode="aspectFill"
              bindtap="previewImage"
              data-url="{{item}}"
            ></image>
          </view>
        </view>

        <view class="detail-actions" wx:if="{{currentAppointment.status === '待上门'}}">
          <button class="detail-action-btn cancel-btn" bindtap="cancelCurrentAppointment">取消预约</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 取消预约确认弹窗 -->
  <view class="confirm-modal {{showCancelConfirm ? 'visible' : ''}}" bindtap="hideCancelConfirm">
    <view class="confirm-content" catchtap="stopPropagation">
      <view class="confirm-title">确认取消预约</view>
      <view class="confirm-message">您确定要取消此次废品回收预约吗？</view>
      <view class="confirm-buttons">
        <button class="confirm-btn cancel" bindtap="hideCancelConfirm">再想想</button>
        <button class="confirm-btn confirm" bindtap="confirmCancel">确认取消</button>
      </view>
    </view>
  </view>
</view>
