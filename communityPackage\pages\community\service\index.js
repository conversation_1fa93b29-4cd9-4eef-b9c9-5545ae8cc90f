// 便民服务主页面
const app = getApp()
const navigator = require('../../../../utils/navigator')

Page({
  data: {
    activeTab: 'directory' // 默认选中信息黄页选项卡
  },

  onLoad: function() {
    // 页面加载时的逻辑
  },

  onShow: function() {
    // 页面显示时的逻辑
    // 清除所有导航节流标记
    navigator.clearThrottles();
  },

  // 处理选项卡切换事件
  handleTabChange: function(e) {
    const { tabId } = e.detail;
    this.setData({
      activeTab: tabId
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '便民服务',
      path: '/servicePackage/pages/community/service/index'
    };
  }
})
