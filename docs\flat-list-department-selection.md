# 扁平化列表实现无限层级部门选择

## 问题分析

微信小程序的递归模板存在一些限制和问题，导致子级无法正常显示。为了解决这个问题，我们改用**扁平化列表**的方法。

## 解决方案

### 1. 核心思想

将嵌套的树形数据结构转换为扁平化的列表，根据展开状态动态生成显示列表。

```
原始嵌套结构:
网信科技公司 (expanded: true)
├── 研发部
├── 销售部  
└── 智慧物业发展规划公司 (expanded: false)
    ├── 市场调查部 (不显示，因为父级未展开)
    └── 应用程序开发部 (不显示)

转换为扁平化列表:
[
  {id: "xxx", orgName: "网信科技公司", level: 0, expanded: true},
  {id: "xxx", orgName: "研发部", level: 1},
  {id: "xxx", orgName: "销售部", level: 1},
  {id: "xxx", orgName: "智慧物业发展规划公司", level: 1, expanded: false}
  // 智慧物业的子级不在列表中，因为它未展开
]
```

### 2. 实现步骤

#### 2.1 扁平化函数

```javascript
// 将嵌套的组织数据扁平化为显示列表
flattenOrgData: function(orgList, level = 0, result = []) {
  orgList.forEach(org => {
    // 添加当前项到结果列表
    result.push({
      ...org,
      level: level,
      displayLevel: level
    });

    // 如果是展开的公司且有子级，递归添加子级
    if (org.expanded && org.children && org.children.length) {
      this.flattenOrgData(org.children, level + 1, result);
    }
  });

  return result;
}
```

#### 2.2 展开状态切换

```javascript
toggleCompanyExpanded: function(companyId) {
  // 1. 更新嵌套数据结构中的展开状态
  const updatedDepartments = updateExpanded(this.data.filteredDepartments);
  
  // 2. 重新生成扁平化列表
  const flatList = this.flattenOrgData(updatedDepartments);
  
  // 3. 更新页面数据
  this.setData({
    filteredDepartments: updatedDepartments,
    flatOrgList: flatList  // 新增：扁平化显示列表
  });
}
```

#### 2.3 WXML模板简化

```xml
<!-- 使用扁平化列表渲染组织结构 -->
<block wx:for="{{flatOrgList}}" wx:key="id">
  <view class="org-item level-{{item.level}}" style="margin-left: {{item.level * 20}}px;">
    <!-- 调试信息 -->
    <text style="font-size: 12px; color: #999;">
      DEBUG: {{item.orgName}} - level: {{item.level}} - expanded: {{item.expanded}} - hasChildren: {{item.children && item.children.length}}
    </text>
    
    <view class="selector-item {{item.type === 'dept' ? 'dept-item' : 'company-item'}} {{item.id === selectedDepartmentId ? 'active' : ''}}"
          data-id="{{item.id}}"
          data-name="{{item.orgName}}"
          data-type="{{item.type}}"
          data-level="{{item.level}}"
          bindtap="selectDepartment">
      <view class="selector-item-content">
        <view class="selector-item-title">
          <text class="org-type-icon">{{item.type === 'company' ? '🏢' : '📁'}}</text>
          {{item.orgName}}
          <!-- 公司类型且有子级时显示展开/收起图标 -->
          <text wx:if="{{item.type === 'company' && item.children && item.children.length}}"
                class="expand-icon {{item.expanded ? 'expanded' : ''}}">▼</text>
        </view>
      </view>
      <!-- 只有部门类型才显示对勾 -->
      <view class="selector-item-check" wx:if="{{item.type === 'dept' && item.id === selectedDepartmentId}}"></view>
    </view>
  </view>
</block>
```

### 3. 优势

#### 3.1 避免递归模板问题
- ✅ 不依赖微信小程序的模板递归功能
- ✅ 事件绑定简单可靠
- ✅ 数据传递清晰明确

#### 3.2 性能优化
- ✅ 只渲染需要显示的节点
- ✅ 避免深层嵌套的DOM结构
- ✅ 减少不必要的条件判断

#### 3.3 调试友好
- ✅ 扁平化列表结构清晰
- ✅ 每个节点的层级信息明确
- ✅ 展开状态一目了然

### 4. 工作流程

```
用户点击公司 → 
更新嵌套数据的expanded状态 → 
重新生成扁平化列表 → 
setData更新flatOrgList → 
页面重新渲染显示新的列表
```

### 5. 调试信息

页面上会显示调试信息：
```
DEBUG: 网信科技公司 - level: 0 - expanded: true - hasChildren: true
DEBUG: 研发部 - level: 1 - expanded: false - hasChildren: false  
DEBUG: 智慧物业发展规划公司 - level: 1 - expanded: false - hasChildren: true
```

当点击"智慧物业发展规划公司"后，应该会看到：
```
DEBUG: 网信科技公司 - level: 0 - expanded: true - hasChildren: true
DEBUG: 研发部 - level: 1 - expanded: false - hasChildren: false
DEBUG: 智慧物业发展规划公司 - level: 1 - expanded: true - hasChildren: true
DEBUG: 市场调查部 - level: 2 - expanded: false - hasChildren: false
DEBUG: 应用程序开发部 - level: 2 - expanded: false - hasChildren: false
DEBUG: 数据支持保障部 - level: 2 - expanded: false - hasChildren: false
```

### 6. 数据结构

#### 6.1 原始嵌套数据 (filteredDepartments)
保持原有的树形结构，用于状态管理和搜索

#### 6.2 扁平化显示数据 (flatOrgList)  
用于页面渲染的线性列表

```javascript
// 示例数据
flatOrgList: [
  {
    id: "7091961583521234952",
    orgName: "网信科技公司", 
    type: "company",
    level: 0,
    expanded: true,
    children: [...] // 保留原始children用于判断是否有子级
  },
  {
    id: "7091961583521234954",
    orgName: "研发部",
    type: "dept", 
    level: 1,
    children: null
  }
  // ... 更多项目
]
```

## 测试验证

1. **初始状态**：只显示顶级公司
2. **点击展开**：子级部门出现在列表中
3. **层级缩进**：不同层级有正确的缩进
4. **调试信息**：显示正确的层级和展开状态
5. **事件响应**：点击事件正常触发

这种方法完全避开了微信小程序递归模板的限制，应该能够稳定工作。
