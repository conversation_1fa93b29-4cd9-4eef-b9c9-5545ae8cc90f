/* 社区信息展示页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
}

/* 页面顶部样式 */

/* 分类选项卡样式 */
.tabs {
  display: flex;
  background-color: #fff;
  padding: 0 24px;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 48px;
  font-size: 16px;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 24px;
  height: 3px;
  background-color: #ff8c00;
  border-radius: 3px 3px 0 0;
  transition: all 0.3s ease;
}

/* 内容容器样式 */
.content-container {
  flex: 1;
  padding: 16px 24px;
}

/* 社区兴趣群列表样式 */
.group-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.group-item {
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.group-content {
  flex: 1;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.group-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.group-members {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.member-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  opacity: 0.5;
}

.group-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.group-owner {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.owner-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 4px;
}

.group-action {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.detail-btn {
  background-color: transparent;
  color: #ff8c00;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #ff8c00;
  line-height: 1.2;
  font-weight: normal;
}

.detail-btn::after {
  border: none;
}

/* 失物招领筛选选项卡 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #ff8c00;
  color: #fff;
  font-weight: 500;
}

/* 失物招领列表样式 */
.lostfound-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.lostfound-item {
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.lostfound-content {
  flex: 1;
}

.lostfound-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.lostfound-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.lostfound-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #fff;
}

.lost-tag {
  background-color: #ff8c00;
}

.found-tag {
  background-color: #36c;
}

.lostfound-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.lostfound-time, .lostfound-location {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.info-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  opacity: 0.5;
}

.lostfound-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.lostfound-images {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.lostfound-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  flex-shrink: 0;
}

.lostfound-action {
  display: flex;
  justify-content: flex-end;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}
