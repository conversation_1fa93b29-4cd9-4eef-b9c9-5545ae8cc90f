<!--服务卡片组件-->
<view class="service-card {{shadow ? 'with-shadow' : ''}}" bindtap="onCardTap">
  <view class="card-icon" wx:if="{{icon}}">
    <image class="icon-image" src="{{icon}}" mode="aspectFit"></image>
  </view>
  <view class="card-content">
    <view class="card-title">{{title}}</view>
    <view class="card-desc" wx:if="{{description}}">{{description}}</view>
    <slot></slot>
  </view>
  <view class="card-arrow" wx:if="{{showArrow}}">
    <image class="arrow-image" src="/images/icons/arrow-right.svg" mode="aspectFit"></image>
  </view>
</view>
