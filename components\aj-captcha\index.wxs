/**
 * 滑动验证码wxs
 */
var startX = 0; //开始的位置坐标
var startY = 0;
var moveX = 0; //滑动时的位置
var lastMoveX = 0; //上一次滑动的位置
var moveStartTime = 0; //移动开始的时间
var moveEndTime = 0; //移动结束的时间
var totalMoveX = 0; //总的移动长度
var startMoveTime = 0; //开始移动的时间
var startT = 0; //滑动开始触发的时间
var endT = 0; //滑动结束触发的时间
var maxLeft = 0; //滑块最大left值
var lastMoveTime = 0; //上次滑动的时间
var offsetX = 0; //滑块相对于容器的偏移量
var isMouseDown = false; //是否按下
var barWidth = 0; //滑块容器宽度
var blockWidth = 0; //滑块宽度

/**
 * 触摸开始
 */
function touchstart(e, ins) {
  var touch = e.touches[0] || e.changedTouches[0];
  startX = touch.pageX;
  startY = touch.pageY;
  startT = Date.now(); //开始触发的时间
  startMoveTime = Date.now();
  isMouseDown = true;

  var verifyBarArea = ins.selectComponent('.verify-bar-area');
  var verifyLeftBar = ins.selectComponent('.verify-left-bar');

  barWidth = verifyBarArea.getBoundingClientRect().width;
  blockWidth = verifyLeftBar.getBoundingClientRect().width;
  maxLeft = barWidth - blockWidth;

  offsetX = 0;
  lastMoveX = 0;
  lastMoveTime = Date.now();
}

/**
 * 触摸移动
 */
function touchmove(e, ins) {
  if (!isMouseDown) {
    return false;
  }
  var touch = e.touches[0] || e.changedTouches[0];
  moveX = touch.pageX;
  var offsetX = moveX - startX;

  //判断是否来回滑动了
  if (offsetX < lastMoveX) {
    //如果是重新滑动了，重新开始计算
    lastMoveX = offsetX;
    startMoveTime = Date.now();
    offsetX = 0;
  }

  lastMoveX = offsetX;
  lastMoveTime = Date.now();

  //小于0 不拖动
  if (offsetX < 0) {
    offsetX = 0;
  }
  //大于最大值 不拖动
  if (offsetX > maxLeft) {
    offsetX = maxLeft;
  }

  var verifyLeftBar = ins.selectComponent('.verify-left-bar');
  verifyLeftBar.setStyle({
    'left': offsetX + "px",
  });

  var verifySubBlock = ins.selectComponent('.verify-sub-block');
  verifySubBlock.setStyle({
    'left': offsetX + "px",
  });
}

/**
 * 触摸结束
 */
function touchend(e, ins) {
  if (isMouseDown) {
    isMouseDown = false;
    endT = Date.now(); //结束触发的时间
    var touch = e.touches[0] || e.changedTouches[0];
    moveEndTime = Date.now();
    totalMoveX = touch.pageX - startX;
    if (totalMoveX < 0) {
      totalMoveX = 0;
    }
    if (totalMoveX > maxLeft) {
      totalMoveX = maxLeft;
    }
    //判断是否重合
    console.log("滑动结束，总移动距离：" + totalMoveX + "，最大距离：" + maxLeft);

    // 无论是否重合，都调用验证方法
    ins.callMethod('_blockPuzzleCheck', {
      offsetX: totalMoveX,
      expendTime: Math.ceil((moveEndTime - startMoveTime) / 1000)
    });
  }
}

module.exports = {
  touchstart: touchstart,
  touchmove: touchmove,
  touchend: touchend
};
