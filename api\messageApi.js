const REQUEST = require('../utils/request.js')

//获取聊天记录
function getMessageRecord(userId){
	return REQUEST.request('/users-api/v1/private-message/record?userId='+userId, 'GET', {}, true)
}

//获取站内私信列表
function getPrivateMessageList(){
	return REQUEST.request('/users-api/v1/private-message/list', 'GET', {}, true)
}

//分页查询系统该消息
function getSystemMessageList(params){
	return REQUEST.request('/users-api/v1/sys-message/page', 'GET', params, true)
}

//获取未读消息数量
function getUnReadCount(params)
{
	return REQUEST.request('/users-api/v1/message/count-unread', 'GET',params, true)
}

//标记系统消息已读
function markSystemMessageRead(id){
	return REQUEST.request('/users-api/v1/sys-message/read?id='+id, 'POST', {}, true)
}

//标记私信已读
function markPrivateMessageRead(id){
	return REQUEST.request('/users-api/v1/private-message/read?id='+id, 'POST', {}, true)
}

module.exports = {
	getMessageRecord,
	getPrivateMessageList,
	getSystemMessageList,
	getUnReadCount,
	markSystemMessageRead,
	markPrivateMessageRead
}