/* mall.wxss */
.container {
  padding: 30rpx;
}

.header {
  margin-bottom: 30rpx;
}

.points-info {
  background: linear-gradient(to right, #ff8c00, #ff6b00);
  border-radius: 16rpx;
  padding: 30rpx;
  color: white;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
}

.points-label {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff8c00;
}

.reset-btn {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 8rpx;
}

.search-box {
  position: relative;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  margin-right: 16rpx;
  color: #999;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-icon {
  color: #999;
}

.categories {
  margin-bottom: 30rpx;
}

.categories-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-item:last-child {
  margin-right: 0;
}

.category-item.active {
  background: #ff8c00;
  color: white;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  font-size: 40rpx;
  color: #ff8c00;
}

.category-item.active .category-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.category-name {
  font-size: 26rpx;
}

.sort-bar {
  height: 80rpx;
  background: white;
  border-radius: 16rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.sort-text {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.goods-mall-link {
  font-size: 26rpx;
  color: #ff8c00;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 8rpx;
}

.sort-icon {
  margin-left: 8rpx;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.product-item {
  width: calc(50% - 20rpx);
  margin: 0 10rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-points {
  font-size: 30rpx;
  color: #ff8c00;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.cash-price {
  font-size: 26rpx;
  color: #ff8c00;
  margin-left: 8rpx;
}

.product-stock {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.exchange-btn {
  width: 100%;
  height: 64rpx;
  background: #ff8c00;
  color: white;
  font-size: 26rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.exchange-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.empty-products {
  width: 100%;
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #e0e0e0;
}

.empty-text {
  font-size: 28rpx;
}

.sort-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.sort-popup.show {
  visibility: visible;
  opacity: 1;
}

.sort-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.sort-popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.sort-popup.show .sort-popup-content {
  transform: translateY(0);
}

.sort-popup-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-align: center;
}

.sort-options {
  margin-bottom: 30rpx;
}

.sort-option {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #ff8c00;
}
