/* 便民信息黄页页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
}

/* 页面顶部样式 */

/* 搜索框样式 */
.search-container {
  padding: 12px 24px;
  background-color: #ff8c00;
  margin-bottom: 8px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 0 12px;
  height: 44px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #333;
}

.search-clear {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear image {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

/* 服务分类网格样式 */
.category-grid {
  padding: 16px 24px;
  margin-bottom: 24px;
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.grid-icon-bg {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  background-color: rgba(255, 140, 0, 0.1);
  transition: all 0.3s ease;
}

.grid-item:active .grid-icon-bg {
  transform: scale(0.95);
  background-color: rgba(255, 140, 0, 0.2);
}

.grid-icon {
  width: 32px;
  height: 32px;
}

.grid-name {
  font-size: 15px;
  color: #333;
  text-align: center;
}

/* 搜索结果列表样式 */
.search-results {
  padding: 0 24px;
  margin-bottom: 24px;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.result-list {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.result-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.result-content {
  flex: 1;
}

.result-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.result-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.result-info {
  font-size: 12px;
  color: #999;
}

.result-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-arrow image {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 热门服务样式 */
.popular-services {
  padding: 0 24px;
  margin-bottom: 24px;
}

.popular-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.popular-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.popular-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.popular-item:active {
  transform: scale(0.99);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.popular-content {
  flex: 1;
}

.popular-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.popular-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.popular-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.popular-address, .popular-phone {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.info-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  opacity: 0.5;
}

.popular-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popular-arrow image {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}
