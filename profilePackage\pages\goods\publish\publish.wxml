<!--发布商品页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <form bindsubmit="submitForm">
    <!-- 统一表单卡片 -->
    <view class="form-card">
      <view class="image-tips" wx:if="{{isEdit}}">
            <text class="tip-icon">i</text>
            <text>修改名称,描述和商品图片时,会进入重新审核</text>
          </view>


      <!-- 商品描述 -->
      <view class="form-section">

        <view class="section-title">好物名称<text class="required">*</text></view>
        <view class="form-item">
          <input class="item-input" name="title" placeholder="请输入好物名称，简洁明了" value="{{formData.title}}" bindinput="inputTitle" maxlength="30" />
          <view class="input-count">{{formData.title.length}}/30</view>
        </view>

        <view class="section-title">商品描述<text class="required">*</text></view>

        <!-- 编辑器工具栏 -->
        <view class="editor-toolbar" wx:if="{{showRichEditor}}">
          <view class="toolbar-group">
            <view class="toolbar-btn {{editorFormats.bold ? 'active' : ''}}" bindtap="formatBold">
              <text class="icon-bold">B</text>
            </view>
            <view class="toolbar-btn {{editorFormats.italic ? 'active' : ''}}" bindtap="formatItalic">
              <text class="icon-italic">I</text>
            </view>
            <view class="toolbar-btn {{editorFormats.underline ? 'active' : ''}}" bindtap="formatUnderline">
              <text class="icon-underline">U</text>
            </view>
          </view>
          <view class="toolbar-group">
            <view class="toolbar-btn" bindtap="showColorPicker">
              <text class="icon-color" style="color: {{currentTextColor}}">A</text>
            </view>
            <view class="toolbar-btn" bindtap="showSizePicker">
              <text class="icon-size">T</text>
            </view>
          </view>
          <view class="toolbar-group">
            <view class="toolbar-btn" bindtap="toggleEditor">
              <text class="icon-text">文本</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <!-- 富文本编辑器 -->
          <editor wx:if="{{showRichEditor}}"
                  class="rich-editor"
                  placeholder="请详细描述商品的成色、规格、使用感受等信息，让买家更了解您的商品"
                  bindready="onEditorReady"
                  bindinput="onEditorInput"
                  bindstatuschange="onEditorStatusChange"
                  show-img-size="{{false}}"
                  show-img-toolbar="{{false}}"
                  show-img-resize="{{false}}">
          </editor>

          <!-- 普通文本框 -->
          <textarea wx:else
                    class="item-textarea"
                    name="stuffDescribe"
                    placeholder="请详细描述商品的成色、规格、使用感受等信息，让买家更了解您的商品"
                    value="{{formData.stuffDescribe}}"
                    bindinput="inputDescription"
                    maxlength="500">
          </textarea>

          <view class="editor-toggle">
            <view class="toggle-btn" bindtap="toggleEditor">
              {{showRichEditor ? '切换到普通文本' : '切换到富文本编辑'}}
            </view>
            <view class="input-count">{{descriptionLength}}/500</view>
          </view>
        </view>

        <view class="section-title">商品图片<text class="required">*</text></view>
        <view class="image-uploader">
          <view class="image-list">
            <view class="image-item" wx:for="{{formData.media}}" wx:key="index">
              <image class="uploaded-image" src="{{apiUrl}}{{item}}" mode="aspectFill"></image>
              <view class="image-delete" catchtap="deleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="image-add" bindtap="chooseImage" wx:if="{{formData.media.length < 9}}">
              <view class="add-icon">+</view>
              <view class="add-text">添加图片</view>
            </view>
          </view>
          <view class="image-tips">
            <text class="tip-icon">i</text>
            <text>清晰的商品照片更容易吸引买家，最多上传9张</text>
          </view>
        </view>

        <!-- 商品类型 - 左右对齐布局 -->
        <view class="form-item-horizontal">
          <view class="item-label-left">商品类型<text class="required">*</text></view>
          <view class="type-selector-right">
            <view class="type-option {{formData.type === item.nameEn ? 'active' : ''}}" bindtap="selectType" data-type="{{item.nameEn}}" wx:for="{{typeOptions}}" wx:key="id">
              <view class="type-name">{{item.nameCn}}</view>
            </view>
          </view>
        </view>

        <!-- 商品分类 - 左右对齐布局 -->
        <view class="form-item-horizontal">
          <view class="item-label-left">商品分类<text class="required">*</text></view>
          <view class="category-selector-right">
            <picker bindchange="categoryChange" value="{{categoryIndex}}" range="{{categoryOptions}}" range-key="nameCn">
              <view class="picker-view">
                <text wx:if="{{formData.categoryCode}}">{{selectedCategoryName}}</text>
                <text wx:else class="placeholder">请选择商品分类</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 价格信息 - 左右对齐布局 -->
        <view class="form-item-horizontal">
          <view class="item-label-left">价格信息<text class="required">*</text></view>
          <view class="price-input-wrapper-right">
            <text class="price-symbol">¥</text>
            <input class="price-input {{formData.type === 'free' ? 'disabled' : ''}}"
                   type="digit"
                   name="amount"
                   placeholder="{{formData.type === 'free' ? '免费商品' : '0.00'}}"
                   value="{{formData.type === 'free' ? '0.00' : formData.amount}}"
                   bindinput="inputPrice"
                   disabled="{{formData.type === 'free'}}" />
          </view>
        </view>

        <!-- 库存数量 - 左右对齐布局 -->
        <view class="form-item-horizontal">
          <view class="item-label-left">库存数量<text class="required">*</text></view>
          <view class="stock-stepper-right">
            <view class="stepper-btn minus {{formData.stock <= 1 ? 'disabled' : ''}}" bindtap="decreaseStock">-</view>
            <input class="stepper-input" type="number" name="stock" value="{{formData.stock}}" bindinput="inputStock" />
            <view class="stepper-btn plus" bindtap="increaseStock">+</view>
          </view>
        </view>

        <!-- 订单超时时间 - 左右对齐布局 -->
        <view class="form-item-horizontal">
          <view class="item-label-left">订单超时时间<text class="required">*</text></view>
          <view class="expire-time-wrapper-right">
            <view class="expire-time-input">
              <view class="stepper-btn minus {{formData.orderExpireTime <= 1 ? 'disabled' : ''}}" bindtap="decreaseOrderExpireTime">-</view>
              <input class="stepper-input" type="number" value="{{formData.orderExpireTime}}" bindinput="inputOrderExpireTime" />
              <view class="stepper-btn plus" bindtap="increaseOrderExpireTime">+</view>
            </view>
            <view class="expire-time-unit">
              <picker bindchange="timeUnitChange" value="{{timeUnitIndex}}" range="{{timeUnitOptions}}" range-key="label">
                <view class="picker-view-small">
                  <text>{{timeUnitOptions[timeUnitIndex].label}}</text>
                  <text class="picker-arrow">›</text>
                </view>
              </picker>
            </view>
          </view>
        </view>


        <!-- 积分设置已隐藏 -->
        <view class="section-title" style="display: none;">积分设置</view>
        <view class="form-item" style="display: none;">
          <view class="item-label">积分</view>
          <input class="item-input" type="number" name="points" placeholder="0" value="{{formData.points}}" bindinput="inputPoints" />
        </view>

        <view class="section-title">地点<text class="required">*</text></view>
        <view class="form-item">
          <view class="location-input-wrapper">
            <input class="item-input" name="address" placeholder="请输入交易地点" value="{{formData.address}}" bindinput="inputAddress" />
            <view class="location-btn" bindtap="chooseLocation">
              <text class="location-icon">📍</text>
            </view>
          </view>
        </view>


        <!-- 联系方式已删除 -->
      </view>



      <!-- 发布须知 -->
      <view class="form-section">
        <view class="section-title">发布须知</view>
        <view class="notice-content">
          <view class="notice-item">
            <text class="notice-dot">•</text>
            <text>请确保您发布的商品信息真实有效</text>
          </view>
          <view class="notice-item">
            <text class="notice-dot">•</text>
            <text>禁止发布违禁物品、侵权商品等</text>
          </view>
          <view class="notice-item">
            <text class="notice-dot">•</text>
            <text>平台将对违规商品进行下架处理</text>
          </view>
        </view>
        <view class="agreement-check">
          <checkbox-group bindchange="agreeChange">
            <label class="checkbox-label">
              <checkbox value="agree" checked="{{isAgreed}}" color="#ff8c00" />
              <text>我已阅读并同意《发布规则》和《交易条款》</text>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="form-buttons">
      <button class="form-button cancel" bindtap="cancelPublish">取消</button>

      <button class="form-button submit" form-type="submit" disabled="{{!isAgreed}}">{{isEdit ? '保存' : '发布'}}</button>
    </view>
  </form>
</view>