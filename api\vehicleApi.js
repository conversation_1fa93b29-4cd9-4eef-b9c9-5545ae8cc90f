const REQUEST = require('@/utils/request.js')
const util = require('@/utils/util')
// 分页查询我的车辆列表
function getVehicleList(params ) {

  
  return REQUEST.request('/users-api/v1/member/vehicle/page', 'GET', params, true);
}

// 查询单个车辆信息
function getVehicleById(id) {
  return REQUEST.request('/users-api/v1/member/vehicle?id=', 'GET', { id }, true);
}


// 新增车辆
function addVehicle(vehicleData) {
  // 根据接口文档构建车辆数据
  const communityVehicle = {
    plateNumber: vehicleData.plateNumber,
    vehicleColor: vehicleData.vehicleColor || vehicleData.color,
    parkingType: vehicleData.parkingType || '固定车位',
    parkingNumber: vehicleData.parkingNumber || vehicleData.parkingSpace,
    validBeginTime: vehicleData.validBeginTime || new Date().toISOString(),
    validEndTime: vehicleData.validEndTime || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 默认一年有效期
    note: vehicleData.note || '',
    status: vehicleData.status || 'pending',
    mainUse: vehicleData.mainUse !== undefined ? vehicleData.mainUse : vehicleData.isPrimaryVehicle,
    media: vehicleData.media || vehicleData.drivingLicenseUrl || '',
    communityId: wx.getStorageSync('selectedCommunity').id
  };

  return REQUEST.request('/users-api/v1/member/vehicle', 'POST', communityVehicle, true);
}

// 编辑车辆
function updateVehicle(vehicleData) {
  // 根据接口文档构建车辆数据
  const communityVehicle = {
    id: vehicleData.id,
    plateNumber: vehicleData.plateNumber,
    vehicleColor: vehicleData.vehicleColor || vehicleData.color,
    parkingType: vehicleData.parkingType || '固定车位',
    parkingNumber: vehicleData.parkingNumber || vehicleData.parkingSpace,
    validBeginTime: vehicleData.validBeginTime,
    validEndTime: vehicleData.validEndTime,
    note: vehicleData.note || '',
    status: vehicleData.status,
    mainUse: vehicleData.mainUse !== undefined ? vehicleData.mainUse : vehicleData.isPrimaryVehicle,
    media: vehicleData.media || vehicleData.drivingLicenseUrl || '',
    createTime: vehicleData.createTime,
    updateTime: new Date().toISOString(),
    residentId: vehicleData.residentId,
    communityId: vehicleData.communityId
  };

  return REQUEST.request('/users-api/v1/member/vehicle', 'PUT', communityVehicle, true);
}

// 删除车辆
function deleteVehicle(id) {
  return REQUEST.request('/users-api/v1/member/vehicle?id=' + id, 'DELETE', {}, true);
}

// 格式化车辆数据
function formatVehicleData(vehicle) {
  // 确定状态样式
  let statusClass = '';
  let statusText = vehicle.status;


  var dictList = util.getDictByNameEn('vehicle_status')[0].children

  var dict = dictList.filter(item => item.nameEn === vehicle.status)

  statusText = dict[0].nameCn
  statusClass = dict[0].cssClass


  // 格式化日期
  let formattedDate = '';
  let formattedDateTime = '';
  if (vehicle.createTime) {
    const createDate = new Date(vehicle.createTime);
    formattedDate = `${createDate.getFullYear()}-${String(createDate.getMonth() + 1).padStart(2, '0')}-${String(createDate.getDate()).padStart(2, '0')}`;
    formattedDateTime = `${formattedDate} ${String(createDate.getHours()).padStart(2, '0')}:${String(createDate.getMinutes()).padStart(2, '0')}`;
  }

  // 格式化有效期
  let formattedValidPeriod = '';
  let hasValidPeriod = false;

  if (vehicle.validBeginTime && vehicle.validEndTime) {
    // 都有开始和结束时间
    const beginDate = new Date(vehicle.validBeginTime);
    const endDate = new Date(vehicle.validEndTime);
    const beginStr = `${beginDate.getFullYear()}-${String(beginDate.getMonth() + 1).padStart(2, '0')}-${String(beginDate.getDate()).padStart(2, '0')}`;
    const endStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
    formattedValidPeriod = `有效期：${beginStr} 至 ${endStr}`;
    hasValidPeriod = true;
  } else if (vehicle.validBeginTime) {
    // 只有开始时间
    const beginDate = new Date(vehicle.validBeginTime);
    const beginStr = `${beginDate.getFullYear()}-${String(beginDate.getMonth() + 1).padStart(2, '0')}-${String(beginDate.getDate()).padStart(2, '0')}`;
    formattedValidPeriod = `有效期：${beginStr} 起`;
    hasValidPeriod = true;
  } else if (vehicle.validEndTime) {
    // 只有结束时间
    const endDate = new Date(vehicle.validEndTime);
    const endStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
    formattedValidPeriod = `有效期：至 ${endStr}`;
    hasValidPeriod = true;
  }

  // 根据颜色名称设置颜色值
  let colorValue = '#CCCCCC';
  const color = vehicle.vehicleColor || vehicle.color || '';

  switch (color.trim()) {
    case '白色': colorValue = '#FFFFFF'; break;
    case '黑色': colorValue = '#000000'; break;
    case '银色': colorValue = '#C0C0C0'; break;
    case '灰色': colorValue = '#808080'; break;
    case '红色': colorValue = '#FF0000'; break;
    case '蓝色': colorValue = '#0000FF'; break;
    case '绿色': colorValue = '#008000'; break;
    case '黄色': colorValue = '#FFFF00'; break;
    case '棕色': colorValue = '#A52A2A'; break;
    case '金色': colorValue = '#FFD700'; break;
  }

  // 处理车位类型显示
  let parkingTypeDisplay = '未设置';

  console.log('处理车位类型显示，原始数据:', vehicle.parkingType);

  if (vehicle.parkingType) {
    try {
      const parkingTypes = util.getDictByNameEn('parking_type');
      console.log('获取到的车位类型字典:', parkingTypes);

      if (parkingTypes && parkingTypes.length > 0 && parkingTypes[0].children) {
        // 先尝试按字典值匹配
        let parkingTypeDict = parkingTypes[0].children.find(item =>
          item.dictValue === vehicle.parkingType
        );

        console.log('按字典值匹配结果:', parkingTypeDict);

        // 如果按值没找到，再尝试按标签匹配（兼容旧数据）
        if (!parkingTypeDict) {
          parkingTypeDict = parkingTypes[0].children.find(item =>
            item.dictLabel === vehicle.parkingType
          );
          console.log('按标签匹配结果:', parkingTypeDict);
        }

        // 如果找到了匹配项，使用字典标签
        if (parkingTypeDict) {
          parkingTypeDisplay = parkingTypeDict.dictLabel;
          console.log('最终显示的车位类型:', parkingTypeDisplay);
        } else {
          // 如果都没找到，直接显示原值
          parkingTypeDisplay = vehicle.parkingType;
          console.log('未找到匹配，使用原值:', parkingTypeDisplay);
        }
      } else {
        // 如果没有字典数据，直接显示原值
        parkingTypeDisplay = vehicle.parkingType;
        console.log('没有字典数据，使用原值:', parkingTypeDisplay);
      }
    } catch (error) {
      console.warn('获取车位类型字典失败:', error);
      // 出错时直接显示原值
      parkingTypeDisplay = vehicle.parkingType;
    }
  }

  return {
    ...vehicle,
    statusClass,
    statusText,
    formattedDate,
    formattedDateTime,
    formattedValidPeriod,
    hasValidPeriod,
    colorValue,
    color: vehicle.vehicleColor || vehicle.color || '',
    parkingSpace: vehicle.parkingNumber || vehicle.parkingSpace || '未设置',
    parkingTypeDisplay: parkingTypeDisplay, // 添加车位类型显示字段
    isPrimaryVehicle: vehicle.mainUse !== undefined ? vehicle.mainUse : vehicle.isPrimaryVehicle,
    drivingLicenseUrl: vehicle.media || vehicle.drivingLicenseUrl || ''
  };
}

// 获取固定的颜色选项列表
function getVehicleColors() {
  return [
    { name: '白色', value: '#FFFFFF', code: 'white' },
    { name: '黑色', value: '#000000', code: 'black' },
    { name: '银色', value: '#C0C0C0', code: 'silver' },
    { name: '灰色', value: '#808080', code: 'gray' },
    { name: '红色', value: '#FF0000', code: 'red' },
    { name: '蓝色', value: '#0000FF', code: 'blue' },
    { name: '绿色', value: '#008000', code: 'green' },
    { name: '黄色', value: '#FFFF00', code: 'yellow' },
    { name: '棕色', value: '#A52A2A', code: 'brown' },
    { name: '金色', value: '#FFD700', code: 'gold' }
  ];
}

module.exports = {
  getVehicleList,
  getVehicleById,
  addVehicle,
  updateVehicle,
  deleteVehicle,
  formatVehicleData,
  getVehicleColors
}