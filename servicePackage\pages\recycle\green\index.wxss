/* 绿色循环页面样式 */
.container {
  padding: 0;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}



.banner-content {
  text-align: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #27AE60, #1E8449);
  color: white;
  margin-bottom: 0;
}

/* 轮播口号样式 */
.slogan-swiper {
  height: 80rpx;
  width: 100%;
}

.banner-slogan {
  font-size: 32rpx;
  opacity: 0.9;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Sticky搜索栏 */
.sticky-search {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 56rpx;
  padding: 0 20rpx;
  height: 90rpx;
}

.search-icon {
  margin-right: 10rpx;
  color: #999;
}

.search-input {
  flex: 1;
  height: 90rpx;
  font-size: 28rpx;
}

.search-actions {
  display: flex;
  align-items: center;
}

.voice-btn, .camera-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.voice-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z'%3E%3C/path%3E%3Cpath d='M19 10v2a7 7 0 0 1-14 0v-2'%3E%3C/path%3E%3Cline x1='12' y1='19' x2='12' y2='23'%3E%3C/line%3E%3Cline x1='8' y1='23' x2='16' y2='23'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.camera-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z'%3E%3C/path%3E%3Ccircle cx='12' cy='13' r='4'%3E%3C/circle%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.clear-icon {
  margin-left: 10rpx;
}

/* 搜索结果样式 */
.search-results {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.result-type {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.no-results-icon {
  margin-bottom: 20rpx;
}

.no-results-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.upload-photo-btn {
  font-size: 28rpx;
  color: #27AE60;
  padding: 10rpx 30rpx;
  border: 1rpx solid #27AE60;
  border-radius: 30rpx;
}

/* 首屏核心区 */
.core-section {
  padding: 30rpx;
}

/* 环保成就卡片 */
.achievement-card {
  background: linear-gradient(135deg, #27AE60, #1E8449);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(39, 174, 96, 0.2);
}

.achievement-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-value {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 三个核心功能卡片 */
.core-feature-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.feature-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.2s;
}

.feature-card:active {
  background-color: #f5f5f5;
  transform: scale(0.98);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 32rpx;
}

.guide-icon {
  background-color: rgba(39, 174, 96, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z'%3E%3C/path%3E%3Cpath d='M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z'%3E%3C/path%3E%3C/svg%3E");
}

.map-icon {
  background-color: rgba(39, 174, 96, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6'%3E%3C/polygon%3E%3Cline x1='8' y1='2' x2='8' y2='18'%3E%3C/line%3E%3Cline x1='16' y1='6' x2='16' y2='22'%3E%3C/line%3E%3C/svg%3E");
}

.schedule-icon {
  background-color: rgba(39, 174, 96, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.card-content {
  text-align: center;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

.card-arrow {
  display: none;
}

/* 拍照识别按钮 */
.photo-recognition {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #4caf50;
}

.photo-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z'%3E%3C/path%3E%3Ccircle cx='12' cy='13' r='4'%3E%3C/circle%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.photo-text {
  font-size: 28rpx;
}

/* 搜索结果样式 */
.search-results {
  background-color: #f5f5f5;
  border-radius: 16rpx;
  overflow: hidden;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
}

.result-item:last-child {
  border-bottom: none;
}

.result-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.result-type {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  margin-top: 20rpx;
}

.no-results-icon {
  margin-bottom: 20rpx;
}

.no-results-text {
  font-size: 28rpx;
  color: #999;
}

/* 二级信息区 - 垃圾分类知识 */
.knowledge-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-left {
  display: flex;
  align-items: center;
}

.title-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.toggle-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: transform 0.3s ease-out;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 标题图标样式 - 统一颜色 */
.knowledge-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z'%3E%3C/path%3E%3Cpath d='M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z'%3E%3C/path%3E%3C/svg%3E");
}

.confusion-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
}

.community-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

/* Tab样式 */
.waste-type-tabs {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.waste-type-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  border-bottom: 3rpx solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waste-type-icon-small {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.recyclable-icon-small {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.hazardous-icon-small {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.kitchen-icon-small {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8h1a4 4 0 0 1 0 8h-1'%3E%3C/path%3E%3Cpath d='M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z'%3E%3C/path%3E%3Cline x1='6' y1='1' x2='6' y2='4'%3E%3C/line%3E%3Cline x1='10' y1='1' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='1' x2='14' y2='4'%3E%3C/line%3E%3C/svg%3E");
}

.other-icon-small {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%239e9e9e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='4.93' y1='4.93' x2='19.07' y2='19.07'%3E%3C/line%3E%3C/svg%3E");
}

.waste-type-tab.active {
  font-weight: 600;
}

/* Tab内容区域样式 */
.waste-type-content {
  display: flex;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.waste-type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 40rpx;
  flex-shrink: 0;
}

.recyclable-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='28' height='28' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.hazardous-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='28' height='28' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.kitchen-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='28' height='28' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8h1a4 4 0 0 1 0 8h-1'%3E%3C/path%3E%3Cpath d='M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z'%3E%3C/path%3E%3Cline x1='6' y1='1' x2='6' y2='4'%3E%3C/line%3E%3Cline x1='10' y1='1' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='1' x2='14' y2='4'%3E%3C/line%3E%3C/svg%3E");
}

.other-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='28' height='28' fill='none' stroke='%239e9e9e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='4.93' y1='4.93' x2='19.07' y2='19.07'%3E%3C/line%3E%3C/svg%3E");
}

.waste-type-info {
  flex: 1;
}

.waste-type-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.waste-type-requirements {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.waste-type-examples {
  margin-top: 16rpx;
}

.examples-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.example-item {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(39, 174, 96, 0.1);
  border-radius: 20rpx;
}

/* 辅助区 */
.auxiliary-section {
  margin: 0 30rpx 30rpx;
}

/* 容易混淆专区样式 */
.confusion-section {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
}

.expand-all {
  text-align: right;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #27AE60;
}

.confusion-list {
  background-color: #fff;
  overflow: hidden;
}

.confusion-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.confusion-item:last-child {
  border-bottom: none;
}

.confusion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
}

.confusion-item-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.confusion-arrow {
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: transform 0.3s ease-out;
}

.confusion-arrow.expanded {
  transform: rotate(180deg);
}

.confusion-content {
  padding: 0 24rpx 24rpx;
  background-color: #fff;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.confusion-text {
  margin-bottom: 16rpx;
}

.confusion-types {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.confusion-type {
  display: flex;
  align-items: center;
}

.type-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.type-badge.recyclable {
  background-color: rgba(33, 150, 243, 0.1);
}

.type-badge.hazardous {
  background-color: rgba(244, 67, 54, 0.1);
}

.type-badge.kitchen {
  background-color: rgba(76, 175, 80, 0.1);
}

.type-badge.other {
  background-color: rgba(158, 158, 158, 0.1);
}

.type-icon {
  width: 16rpx;
  height: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.recyclable-icon-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.hazardous-icon-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.kitchen-icon-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8h1a4 4 0 0 1 0 8h-1'%3E%3C/path%3E%3Cpath d='M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z'%3E%3C/path%3E%3Cline x1='6' y1='1' x2='6' y2='4'%3E%3C/line%3E%3Cline x1='10' y1='1' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='1' x2='14' y2='4'%3E%3C/line%3E%3C/svg%3E");
}

.other-icon-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='none' stroke='%239e9e9e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='4.93' y1='4.93' x2='19.07' y2='19.07'%3E%3C/line%3E%3C/svg%3E");
}

.confusion-type-name {
  font-size: 24rpx;
  color: #333;
}

/* 社区互动区域 */
.community-section {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.community-content {
  padding: 24rpx;
}

.community-card {
  background-color: rgba(39, 174, 96, 0.05);
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #27AE60;
}

.community-card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.community-card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 清运时间提醒 */
.schedule-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 30rpx 40rpx;
}

.schedule-card {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}

.schedule-header {
  background-color: #4caf50;
  color: white;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-day {
  font-size: 32rpx;
  font-weight: 600;
}

.schedule-date {
  font-size: 26rpx;
  opacity: 0.9;
}

.schedule-items {
  padding: 20rpx;
}

.schedule-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.schedule-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.schedule-type {
  font-size: 26rpx;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 20rpx;
  min-width: 120rpx;
  text-align: center;
}

.schedule-time {
  font-size: 28rpx;
  color: #333;
}

/* 环保成就 */
.achievement-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 30rpx 40rpx;
}

.achievement-card {
  background: linear-gradient(135deg, #f5f5f5, #fff);
  border-radius: 12rpx;
  padding: 30rpx;
  border: 1rpx solid #eee;
}

.achievement-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #4caf50;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.achievement-badges {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.badge-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.badge-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 60%;
}

.badge-icon.locked {
  opacity: 0.5;
  filter: grayscale(1);
}

.badge-beginner {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='7'%3E%3C/circle%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.badge-recycler {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.badge-expert {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'%3E%3C/path%3E%3C/svg%3E");
}

.badge-carbon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9800' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8h1a4 4 0 0 1 0 8h-1'%3E%3C/path%3E%3Cpath d='M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z'%3E%3C/path%3E%3Cline x1='6' y1='1' x2='6' y2='4'%3E%3C/line%3E%3Cline x1='10' y1='1' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='1' x2='14' y2='4'%3E%3C/line%3E%3C/svg%3E");
}

.badge-master {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.badge-leader {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239c27b0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

.badge-name {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.badge-status {
  font-size: 20rpx;
  color: #999;
}

/* 回收积分样式 */
.points-card {
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  border-radius: 16rpx;
  padding: 40rpx;
  color: white;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.2);
  text-align: center;
}

.points-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.points-value {
  font-size: 72rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.points-tip {
  font-size: 24rpx;
  opacity: 0.8;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.records-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-type {
  width: 160rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.record-info {
  flex: 1;
}

.record-weight {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-points {
  font-size: 32rpx;
  font-weight: 600;
  color: #4caf50;
}

/* 回收站点样式 */
.stations-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.station-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.station-item:last-child {
  border-bottom: none;
}

.station-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  color: #4caf50;
}

.station-info {
  flex: 1;
}

.station-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.station-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.station-meta {
  display: flex;
  justify-content: space-between;
}

.station-distance, .station-time {
  font-size: 24rpx;
  color: #999;
}

/* 清运时间详情 */
.schedule-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.schedule-detail.visible {
  opacity: 1;
  visibility: visible;
}

.schedule-detail-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.schedule-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.schedule-detail-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.schedule-detail-date {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f9f9f9;
}

.schedule-detail-items {
  padding: 16rpx 0;
}

.schedule-detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-detail-item:last-child {
  border-bottom: none;
}

.schedule-type-badge {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.schedule-type-label {
  font-size: 24rpx;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 8rpx;
}

.schedule-detail-time {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 清运时间详情 */
.schedule-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.schedule-detail.visible {
  opacity: 1;
  visibility: visible;
}

.schedule-detail-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.schedule-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.schedule-detail-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.schedule-detail-date {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f9f9f9;
}

.schedule-detail-items {
  padding: 16rpx 0;
}

.schedule-detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-detail-item:last-child {
  border-bottom: none;
}

.schedule-type-badge {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.schedule-type-label {
  font-size: 24rpx;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 8rpx;
}

.schedule-detail-time {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 底部固定按钮区域 */
.fixed-buttons {
  position: fixed;
  bottom: 30rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  z-index: 100;
}

/* 我的预约按钮样式 */
.my-appointment-btn {
  width: 220rpx;
  height: 90rpx;
  background-color: #f5f5f5;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.my-appointment-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.my-appointment-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 预约回收按钮样式 */
.appointment-btn {
  width: 440rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #27AE60, #1E8449);
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(39, 174, 96, 0.3);
}

.appointment-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.appointment-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .banner {
  background-color: #388e3c;
}

.darkMode .feature-card {
  background-color: #2c2c2e;
}

.darkMode .card-title {
  color: #f5f5f7;
}

.darkMode .card-desc {
  color: #8e8e93;
}

.darkMode .feature-card:active {
  background-color: #3a3a3c;
}

.darkMode .quick-search-section,
.darkMode .knowledge-section,
.darkMode .schedule-section,
.darkMode .achievement-section {
  background-color: #2c2c2e;
}

/* 暗黑模式下的Tab样式 - 简化版 */
.darkMode .waste-type-tabs {
  border-bottom-color: #3a3a3c;
}

.darkMode .waste-type-tab {
  color: #8e8e93;
}

.darkMode .waste-type-content {
  border-bottom-color: #3a3a3c;
}

.darkMode .waste-type-description,
.darkMode .examples-title,
.darkMode .confusion-title,
.darkMode .confusion-item-title,
.darkMode .confusion-type-name {
  color: #f5f5f7;
}

.darkMode .waste-type-requirements,
.darkMode .confusion-text {
  color: #8e8e93;
}

.darkMode .confusion-header {
  background-color: #2c2c2e;
}

.darkMode .confusion-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .confusion-list,
.darkMode .confusion-content {
  background-color: #1c1c1e;
}

.darkMode .title-text,
.darkMode .confusion-title,
.darkMode .confusion-title-text {
  color: #f5f5f7;
}

/* 暗黑模式下的图标 - 统一使用亮色 */
.darkMode .title-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3C/svg%3E");
}

.darkMode .title-icon.achievement-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.darkMode .title-icon.search-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
}

.darkMode .title-icon.knowledge-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z'%3E%3C/path%3E%3Cpath d='M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z'%3E%3C/path%3E%3C/svg%3E");
}

.darkMode .title-icon.schedule-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.darkMode .title-icon.confusion-icon-title {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
}

.darkMode .title-desc {
  color: #8e8e93;
}

.darkMode .search-input-wrapper {
  background-color: #3a3a3c;
}

.darkMode .search-input {
  color: #f5f5f7;
}

.darkMode .photo-recognition {
  color: #8bc34a;
}

.darkMode .search-results {
  background-color: #3a3a3c;
}

.darkMode .result-item {
  background-color: #2c2c2e;
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .result-name {
  color: #f5f5f7;
}

.darkMode .no-results {
  background-color: #2c2c2e;
}

.darkMode .no-results-text {
  color: #8e8e93;
}

.darkMode .knowledge-card {
  border-color: #3a3a3c;
}

.darkMode .knowledge-examples text {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .schedule-card {
  background-color: #3a3a3c;
}

.darkMode .schedule-item {
  border-bottom: 1rpx solid #2c2c2e;
}

.darkMode .schedule-time {
  color: #f5f5f7;
}

.darkMode .achievement-card {
  background: linear-gradient(135deg, #2c2c2e, #3a3a3c);
  border-color: #3a3a3c;
}

.darkMode .stat-label {
  color: #8e8e93;
}

.darkMode .badge-icon {
  background-color: #3a3a3c;
}

.darkMode .badge-name {
  color: #8e8e93;
}

.darkMode .badge-status {
  color: #6e6e73;
}

.darkMode .floating-photo-btn {
  background-color: #1976d2;
  box-shadow: 0 6rpx 20rpx rgba(25, 118, 210, 0.3);
}

.darkMode .my-appointment-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.darkMode .my-appointment-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23f5f5f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.darkMode .appointment-btn {
  background: linear-gradient(135deg, #388e3c, #2e7d32);
  box-shadow: 0 4rpx 16rpx rgba(56, 142, 60, 0.2);
}

/* 碳减排树可视化 */
.carbon-tree-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.carbon-tree-modal.visible {
  opacity: 1;
  visibility: visible;
}

.carbon-tree-content {
  width: 90%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.carbon-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.carbon-tree-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.carbon-tree-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.carbon-tree-visualization {
  padding: 30rpx;
}

.carbon-tree-image {
  height: 300rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-5'%3E%3C/path%3E%3Cpath d='M9 7h6'%3E%3C/path%3E%3Cpath d='M11 2h2'%3E%3C/path%3E%3Cpath d='M18 14c0-3.3-2.7-6-6-6s-6 2.7-6 6c0 2.2 1.2 4.1 3 5.2'%3E%3C/path%3E%3Cpath d='M15 14c0-1.7-1.3-3-3-3s-3 1.3-3 3c0 1.1 0.6 2.1 1.5 2.6'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 30rpx;
}

.carbon-tree-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.carbon-stat {
  text-align: center;
}

.carbon-stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #27AE60;
  margin-bottom: 8rpx;
}

.carbon-stat-label {
  font-size: 24rpx;
  color: #666;
}

.carbon-tree-progress {
  margin-bottom: 30rpx;
}

.carbon-progress-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.carbon-progress-fill {
  height: 100%;
  background-color: #27AE60;
  border-radius: 8rpx;
}

.carbon-progress-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.carbon-tree-badges {
  display: flex;
  justify-content: space-around;
}

.carbon-badge {
  text-align: center;
  opacity: 0.5;
}

.carbon-badge.unlocked {
  opacity: 1;
}

.carbon-badge-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 8rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.beginner-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.intermediate-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
}

.expert-badge {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
}

.carbon-badge-name {
  font-size: 24rpx;
  color: #333;
}

/* 加载骨架屏 */
.skeleton-screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 300;
  padding: 0 30rpx;
}

.skeleton-banner {
  height: 160rpx;
  background-color: #f0f0f0;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
}

.skeleton-search {
  height: 90rpx;
  background-color: #f0f0f0;
  margin-bottom: 30rpx;
  border-radius: 45rpx;
}

.skeleton-card {
  height: 160rpx;
  background-color: #f0f0f0;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
}

.skeleton-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.skeleton-feature {
  height: 120rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
}

.skeleton-section {
  height: 300rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
}

/* 暗黑模式适配 */
.darkMode .carbon-tree-content {
  background-color: #2c2c2e;
}

.darkMode .carbon-tree-title {
  color: #f5f5f7;
}

.darkMode .carbon-stat-label {
  color: #8e8e93;
}

.darkMode .carbon-progress-bar {
  background-color: #3a3a3c;
}

.darkMode .carbon-progress-text {
  color: #8e8e93;
}

.darkMode .carbon-badge-name {
  color: #f5f5f7;
}

.darkMode .skeleton-banner,
.darkMode .skeleton-search,
.darkMode .skeleton-card,
.darkMode .skeleton-feature,
.darkMode .skeleton-section {
  background-color: #3a3a3c;
}
