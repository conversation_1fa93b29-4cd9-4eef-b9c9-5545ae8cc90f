/* 设施详情页样式 */

/* 基本容器 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部操作区域预留空间 */
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #ff8c00;
  color: #fff;
  z-index: 100;
  padding-left: 16px;
  padding-right: 16px;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.nav-action {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.edit-icon {
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 4H4C3.44772 4 3 4.44772 3 5V20C3 20.5523 3.44772 21 4 21H19C19.5523 21 20 20.5523 20 20V13' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M18.5 2.5C18.7626 2.23735 19.1131 2.08901 19.4799 2.08901C19.8468 2.08901 20.1973 2.23735 20.46 2.5C20.7226 2.76264 20.871 3.11315 20.871 3.48001C20.871 3.84687 20.7226 4.19738 20.46 4.46L12 13L8 14L9 10L17.5 1.5L18.5 2.5Z' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding-top: 44px; /* 导航栏高度 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 设施图片区域 */
.facility-images {
  margin-top: 44px; /* 导航栏高度 */
  position: relative;
  height: 400rpx;
  background-color: #f0f0f0;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.facility-image {
  width: 100%;
  height: 100%;
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
}

.no-image-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 15L16 10L5 21' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 设施图片占位符 */
.facility-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.facility-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80%;
  height: 80%;
  background-color: #f5f5f5;
  border-radius: 16rpx;
}

.facility-image-placeholder .facility-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.facility-image-placeholder text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.camera-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.icon-camera {
  width: 40rpx;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M23 19C23 19.5304 22.7893 20.0391 22.4142 20.4142C22.0391 20.7893 21.5304 21 21 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V8C1 7.46957 1.21071 6.96086 1.58579 6.58579C1.96086 6.21071 2.46957 6 3 6H7L9 3H15L17 6H21C21.5304 6 22.0391 6.21071 22.4142 6.58579C22.7893 6.96086 23 7.46957 23 8V19Z' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='13' r='4' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 设施核心信息 */
.facility-core-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.facility-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.facility-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.facility-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.facility-status.normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.facility-status.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.facility-status.fault {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.facility-status.offline {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9E9E9E;
}

.facility-status.maintenance {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.facility-code {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.facility-qrcode {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #ff8c00;
  margin-bottom: 20rpx;
}

.qrcode-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 3H10V10H3V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 3H21V10H14V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 14H10V21H3V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 14H21V21H14V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.facility-location, .facility-responsible {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.facility-location:last-child, .facility-responsible:last-child {
  margin-bottom: 0;
}

.location-icon, .person-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.location-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.person-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='7' r='4' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 详细信息版块 */
.facility-details {
  background-color: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-tabs {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.detail-tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.detail-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  right: 25%;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

.detail-content {
  padding: 30rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 20rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #999;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.no-data-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.172 14.828L12.001 12M14.829 9.172L12.001 12M12.001 12L9.172 9.172M12.001 12L14.829 14.828M12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22Z' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.document-item:last-child {
  border-bottom: none;
}

.document-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 2V8H20' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M16 13H8' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M16 17H8' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M10 9H9H8' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.document-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.document-action {
  font-size: 28rpx;
  color: #ff8c00;
}

/* 维护历史卡片 */
.maintenance-history {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: #ff8c00;
  border-radius: 3rpx;
}

.timeline {
  position: relative;
  padding-left: 30rpx;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background-color: #e0e0e0;
}

.timeline-item {
  position: relative;
  margin-bottom: 40rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -30rpx;
  top: 10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  z-index: 1;
}

.timeline-dot.maintenance {
  background-color: #2196F3;
}

.timeline-dot.repair {
  background-color: #F44336;
}

.timeline-dot.inspection {
  background-color: #4CAF50;
}

.timeline-content {
  background-color: #f9f9f9;
  border-radius: 10rpx;
  padding: 20rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.timeline-date {
  font-size: 24rpx;
  color: #999;
}

.timeline-type {
  font-size: 24rpx;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
}

.timeline-type.maintenance {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.timeline-type.repair {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.timeline-type.inspection {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.timeline-description {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.timeline-footer {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

/* 故障信息 */
.fault-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.fault-description {
  font-size: 28rpx;
  color: #F44336;
  background-color: rgba(244, 67, 54, 0.05);
  padding: 20rpx;
  border-radius: 10rpx;
  border-left: 6rpx solid #F44336;
}

/* 底部操作按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-button {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.action-button text {
  font-size: 24rpx;
  color: #333;
}

.repair-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.maintenance-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.inspection-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 11L12 14L22 4M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.status-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 16V12' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 8H12.01' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.monitor-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 8C2 7.44772 2.44772 7 3 7H21C21.5523 7 22 7.44772 22 8V19C22 19.5523 21.5523 20 21 20H3C2.44772 20 2 19.5523 2 19V8Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 8V8C7 5.23858 9.23858 3 12 3V3C14.7614 3 17 5.23858 17 8V8' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='13' r='3' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.control-icon {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 图片查看器 */
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.image-viewer.active {
  opacity: 1;
  pointer-events: auto;
}

.viewer-image {
  max-width: 100%;
  max-height: 100%;
}

.viewer-close {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 30rpx;
  height: 30rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 二维码查看器 */
.qrcode-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.qrcode-viewer.active {
  opacity: 1;
  pointer-events: auto;
}

.qrcode-container {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 30rpx;
}

/* 二维码占位符 */
.qrcode-image-placeholder {
  width: 400rpx;
  height: 400rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.qrcode-placeholder-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 3H10V10H3V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 3H21V10H14V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 14H10V21H3V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 14H21V21H14V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.qrcode-image-placeholder text {
  font-size: 28rpx;
  color: #666;
}

.qrcode-tip {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.qrcode-close {
  width: 400rpx;
  height: 80rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .custom-nav {
  background-color: #ff8c00;
}

.darkMode .facility-core-info,
.darkMode .facility-details,
.darkMode .maintenance-history,
.darkMode .fault-info,
.darkMode .bottom-actions {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .facility-name,
.darkMode .card-title,
.darkMode .detail-value,
.darkMode .document-name,
.darkMode .timeline-description {
  color: #f5f5f7;
}

.darkMode .facility-code,
.darkMode .facility-location,
.darkMode .facility-responsible,
.darkMode .detail-label,
.darkMode .timeline-date,
.darkMode .timeline-footer,
.darkMode .qrcode-tip,
.darkMode .action-button text {
  color: #8e8e93;
}

.darkMode .detail-tabs {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .detail-tab {
  color: #8e8e93;
}

.darkMode .timeline::before {
  background-color: #3a3a3c;
}

.darkMode .timeline-content {
  background-color: #3a3a3c;
}

.darkMode .document-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .qrcode-container {
  background-color: #2c2c2e;
}

.darkMode .qrcode-title {
  color: #f5f5f7;
}
