const REQUEST = require('@/utils/request.js')

// 图文内容API接口
// 基于API文档：/users-api/v1/imagetext

/**
 * 查询图文内容
 * @param {number} id - 图文ID
 * @returns {Promise} 图文信息
 */
function getImagetextById(id) {
  if (!id) {
    return Promise.reject(new Error('图文ID不能为空'))
  }
  
  return REQUEST.request('/users-api/v1/imagetext', 'GET', { id }, true)
}

/**
 * 分页查询图文内容
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @param {string} params.type - 类型（banner：轮播图,notice：通知,about_us：关于我们,privacy_policy：隐私政策）
 * @returns {Promise} 图文列表
 */
function getImagetextList(params = {}) {
  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }
  
  return REQUEST.request('/users-api/v1/imagetext/page', 'GET', defaultParams, true)
}

/**
 * 获取轮播图列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 轮播图列表
 */
function getBannerList(params = {}) {
  const bannerParams = {
    pageNum: 1,
    pageSize: 10,
    type: 'banner', // 固定类型为banner
    ...params
  }
  
  return getImagetextList(bannerParams)
}

/**
 * 获取公告列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 公告列表
 */
function getNoticeList(params = {}) {
  const noticeParams = {
    pageNum: 1,
    pageSize: 10,
    type: 'notice', // 固定类型为notice
    ...params
  }
  
  return getImagetextList(noticeParams)
}

/**
 * 获取新闻列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 新闻列表
 */
function getNewsList(params = {}) {
  const newsParams = {
    pageNum: 1,
    pageSize: 10,
    type: 'news', // 固定类型为news
    ...params
  }
  
  return getImagetextList(newsParams)
}

/**
 * 获取活动列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @returns {Promise} 活动列表
 */
function getActivityList(params = {}) {
  const activityParams = {
    pageNum: 1,
    pageSize: 10,
    type: 'activity', // 固定类型为activity
    ...params
  }
  
  return getImagetextList(activityParams)
}

/**
 * 格式化图文数据
 * @param {Object} imagetext - 原始图文数据
 * @returns {Object} 格式化后的图文数据
 */
function formatImagetext(imagetext) {
  if (!imagetext) return null
  
  return {
    id: imagetext.id,
    title: imagetext.title || '',
    content: imagetext.content || '',
    imageUrl: imagetext.imageUrl || '',
    link: imagetext.link || '',
    type: imagetext.type || '',
    sort: imagetext.sort || 0,
    publisherUserId: imagetext.publisherUserId || null,
    masterType: imagetext.masterType || '',
    masterIds: imagetext.masterIds || null,
    extendData: imagetext.extendData || null,
    createTime: imagetext.createTime || '',
    updateTime: imagetext.updateTime || ''
  }
}

/**
 * 格式化图文列表数据
 * @param {Array} imagetextList - 原始图文列表
 * @returns {Array} 格式化后的图文列表
 */
function formatImagetextList(imagetextList) {
  if (!Array.isArray(imagetextList)) return []
  
  return imagetextList.map(item => formatImagetext(item)).filter(item => item !== null)
}

/**
 * 获取图片完整URL
 * @param {string} imageUrl - 图片相对路径
 * @param {string} baseUrl - 基础URL，默认使用配置的服务器地址
 * @returns {string} 完整的图片URL
 */
function getFullImageUrl(imageUrl, baseUrl = 'http://10.37.13.5:8080') {
  if (!imageUrl) return ''
  
  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }
  
  // 如果是相对路径，拼接基础URL
  const cleanImageUrl = imageUrl.startsWith('/') ? imageUrl : '/' + imageUrl
  return baseUrl + cleanImageUrl
}

/**
 * 处理轮播图数据，添加完整图片URL
 * @param {Array} bannerList - 轮播图列表
 * @returns {Array} 处理后的轮播图列表
 */
function processBannerList(bannerList) {
  if (!Array.isArray(bannerList)) return []
  
  return bannerList.map(banner => ({
    ...formatImagetext(banner),
    fullImageUrl: getFullImageUrl(banner.imageUrl),
    // 为轮播图添加默认属性
    autoplay: true,
    interval: 3000,
    duration: 500
  })).filter(banner => banner.imageUrl) // 过滤掉没有图片的项
}

/**
 * 缓存管理 - 简单的内存缓存
 */
const cache = {
  data: new Map(),
  
  set(key, value, expireTime = 5 * 60 * 1000) { // 默认5分钟过期
    this.data.set(key, {
      value,
      expireTime: Date.now() + expireTime
    })
  },
  
  get(key) {
    const item = this.data.get(key)
    if (item && Date.now() < item.expireTime) {
      return item.value
    }
    this.data.delete(key)
    return null
  },
  
  clear(key) {
    if (key) {
      this.data.delete(key)
    } else {
      this.data.clear()
    }
  }
}


module.exports = {
  getImagetextById,
  getImagetextList,
  getBannerList,
  getNoticeList,
  getNewsList,
  getActivityList,
  formatImagetext,
  formatImagetextList,
  getFullImageUrl,
  processBannerList,
  cache
}
