/* 发布商品页样式 */
.container {
  padding: 30rpx;
  padding-bottom: 150rpx;
  background-color: #f8f8f8;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 20rpx 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #ff8c00;
  color: #ffffff;
}

.step.completed .step-number {
  background-color: #4caf50;
  color: #ffffff;
}

.step-name {
  font-size: 24rpx;
  color: #999;
  transition: all 0.3s;
}

.step.active .step-name, .step.completed .step-name {
  color: #333;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background-color: #f0f0f0;
  position: relative;
  z-index: 1;
  transition: all 0.3s;
}

.step-line.active {
  background-color: #4caf50;
}

/* 步骤内容 */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 5rpx 30rpx 30rpx 30rpx;
  
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center ;
  margin-bottom: 24rpx;

}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-icon .icon {
  width: 40rpx;
  height: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-top: 40rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.required {
  color: #ff4d4f;
  margin-left: 8rpx;
}

/* 图片上传 */
.image-uploader {
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.image-item, .image-add {
  width: 200rpx;
  height: 200rpx;
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 44rpx;
  height: 44rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.image-add {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.add-icon {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

.image-tips {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.tip-icon {
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  line-height: 28rpx;
  text-align: center;
  background-color: #ff8c00;
  color: #fff;
  border-radius: 50%;
  font-size: 20rpx;
  margin-right: 10rpx;
}

/* 商品类型选择器 */
.type-selector {
  display: flex;
  justify-content: space-between;
}

.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  margin: 0 10rpx;
  transition: all 0.3s;
}

.type-option:first-child {
  margin-left: 0;
}

.type-option:last-child {
  margin-right: 0;
}

.type-option.active {
  background-color: rgba(255, 140, 0, 0.1);
  border: 2rpx solid #ff8c00;
}

.type-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-icon .icon {
  width: 48rpx;
  height: 48rpx;
}

.type-name {
  font-size: 28rpx;
  color: #333;
}

/* 分类选择器 */
.category-selector {
  width: 100%;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.item-input, .item-textarea, .picker-view {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: none;
  outline: none;
}

.item-textarea {
  height: 240rpx;
  line-height: 1.6;
}

.input-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-view .placeholder {
  color: #999;
}

.picker-arrow {
  color: #999;
  font-size: 32rpx;
}

/* 价格输入 */
.price-input-wrapper, .rate-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
  font-weight: 500;
}

.price-input, .rate-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.price-input.disabled {
  color: #999;
  background-color: #f0f0f0;
}

/* 富文本编辑器样式 */
.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.toolbar-group {
  display: flex;
  gap: 8rpx;
}

.toolbar-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  font-size: 24rpx;
  font-weight: bold;
}

.toolbar-btn.active {
  background: #ff8c00;
  color: white;
  border-color: #ff8c00;
}

.icon-bold {
  font-weight: bold;
}

.icon-italic {
  font-style: italic;
}

.icon-underline {
  text-decoration: underline;
}

.icon-color {
  font-weight: bold;
}

.icon-size {
  font-size: 20rpx;
}

.icon-text {
  font-size: 20rpx;
}

.rich-editor {
  min-height: 300rpx;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  padding: 20rpx;
}

.editor-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.toggle-btn {
  font-size: 24rpx;
  color: #ff8c00;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff8c00;
  border-radius: 20rpx;
  background: transparent;
}

.rate-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 库存步进器 */
.stock-stepper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  width: 300rpx;
}

.stepper-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
  background-color: #e0e0e0;
}

.stepper-btn.disabled {
  color: #ccc;
}

.stepper-input {
  flex: 1;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

/* 开关 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.item-switch {
  transform: scale(0.9);
}

/* 位置输入 */
.location-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
}

.location-input-wrapper .item-input {
  flex: 1;
  background: none;
  padding: 0;
  margin-right: 10rpx;
}

.location-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.location-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 修复特定输入字段样式 */
.form-section .item-input[name="title"] {
  min-height: 80rpx;
  line-height: 1.4;
}

.form-section .item-input[name="points"] {
  min-height: 80rpx;
  text-align: left;
}

.form-section .item-input[name="location"] {
  min-height: 80rpx;
  line-height: 1.4;
}

/* 确保输入框在不同设备上显示一致 */
.item-input::-webkit-input-placeholder {
  color: #999;
}

.item-input::placeholder {
  color: #999;
}

/* 联系方式选择器 */
.contact-selector {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.contact-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  margin: 0 10rpx;
  transition: all 0.3s;
}

.contact-option:first-child {
  margin-left: 0;
}

.contact-option:last-child {
  margin-right: 0;
}

.contact-option.active {
  background-color: rgba(255, 140, 0, 0.1);
  border: 2rpx solid #ff8c00;
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon .icon {
  width: 48rpx;
  height: 48rpx;
}

.contact-name {
  font-size: 28rpx;
  color: #333;
}

/* 发布须知 */
.notice-content {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.notice-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.notice-dot {
  margin-right: 10rpx;
  color: #ff8c00;
}

.agreement-check {
  margin-top: 20rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

/* 按钮 */
.form-buttons {
  display: flex;
  margin-top: 50rpx;
  padding: 0 20rpx;
}

.form-button {
  flex: 1;

  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 0 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-button.prev {
  background-color: #f5f5f5;
  color: #666;
}

.form-button.next {
  background-color: #ff8c00;
  color: #ffffff;
}

.form-button.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.form-button.submit {
  background-color: #ff8c00;
  color: #ffffff;
}

.form-button[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

/* 暗黑模式 */
.darkMode, page[data-darkmode="true"] {
  background-color: #1c1c1e;
  color: #f5f5f7;
}

.darkMode .step-indicator,
.darkMode .form-section,
page[data-darkmode="true"] .step-indicator,
page[data-darkmode="true"] .form-section {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .step-number,
page[data-darkmode="true"] .step-number {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .step.active .step-number,
page[data-darkmode="true"] .step.active .step-number {
  background-color: #ff9500;
  color: #ffffff;
}

.darkMode .step.completed .step-number,
page[data-darkmode="true"] .step.completed .step-number {
  background-color: #30d158;
  color: #ffffff;
}

.darkMode .step-name,
page[data-darkmode="true"] .step-name {
  color: #8e8e93;
}

.darkMode .step.active .step-name,
.darkMode .step.completed .step-name,
page[data-darkmode="true"] .step.active .step-name,
page[data-darkmode="true"] .step.completed .step-name {
  color: #f5f5f7;
}

.darkMode .step-line,
page[data-darkmode="true"] .step-line {
  background-color: #3a3a3c;
}

.darkMode .step-line.active,
page[data-darkmode="true"] .step-line.active {
  background-color: #30d158;
}

.darkMode .section-title,
.darkMode .item-label,
.darkMode .switch-label,
.darkMode .type-name,
.darkMode .contact-name,
page[data-darkmode="true"] .section-title,
page[data-darkmode="true"] .item-label,
page[data-darkmode="true"] .switch-label,
page[data-darkmode="true"] .type-name,
page[data-darkmode="true"] .contact-name {
  color: #f5f5f7;
}

.darkMode .section-subtitle,
page[data-darkmode="true"] .section-subtitle {
  color: #8e8e93;
}

.darkMode .item-input,
.darkMode .item-textarea,
.darkMode .picker-view,
.darkMode .price-input-wrapper,
.darkMode .rate-input-wrapper,
.darkMode .type-option,
.darkMode .contact-option,
.darkMode .stock-stepper,
.darkMode .location-btn,
.darkMode .image-tips,
.darkMode .notice-content,
page[data-darkmode="true"] .item-input,
page[data-darkmode="true"] .item-textarea,
page[data-darkmode="true"] .picker-view,
page[data-darkmode="true"] .price-input-wrapper,
page[data-darkmode="true"] .rate-input-wrapper,
page[data-darkmode="true"] .type-option,
page[data-darkmode="true"] .contact-option,
page[data-darkmode="true"] .stock-stepper,
page[data-darkmode="true"] .location-btn,
page[data-darkmode="true"] .image-tips,
page[data-darkmode="true"] .notice-content {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .stepper-btn,
page[data-darkmode="true"] .stepper-btn {
  background-color: #2c2c2e;
  color: #f5f5f7;
}

.darkMode .stepper-btn.disabled,
page[data-darkmode="true"] .stepper-btn.disabled {
  color: #636366;
}

.darkMode .type-option.active,
.darkMode .contact-option.active,
page[data-darkmode="true"] .type-option.active,
page[data-darkmode="true"] .contact-option.active {
  background-color: rgba(255, 149, 0, 0.2);
  border: 2rpx solid #ff9500;
}

.darkMode .price-symbol,
.darkMode .price-input,
.darkMode .rate-input,
.darkMode .stepper-input,
page[data-darkmode="true"] .price-symbol,
page[data-darkmode="true"] .price-input,
page[data-darkmode="true"] .rate-input,
page[data-darkmode="true"] .stepper-input {
  color: #f5f5f7;
}

.darkMode .rate-text,
page[data-darkmode="true"] .rate-text {
  color: #aeaeb2;
}

.darkMode .image-add,
page[data-darkmode="true"] .image-add {
  background-color: #3a3a3c;
  border-color: #636366;
}

.darkMode .add-icon,
.darkMode .add-text,
.darkMode .image-tips,
.darkMode .input-count,
.darkMode .picker-arrow,
.darkMode .picker-view .placeholder,
.darkMode .notice-item,
.darkMode .checkbox-label,
page[data-darkmode="true"] .add-icon,
page[data-darkmode="true"] .add-text,
page[data-darkmode="true"] .image-tips,
page[data-darkmode="true"] .input-count,
page[data-darkmode="true"] .picker-arrow,
page[data-darkmode="true"] .picker-view .placeholder,
page[data-darkmode="true"] .notice-item,
page[data-darkmode="true"] .checkbox-label {
  color: #8e8e93;
}

.darkMode .tip-icon,
page[data-darkmode="true"] .tip-icon {
  background-color: #ff9500;
}

.darkMode .notice-dot,
page[data-darkmode="true"] .notice-dot {
  color: #ff9500;
}

.darkMode .form-button.prev,
.darkMode .form-button.cancel,
page[data-darkmode="true"] .form-button.prev,
page[data-darkmode="true"] .form-button.cancel {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .form-button.next,
.darkMode .form-button.submit,
page[data-darkmode="true"] .form-button.next,
page[data-darkmode="true"] .form-button.submit {
  background-color: #ff9500;
  color: #ffffff;
}

.darkMode .form-button[disabled],
page[data-darkmode="true"] .form-button[disabled] {
  background-color: #3a3a3c;
  color: #636366;
}

/* 左右对齐布局 */
.form-item-horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-label-left {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 160rpx;
}

/* 右侧组件样式 */
.type-selector-right {
  display: flex;
  gap: 16rpx;
  flex: 1;
  justify-content: flex-end;
}

.type-selector-right .type-option {
  flex: 0 0 auto;
  min-width: 120rpx;
  margin: 0;
  padding: 6rpx 10rpx;
}

.category-selector-right {
  flex: 1;
  max-width: 300rpx;
}

.category-selector-right .picker-view {
  margin: 0;
  padding: 16rpx 20rpx;
  min-width: 200rpx;
}

.price-input-wrapper-right {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  flex: 1;
  max-width: 200rpx;
}

.stock-stepper-right {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  width: 200rpx;
}

/* 过期时间设置样式 */
.expire-time-wrapper-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  justify-content: flex-end;
}

.expire-time-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  width: 160rpx;
}

.expire-time-input .stepper-btn {
  width: 50rpx;
  height: 60rpx;
  font-size: 28rpx;
}

.expire-time-input .stepper-input {
  flex: 1;
  height: 60rpx;
  font-size: 24rpx;
}

.expire-time-unit {
  flex-shrink: 0;
}

.picker-view-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  min-width: 100rpx;
  font-size: 26rpx;
}

.picker-view-small .picker-arrow {
  font-size: 24rpx;
  margin-left: 8rpx;
}

/* 暗黑模式适配 */
.darkMode .form-item-horizontal,
page[data-darkmode="true"] .form-item-horizontal {
  border-bottom-color: #3a3a3c;
}

.darkMode .item-label-left,
page[data-darkmode="true"] .item-label-left {
  color: #f5f5f7;
}

.darkMode .price-input-wrapper-right,
.darkMode .expire-time-input,
.darkMode .picker-view-small,
.darkMode .stock-stepper-right,
page[data-darkmode="true"] .price-input-wrapper-right,
page[data-darkmode="true"] .expire-time-input,
page[data-darkmode="true"] .picker-view-small,
page[data-darkmode="true"] .stock-stepper-right {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .expire-time-input .stepper-btn,
page[data-darkmode="true"] .expire-time-input .stepper-btn {
  background-color: #2c2c2e;
  color: #f5f5f7;
}
