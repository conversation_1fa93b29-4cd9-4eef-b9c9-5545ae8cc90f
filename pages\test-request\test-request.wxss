/* 请求队列管理测试页面样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
}

.container {
  padding: 20px;
  padding-bottom: 40px;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 区域标题 */
.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 测试按钮区域 */
.test-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.test-button {
  height: 48px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.test-button.primary {
  background: #007AFF;
  color: white;
}

.test-button.primary:active {
  background: #0056CC;
}

.test-button.warning {
  background: #FF9500;
  color: white;
}

.test-button.warning:active {
  background: #CC7700;
}

.test-button.danger {
  background: #FF3B30;
  color: white;
}

.test-button.danger:active {
  background: #CC2E26;
}

.test-button.info {
  background: #5AC8FA;
  color: white;
}

.test-button.info:active {
  background: #48A0C8;
}

.test-button:disabled {
  opacity: 0.6;
  transform: none !important;
}

/* 工具按钮区域 */
.tool-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-button {
  height: 40px;
  background: #F2F2F7;
  color: #333;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.tool-button:active {
  background: #E5E5EA;
}

/* 测试结果区域 */
.results-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.results-count {
  font-size: 14px;
  color: #666;
}

.results-list {
  max-height: 400px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  background: #FAFAFA;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #E5E5EA;
  transition: background-color 0.2s ease;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:hover {
  background-color: #F0F0F0;
}

.result-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.result-item.success .result-icon {
  background: #E3F9E5;
  color: #34C759;
}

.result-item.error .result-icon {
  background: #FFE5E5;
  color: #FF3B30;
}

.result-item.warning .result-icon {
  background: #FFF3E0;
  color: #FF9500;
}

.result-item.info .result-icon {
  background: #E3F2FD;
  color: #007AFF;
}

.result-message {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  word-break: break-all;
}

.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-results text:first-child {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.empty-results text:last-child {
  font-size: 14px;
  color: #999;
}

/* 说明区域 */
.info-section {
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
  background: #F8F9FA;
  border-radius: 8px;
  padding: 16px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 120px;
}

.info-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 16px;
  }
  
  .title {
    font-size: 20px;
  }
}
