/**
 * 工单提醒管理工具类
 * 用于管理工单提醒，包括截止时间提醒和状态变更通知
 */

// 提醒类型
const REMINDER_TYPES = {
  DEADLINE: 'deadline', // 截止时间提醒
  STATUS_CHANGE: 'status_change' // 状态变更通知
};

// 提醒状态
const REMINDER_STATUS = {
  PENDING: 'pending', // 待提醒
  SENT: 'sent', // 已发送
  READ: 'read' // 已读
};

/**
 * 初始化提醒数据
 */
function initReminders() {
  const reminders = wx.getStorageSync('workorder_reminders');
  
  if (!reminders) {
    wx.setStorageSync('workorder_reminders', []);
  }
}

/**
 * 获取所有提醒
 * @param {Object} options 过滤选项
 * @returns {Array} 提醒列表
 */
function getReminders(options = {}) {
  const reminders = wx.getStorageSync('workorder_reminders') || [];
  
  // 过滤提醒
  return reminders.filter(reminder => {
    // 按类型过滤
    if (options.type && reminder.type !== options.type) {
      return false;
    }
    
    // 按状态过滤
    if (options.status && reminder.status !== options.status) {
      return false;
    }
    
    // 按工单ID过滤
    if (options.orderId && reminder.orderId !== options.orderId) {
      return false;
    }
    
    return true;
  });
}

/**
 * 添加提醒
 * @param {Object} reminder 提醒对象
 * @returns {Promise<Array>} 更新后的提醒列表
 */
function addReminder(reminder) {
  return new Promise((resolve, reject) => {
    try {
      if (!reminder.type || !Object.values(REMINDER_TYPES).includes(reminder.type)) {
        reject(new Error('无效的提醒类型'));
        return;
      }
      
      if (!reminder.orderId) {
        reject(new Error('工单ID不能为空'));
        return;
      }
      
      const reminders = wx.getStorageSync('workorder_reminders') || [];
      
      // 生成唯一ID
      const id = `reminder_${Date.now()}`;
      
      // 创建提醒对象
      const newReminder = {
        id,
        type: reminder.type,
        orderId: reminder.orderId,
        orderTitle: reminder.orderTitle || '',
        content: reminder.content || '',
        status: REMINDER_STATUS.PENDING,
        createTime: new Date().toISOString(),
        remindTime: reminder.remindTime || new Date().toISOString(),
        ...reminder
      };
      
      // 添加到列表
      reminders.push(newReminder);
      
      // 保存到本地存储
      wx.setStorageSync('workorder_reminders', reminders);
      
      resolve(reminders);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 更新提醒状态
 * @param {string} id 提醒ID
 * @param {string} status 提醒状态
 * @returns {Promise<Object>} 更新后的提醒对象
 */
function updateReminderStatus(id, status) {
  return new Promise((resolve, reject) => {
    try {
      if (!id) {
        reject(new Error('提醒ID不能为空'));
        return;
      }
      
      if (!Object.values(REMINDER_STATUS).includes(status)) {
        reject(new Error('无效的提醒状态'));
        return;
      }
      
      const reminders = wx.getStorageSync('workorder_reminders') || [];
      
      // 查找提醒
      const index = reminders.findIndex(r => r.id === id);
      
      if (index === -1) {
        reject(new Error('提醒不存在'));
        return;
      }
      
      // 更新状态
      reminders[index].status = status;
      
      // 如果是已读状态，添加读取时间
      if (status === REMINDER_STATUS.READ) {
        reminders[index].readTime = new Date().toISOString();
      }
      
      // 保存到本地存储
      wx.setStorageSync('workorder_reminders', reminders);
      
      resolve(reminders[index]);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 删除提醒
 * @param {string} id 提醒ID
 * @returns {Promise<Array>} 更新后的提醒列表
 */
function deleteReminder(id) {
  return new Promise((resolve, reject) => {
    try {
      if (!id) {
        reject(new Error('提醒ID不能为空'));
        return;
      }
      
      const reminders = wx.getStorageSync('workorder_reminders') || [];
      
      // 过滤掉要删除的提醒
      const updatedReminders = reminders.filter(r => r.id !== id);
      
      // 保存到本地存储
      wx.setStorageSync('workorder_reminders', updatedReminders);
      
      resolve(updatedReminders);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 清除所有已读提醒
 * @returns {Promise<Array>} 更新后的提醒列表
 */
function clearReadReminders() {
  return new Promise((resolve, reject) => {
    try {
      const reminders = wx.getStorageSync('workorder_reminders') || [];
      
      // 过滤掉已读提醒
      const updatedReminders = reminders.filter(r => r.status !== REMINDER_STATUS.READ);
      
      // 保存到本地存储
      wx.setStorageSync('workorder_reminders', updatedReminders);
      
      resolve(updatedReminders);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 创建工单截止时间提醒
 * @param {string} orderId 工单ID
 * @param {string} orderTitle 工单标题
 * @param {string} deadlineTime 截止时间
 * @param {number} advanceMinutes 提前多少分钟提醒
 * @returns {Promise<Object>} 创建的提醒对象
 */
function createDeadlineReminder(orderId, orderTitle, deadlineTime, advanceMinutes = 60) {
  return new Promise((resolve, reject) => {
    try {
      if (!orderId) {
        reject(new Error('工单ID不能为空'));
        return;
      }
      
      if (!deadlineTime) {
        reject(new Error('截止时间不能为空'));
        return;
      }
      
      // 计算提醒时间
      const deadline = new Date(deadlineTime);
      const remindTime = new Date(deadline.getTime() - advanceMinutes * 60 * 1000);
      
      // 创建提醒内容
      const content = `工单"${orderTitle || orderId}"即将到期，请及时处理。`;
      
      // 创建提醒对象
      const reminder = {
        type: REMINDER_TYPES.DEADLINE,
        orderId,
        orderTitle,
        content,
        remindTime: remindTime.toISOString(),
        deadlineTime
      };
      
      // 添加提醒
      addReminder(reminder)
        .then(reminders => {
          const newReminder = reminders.find(r => 
            r.type === REMINDER_TYPES.DEADLINE && 
            r.orderId === orderId && 
            r.deadlineTime === deadlineTime
          );
          
          resolve(newReminder);
        })
        .catch(error => {
          reject(error);
        });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 创建工单状态变更通知
 * @param {string} orderId 工单ID
 * @param {string} orderTitle 工单标题
 * @param {string} oldStatus 旧状态
 * @param {string} newStatus 新状态
 * @param {string} operator 操作人
 * @returns {Promise<Object>} 创建的提醒对象
 */
function createStatusChangeNotification(orderId, orderTitle, oldStatus, newStatus, operator) {
  return new Promise((resolve, reject) => {
    try {
      if (!orderId) {
        reject(new Error('工单ID不能为空'));
        return;
      }
      
      if (!oldStatus || !newStatus) {
        reject(new Error('工单状态不能为空'));
        return;
      }
      
      // 获取状态名称
      const getStatusName = (status) => {
        switch (status) {
          case 'pending': return '待处理';
          case 'processing': return '处理中';
          case 'completed': return '已完成';
          case 'cancelled': return '已取消';
          default: return status;
        }
      };
      
      // 创建提醒内容
      const content = `工单"${orderTitle || orderId}"状态已从"${getStatusName(oldStatus)}"变更为"${getStatusName(newStatus)}"${operator ? `，操作人：${operator}` : ''}。`;
      
      // 创建提醒对象
      const reminder = {
        type: REMINDER_TYPES.STATUS_CHANGE,
        orderId,
        orderTitle,
        content,
        oldStatus,
        newStatus,
        operator
      };
      
      // 添加提醒
      addReminder(reminder)
        .then(reminders => {
          const newReminder = reminders[reminders.length - 1];
          resolve(newReminder);
        })
        .catch(error => {
          reject(error);
        });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 检查并发送待处理的提醒
 * @returns {Promise<Array>} 发送的提醒列表
 */
function checkAndSendReminders() {
  return new Promise((resolve, reject) => {
    try {
      // 获取所有待处理的提醒
      const pendingReminders = getReminders({ status: REMINDER_STATUS.PENDING });
      const now = new Date();
      const sentReminders = [];
      
      // 遍历待处理的提醒
      pendingReminders.forEach(reminder => {
        // 对于截止时间提醒，检查是否到达提醒时间
        if (reminder.type === REMINDER_TYPES.DEADLINE) {
          const remindTime = new Date(reminder.remindTime);
          
          // 如果已到达提醒时间，发送提醒
          if (remindTime <= now) {
            // 发送提醒
            wx.showToast({
              title: reminder.content,
              icon: 'none',
              duration: 3000
            });
            
            // 更新提醒状态为已发送
            updateReminderStatus(reminder.id, REMINDER_STATUS.SENT);
            
            sentReminders.push(reminder);
          }
        }
        
        // 对于状态变更通知，直接发送
        if (reminder.type === REMINDER_TYPES.STATUS_CHANGE) {
          // 发送通知
          wx.showToast({
            title: reminder.content,
            icon: 'none',
            duration: 3000
          });
          
          // 更新提醒状态为已发送
          updateReminderStatus(reminder.id, REMINDER_STATUS.SENT);
          
          sentReminders.push(reminder);
        }
      });
      
      resolve(sentReminders);
    } catch (error) {
      reject(error);
    }
  });
}

// 导出模块
module.exports = {
  REMINDER_TYPES,
  REMINDER_STATUS,
  initReminders,
  getReminders,
  addReminder,
  updateReminderStatus,
  deleteReminder,
  clearReadReminders,
  createDeadlineReminder,
  createStatusChangeNotification,
  checkAndSendReminders
};
