<!--earn.wxml-->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 顶部信息 -->
  <view class="header">
    <view class="title-row">
      <view class="title">赚积分</view>
      <view class="level-tag" wx:if="{{userLevel}}">{{userLevel.name}}</view>
    </view>
    <view class="subtitle">完成任务获取积分奖励</view>
  </view>

  <!-- 积分信息 -->
  <view class="points-info">
    <view class="points-content">
      <view class="points-label">当前积分</view>
      <view class="points-value">{{userPoints}}</view>
    </view>
    <navigator url="/pages/points/level/level" class="level-link">
      <text>等级特权</text>
      <text class="arrow">></text>
    </navigator>
  </view>

  <!-- 任务类型选择 -->
  <scroll-view scroll-x="true" class="task-types-scroll">
    <view class="task-types">
      <view class="task-type {{currentType === item.id ? 'active' : ''}}"
            wx:for="{{taskTypes}}"
            wx:key="id"
            bindtap="switchTaskType"
            data-type="{{item.id}}">
        {{item.name}}
      </view>
    </view>
  </scroll-view>

  <!-- 任务列表 -->
  <view class="tasks-list">
    <view class="task-card {{item.status}}" wx:for="{{filteredTasks}}" wx:key="id">
      <!-- 任务图标 -->
      <view class="task-icon">
        <view class="task-icon-svg {{item.icon}}-icon"></view>
      </view>

      <!-- 任务信息 -->
      <view class="task-info">
        <view class="task-header">
          <view class="task-title">{{item.title}}</view>
          <view class="task-status" wx:if="{{item.status === 'completed'}}">已完成</view>
          <view class="task-status limit" wx:elif="{{item.status === 'limit_reached'}}">已达上限</view>
          <view class="task-status progress" wx:elif="{{item.status === 'in_progress'}}">进行中</view>
        </view>
        <view class="task-desc">{{item.description}}</view>

        <!-- 任务进度 -->
        <view class="task-progress" wx:if="{{item.progress}}">
          <view class="progress-bar">
            <view class="progress-inner" style="width: {{item.progress.percent}}%"></view>
          </view>
          <view class="progress-text">{{item.progress.current}}/{{item.progress.target}}</view>
        </view>

        <!-- 连续任务信息 -->
        <view class="task-continuous" wx:if="{{item.continuous && item.continuous.enabled}}">
          <view class="continuous-text">已连续{{item.continuous.currentDays}}天</view>
          <view class="continuous-next" wx:if="{{item.continuous.nextRewardDays > 0}}">
            再连续{{item.continuous.nextRewardDays}}天可获得额外奖励
          </view>
        </view>

        <view class="task-points">+{{item.points}}积分</view>
      </view>

      <!-- 任务操作 -->
      <view class="task-action">
        <button class="task-btn {{item.status}}"
                disabled="{{item.status === 'completed' || item.status === 'limit_reached'}}"
                bindtap="completeTask"
                data-id="{{item.id}}"
                size="mini">
          {{item.buttonText}}
        </button>
      </view>
    </view>

    <!-- 空任务提示 -->
    <view class="empty-tasks" wx:if="{{filteredTasks.length === 0}}">
      <view class="empty-icon"></view>
      <text>暂无任务</text>
    </view>
  </view>

  <!-- 积分获取小贴士 -->
  <view class="tips-header" bindtap="toggleTips">
    <view class="tips-title">积分获取小贴士</view>
    <view class="tips-toggle">{{showTips ? '收起' : '展开'}}</view>
  </view>

  <view class="tips" wx:if="{{showTips}}">
    <view class="tips-content">
      <view class="tip-item">1. 每日签到可获得5积分，连续签到7天额外奖励30积分</view>
      <view class="tip-item">2. 连续签到30天额外奖励150积分</view>
      <view class="tip-item">3. 完善个人资料可获得一次性奖励20积分</view>
      <view class="tip-item">4. 浏览社区内容每次可获得1积分，每日上限10积分</view>
      <view class="tip-item">5. 发布社区帖子每次可获得5积分，每日上限15积分</view>
      <view class="tip-item">6. 按时缴纳物业费可获得30积分</view>
      <view class="tip-item">7. 提前10天以上缴费可获得额外10积分</view>
      <view class="tip-item">8. 消费1元返1积分，积分四舍五入到整数</view>
      <view class="tip-item">9. 积分有效期为获取日起1年</view>
    </view>
  </view>
</view>
