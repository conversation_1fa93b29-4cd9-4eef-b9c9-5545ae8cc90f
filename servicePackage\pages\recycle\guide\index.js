// pages/recycle/guide/index.js
Page({
  data: {
    darkMode: false,
    currentTipIndex: 0,
    recycleTypes: [
      {
        id: 'recyclable',
        name: '可回收物',
        color: '#2196f3',
        icon: 'recyclable',
        examples: ['纸张', '塑料', '金属', '玻璃', '织物']
      },
      {
        id: 'hazardous',
        name: '有害垃圾',
        color: '#f44336',
        icon: 'hazardous',
        examples: ['电池', '灯管', '药品', '油漆', '化妆品']
      },
      {
        id: 'kitchen',
        name: '厨余垃圾',
        color: '#4caf50',
        icon: 'kitchen',
        examples: ['食物残渣', '果皮', '茶叶', '骨头', '花卉']
      },
      {
        id: 'other',
        name: '其他垃圾',
        color: '#9e9e9e',
        icon: 'other',
        examples: ['卫生纸', '尿不湿', '烟头', '陶瓷', '污染物']
      }
    ],
    recycleTips: [
      {
        id: 1,
        content: '干电池、纽扣电池等有害垃圾应单独投放，不要与其他垃圾混合',
        color: '#f44336'
      },
      {
        id: 2,
        content: '塑料瓶、易拉罐等可回收物应清空内容物，并尽量压扁后投放',
        color: '#2196f3'
      },
      {
        id: 3,
        content: '厨余垃圾应沥干水分后再投放，减少异味和细菌滋生',
        color: '#4caf50'
      },
      {
        id: 4,
        content: '玻璃制品破碎后应用纸包好再投放，防止割伤清洁工人',
        color: '#2196f3'
      },
      {
        id: 5,
        content: '过期药品应去药店或医院的专用回收箱投放，不要随意丢弃',
        color: '#f44336'
      }
    ]
  },

  onLoad: function() {
    // 页面加载时的初始化
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 导航到AI拍照识别页面
  navigateToCamera: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/camera/index'
    })
  },

  // 导航到每日5题闯关页面
  navigateToQuiz: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/quiz/index'
    })
  },

  // 导航到附近回收点页面
  navigateToRecyclePoints: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/map/index'
    })
  },

  // 导航到我的贡献页面
  navigateToContribution: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/recycle/contribution/index'
    })
  },

  // 显示垃圾分类详情
  showTypeDetail: function(e) {
    const typeId = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/servicePackage/pages/recycle/guide/detail?type=${typeId}`
    })
  },

  // 小贴士轮播切换事件
  onTipChange: function(e) {
    this.setData({
      currentTipIndex: e.detail.current
    })
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    })
  }
})
