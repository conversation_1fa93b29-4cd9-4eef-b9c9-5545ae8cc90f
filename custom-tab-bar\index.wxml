<!--custom-tab-bar/index.wxml-->
<view class="tab-bar {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="tab-bar-border"></view>
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item {{selected === index ? 'active' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <view class="tab-icon-container">
      <view class="tab-icon {{item.iconName}}-icon {{selected === index ? 'active' : ''}}"></view>
      <!-- 消息角标 -->
      <view class="message-badge" wx:if="{{item.iconName === 'message' && totalUnreadCount > 0}}">
        {{totalUnreadCount > 99 ? '99+' : totalUnreadCount}}
      </view>
    </view>
    <view class="tab-text {{selected === index ? 'active' : ''}}">{{item.text}}</view>
  </view>
</view>
