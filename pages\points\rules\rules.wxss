/* rules.wxss */
/* 容器样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #222;
  color: #fff;
}

.dark-mode .title,
.dark-mode .rule-title {
  color: #fff;
}

.dark-mode .subtitle,
.dark-mode .rule-content,
.dark-mode .contact-text,
.dark-mode .tooltip-content {
  color: #aaa;
}

.dark-mode .rule-section,
.dark-mode .contact-support {
  background-color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.dark-mode .rule-categories {
  background-color: #444;
}

.dark-mode .rule-category {
  color: #aaa;
}

.dark-mode .rule-category.active {
  background-color: #555;
  color: #ff8c00;
}

.dark-mode .rule-tooltip {
  background-color: #444;
}

.dark-mode .rule-tooltip-icon {
  background-color: #555;
  color: #ff8c00;
}

.dark-mode .rule-badge {
  background-color: rgba(255, 140, 0, 0.2);
  color: #ff8c00;
}

/* 顶部信息 */
.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 规则分类标签 */
.rule-categories {
  display: flex;
  margin-bottom: 30rpx;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 8rpx;
  flex-wrap: wrap;
}

.rule-category {
  flex: 1;
  min-width: 25%;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 32rpx;
}

.rule-category.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 规则内容 */
.rules-content {
  margin-bottom: 40rpx;
}

.rule-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

.rule-section.important {
  border-left: 6rpx solid #ff8c00;
}

.rule-section.expanded {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 规则标题栏 */
.rule-header {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.rule-title-wrap {
  display: flex;
  align-items: center;
  flex: 1;
}

.rule-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.rule-badge {
  margin-left: 16rpx;
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.rule-toggle {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

/* 规则内容区 */
.rule-body {
  padding: 0 30rpx 24rpx;
  position: relative;
}

.rule-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
  margin-bottom: 16rpx;
}

/* 提示图标和内容 */
.rule-tooltip-wrap {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.rule-tooltip-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.rule-tooltip {
  position: absolute;
  bottom: 60rpx;
  right: 0;
  width: 400rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  opacity: 0;
  transform: translateY(10rpx);
  transition: all 0.3s ease;
  pointer-events: none;
}

.rule-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

.tooltip-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  white-space: pre-line;
}

.tooltip-arrow {
  position: absolute;
  bottom: -16rpx;
  right: 20rpx;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid #fff;
}

.tooltip-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}

/* 联系客服 */
.contact-support {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.contact-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.contact-btn {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
  box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.2);
}
