/* community-select.wxss */
.container {
  padding: 20rpx;
}

.search-bar {
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-icon {
  margin-left: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 30rpx 0 20rpx;
  padding-left: 10rpx;
}

.community-list {
  margin-bottom: 30rpx;
}

.community-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.community-info {
  flex: 1;
}

.community-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.community-address {
  font-size: 26rpx;
  color: #999;
}

.community-distance {
  font-size: 26rpx;
  color: #666;
  margin-left: 20rpx;
}

.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-result-icon {
  margin-bottom: 20rpx;
}

.no-result-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
