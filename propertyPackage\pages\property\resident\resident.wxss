/* 居民管理主页样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 40rpx;
}

/* 头部区域 */
.header {
  padding: 20rpx 32rpx 40rpx;
  background: linear-gradient(to bottom, #FF8C00, #FFA940);
  color: #FFFFFF;
  border-radius: 0 0 32rpx 32rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 统计卡片样式 */
.stats-card {
  margin: -30rpx 32rpx 32rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
}

.stats-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16rpx;
}

.stats-more {
  font-size: 28rpx;
  color: #FF8C00;
  display: flex;
  align-items: center;
}

.stats-more::after {
  content: '';
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #FF8C00;
  border-right: 2rpx solid #FF8C00;
  transform: rotate(45deg);
  margin-left: 8rpx;
}

.stats-content {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
}

.stats-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF8C00;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 快速操作区样式 */
.quick-actions {
  margin: 0 32rpx 32rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 40rpx 40rpx;
}

.review-icon {
  background-color: rgba(255, 140, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.search-icon {
  background-color: rgba(0, 122, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23007AFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
}

.add-icon {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='8.5' cy='7' r='4'%3E%3C/circle%3E%3Cline x1='20' y1='8' x2='20' y2='14'%3E%3C/line%3E%3Cline x1='23' y1='11' x2='17' y2='11'%3E%3C/line%3E%3C/svg%3E");
}

.stats-icon {
  background-color: rgba(255, 69, 58, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF453A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 20V10'%3E%3C/path%3E%3Cpath d='M12 20V4'%3E%3C/path%3E%3Cpath d='M6 20v-6'%3E%3C/path%3E%3C/svg%3E");
}

.action-label {
  font-size: 26rpx;
  color: #000000;
}

/* 最近活动样式 */
.recent-activities {
  margin: 0 32rpx 32rpx;
}

.activity-list {
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 40rpx 40rpx;
}

.icon-identity {
  background-color: rgba(255, 140, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='16' rx='2'%3E%3C/rect%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Ccircle cx='12' cy='11' r='3'%3E%3C/circle%3E%3Cpath d='M17 18.5c-1.4-1-3.1-1.5-5-1.5s-3.6.5-5 1.5'%3E%3C/path%3E%3C/svg%3E");
}

.icon-contact {
  background-color: rgba(0, 122, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23007AFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
}

.icon-vehicle {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='1' y='6' width='22' height='12' rx='2'%3E%3C/rect%3E%3Cpath d='M4 12h16'%3E%3C/path%3E%3Cpath d='M7 18v2'%3E%3C/path%3E%3Cpath d='M17 18v2'%3E%3C/path%3E%3Cpath d='M7 6v2'%3E%3C/path%3E%3Cpath d='M17 6v2'%3E%3C/path%3E%3C/svg%3E");
}

.icon-house {
  background-color: rgba(255, 69, 58, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF453A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.activity-time {
  font-size: 24rpx;
  color: #8E8E93;
}

.activity-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  font-size: 24rpx;
  color: #007AFF;
}

.activity-detail {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.empty-state {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.icon-vehicle {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0'%3E%3C/path%3E%3Cpath d='M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0'%3E%3C/path%3E%3Cpath d='M5 17h-2v-6l2-5h9l4 5h1a2 2 0 0 1 2 2v4h-2m-4 0h-6m-6 -6h15m-6 0v-5'%3E%3C/path%3E%3C/svg%3E");
}
