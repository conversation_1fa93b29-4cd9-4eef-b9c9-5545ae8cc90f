<!--设施报修页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">设施报修</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 报修表单 -->
  <block wx:if="{{!isLoading && facility}}">
    <!-- 设施信息 -->
    <view class="facility-info">
      <view class="facility-name">{{facility.name}}</view>
      <view class="facility-meta">
        <view class="facility-code">编号: {{facility.code}}</view>
        <view class="facility-status {{facility.status}}">{{facility.statusText}}</view>
      </view>
      <view class="facility-location">
        <view class="location-icon"></view>
        <text>{{facility.location}}</text>
      </view>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 问题描述 -->
      <view class="form-group">
        <view class="form-label required">问题描述</view>
        <view class="form-control">
          <textarea 
            class="form-textarea {{formErrors.problemDescription ? 'error' : ''}}" 
            placeholder="请详细描述设施故障或问题" 
            value="{{formData.problemDescription}}" 
            bindinput="onInputChange" 
            data-field="problemDescription"
            maxlength="500"
          ></textarea>
          <view class="error-message" wx:if="{{formErrors.problemDescription}}">{{formErrors.problemDescription}}</view>
        </view>
      </view>
      
      <!-- 图片上传 -->
      <view class="form-group">
        <view class="form-label">问题图片</view>
        <view class="form-control">
          <view class="upload-container">
            <view class="upload-list">
              <view class="upload-item" wx:for="{{uploadedImages}}" wx:key="index">
                <image class="upload-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
                <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}"></view>
              </view>
              <view class="upload-button" bindtap="uploadImage" wx:if="{{uploadedImages.length < 9}}">
                <view class="upload-icon"></view>
                <text>上传图片</text>
              </view>
            </view>
            <view class="upload-tip">最多上传9张图片，可拍照或从相册选择</view>
          </view>
        </view>
      </view>
      
      <!-- 紧急程度 -->
      <view class="form-group">
        <view class="form-label">紧急程度</view>
        <view class="form-control">
          <view class="urgency-selector">
            <view 
              class="urgency-option {{formData.urgencyLevel === item.value ? 'active' : ''}}" 
              wx:for="{{urgencyOptions}}" 
              wx:key="value" 
              bindtap="onUrgencyChange" 
              data-value="{{item.value}}"
            >
              {{item.label}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 期望解决时间 -->
      <view class="form-group">
        <view class="form-label">期望解决时间</view>
        <view class="form-control">
          <view class="form-input date-input" bindtap="showDatePicker">
            <text>{{formData.expectedTime || '请选择期望解决时间'}}</text>
            <view class="calendar-icon"></view>
          </view>
        </view>
      </view>
      
      <!-- 位置信息 -->
      <view class="form-group">
        <view class="form-label">位置信息</view>
        <view class="form-control">
          <input 
            class="form-input" 
            placeholder="请输入详细位置" 
            value="{{formData.location}}" 
            bindinput="onInputChange" 
            data-field="location"
          />
        </view>
      </view>
      
      <!-- 联系人 -->
      <view class="form-group">
        <view class="form-label required">联系人</view>
        <view class="form-control">
          <input 
            class="form-input {{formErrors.contactPerson ? 'error' : ''}}" 
            placeholder="请输入联系人姓名" 
            value="{{formData.contactPerson}}" 
            bindinput="onInputChange" 
            data-field="contactPerson"
          />
          <view class="error-message" wx:if="{{formErrors.contactPerson}}">{{formErrors.contactPerson}}</view>
        </view>
      </view>
      
      <!-- 联系电话 -->
      <view class="form-group">
        <view class="form-label required">联系电话</view>
        <view class="form-control">
          <input 
            class="form-input {{formErrors.contactPhone ? 'error' : ''}}" 
            placeholder="请输入联系电话" 
            value="{{formData.contactPhone}}" 
            bindinput="onInputChange" 
            data-field="contactPhone"
            type="number"
            maxlength="11"
          />
          <view class="error-message" wx:if="{{formErrors.contactPhone}}">{{formErrors.contactPhone}}</view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-container">
      <button 
        class="submit-button {{isSubmitting ? 'disabled' : ''}}" 
        bindtap="submitForm" 
        disabled="{{isSubmitting}}"
      >
        {{isSubmitting ? '提交中...' : '提交报修'}}
      </button>
    </view>
  </block>
  
  <!-- 日期选择器弹窗 -->
  <view class="date-picker-popup {{showDatePicker ? 'active' : ''}}">
    <view class="date-picker-mask" bindtap="hideDatePicker"></view>
    <view class="date-picker-container">
      <view class="date-picker-header">
        <view class="date-picker-cancel" bindtap="hideDatePicker">取消</view>
        <view class="date-picker-title">选择日期</view>
        <view class="date-picker-confirm" bindtap="confirmDatePicker">确定</view>
      </view>
      <picker-view 
        class="date-picker" 
        value="{{currentDate}}" 
        bindchange="onDateChange"
      >
        <picker-view-column>
          <!-- 日期选项 -->
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>
