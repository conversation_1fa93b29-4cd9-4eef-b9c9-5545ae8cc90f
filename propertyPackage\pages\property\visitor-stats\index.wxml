<view class="visitor-stats-container">
  <!-- 标题栏 -->
  <view class="stats-header">
    <view class="stats-title">访客统计分析</view>
    <view class="stats-actions">
      <view class="stats-action" bindtap="exportStatistics">
        <image src="/images/icons/export.svg" class="action-icon"></image>
        <text>导出</text>
      </view>
    </view>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <view class="range-item {{timeRange === 'month' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="month">月度</view>
    <view class="range-item {{timeRange === 'week' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="week">周度</view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">
      <text>访客趋势</text>
    </view>
    <view class="tab-item {{activeTab === 'duration' ? 'active' : ''}}" bindtap="switchTab" data-tab="duration">
      <text>停留时长</text>
    </view>
    <view class="tab-item {{activeTab === 'purpose' ? 'active' : ''}}" bindtap="switchTab" data-tab="purpose">
      <text>来访目的</text>
    </view>
    <view class="tab-item {{activeTab === 'parking' ? 'active' : ''}}" bindtap="switchTab" data-tab="parking">
      <text>停车占用</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 访客趋势内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客总量趋势</text>
        <text class="card-subtitle">{{timeRange === 'month' ? '近30天' : '近7天'}}</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{trendData.total || totalVisitors}}</text>
            <text class="summary-label">访客总量</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{trendData.avgDaily || 0}}</text>
            <text class="summary-label">日均访客</text>
          </view>
        </view>
        <view class="chart-container">
          <view class="echarts-container">
            <ec-canvas id="trendChart" canvas-id="trendChart" ec="{{ecTrend}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 停留时长内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'duration' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客停留时长分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{averageStayDuration || 0}}</text>
            <text class="summary-label">平均停留时长(分钟)</text>
          </view>
        </view>
        <view class="chart-container">
          <view class="echarts-container">
            <ec-canvas id="durationChart" canvas-id="durationChart" ec="{{ecDuration}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客停留热力图</text>
        <text class="card-subtitle">按时间段和星期分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
    
          <view class="mock-chart heatmap-chart">
            <view class="heatmap-y-axis">
              <text wx:for="{{[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}}" wx:key="index">{{item}}时</text>
            </view>
            <view class="heatmap-content">
              <view class="heatmap-grid">
                <view wx:for="{{168}}" wx:key="index" class="heatmap-cell" style="background-color: rgba(79, 70, 229, {{Math.random() * 0.8 + 0.1}});"></view>
              </view>
              <view class="heatmap-x-axis">
                <text>周一</text>
                <text>周二</text>
                <text>周三</text>
                <text>周四</text>
                <text>周五</text>
                <text>周六</text>
                <text>周日</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view> -->
  </view>

  <!-- 来访目的内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'purpose' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客来访目的分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{purposeData.total || 0}}</text>
            <text class="summary-label">总访客量</text>
          </view>
        </view>
        <view class="chart-container">
          <view class="echarts-container">
            <ec-canvas id="purposeChart" canvas-id="purposeChart" ec="{{ecPurpose}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 停车占用内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'parking' && !isLoading}}">

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客车辆停车时长分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <view class="echarts-container">
            <ec-canvas id="parkingDurationChart" canvas-id="parkingDurationChart" ec="{{ecParkingDuration}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="stats-card">
      <view class="card-header">
        <text class="card-title">访客车辆停车占用率</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{parkingData.totalCars || 0}}</text>
            <text class="summary-label">访客车辆总数</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{parkingData.occupancyRate || 0}}%</text>
            <text class="summary-label">平均占用率</text>
          </view>
        </view>
        <view class="chart-container">
          <view class="echarts-container">
            <ec-canvas id="parkingChart" canvas-id="parkingChart" ec="{{ecParking}}"></ec-canvas>
          </view>
        </view>
      </view>
    </view> -->

  
  </view>
</view>
