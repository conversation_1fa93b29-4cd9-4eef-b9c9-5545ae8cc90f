<!--每日5题闯关页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <view class="header">
    <view class="title">每日5题闯关</view>
    <view class="subtitle">答满分+10碳积分</view>
  </view>

  <!-- 未开始状态 -->
  <view class="start-container" wx:if="{{!started && !finished}}">
    <view class="start-card">
      <view class="start-icon"></view>
      <view class="start-title">垃圾分类知识挑战</view>
      <view class="start-desc">完成今日5道题目，测试你的垃圾分类知识</view>
      <view class="start-info">
        <view class="info-item">
          <view class="info-icon time-icon"></view>
          <view class="info-text">预计用时3分钟</view>
        </view>
        <view class="info-item">
          <view class="info-icon reward-icon"></view>
          <view class="info-text">满分奖励10碳积分</view>
        </view>
      </view>
      <view class="start-btn" bindtap="startQuiz">开始挑战</view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-title">
        <view class="title-text">历史成绩</view>
      </view>
      
      <view class="history-list" wx:if="{{quizHistory.length > 0}}">
        <view class="history-item" wx:for="{{quizHistory}}" wx:key="date">
          <view class="history-date">{{item.date}}</view>
          <view class="history-score">{{item.score}}/5</view>
          <view class="history-reward" wx:if="{{item.score === 5}}">+10积分</view>
        </view>
      </view>
      
      <view class="no-history" wx:else>
        <view class="no-history-text">暂无历史记录</view>
      </view>
    </view>
  </view>

  <!-- 答题状态 -->
  <view class="quiz-container" wx:if="{{started && !finished}}">
    <view class="progress-bar">
      <view class="progress-text">{{currentIndex + 1}}/5</view>
      <view class="progress-track">
        <view class="progress-fill" style="width: {{(currentIndex + 1) * 20}}%;"></view>
      </view>
    </view>
    
    <view class="question-card">
      <view class="question-text">{{currentQuestion.question}}</view>
      
      <view class="options-list">
        <view class="option-item {{selectedOption === index ? (answered ? (currentQuestion.correctOption === index ? 'correct' : 'wrong') : 'selected') : ''}} {{answered && currentQuestion.correctOption === index ? 'correct' : ''}}"
              wx:for="{{currentQuestion.options}}" 
              wx:key="index"
              bindtap="selectOption"
              data-index="{{index}}">
          <view class="option-letter">{{['A', 'B', 'C', 'D'][index]}}</view>
          <view class="option-text">{{item}}</view>
        </view>
      </view>
    </view>
    
    <view class="explanation" wx:if="{{answered}}">
      <view class="explanation-title">{{isCorrect ? '回答正确！' : '回答错误！'}}</view>
      <view class="explanation-text">{{currentQuestion.explanation}}</view>
    </view>
    
    <view class="quiz-controls">
      <view class="control-btn next-btn" bindtap="nextQuestion" wx:if="{{answered}}">
        {{currentIndex < 4 ? '下一题' : '完成'}}
      </view>
    </view>
  </view>

  <!-- 完成状态 -->
  <view class="result-container" wx:if="{{finished}}">
    <view class="result-card">
      <view class="result-header">
        <view class="result-icon {{score === 5 ? 'perfect' : (score >= 3 ? 'good' : 'normal')}}"></view>
        <view class="result-title">挑战完成</view>
        <view class="result-score">得分: {{score}}/5</view>
      </view>
      
      <view class="result-reward" wx:if="{{score === 5}}">
        <view class="reward-icon"></view>
        <view class="reward-text">恭喜获得10碳积分！</view>
      </view>
      
      <view class="result-summary">
        <view class="summary-item">
          <view class="summary-label">正确</view>
          <view class="summary-value correct">{{score}}</view>
        </view>
        <view class="summary-item">
          <view class="summary-label">错误</view>
          <view class="summary-value wrong">{{5 - score}}</view>
        </view>
        <view class="summary-item">
          <view class="summary-label">完成时间</view>
          <view class="summary-value">{{quizTime}}秒</view>
        </view>
      </view>
      
      <view class="result-controls">
        <view class="result-btn review-btn" bindtap="reviewQuiz">查看答案</view>
        <view class="result-btn home-btn" bindtap="backToHome">返回首页</view>
      </view>
    </view>
  </view>
</view>
