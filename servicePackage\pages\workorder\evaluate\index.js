// pages/workorder/evaluate/index.js
const workOrderManager = require('@/utils/workorder-manager');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    rating: 5,
    ratingTexts: ['非常差', '差', '一般', '好', '非常好'],
    comment: '',
    images: [],
    isAnonymous: false,
    submitting: false
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ orderId: id });
      this.loadOrderInfo(id);
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载工单基本信息
  loadOrderInfo(id) {
    workOrderManager.getWorkOrderDetail(id)
      .then(order => {
        if (order.status !== 'completed') {
          wx.showToast({
            title: '只能评价已完成的工单',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        if (order.evaluation) {
          wx.showToast({
            title: '该工单已评价',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        this.setData({ orderInfo: order });
      })
      .catch(error => {
        console.error('加载工单信息失败', error);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 设置评分
  setRating(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({ rating });
  },

  // 输入评价内容
  inputComment(e) {
    this.setData({ comment: e.detail.value });
  },

  // 切换匿名评价
  toggleAnonymous(e) {
    this.setData({ isAnonymous: e.detail.value });
  },

  // 选择图片
  chooseImage() {
    const { images } = this.data;
    const count = 3 - images.length;
    
    if (count <= 0) {
      wx.showToast({
        title: '最多上传3张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        // 将选择的图片添加到图片列表
        this.setData({
          images: [...images, ...res.tempFilePaths]
        });
      }
    });
  },

  // 删除图片
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data;
    
    images.splice(index, 1);
    this.setData({ images });
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data;
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 提交评价
  submitEvaluation() {
    const { orderId, rating, comment, images, isAnonymous } = this.data;
    
    // 验证评价内容
    if (!comment.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    // 调用工单管理器提交评价
    workOrderManager.evaluateOrder(orderId, rating, comment, images, isAnonymous)
      .then(() => {
        this.setData({ submitting: false });
        
        wx.showToast({
          title: '评价成功',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        console.error('提交评价失败', error);
        this.setData({ submitting: false });
        
        wx.showToast({
          title: error.message || '评价失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 导航返回
  navigateBack() {
    wx.navigateBack();
  }
});
