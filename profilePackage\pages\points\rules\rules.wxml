<!--rules.wxml-->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <view class="header">
    <view class="title">积分规则</view>
    <view class="subtitle">了解如何获取和使用积分</view>
  </view>

  <!-- 规则分类标签 -->
  <view class="rule-categories">
    <view class="rule-category {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{ruleCategories}}"
          wx:key="id"
          bindtap="switchCategory"
          data-category="{{item.id}}">
      {{item.name}}
    </view>
  </view>

  <!-- 规则内容 - 折叠面板 -->
  <view class="rules-content">
    <block wx:for="{{rules[currentCategory]}}" wx:key="title" wx:for-index="ruleIndex">
      <view class="rule-section {{expandedRules[currentCategory+'_'+ruleIndex] ? 'expanded' : ''}} {{item.important ? 'important' : ''}}">
        <!-- 规则标题栏 -->
        <view class="rule-header" bindtap="toggleRule" data-category="{{currentCategory}}" data-index="{{ruleIndex}}">
          <view class="rule-title-wrap">
            <view class="rule-title">{{item.title}}</view>
            <view class="rule-badge" wx:if="{{item.important}}">重要</view>
          </view>
          <view class="rule-toggle">
            <text class="{{expandedRules[currentCategory+'_'+ruleIndex] ? 'icon-up' : 'icon-down'}}">
              {{expandedRules[currentCategory+'_'+ruleIndex] ? '∧' : '∨'}}
            </text>
          </view>
        </view>

        <!-- 规则内容区 -->
        <view class="rule-body" wx:if="{{expandedRules[currentCategory+'_'+ruleIndex]}}">
          <view class="rule-content">{{item.content}}</view>

          <!-- 提示图标 -->
          <view class="rule-tooltip-wrap" wx:if="{{item.tooltip}}">
            <view class="rule-tooltip-icon" bindtap="toggleTooltip" data-category="{{currentCategory}}" data-index="{{ruleIndex}}">?</view>

            <!-- 提示内容 -->
            <view class="rule-tooltip {{tooltipVisible[currentCategory+'_'+ruleIndex] ? 'visible' : ''}}" wx:if="{{tooltipVisible[currentCategory+'_'+ruleIndex]}}">
              <view class="tooltip-content">{{item.tooltip}}</view>
              <view class="tooltip-arrow"></view>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 点击其他区域关闭提示 -->
  <view class="tooltip-overlay" wx:if="{{Object.keys(tooltipVisible).length > 0}}" bindtap="closeTooltip"></view>

  <!-- 联系客服 -->
  <view class="contact-support" wx:if="{{currentCategory === 'faq'}}">
    <view class="contact-text">还有其他问题？</view>
    <button class="contact-btn" bindtap="contactSupport">联系客服</button>
  </view>
</view>
