// pages/auth/property-auth/property-auth.js
const util = require('../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    // 表单数据
    name: '',
    phone: '',
    idCard: '',
    employeeId: '',
    department: '',
    departmentIndex: -1,
    position: '',
    positionIndex: -1,
    employeeCardPhotoPath: '',
    facePhotoPath: '',

    // 表单验证状态
    nameValid: false,
    nameError: false,
    phoneValid: false,
    phoneError: false,
    idCardValid: false,
    idCardError: false,
    employeeIdValid: false,
    employeeIdError: false,
    departmentValid: false,
    departmentError: false,
    positionValid: false,
    positionError: false,

    // 选项数据
    departments: ['物业管理部', '客服部', '工程部', '保安部', '保洁部', '绿化部', '财务部'],
    positions: ['经理', '主管', '工程师', '客服专员', '保安', '保洁员', '绿化工', '财务专员'],

    isSubmitting: false,
    errorMsg: ''
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 检查是否已登录
    const isPropertyStaff = wx.getStorageSync('isPropertyStaff')
    if (!isPropertyStaff) {
      // 如果未登录，返回上一页
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            wx.navigateBack()
          }, 2000)
        }
      })
    }
  },

  // 输入姓名
  inputName: function(e) {
    this.setData({
      name: e.detail.value
    })
  },

  // 验证姓名
  validateName: function() {
    const name = this.data.name.trim()
    const isValid = name.length >= 2
    this.setData({
      nameValid: isValid,
      nameError: name.length > 0 && !isValid
    })
    return isValid
  },

  // 输入手机号
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    })
  },

  // 验证手机号
  validatePhone: function() {
    const util = require('@/utils/util');
    const phone = this.data.phone.trim()
    const isValid = util.validatePhone(phone)
    this.setData({
      phoneValid: isValid,
      phoneError: phone.length > 0 && !isValid
    })
    return isValid
  },

  // 输入身份证号
  inputIdCard: function(e) {
    this.setData({
      idCard: e.detail.value
    })
  },

  // 验证身份证号
  validateIdCard: function() {
    const util = require('@/utils/util');
    const idCard = this.data.idCard.trim()
    const isValid = util.validateIdCard(idCard)
    this.setData({
      idCardValid: isValid,
      idCardError: idCard.length > 0 && !isValid
    })
    return isValid
  },

  // 输入员工编号
  inputEmployeeId: function(e) {
    this.setData({
      employeeId: e.detail.value
    })
  },

  // 验证员工编号
  validateEmployeeId: function() {
    const employeeId = this.data.employeeId.trim()
    const isValid = employeeId.length >= 2
    this.setData({
      employeeIdValid: isValid,
      employeeIdError: employeeId.length > 0 && !isValid
    })
    return isValid
  },

  // 选择部门
  bindDepartmentChange: function(e) {
    const index = e.detail.value
    this.setData({
      department: this.data.departments[index],
      departmentIndex: index,
      departmentValid: true,
      departmentError: false
    })
  },

  // 选择职位
  bindPositionChange: function(e) {
    const index = e.detail.value
    this.setData({
      position: this.data.positions[index],
      positionIndex: index,
      positionValid: true,
      positionError: false
    })
  },

  // 上传工作证照片
  uploadEmployeeCard: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0]

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        })

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            employeeCardPhotoPath: tempFilePath
          })
          wx.hideLoading()
        }, 1000)
      }
    })
  },

  // 上传人脸照片
  uploadFacePhoto: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0]

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        })

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            facePhotoPath: tempFilePath
          })
          wx.hideLoading()
        }, 1000)
      }
    })
  },

  // 提交认证
  submitAuth: function() {
    // 验证表单
    let isValid = true

    // 验证姓名
    if (!this.validateName()) {
      isValid = false
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false
    }

    // 验证身份证号
    if (!this.validateIdCard()) {
      isValid = false
    }

    // 验证员工编号
    if (!this.validateEmployeeId()) {
      isValid = false
    }

    // 验证部门
    if (!this.data.department) {
      this.setData({
        departmentError: true
      })
      isValid = false
    }

    // 验证职位
    if (!this.data.position) {
      this.setData({
        positionError: true
      })
      isValid = false
    }

    // 验证工作证照片
    if (!this.data.employeeCardPhotoPath) {
      this.showError('请上传工作证照片')
      isValid = false
    }

    if (!isValid) {
      return
    }

    // 开始提交
    this.setData({
      isSubmitting: true
    })

    // 模拟提交认证
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      })

      // 保存登记状态
      wx.setStorageSync('isPropertyStaff', true)
      wx.setStorageSync('userAuthStatus', 'verified')
      wx.setStorageSync('userType', 'property')

      // 保存员工信息
      wx.setStorageSync('propertyName', this.data.name)
      wx.setStorageSync('propertyPhone', this.data.phone)
      wx.setStorageSync('propertyIdCard', this.data.idCard)
      wx.setStorageSync('propertyEmployeeId', this.data.employeeId)
      wx.setStorageSync('propertyDepartment', this.data.department)
      wx.setStorageSync('propertyPosition', this.data.position)

      // 显示登记成功提示
      wx.showToast({
        title: '登记成功',
        icon: 'success',
        duration: 2000
      })

      // 检查是否需要跳转到之前的页面
      const redirectUrl = wx.getStorageSync('redirectAfterPropertyAuth')
      if (redirectUrl) {
        wx.removeStorageSync('redirectAfterPropertyAuth')
        setTimeout(() => {
          wx.navigateTo({
            url: redirectUrl
          })
        }, 2000)
      } else {
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      }
    }, 2000)
  },

  // 显示错误信息
  showError: function(msg) {
    this.setData({
      errorMsg: msg
    })
    setTimeout(() => {
      this.setData({
        errorMsg: ''
      })
    }, 3000)
  }
})
