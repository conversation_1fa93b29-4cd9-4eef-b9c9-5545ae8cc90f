/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 访客登记表单页特定样式 */
/* 导航标签样式 */
.visitor-nav-tabs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
  padding-bottom: 12px;
}

.visitor-nav-tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

.visitor-nav-tab:active {
  background-color: #e6e8eb;
  transform: scale(0.98);
}

.visitor-nav-tab-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* 修正提交按钮文字居中 - 已移至公共样式 */
.visitor-form-item-tips {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 日期时间选择器样式 */
.visitor-picker-item {
  line-height: 50px;
  text-align: center;
}

/* 滞留时长快速选择 */
.visitor-duration-quick-select {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.visitor-duration-option {
  width: calc(25% - 8px);
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin: 4px;
  font-size: 14px;
}

.visitor-duration-option.active {
  background-color: #4f46e5;
  color: #fff;
}

/* 来访目的快速选择 */
.visitor-purpose-options {
  margin-bottom: 16px;
}

.visitor-purpose-option {
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.visitor-purpose-option:last-child {
  border-bottom: none;
}

/* 车牌号历史记录 */
.visitor-car-number-history {
  margin-bottom: 16px;
}

.visitor-car-number-history-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.visitor-car-number-history-item:last-child {
  border-bottom: none;
}

/* 隐私协议弹窗 */
.visitor-privacy-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.visitor-privacy-content {
  width: 80%;
  max-height: 70%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.visitor-privacy-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.visitor-privacy-title {
  font-size: 18px;
  font-weight: 500;
}

.visitor-privacy-body {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.visitor-privacy-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.visitor-privacy-footer {
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
}

.visitor-privacy-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 8px;
  font-size: 16px;
}

.visitor-privacy-cancel-btn {
  background-color: #f0f0f0;
  color: #333;
  margin-right: 8px;
}

.visitor-privacy-agree-btn {
  background-color: #4f46e5;
  color: #fff;
}
