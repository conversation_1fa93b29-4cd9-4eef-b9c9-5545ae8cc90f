// richtext.js
Page({
  data: {
    title: '',
    content: '',
    loading: true
  },

  onLoad: function(options) {
    // 获取传递的参数
    const title = options.title ? decodeURIComponent(options.title) : '内容详情';
    const content = options.content ? decodeURIComponent(options.content) : '';

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: title
    });

    // 设置数据
    this.setData({
      title: title,
      content: content,
      loading: false
    });
  }
})
