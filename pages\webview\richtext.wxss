/* richtext.wxss */
.container {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.content-container {
  padding-bottom: 60rpx;
}

.rich-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
}

/* 富文本内容样式 */
.rich-content :global(p) {
  margin-bottom: 20rpx;
}

.rich-content :global(h1) {
  font-size: 36rpx;
  font-weight: bold;
  margin: 30rpx 0 20rpx;
}

.rich-content :global(h2) {
  font-size: 32rpx;
  font-weight: bold;
  margin: 26rpx 0 16rpx;
}

.rich-content :global(h3) {
  font-size: 30rpx;
  font-weight: bold;
  margin: 22rpx 0 14rpx;
}

.rich-content :global(img) {
  max-width: 100%;
  height: auto;
  margin: 20rpx 0;
}

.rich-content :global(a) {
  color: #ff8c00;
  text-decoration: none;
}

.rich-content :global(ul), .rich-content :global(ol) {
  padding-left: 40rpx;
  margin: 20rpx 0;
}

.rich-content :global(li) {
  margin-bottom: 10rpx;
}

.rich-content :global(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 20rpx 0;
}

.rich-content :global(th), .rich-content :global(td) {
  border: 1px solid #ddd;
  padding: 16rpx;
  text-align: left;
}

.rich-content :global(th) {
  background-color: #f5f5f5;
}

.rich-content :global(blockquote) {
  border-left: 8rpx solid #f0f0f0;
  padding: 10rpx 20rpx;
  color: #666;
  margin: 20rpx 0;
}

.rich-content :global(code) {
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-family: monospace;
}
