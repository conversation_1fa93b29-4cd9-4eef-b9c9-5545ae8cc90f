// pages/goods/verification/list.js
const util = require('@/utils/util.js')

Page({
  data: {
    darkMode: false,
    goodsId: null,
    goods: null,
    pendingOrders: []
  },

  onLoad: function(options) {
    if (options.goodsId) {
      this.setData({
        goodsId: options.goodsId
      })
      this.loadGoodsInfo(options.goodsId)
      this.loadPendingOrders(options.goodsId)
    }
  },

  onPullDownRefresh: function() {
    // 下拉刷新
    Promise.all([
      this.loadGoodsInfo(this.data.goodsId),
      this.loadPendingOrders(this.data.goodsId)
    ]).then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载商品信息
  loadGoodsInfo: function(goodsId) {
    // 模拟加载数据
    return new Promise((resolve) => {
      setTimeout(() => {
        this.setData({
          goods: {
            id: goodsId,
            title: '手工曲奇饼干',
            price: '38.00',
            image: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
            stock: 10,
            sold: 5
          }
        })
        resolve()
      }, 500)
    })

    // TODO: 从服务器获取商品信息
    // return util.request({
    //   url: `/api/goods/${goodsId}`,
    //   method: 'GET'
    // }).then(res => {
    //   this.setData({
    //     goods: res.data
    //   })
    //   resolve()
    // })
  },

  // 加载待核销订单
  loadPendingOrders: function(goodsId) {
    wx.showLoading({
      title: '加载中...'
    })

    // 模拟加载数据
    return new Promise((resolve) => {
      setTimeout(() => {
        this.setData({
          pendingOrders: [
            {
              id: 301,
              orderSn: 'ORD30120230422',
              buyerName: '李四',
              buyerAvatar: '../../../images/default-avatar.svg',
              quantity: 2,
              totalAmount: '76.00',
              createTime: '2023-04-22 14:30'
            },
            {
              id: 302,
              orderSn: 'ORD30220230422',
              buyerName: '王五',
              buyerAvatar: '../../../images/default-avatar.svg',
              quantity: 1,
              totalAmount: '38.00',
              createTime: '2023-04-22 16:15'
            }
          ]
        })
        wx.hideLoading()
        resolve()
      }, 1000)
    })

    // TODO: 从服务器获取待核销订单
    // return util.request({
    //   url: `/api/goods/${goodsId}/pending_verifications`,
    //   method: 'GET'
    // }).then(res => {
    //   this.setData({
    //     pendingOrders: res.data
    //   })
    //   wx.hideLoading()
    //   resolve()
    // }).catch(err => {
    //   wx.hideLoading()
    //   wx.showToast({
    //     title: '加载失败',
    //     icon: 'none'
    //   })
    //   resolve()
    // })
  },

  // 导航到订单详情
  navigateToOrderDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/goods/order/order?id=${id}`
    })
  },

  // 导航到扫码核销页面
  navigateToScan: function() {
    wx.navigateTo({
      url: `/pages/goods/verification/scan?goodsId=${this.data.goodsId}`
    })
  },

  // 联系买家
  contactBuyer: function(e) {
    const { id, name } = e.currentTarget.dataset

    wx.showActionSheet({
      itemList: ['发送私信'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 发送私信
          wx.navigateTo({
            url: `/pages/messages/chat?targetId=${id}&targetName=${name}`
          })
        }
      }
    })
  }
})
