/* pages/property/workorder/templates/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #ff8c00; /* 主品牌色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background-color: #fff;
  margin-top: 88px; /* 导航栏高度(44px) + 状态栏高度(~44px) */
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 88px;
  z-index: 10;
}

.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #ff8c00;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #ff8c00;
  border-radius: 1.5px;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  padding: 16px;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

/* 空列表提示 */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'%3E%3C/path%3E%3Cpolyline points='13 2 13 9 20 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 模板列表 */
.template-list {
  margin-bottom: 16px;
}

.template-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
}

.template-content {
  flex: 1;
  margin-right: 12px;
}

.template-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.template-remark {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.template-actions {
  display: flex;
  flex-direction: column;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.action-button:last-child {
  margin-bottom: 0;
}

.action-button.edit {
  background-color: #e3f2fd;
}

.action-button.delete {
  background-color: #ffebee;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.btn-add, .btn-reset {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
}

.btn-add {
  background-color: #ff8c00;
  color: #fff;
  margin-right: 16px;
}

.btn-reset {
  background-color: #f5f5f5;
  color: #666;
}

.btn-add:active {
  background-color: #e67e00;
}

.btn-reset:active {
  background-color: #e0e0e0;
}

/* 对话框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.modal-content {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.modal-btn {
  min-width: 80px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 18px;
  font-size: 14px;
  margin-left: 12px;
}

.modal-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background-color: #ff8c00;
  color: #fff;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-textarea {
  width: 100%;
  height: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .tab-nav {
  background-color: #2a2a2a;
  border-bottom-color: #333;
}

.darkMode .tab-item {
  color: #aaa;
}

.darkMode .tab-item.active {
  color: #ff8c00;
}

.darkMode .template-item {
  background-color: #2a2a2a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.darkMode .template-text {
  color: #fff;
}

.darkMode .template-remark {
  color: #ccc;
  background-color: #333;
}

.darkMode .bottom-buttons {
  background-color: #2a2a2a;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

.darkMode .btn-reset {
  background-color: #333;
  color: #ccc;
}

.darkMode .modal-dialog {
  background-color: #2a2a2a;
}

.darkMode .modal-header,
.darkMode .modal-footer {
  border-color: #333;
}

.darkMode .modal-title {
  color: #fff;
}

.darkMode .form-label {
  color: #ccc;
}

.darkMode .form-textarea {
  background-color: #333;
  border-color: #444;
  color: #fff;
}

.darkMode .modal-btn.cancel {
  background-color: #333;
  color: #ccc;
}
