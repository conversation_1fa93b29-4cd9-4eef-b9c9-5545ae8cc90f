<!--community-select.wxml-->
<view class="container">
  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="14"></icon>
      <input class="search-input" placeholder="搜索小区名称" bindinput="onSearchInput" value="{{searchKeyword}}"/>
      <icon wx:if="{{searchKeyword}}" class="clear-icon" type="clear" size="14" bindtap="clearSearch"></icon>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-text">正在加载小区列表...</view>
  </view>

  <!-- 小区列表 -->
  <view class="community-list" wx:if="{{!loading}}">
    <!-- 全部小区 -->
    <view class="section-title">小区列表</view>
    <view class="community-item"
          wx:for="{{filteredList}}"
          wx:key="id"
          bindtap="selectCommunity"
          data-community="{{item}}">
      <view class="community-info">
        <view class="community-name">{{item.communityName}}</view>
        <view class="community-address">{{item.address}}</view>
      </view>
      <view class="community-distance">{{item.distance}}</view>
    </view>
  </view>

  <!-- 搜索无结果 -->
  <view class="no-result" wx:if="{{!loading && searchKeyword && filteredList.length === 0}}">
    <icon class="no-result-icon" type="info" size="40" color="#ccc"></icon>
    <view class="no-result-text">未找到相关小区</view>
  </view>

  <!-- 数据为空 -->
  <view class="no-result" wx:if="{{!loading && !searchKeyword && allList.length === 0}}">
    <icon class="no-result-icon" type="info" size="40" color="#ccc"></icon>
    <view class="no-result-text">暂无小区数据</view>
  </view>
</view>
