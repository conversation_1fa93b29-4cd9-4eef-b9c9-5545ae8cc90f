/* payment.wxss */
.container {
  padding: 30rpx;
  padding-bottom: 160rpx;
  background-color: #f5f5f5;
}

.fixed-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #f5f5f5;
  padding: 30rpx 30rpx 0;
}

.payment-header {
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.content-container {
  padding-top: 80rpx;
}

.payment-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  position: absolute;
  right: 0;
  top: 10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-actions .iconfont {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z' fill='%23666666'/%3E%3Cpath d='M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z' fill='%23666666'/%3E%3Cpath d='M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z' fill='%23666666'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  color: #666;
}

/* 费用摘要卡片 */
.fee-summary-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
}

.total-amount {
  text-align: center;
  margin-bottom: 32rpx;
}

.total-amount-label {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.total-amount-value {
  font-size: 68rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  letter-spacing: 1rpx;
}

.fee-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.fee-tag {
  background-color: #f2f2f7;
  color: #666;
  padding: 8rpx 20rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
}

.due-date {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.due-date.urgent {
  color: #ff3b30;
}

.due-date-badge {
  background-color: #ff3b30;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

.data-source {
  text-align: center;
  font-size: 22rpx;
  color: #999;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}

.analysis-entry {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 16rpx;
}

.analysis-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21H3V3' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 9L13 17L9 13L3 19' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 12rpx;
}

.analysis-entry text {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 18L15 12L9 6' stroke='%23ff8c00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 8rpx;
}

/* 缴费项目列表 */
.fee-items-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.select-all {
  display: flex;
  align-items: center;
}

.select-all text {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

.select-all checkbox {
  transform: scale(0.8);
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 96rpx;
}

.fee-item:last-child {
  border-bottom: none;
}

.fee-item-checkbox {
  margin-right: 16rpx;
}

.fee-item-checkbox checkbox {
  transform: scale(1.2);
}

.fee-item-left {
  display: flex;
  align-items: center;
  flex: 1;
  padding-left: 10rpx;
}

/* 移除了缴费项目图标相关样式 */

.fee-item-info {
  display: flex;
  flex-direction: column;
}

.fee-item-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.fee-item-period {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.fee-item-amount {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: right;
  min-width: 120rpx;
}

/* 支付方式选择 */
.payment-methods-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.change-method {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #007aff;
}

.change-method .iconfont {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-down {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 9L12 15L18 9' stroke='%23007AFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-up {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 15L12 9L6 15' stroke='%23007AFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.current-payment-method {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 96rpx;
}

.payment-methods-list {
  margin-top: 16rpx;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 96rpx;
  transition: background-color 0.15s ease-out;
}

.payment-method:active {
  background-color: #f5f5f5;
}

.payment-method:last-child {
  border-bottom: none;
}

.payment-method-left {
  display: flex;
  align-items: center;
}

.payment-method-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  border-radius: 8rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.wechat-icon {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.5 10C10.3284 10 11 9.32843 11 8.5C11 7.67157 10.3284 7 9.5 7C8.67157 7 8 7.67157 8 8.5C8 9.32843 8.67157 10 9.5 10Z' fill='%2307C160'/%3E%3Cpath d='M14.5 10C15.3284 10 16 9.32843 16 8.5C16 7.67157 15.3284 7 14.5 7C13.6716 7 13 7.67157 13 8.5C13 9.32843 13.6716 10 14.5 10Z' fill='%2307C160'/%3E%3Cpath d='M10 15C10.8284 15 11.5 14.3284 11.5 13.5C11.5 12.6716 10.8284 12 10 12C9.17157 12 8.5 12.6716 8.5 13.5C8.5 14.3284 9.17157 15 10 15Z' fill='%2307C160'/%3E%3Cpath d='M15 15C15.8284 15 16.5 14.3284 16.5 13.5C16.5 12.6716 15.8284 12 15 12C14.1716 12 13.5 12.6716 13.5 13.5C13.5 14.3284 14.1716 15 15 15Z' fill='%2307C160'/%3E%3Cpath d='M7.00004 11.9999C6.06704 11.9999 5.24204 12.3879 4.62204 12.9999C3.79404 13.8279 3.33304 14.9759 3.33304 16.2279C3.33304 16.4719 3.35404 16.7119 3.39304 16.9439C3.45304 17.3279 3.90004 17.5679 4.25404 17.4119C4.51304 17.2959 4.78404 17.1959 5.06604 17.1159C5.40804 17.0199 5.76604 17.1439 5.98604 17.4079C6.29004 17.7679 6.63004 18.0959 7.00004 18.3879C7.31604 18.6359 7.68404 18.6359 8.00004 18.3879C8.37004 18.0959 8.71004 17.7679 9.01404 17.4079C9.23404 17.1439 9.59204 17.0199 9.93404 17.1159C10.216 17.1959 10.488 17.2959 10.746 17.4119C11.1 17.5679 11.548 17.3279 11.608 16.9439C11.646 16.7119 11.668 16.4719 11.668 16.2279C11.668 14.9759 11.206 13.8279 10.378 12.9999C9.75804 12.3879 8.93304 11.9999 8.00004 11.9999H7.00004Z' fill='%2307C160'/%3E%3Cpath d='M16 11.9999C15.067 11.9999 14.242 12.3879 13.622 12.9999C12.794 13.8279 12.333 14.9759 12.333 16.2279C12.333 16.4719 12.354 16.7119 12.393 16.9439C12.453 17.3279 12.9 17.5679 13.254 17.4119C13.513 17.2959 13.784 17.1959 14.066 17.1159C14.408 17.0199 14.766 17.1439 14.986 17.4079C15.29 17.7679 15.63 18.0959 16 18.3879C16.316 18.6359 16.684 18.6359 17 18.3879C17.37 18.0959 17.71 17.7679 18.014 17.4079C18.234 17.1439 18.592 17.0199 18.934 17.1159C19.216 17.1959 19.488 17.2959 19.746 17.4119C20.1 17.5679 20.548 17.3279 20.608 16.9439C20.646 16.7119 20.668 16.4719 20.668 16.2279C20.668 14.9759 20.206 13.8279 19.378 12.9999C18.758 12.3879 17.933 11.9999 17 11.9999H16Z' fill='%2307C160'/%3E%3Cpath d='M16.5 4H7.5C4.46243 4 2 6.46243 2 9.5V16.5C2 19.5376 4.46243 22 7.5 22H16.5C19.5376 22 22 19.5376 22 16.5V9.5C22 6.46243 19.5376 4 16.5 4Z' stroke='%2307C160' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.alipay-icon {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M22 10.6154V8C22 5.79086 20.2091 4 18 4H6C3.79086 4 2 5.79086 2 8V16C2 18.2091 3.79086 20 6 20H18C19.6569 20 21 18.6569 21 17V16.9231' stroke='%231677FF' stroke-width='1.5' stroke-linecap='round'/%3E%3Cpath d='M17.5 14.5H19.5' stroke='%231677FF' stroke-width='1.5' stroke-linecap='round'/%3E%3Cpath d='M2 9H22' stroke='%231677FF' stroke-width='1.5' stroke-linecap='round'/%3E%3Cpath d='M5.5 15.5C6.5 13.5 9 12 12 13C15 14 16.5 16 16.5 16' stroke='%231677FF' stroke-width='1.5' stroke-linecap='round'/%3E%3C/svg%3E");
}

.bank-icon {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 9L12 3L22 9' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 10V19' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M20 10V19' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2 19H22' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2 21H22' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M8 10V19' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 10V19' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M16 10V19' stroke='%23FF6B00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.payment-method-name {
  font-size: 30rpx;
  color: #333;
}

.payment-method-radio {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 1rpx solid #ddd;
  position: relative;
  transition: all 0.15s ease-out;
}

.payment-method-radio.selected {
  border-color: #007aff;
  background-color: #007aff;
  transform: scale(1.05);
}

.payment-method-radio.selected::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 用户信息卡片 */
.user-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
}

.info-row {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 支付摘要 */
.payment-summary {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-summary-left {
  display: flex;
  flex-direction: column;
}

.payment-summary-method {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.payment-summary-method .iconfont {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.payment-summary-method text {
  font-size: 26rpx;
  color: #666;
}

.payment-summary-amount {
  font-size: 28rpx;
  color: #333;
}

.payment-summary-right {
  display: flex;
  align-items: center;
}

.payment-security-badges {
  display: flex;
  gap: 12rpx;
}

.security-badge {
  width: 48rpx;
  height: 24rpx;
}

/* 安全支付提示 */
.security-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.security-tip .iconfont {
  width: 22rpx;
  height: 22rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C12 22 20 18 20 12V5L12 2L4 5V12C4 18 12 22 12 22Z' stroke='%23999999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M9 12L11 14L15 10' stroke='%23999999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 底部支付按钮 */
.payment-button-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.payment-button {
  width: 100%;
  height: 88rpx;
  background: #ff6b00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease-out;
}

.payment-button:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.payment-button[disabled] {
  background: #ffcca6;
  opacity: 0.8;
}

/* 支付确认弹窗 */
.payment-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.payment-confirm-modal.show {
  opacity: 1;
  visibility: visible;
}

.payment-confirm-content {
  width: 80%;
  max-width: 540rpx;
  background: white;
  border-radius: 28rpx;
  overflow: hidden;
  padding: 40rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.payment-confirm-modal.show .payment-confirm-content {
  transform: scale(1);
}

.payment-confirm-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 20rpx;
}

.payment-confirm-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.payment-confirm-amount {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.payment-confirm-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.payment-confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-confirm-btn.cancel {
  color: #666;
  background: #f2f2f7;
  border: none;
}

.payment-confirm-btn.confirm {
  color: white;
  background: #ff6b00;
  border: none;
}

/* 支付结果弹窗 */
.payment-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.payment-result-modal.show {
  opacity: 1;
  visibility: visible;
}

.payment-result-content {
  width: 80%;
  max-width: 540rpx;
  background: white;
  border-radius: 28rpx;
  overflow: hidden;
  padding: 48rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.9);
  transition: transform 0.3s;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.payment-result-modal.show .payment-result-content {
  transform: scale(1);
}

.success-content {
  background: linear-gradient(to bottom, #e8f7ee, white 60%);
}

.payment-result-icon {
  width: 112rpx;
  height: 112rpx;
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 56rpx;
}

.payment-result-icon.success {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M22 11.0857V12.0057C21.9988 14.1621 21.3005 16.2604 20.0093 17.9875C18.7182 19.7147 16.9033 20.9782 14.8354 21.5896C12.7674 22.201 10.5573 22.1276 8.53447 21.3803C6.51168 20.633 4.78465 19.2518 3.61096 17.4428C2.43727 15.6338 1.87979 13.4938 2.02168 11.342C2.16356 9.19029 2.99721 7.14205 4.39828 5.5028C5.79935 3.86354 7.69279 2.72111 9.79619 2.24587C11.8996 1.77063 14.1003 1.98806 16.07 2.86572' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M22 4L12 14.01L9 11.01' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.payment-result-icon.fail {
  background-color: rgba(255, 59, 48, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23FF3B30' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M15 9L9 15' stroke='%23FF3B30' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M9 9L15 15' stroke='%23FF3B30' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.payment-result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 20rpx;
}

.payment-result-desc {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.payment-details {
  width: 100%;
  background-color: rgba(249, 249, 249, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.payment-detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.payment-detail-item:last-child {
  margin-bottom: 0;
}

.payment-result-actions {
  display: flex;
  width: 100%;
  justify-content: space-around;
  margin-bottom: 32rpx;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 16rpx 0;
  border-radius: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border: none;
  padding: 0;
  line-height: 1.2;
}

.action-btn .iconfont {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-download {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 10L12 15L17 10' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 15V3' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-share {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M8.59 13.51L15.42 17.49' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M15.41 6.51L8.59 10.49' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-detail {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM16 18H8V16H16V18ZM16 14H8V12H16V14ZM13 9V3.5L18.5 9H13Z' fill='%23666666'/%3E%3C/svg%3E");
}

.action-btn text {
  font-size: 24rpx;
  color: #666;
}

.highlight {
  font-weight: 500;
  color: #007aff;
}

.payment-history-info {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background-color: rgba(242, 242, 247, 0.8);
  padding: 24rpx;
  border-radius: 20rpx;
  width: 100%;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.payment-date {
  font-weight: 500;
  color: #007aff;
}

.payment-result-footer {
  width: 100%;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
  margin-top: 32rpx;
  padding-top: 32rpx;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.payment-result-btn.secondary {
  background-color: #f2f2f7;
  color: #ff8c00;
  border: 1rpx solid #ff8c00;
  white-space: nowrap;
  min-width: 180rpx;
}

.payment-result-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ff6b00;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 180rpx;
  padding: 0 24rpx;
}

/* 更多菜单弹窗 */
.more-menu-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.more-menu-modal.show {
  opacity: 1;
  visibility: visible;
}

.more-menu-content {
  width: 70%;
  max-width: 480rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.more-menu-modal.show .more-menu-content {
  transform: scale(1);
}

.more-menu-item {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.more-menu-item:last-child {
  border-bottom: none;
}

.more-menu-item .iconfont {
  width: 36rpx;
  height: 36rpx;
  margin-right: 24rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-history {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8V12L15 15' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3.05078 11.0002C3.27246 8.18983 4.76195 5.6184 7.12258 3.99091C9.48321 2.36342 12.4555 1.86884 15.2073 2.63451C17.9591 3.40018 20.2091 5.35235 21.3773 7.96449C22.5455 10.5766 22.5206 13.5921 21.3093 16.1821C20.0981 18.7722 17.8159 20.6823 15.0485 21.3964C12.2811 22.1105 9.31206 21.5602 6.97312 19.8866C4.63418 18.213 3.18596 15.6159 3.00977 12.8002' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 7V11H7' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-check {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M9 12L11 14L15 10' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-uncheck {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-reset {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 4V10H7' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M23 20V14H17' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M20.49 9.00008C19.9828 7.56686 19.1209 6.28548 17.9845 5.27549C16.8482 4.2655 15.4745 3.55976 13.9917 3.22427C12.5089 2.88878 10.9652 2.93436 9.50481 3.35679C8.04437 3.77922 6.71475 4.56473 5.64 5.64008L1 10.0001M23 14.0001L18.36 18.3601C17.2853 19.4354 15.9556 20.221 14.4952 20.6434C13.0348 21.0658 11.4911 21.1114 10.0083 20.7759C8.52547 20.4404 7.1518 19.7347 6.01547 18.7247C4.87913 17.7147 4.01717 16.4333 3.51 15.0001' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-settings {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.icon-analysis {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21H3V3' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 9L13 17L9 13L3 19' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.more-menu-item text {
  font-size: 30rpx;
  color: #333;
}
