# 数据统计页面加载逻辑优化

## 优化目标

实现按需加载的数据请求策略：
- 页面进入时只请求运营概览数据
- 点击运营概览标签时才请求运营概览数据
- 点击分类快照标签时才请求分类快照数据

## 修改的加载逻辑

### 1. 页面初始化 (onLoad)
```javascript
onLoad: function() {
  // 页面进入时只加载仪表盘数据
  this.loadDashboardData();
  // 移除了预加载分类数据的逻辑
}
```

### 2. 标签切换 (switchTab)
```javascript
switchTab: function(e) {
  const tab = e.currentTarget.dataset.tab;
  if (tab !== this.data.activeTab) {
    this.setData({ activeTab: tab });
    
    if (tab === 'dashboard') {
      // 点击运营概览标签，加载运营概览数据
      this.loadDashboardData();
    } else if (tab === 'category') {
      // 点击分类快照标签，加载分类快照数据
      this.loadCategoryData(this.data.currentCategory, this.data.timeLevel);
    }
  }
}
```

### 3. 页面显示 (onShow)
```javascript
onShow: function() {
  // 页面显示时不自动刷新数据，避免不必要的请求
  // 用户可以通过下拉刷新或切换标签来主动刷新数据
}
```

### 4. 下拉刷新 (onPullDownRefresh)
```javascript
onPullDownRefresh: function() {
  // 根据当前活动标签页刷新对应数据
  if (this.data.activeTab === 'dashboard') {
    this.loadDashboardData(true);
  } else if (this.data.activeTab === 'category') {
    this.loadCategoryData(this.data.currentCategory, this.data.timeLevel, true);
  }
}
```

### 5. 重试加载 (retryLoad)
```javascript
retryLoad: function() {
  // 根据当前活动标签重试加载对应数据
  if (this.data.activeTab === 'dashboard') {
    this.loadDashboardData(true);
  } else if (this.data.activeTab === 'category') {
    this.loadCategoryData(this.data.currentCategory, this.data.timeLevel, true);
  }
}
```

## 数据加载时机

| 操作 | 运营概览数据 | 分类快照数据 |
|------|-------------|-------------|
| 页面进入 | ✅ 加载 | ❌ 不加载 |
| 点击运营概览标签 | ✅ 加载 | ❌ 不加载 |
| 点击分类快照标签 | ❌ 不加载 | ✅ 加载 |
| 下拉刷新 | 根据当前标签 | 根据当前标签 |
| 重试按钮 | 根据当前标签 | 根据当前标签 |
| 页面显示(onShow) | ❌ 不加载 | ❌ 不加载 |

## 优化效果

1. **减少不必要的API请求**：页面进入时不会同时请求两个标签的数据
2. **提升页面加载速度**：初始只加载运营概览数据，响应更快
3. **按需加载**：用户点击哪个标签才加载对应数据
4. **保持timeLevel全局性**：分类切换时仍然使用当前选中的timeLevel

## 调试信息

添加了console.log来跟踪数据加载：
- `loadDashboardData`: 显示运营概览数据加载时机
- `loadCategoryData`: 显示分类快照数据加载时机和参数

## 注意事项

1. 用户首次点击分类快照标签时会有短暂的加载时间
2. 下拉刷新和重试功能只会刷新当前活动标签的数据
3. 页面显示时不会自动刷新，避免从其他页面返回时的不必要请求
