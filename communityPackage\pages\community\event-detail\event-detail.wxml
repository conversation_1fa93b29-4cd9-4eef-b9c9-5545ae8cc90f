<!--event-detail.wxml-->
<view class="container page-bottom-safe-area {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="event-image" style="background-image: url('{{event.image}}');"></view>

  <view class="event-content">
    <view class="event-header">
      <view class="event-title">{{event.title}}</view>
      <view class="event-status {{event.status === '报名中' ? 'active' : ''}}">{{event.status}}</view>
    </view>

    <view class="event-info">
      <view class="info-item">
        <view class="info-icon">
          <view class="icon-calendar-detail"></view>
        </view>
        <view class="info-text">
          <view class="info-label">活动时间</view>
          <view class="info-value">{{event.time}}</view>
        </view>
      </view>

      <view class="info-item">
        <view class="info-icon">
          <view class="icon-location-detail"></view>
        </view>
        <view class="info-text">
          <view class="info-label">活动地点</view>
          <view class="info-value">{{event.location}}</view>
        </view>
      </view>

      <view class="info-item">
        <view class="info-icon">
          <view class="icon-people-detail"></view>
        </view>
        <view class="info-text">
          <view class="info-label">参与人数</view>
          <view class="info-value">{{event.participants}}/{{event.maxParticipants}}</view>
        </view>
      </view>
    </view>

    <view class="event-description">
      <view class="description-title">活动详情</view>
      <text class="description-content">{{event.description}}</text>
    </view>

    <view class="event-notice">
      <view class="notice-title">活动须知</view>
      <view class="notice-content">
        <view class="notice-item" wx:for="{{event.notices}}" wx:key="index">{{index + 1}}. {{item}}</view>
      </view>
    </view>
  </view>

  <view class="event-footer">
    <button class="share-btn" open-type="share">
      <view class="icon-share-detail"></view>
      <text>分享</text>
    </button>
    <button class="join-btn {{event.status !== '报名中' || event.isEnrolled ? 'disabled' : ''}}" bindtap="joinEvent" disabled="{{event.status !== '报名中' || event.isEnrolled}}">
      {{event.isEnrolled ? '已报名' : (event.status === '报名中' ? '立即报名' : (event.status === '已结束' ? '活动已结束' : '报名已满'))}}
    </button>
  </view>
</view>

<!-- 报名成功弹窗 -->
<view class="join-modal {{showJoinModal ? 'show' : ''}} {{darkMode ? 'darkMode' : ''}}" bindtap="closeJoinModal" data-darkmode="{{darkMode}}">
  <view class="join-modal-content {{darkMode ? 'darkMode' : ''}}" catchtap="stopPropagation" data-darkmode="{{darkMode}}">
    <view class="join-modal-icon">
      <view class="icon-success-detail"></view>
    </view>
    <view class="join-modal-title">报名成功</view>
    <view class="join-modal-desc">您已成功报名参加"{{event.title}}"活动</view>
    <view class="join-modal-tips">活动开始前将通过消息提醒您</view>
    <button class="join-modal-btn" bindtap="closeJoinModal">我知道了</button>
  </view>
</view>
