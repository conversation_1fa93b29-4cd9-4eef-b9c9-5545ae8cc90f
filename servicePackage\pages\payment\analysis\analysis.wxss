/* pages/payment/analysis/analysis.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  color: white;
  padding: 0 30rpx;
}

/* 固定导航栏容器 */
.fixed-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
}

.nav-bar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 18l-6-6 6-6' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.nav-bar-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
}

.nav-bar-right {
  width: 60rpx;
}

/* 时间范围选择器 */
.time-range-selector {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.time-range-item {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f2f2f7;
  transition: all 0.3s ease;
}

.time-range-item.active {
  background-color: #ff8c00;
  color: white;
}

/* 图表类型选择器 */
.chart-type-selector {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.chart-type-item {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f2f2f7;
  transition: all 0.3s ease;
}

.chart-type-item.active {
  background-color: #ff8c00;
  color: white;
}

/* 图表容器 */
.chart-container {
  position: relative;
  background-color: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.analysis-canvas {
  width: 100%;
  height: 440rpx;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 统计数据 */
.statistics-container {
  background-color: white;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.statistics-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.statistics-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21H3V3' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 9L13 17L9 13L3 19' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.statistics-grid {
  display: flex;
  flex-wrap: wrap;
}

.statistics-item {
  width: 50%;
  padding: 20rpx 0;
  text-align: center;
}

.statistics-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 8rpx;
}

.statistics-label {
  font-size: 26rpx;
  color: #666;
}

/* 节约建议 */
.saving-tips-container {
  background-color: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.saving-tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.saving-tips-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 2L2 7L12 12L22 7L12 2Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2 17L12 22L22 17' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M2 12L12 17L22 12' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.saving-tips-list {
  display: flex;
  flex-direction: column;
}

.saving-tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.saving-tip-bullet {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ff8c00;
  margin-top: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
  color: #fff;
}

.darkMode .time-range-selector,
.darkMode .chart-type-selector,
.darkMode .chart-container,
.darkMode .statistics-container,
.darkMode .saving-tips-container {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .time-range-item,
.darkMode .chart-type-item {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .time-range-item.active,
.darkMode .chart-type-item.active {
  background-color: #ff8c00;
  color: white;
}

.darkMode .statistics-title,
.darkMode .saving-tips-title {
  color: #fff;
}

.darkMode .statistics-label {
  color: #8e8e93;
}

.darkMode .saving-tip-item {
  color: #fff;
}

.darkMode .loading-overlay {
  background-color: rgba(44, 44, 46, 0.8);
}
