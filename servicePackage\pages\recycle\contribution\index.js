// pages/recycle/contribution/index.js
Page({
  data: {
    darkMode: false,
    contribution: {
      count: 0,
      carbon: 0,
      points: 0,
      level: 1,
      progress: 0,
      nextLevelCarbon: 5
    },
    achievements: [
      {
        id: 'beginner',
        name: '初级环保者',
        unlocked: true,
        description: '完成第一次垃圾分类',
        progress: 1,
        target: 1,
        reward: '解锁环保徽章'
      },
      {
        id: 'recycler',
        name: '回收达人',
        unlocked: false,
        description: '累计正确分类20件物品',
        progress: 0,
        target: 20,
        reward: '10碳积分'
      },
      {
        id: 'expert',
        name: '分类专家',
        unlocked: false,
        description: '连续5天完成每日答题',
        progress: 0,
        target: 5,
        reward: '20碳积分'
      },
      {
        id: 'carbon',
        name: '减碳先锋',
        unlocked: false,
        description: '累计减少10kg碳排放',
        progress: 0,
        target: 10,
        reward: '解锁特殊壁纸'
      },
      {
        id: 'master',
        name: '环保大师',
        unlocked: false,
        description: '累计获得100碳积分',
        progress: 0,
        target: 100,
        reward: '环保大师证书'
      },
      {
        id: 'leader',
        name: '社区领袖',
        unlocked: false,
        description: '邀请5位好友加入环保行动',
        progress: 0,
        target: 5,
        reward: '社区领袖徽章'
      }
    ],
    records: [
      {
        id: '1',
        type: 'recycle',
        title: '正确分类塑料瓶',
        time: '2023-05-15 14:30',
        value: '0.5kg',
        prefix: '-',
        valueType: 'positive'
      },
      {
        id: '2',
        type: 'quiz',
        title: '完成每日5题',
        time: '2023-05-15 10:15',
        value: '10积分',
        prefix: '+',
        valueType: 'positive'
      },
      {
        id: '3',
        type: 'achievement',
        title: '获得初级环保者成就',
        time: '2023-05-14 16:45',
        value: '5积分',
        prefix: '+',
        valueType: 'positive'
      },
      {
        id: '4',
        type: 'recycle',
        title: '正确分类废纸',
        time: '2023-05-14 09:20',
        value: '0.3kg',
        prefix: '-',
        valueType: 'positive'
      },
      {
        id: '5',
        type: 'recycle',
        title: '正确分类废电池',
        time: '2023-05-13 15:10',
        value: '0.2kg',
        prefix: '-',
        valueType: 'positive'
      }
    ],
    selectedAchievement: null
  },

  onLoad: function() {
    // 从本地存储加载用户贡献数据
    this.loadContribution()
    
    // 更新成就进度
    this.updateAchievements()
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 加载用户贡献数据
  loadContribution: function() {
    // 从本地存储获取用户贡献数据
    const userContribution = wx.getStorageSync('userContribution') || {
      count: 0,
      carbon: 0
    }
    
    // 从本地存储获取碳积分
    const carbonPoints = wx.getStorageSync('carbonPoints') || 0
    
    // 计算等级和进度
    const level = this.calculateLevel(userContribution.carbon)
    const progress = this.calculateProgress(userContribution.carbon, level)
    const nextLevelCarbon = this.getNextLevelCarbon(level, userContribution.carbon)
    
    this.setData({
      contribution: {
        count: userContribution.count,
        carbon: userContribution.carbon.toFixed(1),
        points: carbonPoints,
        level: level,
        progress: progress,
        nextLevelCarbon: nextLevelCarbon
      }
    })
  },

  // 计算等级
  calculateLevel: function(carbon) {
    if (carbon < 5) return 1
    if (carbon < 15) return 2
    if (carbon < 30) return 3
    if (carbon < 50) return 4
    return 5
  },

  // 计算进度百分比
  calculateProgress: function(carbon, level) {
    const levelThresholds = [0, 5, 15, 30, 50, Infinity]
    const currentLevelThreshold = levelThresholds[level - 1]
    const nextLevelThreshold = levelThresholds[level]
    
    const progress = ((carbon - currentLevelThreshold) / (nextLevelThreshold - currentLevelThreshold)) * 100
    return Math.min(Math.round(progress), 100)
  },

  // 获取下一级所需减碳量
  getNextLevelCarbon: function(level, carbon) {
    const levelThresholds = [0, 5, 15, 30, 50, Infinity]
    const nextLevelThreshold = levelThresholds[level]
    
    return (nextLevelThreshold - carbon).toFixed(1)
  },

  // 更新成就进度
  updateAchievements: function() {
    const userContribution = wx.getStorageSync('userContribution') || {
      count: 0,
      carbon: 0
    }
    
    const carbonPoints = wx.getStorageSync('carbonPoints') || 0
    const quizHistory = wx.getStorageSync('quizHistory') || []
    
    // 计算连续答题天数
    let consecutiveDays = 0
    if (quizHistory.length > 0) {
      const sortedHistory = [...quizHistory].sort((a, b) => new Date(b.date) - new Date(a.date))
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      let currentDate = today
      for (const record of sortedHistory) {
        const recordDate = new Date(record.date)
        recordDate.setHours(0, 0, 0, 0)
        
        const diffDays = Math.floor((currentDate - recordDate) / (24 * 60 * 60 * 1000))
        
        if (diffDays === 1) {
          consecutiveDays++
          currentDate = recordDate
        } else if (diffDays === 0) {
          // Same day, continue
          currentDate = recordDate
        } else {
          // Break in streak
          break
        }
      }
    }
    
    // 更新成就进度
    const achievements = this.data.achievements.map(achievement => {
      let progress = 0
      let unlocked = achievement.unlocked
      
      switch (achievement.id) {
        case 'beginner':
          progress = userContribution.count > 0 ? 1 : 0
          unlocked = progress >= achievement.target
          break
        case 'recycler':
          progress = userContribution.count
          unlocked = progress >= achievement.target
          break
        case 'expert':
          progress = consecutiveDays
          unlocked = progress >= achievement.target
          break
        case 'carbon':
          progress = userContribution.carbon
          unlocked = progress >= achievement.target
          break
        case 'master':
          progress = carbonPoints
          unlocked = progress >= achievement.target
          break
        case 'leader':
          // 假设邀请好友数据存储在其他地方
          progress = 0
          unlocked = progress >= achievement.target
          break
      }
      
      return {
        ...achievement,
        progress: Math.min(progress, achievement.target),
        unlocked: unlocked
      }
    })
    
    this.setData({
      achievements: achievements
    })
  },

  // 显示成就详情
  showAchievementDetail: function(e) {
    const id = e.currentTarget.dataset.id
    const achievement = this.data.achievements.find(item => item.id === id)
    
    if (achievement) {
      this.setData({
        selectedAchievement: achievement
      })
    }
  },

  // 关闭成就详情
  closeAchievementDetail: function() {
    this.setData({
      selectedAchievement: null
    })
  },

  // 阻止冒泡
  stopPropagation: function(e) {
    return
  }
})
