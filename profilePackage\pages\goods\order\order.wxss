/* 订单详情页样式 */
.container {
  padding: 30rpx;
  padding-bottom: 160rpx; /* 为底部操作栏留出空间 */
}

/* 订单状态区域 */
.status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  margin-bottom: 30rpx;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  border-radius: 16rpx;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.2);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-bottom: 20rpx;
  position: relative;
}

.status-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.status-icon.pending::before,
.status-icon.wait_complete::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-icon.complete::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-icon.cancel::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

.status-icon.expired::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3Cpath d='M17 2L15 4'%3E%3C/path%3E%3Cpath d='M7 2L9 4'%3E%3C/path%3E%3C/svg%3E");
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.expire-time-info {
  margin-top: 16rpx;
  font-size: 24rpx;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expire-label {
  margin-right: 8rpx;
}

.expire-value {
  font-weight: 500;
}

/* 二维码区域 */
.qrcode-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.qrcode-wrapper {
  position: relative;
  width: 380rpx;
  height: 380rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.qrcode-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 380rpx;
  color: #999;
  font-size: 28rpx;
}

.qrcode-desc {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.qrcode-expire-info {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-expire-info .expire-label {
  opacity: 0.8;
}

.qrcode-expire-info .expire-value {
  font-weight: 500;
  color: #ff6b00;
  margin-left: 8rpx;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

/* 超时区域样式 */
.expired-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid #ff4757;
  background: linear-gradient(135deg, #fff5f5, #ffebee);
}

.expired-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.expired-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff4757;
  margin-bottom: 12rpx;
}

.expired-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.expired-time {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.qrcode-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-mask-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

/* 商品信息区域 */
.goods-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: #ff8c00;
  border-radius: 4rpx;
}

.goods-card {
  display: flex;
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  background-color: #f0f0f0;
}

.goods-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.goods-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.goods-total {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-top: auto;
}

/* 积分信息样式 */
.points-info {
  margin-top: 8rpx;
}

.points-discount, .points-reward {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.points-label {
  color: #666;
}

.points-discount .points-value {
  color: #ff3b30;
}

.points-reward .points-value {
  color: #4cd964;
}

/* 卖家信息和订单信息区域 */
.seller-section,
.order-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  max-width: 70%;
  text-align: right;
  word-break: break-all;
}

/* 底部操作栏 */
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

.footer-btn.contact {
  background: #e3f2fd;
  color: #2196f3;
}

.footer-btn.cancel {
  background: #ffebee;
  color: #f44336;
}

/* 二维码弹窗 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.qrcode-modal.show {
  opacity: 1;
  visibility: visible;
}

.qrcode-modal-content {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.qrcode-modal.show .qrcode-modal-content {
  transform: scale(1);
}

.qrcode-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.qrcode-modal-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.qrcode-modal-image {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 30rpx;
}

.qrcode-modal-btns {
  display: flex;
  width: 100%;
}

.qrcode-modal-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

.qrcode-modal-btn.save {
  background: #ff8c00;
  color: white;
}

.qrcode-modal-btn.close {
  background: #f5f5f5;
  color: #666;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗黑模式样式 */
.darkMode .status-section {
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
}

.darkMode .qrcode-section,
.darkMode .goods-section,
.darkMode .seller-section,
.darkMode .order-section,
.darkMode .footer,
.darkMode .qrcode-modal-content {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .section-title {
  color: #f5f5f7;
}

.darkMode .qrcode-tip {
  color: #8e8e93;
}

.darkMode .qrcode-desc {
  color: #8e8e93;
}

.darkMode .qrcode-expire-info {
  color: #8e8e93;
}

.darkMode .qrcode-expire-info .expire-value {
  color: #ff8c00;
}

.darkMode .goods-card {
  background: #3a3a3c;
}

.darkMode .goods-title {
  color: #f5f5f7;
}

.darkMode .goods-price {
  color: #8e8e93;
}

.darkMode .points-label {
  color: #8e8e93;
}

.darkMode .points-discount .points-value {
  color: #ff453a;
}

.darkMode .points-reward .points-value {
  color: #30d158;
}

.darkMode .info-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .info-label {
  color: #8e8e93;
}

.darkMode .info-value {
  color: #f5f5f7;
}

.darkMode .qrcode-modal-title {
  color: #f5f5f7;
}

.darkMode .qrcode-modal-subtitle {
  color: #8e8e93;
}

.darkMode .qrcode-modal-btn.close {
  background: #3a3a3c;
  color: #8e8e93;
}

.darkMode .loading-icon {
  border: 6rpx solid #3a3a3c;
  border-top: 6rpx solid #ff8c00;
}

.darkMode .loading-text {
  color: #8e8e93;
}

/* 取消订单弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cancel-modal {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 30rpx;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
}

.reason-list {
  margin-bottom: 20rpx;
}

.reason-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.reason-item:last-child {
  border-bottom: none;
}

.reason-item.selected {
  color: #ff8c00;
}

.reason-text {
  font-size: 28rpx;
  flex: 1;
}

.reason-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reason-item.selected .reason-radio {
  border-color: #ff8c00;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: transparent;
}

.radio-inner.selected {
  background: #ff8c00;
}

.custom-reason-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.custom-reason {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.input-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.custom-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  box-sizing: border-box;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #ff8c00;
}

/* 暗黑模式下的弹窗样式 */
.darkMode .cancel-modal {
  background: #2c2c2e;
}

.darkMode .modal-header {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .modal-title {
  color: #f5f5f7;
}

.darkMode .modal-close {
  color: #8e8e93;
}

.darkMode .reason-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .reason-text {
  color: #f5f5f7;
}

.darkMode .input-label {
  color: #8e8e93;
}

.darkMode .custom-input {
  background: #3a3a3c;
  border: 1rpx solid #48484a;
  color: #f5f5f7;
}

.darkMode .modal-footer {
  border-top: 1rpx solid #3a3a3c;
}

.darkMode .cancel-btn {
  color: #8e8e93;
  border-right: 1rpx solid #3a3a3c;
}

.darkMode .reason-radio {
  border-color: #48484a;
}

.darkMode .reason-item.selected .reason-radio {
  border-color: #ff8c00;
}

.darkMode .custom-reason-item {
  border-bottom: 1rpx solid #3a3a3c;
}

/* 暗黑模式下的超时区域样式 */
.darkMode .expired-section {
  background: #2c2c2e;
  border-color: #ff453a;
  background: linear-gradient(135deg, #3a2c2c, #3a2e2e);
}

.darkMode .expired-title {
  color: #ff453a;
}

.darkMode .expired-desc {
  color: #8e8e93;
}

.darkMode .expired-time {
  color: #636366;
}
