// points-level.js
/**
 * 积分等级工具类
 */
const PointsLevelUtil = {
  // 等级定义
  levels: [
    {
      id: 1,
      name: '普通会员',
      minPoints: 0,
      maxPoints: 999,
      icon: 'level1',
      color: '#8E8E8E',
      privileges: [
        { id: 'p1', name: '积分商城兑换', description: '可使用积分在积分商城兑换商品' },
        { id: 'p2', name: '积分抵扣', description: '可使用积分抵扣部分商品金额，100积分=1元' }
      ]
    },
    {
      id: 2,
      name: '银卡会员',
      minPoints: 1000,
      maxPoints: 4999,
      icon: 'level2',
      color: '#A8A8A8',
      privileges: [
        { id: 'p1', name: '积分商城兑换', description: '可使用积分在积分商城兑换商品' },
        { id: 'p2', name: '积分抵扣', description: '可使用积分抵扣部分商品金额，100积分=1元' },
        { id: 'p3', name: '生日特权', description: '生日当月额外赠送100积分' },
        { id: 'p4', name: '专属优惠', description: '部分商品享受95折优惠' }
      ]
    },
    {
      id: 3,
      name: '金卡会员',
      minPoints: 5000,
      maxPoints: 9999,
      icon: 'level3',
      color: '#E6C27A',
      privileges: [
        { id: 'p1', name: '积分商城兑换', description: '可使用积分在积分商城兑换商品' },
        { id: 'p2', name: '积分抵扣', description: '可使用积分抵扣部分商品金额，90积分=1元' },
        { id: 'p3', name: '生日特权', description: '生日当月额外赠送200积分' },
        { id: 'p4', name: '专属优惠', description: '部分商品享受9折优惠' },
        { id: 'p5', name: '专属客服', description: '享受优先客服服务' }
      ]
    },
    {
      id: 4,
      name: '钻石会员',
      minPoints: 10000,
      maxPoints: Infinity,
      icon: 'level4',
      color: '#50C8FF',
      privileges: [
        { id: 'p1', name: '积分商城兑换', description: '可使用积分在积分商城兑换商品' },
        { id: 'p2', name: '积分抵扣', description: '可使用积分抵扣部分商品金额，80积分=1元' },
        { id: 'p3', name: '生日特权', description: '生日当月额外赠送500积分' },
        { id: 'p4', name: '专属优惠', description: '部分商品享受85折优惠' },
        { id: 'p5', name: '专属客服', description: '享受优先客服服务' },
        { id: 'p6', name: '专属商品', description: '可购买钻石会员专属商品' },
        { id: 'p7', name: '免费停车', description: '每月赠送2小时免费停车' }
      ]
    }
  ],

  /**
   * 根据积分获取用户等级
   * @param {Number} points 用户积分
   * @returns {Object} 用户等级信息
   */
  getUserLevel: function(points) {
    for (let i = this.levels.length - 1; i >= 0; i--) {
      if (points >= this.levels[i].minPoints) {
        return this.levels[i];
      }
    }
    return this.levels[0]; // 默认返回最低等级
  },

  /**
   * 获取下一等级信息
   * @param {Number} points 用户积分
   * @returns {Object|null} 下一等级信息，如果已是最高等级则返回null
   */
  getNextLevel: function(points) {
    const currentLevel = this.getUserLevel(points);
    const nextLevelIndex = this.levels.findIndex(level => level.id === currentLevel.id) + 1;
    
    if (nextLevelIndex < this.levels.length) {
      return this.levels[nextLevelIndex];
    }
    
    return null; // 已是最高等级
  },

  /**
   * 计算升级还需要的积分
   * @param {Number} points 用户积分
   * @returns {Number} 升级所需积分，如果已是最高等级则返回0
   */
  getPointsToNextLevel: function(points) {
    const nextLevel = this.getNextLevel(points);
    
    if (nextLevel) {
      return nextLevel.minPoints - points;
    }
    
    return 0; // 已是最高等级
  },

  /**
   * 获取等级进度
   * @param {Number} points 用户积分
   * @returns {Object} 等级进度信息
   */
  getLevelProgress: function(points) {
    const currentLevel = this.getUserLevel(points);
    const nextLevel = this.getNextLevel(points);
    
    if (nextLevel) {
      const total = nextLevel.minPoints - currentLevel.minPoints;
      const current = points - currentLevel.minPoints;
      const percentage = Math.min(Math.floor((current / total) * 100), 100);
      
      return {
        current: current,
        total: total,
        percentage: percentage,
        pointsToNext: nextLevel.minPoints - points
      };
    }
    
    // 已是最高等级
    return {
      current: points - currentLevel.minPoints,
      total: points - currentLevel.minPoints,
      percentage: 100,
      pointsToNext: 0
    };
  },

  /**
   * 获取所有等级
   * @returns {Array} 所有等级信息
   */
  getAllLevels: function() {
    return this.levels;
  },

  /**
   * 获取等级特权
   * @param {Number} levelId 等级ID
   * @returns {Array} 特权列表
   */
  getLevelPrivileges: function(levelId) {
    const level = this.levels.find(level => level.id === levelId);
    return level ? level.privileges : [];
  }
};

module.exports = PointsLevelUtil;
