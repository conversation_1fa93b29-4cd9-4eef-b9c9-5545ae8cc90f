/* 实名认证页面样式 - 修复版 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #252f3f;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: auto;
  max-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.icon-check, .icon-user, .icon-property {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-check {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'/%3E%3Cpath d='M22 4 12 14.01l-3-3'/%3E%3C/svg%3E");
}

.icon-user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='12' cy='7' r='4'/%3E%3C/svg%3E");
}

.icon-property {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='8.5' cy='7' r='4'/%3E%3Cpath d='M20 8v6M17 11h6'/%3E%3C/svg%3E");
}

/* 进度条 */
.progress-card {
  animation: fadeIn 0.5s ease-out;
}

.progress-track {
  position: relative;
  margin-bottom: 12px;
}

.progress-track::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e5e7eb;
  z-index: 1;
}

.progress-track::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  height: 2px;
  background-color: #0c8ee7;
  z-index: 2;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 根据当前步骤设置进度条宽度 */
.step:nth-child(1).active ~ .step:nth-child(2):not(.active) ~ .step:nth-child(3):not(.active) ~ .progress-track::after {
  width: 0%;
}

.step:nth-child(1).active ~ .step:nth-child(2).active ~ .step:nth-child(3):not(.active) ~ .progress-track::after {
  width: 50%;
}

.step:nth-child(1).active ~ .step:nth-child(2).active ~ .step:nth-child(3).active ~ .progress-track::after {
  width: 100%;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 3;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
}

.step-dot {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step.active .step-dot {
  background-color: #0c8ee7;
  border-color: #0c8ee7;
  color: white;
  box-shadow: 0 0 0 4px rgba(12, 142, 231, 0.2);
}

.step.completed .step-dot {
  background-color: #3aad57;
  border-color: #3aad57;
  color: white;
}

.step-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

/* 表单区域 */
.form-card {
  animation: slideUp 0.5s ease-out;
}

/* 分段控制器 */
.segmented-control {
  display: flex;
  width: 100%;
  height: 44px;
  background-color: #f4f5f7;
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 2px;
  position: relative;
  overflow: hidden;
}

.segment {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  position: relative;
  z-index: 10;
}

.segment.active {
  color: #ffffff;
  font-weight: 600;
}

.segment-slider {
  position: absolute;
  height: calc(100% - 4px);
  width: calc(50% - 4px);
  left: 2px;
  top: 2px;
  background-color: #0c8ee7;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 表单滑动切换 */
.form-slide {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  width: 100%;
  opacity: 0;
  transform: translateX(30px);
  pointer-events: none;
  left: 0;
  display: none;
}

.form-slide.active {
  opacity: 1;
  transform: translateX(0);
  position: relative;
  pointer-events: all;
  display: block;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
}

.required {
  color: #fa3e3e;
  margin-left: 4px;
}

.optional {
  color: #6b7280;
  font-size: 12px;
  margin-left: 4px;
  font-weight: normal;
}

.form-input {
  position: relative;
  width: 100%;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
  width: 100%;
}

.form-input.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input.error {
  border-color: #fa3e3e;
}

/* 带验证码的输入框 */
.form-input-with-code {
  position: relative;
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  transition: all 0.3s;
}

/* 验证码输入框 */
.form-input-with-verify-code {
  position: relative;
  width: 100%;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input-with-verify-code input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
}

.form-input-with-verify-code .verify-code-btn {
  height: 36px;
  min-width: 100px;
  margin-right: -8px;
  border-radius: 8px;
}

.form-input-with-verify-code.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input-with-verify-code.error {
  border-color: #fa3e3e;
}

.phone-input {
  flex: 1;
  height: 100%;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.phone-input input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
}

.form-input-with-code.valid .phone-input {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input-with-code.error .phone-input {
  border-color: #fa3e3e;
}

/* 验证码按钮 */
.verify-code-btn {
  height: 100%;
  min-width: 110px;
  background-color: #0072c6;
  color: white;
  font-size: 14px;
  border-radius: 12px;
  border: none;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.verify-code-btn.sent {
  background-color: #9ca3af;
}

.verify-code-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 54px;
  background-color: #0072c6;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  margin-top: 32px;
  box-shadow: 0 4px 10px rgba(12, 142, 231, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.submit-btn:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(12, 142, 231, 0.2);
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shake {
  10%, 90% {
    transform: translateX(-1px);
  }
  20%, 80% {
    transform: translateX(2px);
  }
  30%, 50%, 70% {
    transform: translateX(-2px);
  }
  40%, 60% {
    transform: translateX(1px);
  }
}