/* 便民信息黄页详情页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
  padding-bottom: 80px; /* 为底部操作栏预留空间 */
}

/* 页面顶部样式 */

/* 服务基本信息 */
.service-header {
  padding: 24px 24px 16px;
  background: linear-gradient(to bottom, #ff6b00, transparent);
}

.service-name {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.service-category {
  display: inline-block;
  font-size: 14px;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 16px;
}

/* 服务联系信息卡片 */
.info-card {
  margin: 16px 24px;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.phone-icon {
  background-color: rgba(255, 140, 0, 0.1);
}

.location-icon {
  background-color: rgba(52, 152, 219, 0.1);
}

.time-icon {
  background-color: rgba(46, 204, 113, 0.1);
}

.info-icon image {
  width: 20px;
  height: 20px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #999;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  color: #333;
}

.info-action {
  margin-left: 12px;
}

.call-btn, .map-btn {
  background-color: #ff8c00;
  color: #fff;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  line-height: 1.2;
  font-weight: normal;
}

.map-btn {
  background-color: #3498db;
}

.call-btn::after, .map-btn::after {
  border: none;
}

/* 服务描述卡片 */
.description-card {
  margin: 16px 24px;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

.description-content {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.service-list-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.service-list-item {
  display: flex;
  align-items: flex-start;
}

.service-list-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ff8c00;
  margin-top: 8px;
  margin-right: 8px;
  flex-shrink: 0;
}

.service-list-text {
  font-size: 15px;
  color: #666;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 24px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
  line-height: 1;
}

.action-btn::after {
  border: none;
}

.primary-btn {
  background-color: #ff8c00;
  color: #fff;
}

.secondary-btn {
  background-color: #fff;
  color: #ff8c00;
  border: 1px solid #ff8c00;
}

.btn-icon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}
