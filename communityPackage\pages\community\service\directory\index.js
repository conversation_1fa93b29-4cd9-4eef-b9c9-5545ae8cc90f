// 便民信息黄页页面
const app = getApp()
const navigator = require('../../../../utils/navigator')

Page({
  data: {
    searchKeyword: '',
    searchResults: [],
    categories: [
      {
        id: 1,
        name: '家政保洁',
        icon: '/images/icons/directory/cleaning.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 2,
        name: '家电维修',
        icon: '/images/icons/directory/repair.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 3,
        name: '送水/送奶',
        icon: '/images/icons/directory/delivery.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 4,
        name: '洗衣服务',
        icon: '/images/icons/directory/laundry.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 5,
        name: '开锁换锁',
        icon: '/images/icons/directory/locksmith.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 6,
        name: '搬家服务',
        icon: '/images/icons/directory/moving.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 7,
        name: '餐饮外卖',
        icon: '/images/icons/directory/food.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 8,
        name: '超市便利',
        icon: '/images/icons/directory/market.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      },
      {
        id: 9,
        name: '更多服务',
        icon: '/images/icons/directory/more.svg',
        bgColor: 'rgba(255, 140, 0, 0.1)'
      }
    ],
    popularServices: [
      {
        id: 101,
        name: '阿姨家政服务',
        description: '专业保洁、月嫂、育儿嫂服务',
        address: '天河区珠江新城',
        phone: '020-12345678',
        category: 1
      },
      {
        id: 102,
        name: '全能家电维修',
        description: '各品牌家电维修、安装、清洗',
        address: '天河区天河路',
        phone: '020-87654321',
        category: 2
      },
      {
        id: 103,
        name: '优质桶装水配送',
        description: '品牌桶装水配送、饮水机销售维护',
        address: '天河区天河东路',
        phone: '020-66668888',
        category: 3
      }
    ]
  },

  onLoad: function() {
    // 页面加载时的逻辑
  },

  onShow: function() {
    // 页面显示时的逻辑
    // 清除所有导航节流标记
    navigator.clearThrottles();
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    if (keyword) {
      this.searchServices(keyword)
    }
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      searchResults: []
    })
  },

  // 搜索服务
  searchServices: function(keyword) {
    // 模拟搜索结果，实际应该调用API
    const allServices = this.getAllServices()
    const results = allServices.filter(service =>
      service.name.includes(keyword) ||
      service.description.includes(keyword)
    )

    this.setData({
      searchResults: results
    })
  },

  // 获取所有服务数据（模拟）
  getAllServices: function() {
    // 实际应该从API获取
    return [
      ...this.data.popularServices,
      {
        id: 104,
        name: '洁净洗衣店',
        description: '专业洗衣、熨烫、皮具护理',
        address: '天河区体育西路',
        phone: '020-55556666',
        category: 4
      },
      {
        id: 105,
        name: '24小时开锁服务',
        description: '开锁、换锁、保险柜开启',
        address: '天河区天河北路',
        phone: '020-12312345',
        category: 5
      }
    ]
  },

  // 导航到分类页面
  navigateToCategory: function(e) {
    const { id, name } = e.currentTarget.dataset
    navigator.navigateTo(`/communityPackage/pages/community/service/directory/category?id=${id}&name=${name}`);
  },

  // 导航到详情页面
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id
    navigator.navigateTo(`/communityPackage/pages/community/service/directory/detail?id=${id}`);
  }
})
