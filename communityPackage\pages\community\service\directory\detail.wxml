<!--便民信息黄页详情页面-->
<view class="container">

  <!-- 服务基本信息 -->
  <view class="service-header">
    <view class="service-name">{{service.name}}</view>
    <view class="service-category">{{service.categoryName}}</view>
  </view>

  <!-- 服务联系信息卡片 -->
  <view class="info-card">
    <view class="info-item" bindtap="callPhone">
      <view class="info-icon phone-icon">
        <image src="/images/icons/phone.svg" mode="aspectFit"></image>
      </view>
      <view class="info-content">
        <view class="info-label">联系电话</view>
        <view class="info-value">{{service.phone}}</view>
      </view>
      <view class="info-action">
        <button class="call-btn">拨打</button>
      </view>
    </view>

    <view class="info-item" bindtap="openLocation">
      <view class="info-icon location-icon">
        <image src="/images/icons/location.svg" mode="aspectFit"></image>
      </view>
      <view class="info-content">
        <view class="info-label">详细地址</view>
        <view class="info-value">{{service.address}}</view>
      </view>
      <view class="info-action">
        <button class="map-btn">导航</button>
      </view>
    </view>

    <view class="info-item">
      <view class="info-icon time-icon">
        <image src="/images/icons/clock.svg" mode="aspectFit"></image>
      </view>
      <view class="info-content">
        <view class="info-label">营业时间</view>
        <view class="info-value">{{service.hours}}</view>
      </view>
    </view>
  </view>

  <!-- 服务描述 -->
  <info-card title="服务介绍">
    <view class="description-content">{{service.description}}</view>

    <block wx:if="{{service.services && service.services.length > 0}}">
      <view class="service-list-title">服务项目</view>
      <view class="service-list">
        <view class="service-list-item" wx:for="{{service.services}}" wx:key="index">
          <view class="service-list-dot"></view>
          <view class="service-list-text">{{item}}</view>
        </view>
      </view>
    </block>
  </info-card>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="action-btn primary-btn" bindtap="callPhone">
      <image class="btn-icon" src="/images/icons/phone-white.svg" mode="aspectFit"></image>
      <text>联系商家</text>
    </button>
    <button class="action-btn secondary-btn" bindtap="saveContact">
      <image class="btn-icon" src="/images/icons/star.svg" mode="aspectFit"></image>
      <text>收藏</text>
    </button>
  </view>
</view>
