<!--任务管理页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">任务管理</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索任务名称、位置" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
    </view>
  </view>
  
  <!-- 任务类型标签栏 -->
  <scroll-view class="task-type-tabs" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view 
      class="task-type-tab {{item.active ? 'active' : ''}}" 
      wx:for="{{taskTypes}}" 
      wx:key="id" 
      bindtap="switchTaskType" 
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>
  
  <!-- 任务状态标签栏 -->
  <scroll-view class="task-status-tabs" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view 
      class="task-status-tab {{item.active ? 'active' : ''}}" 
      wx:for="{{taskStatuses}}" 
      wx:key="id" 
      bindtap="switchTaskStatus" 
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>
  
  <!-- 统计信息 -->
  <view class="statistics" animation="{{animationData}}">
    <view class="stat-item">
      <view class="stat-value">{{statistics.todayTasks}}</view>
      <view class="stat-label">今日任务</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{statistics.pendingTasks}}</view>
      <view class="stat-label">待处理</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{statistics.completedTasks}}</view>
      <view class="stat-label">已完成</view>
    </view>
  </view>
  
  <!-- 任务列表 -->
  <view class="task-list" wx:if="{{!isLoading && tasks.length > 0}}">
    <view 
      class="task-card {{item.status}}" 
      wx:for="{{tasks}}" 
      wx:key="id" 
      bindtap="navigateToTaskDetail" 
      data-id="{{item.id}}"
    >
      <view class="task-header">
        <view class="task-title">{{item.title}}</view>
        <view class="task-type {{item.type}}">{{item.typeText}}</view>
      </view>
      <view class="task-info">
        <view class="task-time">
          <view class="time-icon"></view>
          <text>{{item.date}} {{item.time}}</text>
        </view>
        <view class="task-location">
          <view class="location-icon"></view>
          <text>{{item.location}}</text>
        </view>
        <view class="task-assignee" wx:if="{{item.assignee}}">
          <view class="person-icon"></view>
          <text>{{item.assignee}}</text>
        </view>
        <view class="task-reporter" wx:if="{{item.reporter}}">
          <view class="reporter-icon"></view>
          <text>{{item.reporter}} {{item.contactPhone}}</text>
        </view>
      </view>
      <view class="task-footer">
        <view class="task-status-tag">{{item.statusText}}</view>
        <view class="task-actions">
          <view class="action-button" catchtap="navigateToTaskExecution" data-id="{{item.id}}" wx:if="{{item.status === 'pending' || item.status === 'processing'}}">
            执行
          </view>
          <view class="action-button" catchtap="assignTask" data-id="{{item.id}}" wx:if="{{item.status === 'pending' && !item.assignee}}">
            分配
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && tasks.length === 0}}">
    <view class="empty-icon"></view>
    <view class="empty-text">暂无任务</view>
    <view class="empty-subtext">尝试更换筛选条件</view>
  </view>
  
  <!-- 添加任务按钮 -->
  <view class="add-task-button" bindtap="navigateToAddTask">
    <view class="add-icon"></view>
  </view>
</view>
