{"openapi": "3.1.0", "info": {"title": "智慧物业API文档", "description": "管理端，客户端API接口文档", "version": "1.0"}, "servers": [{"url": "http://**********:8080", "description": "Generated server url"}], "security": [{"Authorization": []}], "paths": {"/users-api/v1/member/vehicle": {"get": {"tags": ["用户端"], "summary": "通过ID查询车辆", "operationId": "queryById", "parameters": [{"name": "id", "in": "query", "description": "小区车辆ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityVehicle"}}}}}}, "put": {"tags": ["用户端"], "summary": "编辑车辆", "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberVehicleEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["用户端"], "summary": "新增车辆", "operationId": "insert", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberVehicleCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端"], "summary": "删除车辆", "operationId": "deleteById", "parameters": [{"name": "id", "in": "query", "description": "小区车辆ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/member/room": {"put": {"tags": ["用户端"], "summary": "编辑房间", "operationId": "update_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberRoomEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["用户端"], "summary": "新增房间", "operationId": "insert_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberRoomCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端"], "summary": "删除房屋", "operationId": "deleteById_1", "parameters": [{"name": "id", "in": "query", "description": "小区房屋ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/member/good-stuff": {"get": {"tags": ["用户端，好物模块"], "summary": "通过ID查询我的好物", "operationId": "queryById_1", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuff"}}}}}}, "put": {"tags": ["用户端，好物模块"], "summary": "修改我的好物", "operationId": "update_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodStuffEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["用户端，好物模块"], "summary": "添加我的好物", "operationId": "insert_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodStuffCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端，好物模块"], "summary": "删除我的好物", "operationId": "deleteById_2", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/member/good-stuff/order": {"get": {"tags": ["用户端，好物模块"], "summary": "通过ID查询我的好物订单", "operationId": "queryById_2", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuffOrder"}}}}}}, "put": {"tags": ["用户端，好物模块"], "summary": "修改我的好物订单", "operationId": "update_3", "parameters": [{"name": "goodStuffOrder", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/GoodStuffOrder"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuffOrder"}}}}}}, "post": {"tags": ["用户端，好物模块"], "summary": "新增我的好物订单", "operationId": "insert_3", "parameters": [{"name": "goodStuffOrder", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/GoodStuffOrder"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuffOrder"}}}}}}, "delete": {"tags": ["用户端，好物模块"], "summary": "删除我的好物订单", "operationId": "deleteById_3", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/member/family": {"get": {"tags": ["用户端"], "summary": "通过ID查询家人", "operationId": "queryById_3", "parameters": [{"name": "id", "in": "query", "description": "家人ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityResidentFamilyDTO"}}}}}}, "put": {"tags": ["用户端"], "summary": "编辑家人", "operationId": "update_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFamilyEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["用户端"], "summary": "新增家人", "operationId": "insert_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFamilyCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端"], "summary": "删除家人", "operationId": "deleteById_4", "parameters": [{"name": "id", "in": "query", "description": "家人ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/community/visitor": {"get": {"tags": ["用户端"], "summary": "通过ID查询访客", "operationId": "queryById_4", "parameters": [{"name": "id", "in": "query", "description": "访客ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityVisitor"}}}}}}, "put": {"tags": ["用户端"], "summary": "编辑访客", "operationId": "update_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberVisitorEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["用户端"], "summary": "新增访客", "operationId": "insert_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberVisitorCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端"], "summary": "删除访客", "operationId": "deleteById_5", "parameters": [{"name": "id", "in": "query", "description": "访客ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/auth/real-name": {"put": {"tags": ["用户端"], "summary": "用户端修改实名认证", "operationId": "updateRealNameAuth", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealNameDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMemberDetail"}}}}}}, "post": {"tags": ["用户端"], "summary": "用户端实名认证", "operationId": "realNameAuth", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealNameDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMemberDetail"}}}}}}}, "/manage-api/v1/user": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询用户", "operationId": "queryById_5", "parameters": [{"name": "id", "in": "query", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityUserVO"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "编辑用户", "operationId": "edit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "添加用户", "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除用户", "operationId": "deleteById_6", "parameters": [{"name": "id", "in": "query", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/user/password": {"put": {"tags": ["管理端，系统模块"], "summary": "修改用户密码", "operationId": "updatePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/role": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询角色", "operationId": "queryById_6", "parameters": [{"name": "id", "in": "query", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityRoleVO"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "编辑角色", "operationId": "update_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "添加角色", "operationId": "insert_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除角色", "operationId": "deleteById_7", "parameters": [{"name": "id", "in": "query", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/resident": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询居民信息", "operationId": "queryById_7", "parameters": [{"name": "id", "in": "query", "description": "居民ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityResident"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "编辑居民信息", "operationId": "update_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resident"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "新增居民信息", "operationId": "insert_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resident"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除居民信息", "operationId": "deleteById_8", "parameters": [{"name": "id", "in": "query", "description": "居民ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/property/person": {"get": {"tags": ["管理端，物业模块"], "summary": "通过ID查询物业人员", "operationId": "queryById_8", "parameters": [{"name": "id", "in": "query", "description": "物业人员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPropertyPerson"}}}}}}, "put": {"tags": ["管理端，物业模块"], "summary": "编辑物业人员", "operationId": "update_8", "parameters": [{"name": "property<PERSON><PERSON>", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyPerson"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，物业模块"], "summary": "新增物业人员", "operationId": "insert_8", "parameters": [{"name": "property<PERSON><PERSON>", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyPerson"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，物业模块"], "summary": "删除物业人员", "operationId": "deleteById_9", "parameters": [{"name": "id", "in": "query", "description": "物业人员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/property/payment-items": {"get": {"tags": ["管理端，物业模块"], "summary": "通过ID查询缴费物业项目", "operationId": "queryById_9", "parameters": [{"name": "id", "in": "query", "description": "物业缴费项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPropertyPaymentItems"}}}}}}, "put": {"tags": ["管理端，物业模块"], "summary": "编辑物业缴费项目", "operationId": "update_9", "parameters": [{"name": "propertyPaymentItems", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyPaymentItems"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，物业模块"], "summary": "新增物业缴费项目", "operationId": "insert_9", "parameters": [{"name": "propertyPaymentItems", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyPaymentItems"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，物业模块"], "summary": "删除物业缴费项目", "operationId": "deleteById_10", "parameters": [{"name": "id", "in": "query", "description": "物业缴费项目ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/property/bill": {"get": {"tags": ["管理端，物业模块"], "summary": "通过ID查询物业账单", "operationId": "queryById_10", "parameters": [{"name": "id", "in": "query", "description": "物业账单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPropertyBill"}}}}}}, "put": {"tags": ["管理端，物业模块"], "summary": "编辑物业账单", "operationId": "update_10", "parameters": [{"name": "propertyBill", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyBill"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，物业模块"], "summary": "新增物业账单", "operationId": "insert_10", "parameters": [{"name": "propertyBill", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PropertyBill"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，物业模块"], "summary": "删除物业账单", "operationId": "deleteById_11", "parameters": [{"name": "id", "in": "query", "description": "物业账单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/org": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询组织", "operationId": "queryById_11", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityOrg"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "编辑组织", "operationId": "update_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrgEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "添加组织", "operationId": "insert_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrgCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除组织", "operationId": "deleteById_12", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/notice": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询小区通知", "operationId": "queryById_12", "parameters": [{"name": "id", "in": "query", "description": "通知ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityNotice"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "修改小区通知", "operationId": "update_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityNoticeEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "添加小区通知", "operationId": "insert_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityNoticeCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除小区通知", "operationId": "deleteById_13", "parameters": [{"name": "id", "in": "query", "description": "通知ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/menu": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询菜单", "operationId": "queryById_13", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMenu"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "修改菜单", "operationId": "update_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "添加菜单", "operationId": "insert_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除菜单", "operationId": "deleteById_14", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/member": {"get": {"tags": ["管理端，C端用户管理"], "summary": "通过ID查询用户", "operationId": "queryById_14", "parameters": [{"name": "id", "in": "query", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMember"}}}}}}, "put": {"tags": ["管理端，C端用户管理"], "summary": "编辑用户", "operationId": "update_14", "parameters": [{"name": "dto", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MemberEditForm"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "delete": {"tags": ["管理端，C端用户管理"], "summary": "删除用户", "operationId": "deleteById_15", "parameters": [{"name": "id", "in": "query", "description": "小程序用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/imagetext": {"get": {"tags": ["管理端，系统模块"], "summary": "通过ID查询图文", "operationId": "queryById_15", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityImagetext"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "修改图文", "operationId": "update_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImagetextEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "新增图文", "operationId": "insert_14", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImagetextCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "删除图文", "operationId": "deleteById_16", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/dict": {"get": {"tags": ["管理端，系统模块"], "summary": "根据ID查询字典", "operationId": "queryById_16", "parameters": [{"name": "id", "in": "query", "description": "字典ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityDict"}}}}}}, "put": {"tags": ["管理端，系统模块"], "summary": "字典编辑", "operationId": "update_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，系统模块"], "summary": "字典添加", "operationId": "insert_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，系统模块"], "summary": "字典删除", "operationId": "deleteById_17", "parameters": [{"name": "id", "in": "query", "description": "字典ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区信息", "operationId": "queryById_17", "parameters": [{"name": "id", "in": "query", "description": "小区ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunity"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区信息", "operationId": "update_17", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区信息", "operationId": "insert_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区信息", "operationId": "deleteById_18", "parameters": [{"name": "id", "in": "query", "description": "小区ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/work-order": {"get": {"tags": ["管理端，小区模块"], "operationId": "queryById_18", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityWorkOrder"}}}}}}, "put": {"tags": ["管理端，小区模块"], "operationId": "update_18", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityWorkOrder"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityWorkOrder"}}}}}}, "post": {"tags": ["管理端，小区模块"], "operationId": "insert_17", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityWorkOrder"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityWorkOrder"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "operationId": "deleteById_19", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/visitor": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区访客", "operationId": "queryById_19", "parameters": [{"name": "id", "in": "query", "description": "小区访客ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityVisitor"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区访客", "operationId": "update_19", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityVisitorEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区访客", "operationId": "insert_18", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityVisitorCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区访客", "operationId": "deleteById_20", "parameters": [{"name": "id", "in": "query", "description": "小区访客ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/vehicle": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区车辆", "operationId": "queryById_20", "parameters": [{"name": "id", "in": "query", "description": "小区车辆ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityVehicle"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区车辆", "operationId": "update_20", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityVehicleEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区车辆", "operationId": "insert_19", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityVehicleCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区车辆", "operationId": "deleteById_21", "parameters": [{"name": "id", "in": "query", "description": "小区车辆ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/room": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区房间", "operationId": "queryById_21", "parameters": [{"name": "id", "in": "query", "description": "房间ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityRoom"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区房间", "operationId": "edit_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityRoomEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区房间", "operationId": "add_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityRoomCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区房间", "operationId": "deleteById_22", "parameters": [{"name": "id", "in": "query", "description": "房间ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/resident": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区居民", "operationId": "queryById_22", "parameters": [{"name": "id", "in": "query", "description": "小区住户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityResidentDTO"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区居民", "operationId": "update_21", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityResidentEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区居民", "operationId": "insert_20", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityResidentCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区居民", "operationId": "deleteById_23", "parameters": [{"name": "id", "in": "query", "description": "小区住户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/resident/room": {"put": {"tags": ["管理端，小区模块"], "summary": "编辑住户绑定房间", "operationId": "update_22", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityResidentRoomEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增住户绑定房间", "operationId": "insert_21", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityResidentRoomCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除住户绑定房间", "operationId": "deleteById_24", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/community/building": {"get": {"tags": ["管理端，小区模块"], "summary": "通过ID查询小区楼栋", "operationId": "queryById_23", "parameters": [{"name": "id", "in": "query", "description": "小区楼栋ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityCommunityBuilding"}}}}}}, "put": {"tags": ["管理端，小区模块"], "summary": "编辑小区楼栋", "operationId": "update_23", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityBuildingEditForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}, "post": {"tags": ["管理端，小区模块"], "summary": "新增小区楼栋", "operationId": "insert_22", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityBuildingCreateForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["管理端，小区模块"], "summary": "删除小区楼栋", "operationId": "deleteById_25", "parameters": [{"name": "id", "in": "query", "description": "小区楼栋ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/notice/unread-count": {"post": {"tags": ["用户端"], "summary": "获取未读通知数量", "operationId": "unreadCount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberNoticeSearch"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMapStringInteger"}}}}}}}, "/users-api/v1/notice/read": {"post": {"tags": ["用户端"], "summary": "标记通知为已读", "operationId": "mark<PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "query", "description": "通知ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/member/good-stuff/collect": {"post": {"tags": ["用户端，好物模块"], "summary": "收藏好物", "operationId": "collect", "parameters": [{"name": "goodStuffId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityLong"}}}}}}, "delete": {"tags": ["用户端，好物模块"], "summary": "取消收藏好物", "operationId": "deleteCollect", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/users-api/v1/auth/token": {"post": {"tags": ["用户端"], "summary": "用户端获取授权令牌", "operationId": "token", "parameters": [{"name": "code", "in": "query", "description": "微信授权码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityMemberAccessToken"}}}}}}}, "/users-api/v1/auth/refresh-token": {"post": {"tags": ["用户端"], "summary": "刷新登录令牌", "operationId": "refresh", "parameters": [{"name": "refreshToken", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityAccessToken"}}}}}}}, "/manage-api/v1/community/resident/verify": {"post": {"tags": ["管理端，小区模块"], "summary": "实名验证", "operationId": "verify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommunityResidentVerifyDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/auth/token": {"post": {"tags": ["管理端，授权模块"], "summary": "获取授权令牌", "operationId": "auth", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityAccessToken"}}}}}}}, "/manage-api/v1/auth/refresh-token": {"post": {"tags": ["管理端，授权模块"], "summary": "刷新登录令牌", "operationId": "refresh_1", "parameters": [{"name": "refreshToken", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityAccessToken"}}}}}}}, "/manage-api/v1/auth/logout": {"post": {"tags": ["管理端，授权模块"], "summary": "授权注销", "operationId": "logout", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/common-api/v1/sms/code": {"post": {"tags": ["公共模块"], "summary": "通过手机号获取验证码", "operationId": "smsCode", "parameters": [{"name": "phone", "in": "query", "description": "手机号", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityString"}}}}}}}, "/common-api/v1/file/upload": {"post": {"tags": ["公共模块"], "summary": "上传文件", "operationId": "upload", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityString"}}}}}}}, "/users-api/v1/notice": {"get": {"tags": ["用户端"], "summary": "通过ID查询社区通知详情", "operationId": "queryById_24", "parameters": [{"name": "id", "in": "query", "description": "通知ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityNotice"}}}}}}}, "/users-api/v1/notice/page": {"get": {"tags": ["用户端"], "summary": "分页查询社区通知列表", "operationId": "queryByPage", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MemberNoticeSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoNoticeDTO"}}}}}}}, "/users-api/v1/member/vehicle/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询车辆", "operationId": "queryByPage_1", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityVehicleSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityVehicle"}}}}}}}, "/users-api/v1/member/room/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询房屋", "operationId": "queryByPage_2", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityResidentRoomSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityResidentRoomDTO"}}}}}}}, "/users-api/v1/member/good-stuff/page": {"get": {"tags": ["用户端，好物模块"], "summary": "分页查询我的好物", "operationId": "queryByPage_3", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MyGoodStuffSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuff"}}}}}}}, "/users-api/v1/member/good-stuff/order/page": {"get": {"tags": ["用户端，好物模块"], "summary": "分页查询我的好物订单", "operationId": "queryByPage_4", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuffOrder"}}}}}}}, "/users-api/v1/member/good-stuff/collect/page": {"get": {"tags": ["用户端，好物模块"], "summary": "分页查询我收藏的好物", "operationId": "queryCollectByPage", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CollectGoodStuffSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuff"}}}}}}}, "/users-api/v1/member/family/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询家人", "operationId": "queryByPage_5", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ResidentFamilySearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoResidentFamilyDTO"}}}}}}}, "/users-api/v1/imagetext": {"get": {"tags": ["用户端"], "summary": "通过ID查询图文", "operationId": "queryById_25", "parameters": [{"name": "id", "in": "query", "description": "图文ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityImagetext"}}}}}}}, "/users-api/v1/imagetext/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询图文", "operationId": "queryByPage_6", "parameters": [{"name": "pageNum", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "type", "in": "query", "description": "类型（banner：轮播图,notice：通知,about_us：关于我们,privacy_policy：隐私政策）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoImagetext"}}}}}}}, "/users-api/v1/good-stuff": {"get": {"tags": ["用户端，好物模块"], "summary": "通过ID查询平台好物", "operationId": "queryById_26", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuffVO"}}}}}}}, "/users-api/v1/good-stuff/page": {"get": {"tags": ["用户端，好物模块"], "summary": "分页查询平台好物", "operationId": "queryByPage_7", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/NearGoodStuffSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuffVO"}}}}}}}, "/users-api/v1/dict/search": {"get": {"tags": ["用户端"], "summary": "根据代码查询字典", "operationId": "queryDict", "parameters": [{"name": "nameEn", "in": "query", "description": "字典代码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityListDict"}}}}}}}, "/users-api/v1/dict/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询", "operationId": "queryByPage_8", "parameters": [{"name": "pageNum", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "nameEn", "in": "query", "description": "字典代码", "required": false, "schema": {"type": "string"}}, {"name": "nameCn", "in": "query", "description": "字典名称", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoDictVO"}}}}}}}, "/users-api/v1/community/visitor/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询访客", "operationId": "queryByPage_9", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityVisitorSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityVisitor"}}}}}}}, "/users-api/v1/community/room/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询小区房间", "operationId": "queryByRoomPage", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityRoomQuery"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityRoom"}}}}}}}, "/users-api/v1/community/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询小区信息", "operationId": "queryCommunityByPage", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunitySearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunity"}}}}}}}, "/users-api/v1/community/building/page": {"get": {"tags": ["用户端"], "summary": "通过分页查询小区楼栋", "operationId": "queryBuildingByPage", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityBuildingSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityBuilding"}}}}}}}, "/manage-api/v1/user/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询用户", "operationId": "queryByPage_10", "parameters": [{"name": "pageNum", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "keyword", "in": "query", "description": "搜索关键字(用户名|昵称)", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "phone", "in": "query", "description": "手机号", "required": false, "schema": {"type": "string"}}, {"name": "orgId", "in": "query", "description": "组织ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoUserVO"}}}}}}}, "/manage-api/v1/user/current": {"get": {"tags": ["管理端，系统模块"], "summary": "获取当前用户", "operationId": "user", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityUserDetail"}}}}}, "security": [{"Authorization": []}]}}, "/manage-api/v1/role/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询角色", "operationId": "queryByPage_11", "parameters": [{"name": "pageNum", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoRole"}}}}}}}, "/manage-api/v1/resident/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询居民信息", "operationId": "queryByPage_12", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoResident"}}}}}}}, "/manage-api/v1/property/person/page": {"get": {"tags": ["管理端，物业模块"], "summary": "通过分页查询物业人员", "operationId": "queryByPage_13", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoPropertyPerson"}}}}}}}, "/manage-api/v1/property/payment-items/page": {"get": {"tags": ["管理端，物业模块"], "summary": "通过分页查询物业缴费项目", "operationId": "queryByPage_14", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoPropertyPaymentItems"}}}}}}}, "/manage-api/v1/property/bill/page": {"get": {"tags": ["管理端，物业模块"], "summary": "通过分页查询物业账单", "operationId": "queryByPage_15", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoPropertyBill"}}}}}}}, "/manage-api/v1/org/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询组织", "operationId": "queryByPage_16", "parameters": [{"name": "pageNum", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "orgName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoOrgVO"}}}}}}}, "/manage-api/v1/notice/page": {"get": {"tags": ["管理端，系统模块"], "summary": "分页查询小区通知", "operationId": "queryByPage_17", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/NoticeSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoNotice"}}}}}}}, "/manage-api/v1/menu/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询菜单", "operationId": "queryByPage_18", "parameters": [{"name": "pageNum", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "menuName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "roleId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoMenuDTO"}}}}}}}, "/manage-api/v1/menu/load-menu": {"get": {"tags": ["管理端，系统模块"], "summary": "加载菜单", "operationId": "menu", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityListMenuVO"}}}}}}}, "/manage-api/v1/member/page": {"get": {"tags": ["管理端，C端用户管理"], "summary": "通过分页查询用户", "operationId": "queryByPage_19", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoMember"}}}}}}}, "/manage-api/v1/imagetext/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询图文", "operationId": "queryByPage_20", "parameters": [{"name": "pageNum", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "type", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoImagetext"}}}}}}}, "/manage-api/v1/good-stuff": {"get": {"tags": ["管理端，好物模块"], "summary": "通过ID查询好物", "operationId": "queryById_27", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuff"}}}}}}, "delete": {"tags": ["管理端，好物模块"], "summary": "通过ID删除好物", "operationId": "deleteById_26", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/good-stuff/page": {"get": {"tags": ["管理端，好物模块"], "summary": "分页查询好物", "operationId": "queryByPage_21", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/NearGoodStuffSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuff"}}}}}}}, "/manage-api/v1/good-stuff/order": {"get": {"tags": ["管理端，好物模块"], "summary": "通过ID查询好物订单", "operationId": "queryById_28", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityGoodStuffOrder"}}}}}}, "delete": {"tags": ["管理端，好物模块"], "summary": "通过ID删除好物订单", "operationId": "deleteById_27", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityBoolean"}}}}}}}, "/manage-api/v1/good-stuff/order/page": {"get": {"tags": ["管理端，好物模块"], "summary": "分页查询好物订单", "operationId": "queryByPage_22", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoGoodStuffOrder"}}}}}}}, "/manage-api/v1/dict/search": {"get": {"tags": ["管理端，系统模块"], "summary": "根据代码查询字典", "operationId": "queryDict_1", "parameters": [{"name": "nameEn", "in": "query", "description": "字典代码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityListDict"}}}}}}}, "/manage-api/v1/dict/page": {"get": {"tags": ["管理端，系统模块"], "summary": "通过分页查询", "operationId": "queryByPage_23", "parameters": [{"name": "pageNum", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "nameEn", "in": "query", "description": "字典代码", "required": false, "schema": {"type": "string"}}, {"name": "nameCn", "in": "query", "description": "字典名称", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoDictVO"}}}}}}}, "/manage-api/v1/community/work-order/page": {"get": {"tags": ["管理端，小区模块"], "operationId": "queryByPage_24", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityWorkOrder"}}}}}}}, "/manage-api/v1/community/visitor/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区访客", "operationId": "queryByPage_25", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityVisitorSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityVisitor"}}}}}}}, "/manage-api/v1/community/vehicle/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区车辆", "operationId": "queryByPage_26", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityVehicleSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityVehicle"}}}}}}}, "/manage-api/v1/community/room/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区房间", "operationId": "queryByPage_27", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityRoomQuery"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityRoom"}}}}}}}, "/manage-api/v1/community/resident/room/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区住户绑定房间", "operationId": "queryByPage_28", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityResidentRoomSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityResidentRoomDTO"}}}}}}}, "/manage-api/v1/community/resident/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区居民", "operationId": "queryByPage_29", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityResidentSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityResidentDTO"}}}}}}}, "/manage-api/v1/community/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区信息", "operationId": "queryByPage_30", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunitySearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunity"}}}}}}}, "/manage-api/v1/community/building/page": {"get": {"tags": ["管理端，小区模块"], "summary": "通过分页查询小区楼栋", "operationId": "queryByPage_31", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CommunityBuildingSearch"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityPageInfoCommunityBuilding"}}}}}}}, "/manage-api/v1/chat": {"get": {"tags": ["管理端，系统模块"], "summary": "智能客服", "operationId": "chat", "parameters": [{"name": "message", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/event-stream": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/manage-api/v1/chat/search": {"get": {"tags": ["管理端，系统模块"], "summary": "语意检索", "operationId": "search", "parameters": [{"name": "message", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityListDocument"}}}}}}}, "/manage-api/v1/auth/verify-code": {"get": {"tags": ["管理端，授权模块"], "summary": "获取滑动验证码", "operationId": "grantVerifyCode", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseEntityVerify"}}}}}}}, "/common-api/v1/file/**": {"get": {"tags": ["公共模块"], "summary": "获取资源文件", "operationId": "load", "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"MemberVehicleEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区车辆id"}, "plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "parkingType": {"type": "string", "description": "车位类型"}, "parkingNumber": {"type": "string", "description": "车位编号"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "ResponseEntityBoolean": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "boolean"}}}, "MemberRoomEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "关系ID（房间ID）"}, "isDefault": {"type": "boolean", "description": "默认值"}}, "required": ["id", "isDefault"]}, "GoodStuffEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}, "amount": {"type": "number", "description": "金额"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "points": {"type": "number", "description": "积分"}, "media": {"type": "string", "description": "媒体文件（JSON或URL）"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "address": {"type": "string", "description": "地址"}}}, "GoodStuffOrder": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "orderNo": {"type": "string"}, "unitAmount": {"type": "number", "format": "double"}, "quantity": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "number", "format": "double"}, "userId": {"type": "integer", "format": "int64"}, "phone": {"type": "string"}, "status": {"type": "string"}, "sellerId": {"type": "integer", "format": "int64"}, "stiffSnapshot": {"type": "string"}, "note": {"type": "string"}, "userDeleted": {"type": "boolean"}, "sellerDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "ResponseEntityGoodStuffOrder": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/GoodStuffOrder"}}}, "MemberFamilyEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "我的家人ID"}, "familyResidentId": {"type": "integer", "format": "int64", "description": "家人住户ID"}, "residentName": {"type": "string", "description": "家人姓名"}, "certificateType": {"type": "string", "description": "家人证件类型"}, "idCardNumber": {"type": "string", "description": "家人证件编号"}, "phone": {"type": "string", "description": "家人手机号"}, "roomId": {"type": "integer", "format": "int64", "description": "关联房间"}}}, "MemberVisitorEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区访客id"}, "visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "note": {"type": "string", "description": "备注"}, "stayDuration": {"type": "integer", "format": "int32", "description": "停留时长"}, "timeUnit": {"type": "string", "description": "时间单位"}, "visitTime": {"type": "string", "format": "date-time", "description": "来访时间"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}, "isUsual": {"type": "boolean", "description": "是否常用"}}, "required": ["communityId", "id", "phone", "stayDuration", "visitTime", "visitorName"]}, "RealNameDTO": {"type": "object", "properties": {"residentName": {"type": "string", "description": "住户姓名"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "phone": {"type": "string", "description": "联系电话", "pattern": "^1[3-9]\\d{9}$"}, "codeKey": {"type": "string", "description": "验证码key"}, "code": {"type": "string", "description": "验证码"}}, "required": ["certificateType", "code", "codeKey", "idCardNumber", "phone", "<PERSON><PERSON><PERSON>"]}, "MemberDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "openid": {"type": "string", "description": "微信用户openid"}, "unionid": {"type": "string", "description": "微信用户统一ID"}, "userName": {"type": "string", "description": "用户名"}, "nickName": {"type": "string", "description": "昵称"}, "avatarUrl": {"type": "string", "description": "头像"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "gender": {"type": "string", "description": "性别"}, "phone": {"type": "string", "description": "手机号"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "role": {"type": "string", "description": "角色"}}}, "ResponseEntityMemberDetail": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/MemberDetail"}}}, "UserEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "userName": {"type": "string", "description": "用户名"}, "nickName": {"type": "string", "description": "昵称"}, "email": {"type": "string", "description": "邮箱"}, "phone": {"type": "string", "description": "手机号"}, "gender": {"type": "string", "description": "性别"}, "status": {"type": "string", "description": "状态"}, "avatarUrl": {"type": "string", "description": "头像地址"}, "orgId": {"type": "integer", "format": "int64", "description": "组织ID"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}, "note": {"type": "string", "description": "备注"}}, "required": ["id", "orgId", "roleId", "userName"]}, "PasswordEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "oldPassword": {"type": "string", "description": "旧密码"}, "newPassword": {"type": "string", "description": "新密码", "pattern": "^[a-zA-Z0-9.!@#$%&*]{5,17}\\S$"}}}, "RoleEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名"}, "roleCode": {"type": "string", "description": "角色代码"}, "dataScope": {"type": "integer", "format": "int32", "description": "数据范围"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "permissions": {"type": "array", "description": "权限ID（菜单id）", "items": {"type": "integer", "format": "int64"}}}, "required": ["roleCode", "<PERSON><PERSON><PERSON>"]}, "Resident": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "住户id"}, "residentName": {"type": "string", "description": "住户姓名"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "phone": {"type": "string", "description": "联系电话"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "gender": {"type": "string", "description": "性别"}, "nativePlace": {"type": "string", "description": "籍贯"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "PropertyPerson": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "personName": {"type": "string", "description": "人员姓名"}, "gender": {"type": "string", "description": "性别"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCard": {"type": "string", "description": "身份证"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "email": {"type": "string", "description": "邮箱"}, "address": {"type": "string", "description": "住址"}, "phone": {"type": "string", "description": "手机号"}, "emerPhone": {"type": "string", "description": "应急手机号"}, "number": {"type": "integer", "format": "int32", "description": "人员编号"}, "duty": {"type": "string", "description": "职位"}, "entryTime": {"type": "string", "format": "date-time", "description": "入职时间"}, "status": {"type": "string", "description": "状态"}, "salary": {"type": "number", "format": "double", "description": "薪资"}, "performance": {"type": "string", "description": "绩效"}, "qualification": {"type": "string", "description": "学历"}, "major": {"type": "string", "description": "专业"}, "skills": {"type": "string", "description": "技能（财务管理,会计审核）"}, "certificates": {"type": "string", "description": "证书（会计证,财务证）"}, "media": {"type": "string", "description": "照片（{face_url:xxx,type:image}）"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "note": {"type": "string", "description": "备注"}, "orgId": {"type": "integer", "format": "int64", "description": "部门id"}}}, "PropertyPaymentItems": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "物业项目id"}, "paymentItemName": {"type": "string", "description": "缴费项目名"}, "paymentItemDescribe": {"type": "string", "description": "缴费项目描述"}, "amount": {"type": "number", "format": "double", "description": "金额"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}}}, "PropertyBill": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "物业账单id"}, "phone": {"type": "string", "description": "手机号"}, "amount": {"type": "number", "format": "double", "description": "支付金额"}, "payType": {"type": "string", "description": "支付类型"}, "status": {"type": "string", "description": "状态"}, "payNo": {"type": "string", "description": "支付单号"}, "payTime": {"type": "string", "format": "date-time", "description": "支付时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "openid": {"type": "string", "description": "用户openid（业主不是app用户，请通知业主注册缴费）"}, "buildingId": {"type": "integer", "format": "int64", "description": "楼房id"}}}, "OrgEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "组织ID"}, "orgName": {"type": "string", "description": "组织名称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}}}, "CommunityNoticeEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "通知ID"}, "title": {"type": "string", "description": "通知标题"}, "type": {"type": "string", "description": "通知类型"}, "imageUrl": {"type": "string", "description": "配图URL"}, "content": {"type": "string", "description": "通知内容"}, "sort": {"type": "integer", "format": "int32", "description": "排序序号"}, "targetType": {"type": "string", "description": "目标类型(全体: all, 楼栋: building)"}, "targetIds": {"type": "string", "description": "目标ID列表, 逗号分隔"}, "top": {"type": "boolean", "description": "是否置顶"}}, "required": ["content", "id", "title"]}, "MenuEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "菜单ID"}, "menuName": {"type": "string", "description": "菜单名"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "menuType": {"type": "string", "description": "菜单类型"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "path": {"type": "string", "description": "菜单路径（显示路径如/index）"}, "componentPath": {"type": "string", "description": "组件路径"}, "icon": {"type": "string", "description": "权限"}, "note": {"type": "string", "description": "备注"}, "expandData": {"type": "string", "description": "扩展数据"}}, "required": ["menuName"]}, "MemberEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "userName": {"type": "string", "description": "住户姓名"}, "nickName": {"type": "string", "description": "昵称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "gender": {"type": "string", "description": "性别：男man,女woman"}, "phone": {"type": "string", "description": "手机号"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}}}, "ImagetextEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "标题"}, "title": {"type": "string", "description": "标题"}, "imageUrl": {"type": "string", "description": "配图"}, "content": {"type": "string", "description": "内容"}, "link": {"type": "string", "description": "跳转连接"}, "sort": {"type": "integer", "format": "int64", "description": "排序"}, "type": {"type": "string", "description": "类型（banner,notice,about_us,privacy_policy,text）"}}, "required": ["id", "title", "type"]}, "DictEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "字典ID"}, "nameEn": {"type": "string", "description": "英文名(代码)"}, "nameCn": {"type": "string", "description": "中文名"}, "cssClass": {"type": "string", "description": "样式"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}}}, "CommunityEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区ID"}, "communityName": {"type": "string", "description": "小区名称"}, "note": {"type": "string", "description": "备注"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "address": {"type": "string", "description": "地址"}, "expandData": {"type": "string", "description": "扩展数据"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}}, "required": ["communityName", "id"]}, "CommunityWorkOrder": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "工单ID"}, "type": {"type": "string", "description": "工单类型"}, "userDescribe": {"type": "string", "description": "用户描述"}, "personDescribe": {"type": "string", "description": "人员描述"}, "media": {"type": "string", "description": "媒体文件集合"}, "status": {"type": "string", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "personId": {"type": "integer", "format": "int64", "description": "物业人员id"}, "regionType": {"type": "string", "description": "区域类型（room,public）"}, "region": {"type": "string", "description": "区域"}}}, "ResponseEntityCommunityWorkOrder": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityWorkOrder"}}}, "CommunityVisitorEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区访客id"}, "visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "note": {"type": "string", "description": "备注"}, "stayDuration": {"type": "integer", "format": "int32", "description": "停留时长"}, "timeUnit": {"type": "string"}, "visitTime": {"type": "string", "format": "date-time", "description": "来访时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}, "status": {"type": "string", "description": "访客状态"}}, "required": ["communityId", "id", "phone", "residentId", "stayDuration", "timeUnit", "visitTime", "visitorName"]}, "CommunityVehicleEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区车辆id"}, "plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "parkingType": {"type": "string", "description": "车位类型"}, "parkingNumber": {"type": "string", "description": "车位编号"}, "validBeginTime": {"type": "string", "format": "date-time", "description": "有效期开始时间"}, "validEndTime": {"type": "string", "format": "date-time", "description": "有效期结束时间"}, "note": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "状态"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "media": {"type": "string", "description": "媒体，用来存储媒体对象"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "CommunityRoomEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "房间ID"}, "roomNumber": {"type": "string", "description": "房间号"}, "type": {"type": "string", "description": "户型"}, "unitNumber": {"type": "string", "description": "单元号"}, "buildingId": {"type": "integer", "format": "int64", "description": "楼栋id"}, "area": {"type": "number", "format": "double", "description": "房间面积㎡"}, "expandData": {"type": "string", "description": "扩展数据"}, "note": {"type": "string", "description": "备注"}}}, "CommunityResidentEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区住户ID（人房ID）"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentName": {"type": "string", "description": "住户姓名"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "note": {"type": "string", "description": "备注"}, "phone": {"type": "string", "description": "手机号"}, "address": {"type": "string", "description": "住址"}, "tags": {"type": "string", "description": "标签"}}}, "CommunityResidentRoomEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}, "roomId": {"type": "integer", "format": "int64", "description": "房间ID"}, "residentType": {"type": "string", "description": "住户类型"}, "status": {"type": "string", "description": "状态"}, "isDefault": {"type": "boolean", "description": "是否默认"}}, "required": ["id", "residentId", "residentType", "roomId"]}, "CommunityBuildingEditForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区楼房ID"}, "buildingNumber": {"type": "string", "description": "建筑编号"}, "lng": {"type": "number", "format": "double", "description": "纬度"}, "lat": {"type": "number", "format": "double", "description": "经度"}, "alt": {"type": "number", "format": "double", "description": "海拔"}, "expandData": {"type": "string", "description": "扩展数据"}, "note": {"type": "string", "description": "备注"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}, "communityId": {"type": "integer", "format": "int64", "description": "所属小区"}}}, "MemberNoticeSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "title": {"type": "string", "description": "标题"}, "types": {"type": "array", "description": "类型集合", "items": {"type": "string"}}, "type": {"type": "string", "description": "类型"}, "top": {"type": "boolean", "description": "置顶"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}}}, "ResponseEntityMapStringInteger": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}, "MemberVehicleCreateForm": {"type": "object", "properties": {"plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "parkingType": {"type": "string", "description": "车位类型"}, "parkingNumber": {"type": "string", "description": "车位编号"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "ResponseEntityLong": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "integer", "format": "int64"}}}, "MemberRoomCreateForm": {"type": "object", "properties": {"roomId": {"type": "integer", "format": "int64", "description": "房间ID"}, "residentType": {"type": "string", "description": "住户类型（业主，租客，家属）"}}, "required": ["residentType", "roomId"]}, "GoodStuffCreateForm": {"type": "object", "properties": {"stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}, "amount": {"type": "number", "description": "金额"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "points": {"type": "number", "description": "积分"}, "media": {"type": "string", "description": "媒体文件（JSON或URL）"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "address": {"type": "string", "description": "地址"}}}, "MemberFamilyCreateForm": {"type": "object", "properties": {"residentName": {"type": "string", "description": "家人姓名"}, "certificateType": {"type": "string", "description": "家人证件类型"}, "idCardNumber": {"type": "string", "description": "家人证件编号"}, "phone": {"type": "string", "description": "家人手机号"}, "roomId": {"type": "integer", "format": "int64", "description": "关联房间"}}}, "MemberVisitorCreateForm": {"type": "object", "properties": {"visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "note": {"type": "string", "description": "备注"}, "stayDuration": {"type": "integer", "format": "int32", "description": "停留时长"}, "timeUnit": {"type": "string", "description": "时间单位"}, "visitTime": {"type": "string", "format": "date-time", "description": "来访时间"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}, "required": ["communityId", "phone", "stayDuration", "visitTime", "visitorName"]}, "AccessToken": {"type": "object", "properties": {"access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "expires_in": {"type": "integer", "format": "int64"}}}, "MemberAccessToken": {"type": "object", "properties": {"accessToken": {"$ref": "#/components/schemas/AccessToken"}, "memberDetail": {"$ref": "#/components/schemas/MemberDetail"}, "session_key": {"type": "string"}}}, "ResponseEntityMemberAccessToken": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/MemberAccessToken"}}}, "ResponseEntityAccessToken": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/AccessToken"}}}, "UserCreateForm": {"type": "object", "properties": {"userName": {"type": "string", "description": "用户名"}, "nickName": {"type": "string", "description": "昵称"}, "email": {"type": "string", "description": "邮箱"}, "password": {"type": "string", "description": "密码", "pattern": "^[a-zA-Z0-9.!@#$%&*]{6,18}\\S$"}, "phone": {"type": "string", "description": "手机号"}, "gender": {"type": "string", "description": "性别"}, "avatarUrl": {"type": "string", "description": "头像地址"}, "orgId": {"type": "integer", "format": "int64", "description": "组织ID"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}, "note": {"type": "string", "description": "备注"}}, "required": ["orgId", "roleId", "userName"]}, "RoleCreateForm": {"type": "object", "properties": {"roleName": {"type": "string", "description": "角色名"}, "roleCode": {"type": "string", "description": "角色代码"}, "dataScope": {"type": "integer", "format": "int32", "description": "权限范围"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "permissions": {"type": "array", "description": "权限ID（菜单id）", "items": {"type": "integer", "format": "int64"}}}, "required": ["roleCode", "<PERSON><PERSON><PERSON>"]}, "OrgCreateForm": {"type": "object", "properties": {"orgName": {"type": "string", "description": "组织名称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}}}, "CommunityNoticeCreateForm": {"type": "object", "properties": {"title": {"type": "string", "description": "通知标题"}, "type": {"type": "string", "description": "通知类型"}, "imageUrl": {"type": "string", "description": "配图URL"}, "content": {"type": "string", "description": "通知内容"}, "sort": {"type": "integer", "format": "int32", "description": "排序序号"}, "targetType": {"type": "string", "description": "目标类型(全体: all, 楼栋: building)"}, "targetIds": {"type": "string", "description": "目标ID列表, 逗号分隔"}, "top": {"type": "boolean", "description": "是否置顶"}}, "required": ["content", "title"]}, "MenuCreateForm": {"type": "object", "properties": {"menuName": {"type": "string", "description": "菜单名"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "menuType": {"type": "string", "description": "菜单类型"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "path": {"type": "string", "description": "菜单路径"}, "componentPath": {"type": "string", "description": "组件路径"}, "icon": {"type": "string", "description": "图标"}, "note": {"type": "string", "description": "备注"}, "expandData": {"type": "string", "description": "扩展数据"}}, "required": ["menuName"]}, "ImagetextCreateForm": {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "imageUrl": {"type": "string", "description": "配图"}, "content": {"type": "string", "description": "内容"}, "link": {"type": "string", "description": "跳转连接"}, "sort": {"type": "integer", "format": "int64", "description": "排序"}, "type": {"type": "string", "description": "类型（banner,notice,about_us,privacy_policy,text）"}}, "required": ["title", "type"]}, "DictCreateForm": {"type": "object", "properties": {"nameEn": {"type": "string", "description": "英文名(代码)"}, "nameCn": {"type": "string", "description": "中文名"}, "cssClass": {"type": "string", "description": "样式"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}}}, "CommunityCreateForm": {"type": "object", "properties": {"communityName": {"type": "string", "description": "小区名称"}, "note": {"type": "string", "description": "备注"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "address": {"type": "string", "description": "地址"}, "expandData": {"type": "string", "description": "扩展数据"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}}, "required": ["communityName"]}, "CommunityVisitorCreateForm": {"type": "object", "properties": {"visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "note": {"type": "string", "description": "备注"}, "stayDuration": {"type": "integer", "format": "int32", "description": "停留时长"}, "timeUnit": {"type": "string"}, "visitTime": {"type": "string", "format": "date-time", "description": "来访时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}, "required": ["communityId", "phone", "residentId", "stayDuration", "timeUnit", "visitTime", "visitorName"]}, "CommunityVehicleCreateForm": {"type": "object", "properties": {"plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "parkingType": {"type": "string", "description": "车位类型"}, "parkingNumber": {"type": "string", "description": "车位编号"}, "validBeginTime": {"type": "string", "format": "date-time", "description": "有效期开始时间"}, "validEndTime": {"type": "string", "format": "date-time", "description": "有效期结束时间"}, "note": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "状态"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "CommunityRoomCreateForm": {"type": "object", "properties": {"roomNumber": {"type": "string", "description": "房间号"}, "type": {"type": "string", "description": "户型"}, "unitNumber": {"type": "string", "description": "单元号"}, "buildingId": {"type": "integer", "format": "int64", "description": "楼栋id"}, "area": {"type": "number", "format": "double", "description": "房间面积㎡"}, "expandData": {"type": "string", "description": "扩展数据"}, "note": {"type": "string", "description": "备注"}}, "required": ["buildingId", "roomNumber"]}, "CommunityResidentCreateForm": {"type": "object", "properties": {"communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentName": {"type": "string", "description": "住户姓名"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "note": {"type": "string", "description": "备注"}, "phone": {"type": "string", "description": "手机号"}, "address": {"type": "string", "description": "住址"}, "tags": {"type": "string", "description": "标签"}}}, "CommunityResidentVerifyDTO": {"type": "object", "properties": {"residentName": {"type": "string", "description": "住户姓名"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "phone": {"type": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$"}}, "required": ["certificateType", "communityId", "idCardNumber", "<PERSON><PERSON><PERSON>"]}, "CommunityResidentRoomCreateForm": {"type": "object", "properties": {"residentId": {"type": "integer", "format": "int64", "description": "住户ID"}, "roomId": {"type": "integer", "format": "int64", "description": "房间ID"}, "residentType": {"type": "string", "description": "住户类型"}, "isDefault": {"type": "boolean", "description": "是否默认"}}, "required": ["residentId", "residentType", "roomId"]}, "CommunityBuildingCreateForm": {"type": "object", "properties": {"buildingNumber": {"type": "string", "description": "建筑编号"}, "lng": {"type": "number", "format": "double", "description": "纬度"}, "lat": {"type": "number", "format": "double", "description": "经度"}, "alt": {"type": "number", "format": "double", "description": "海拔"}, "expandData": {"type": "string", "description": "扩展数据"}, "note": {"type": "string", "description": "备注"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}, "communityId": {"type": "integer", "format": "int64", "description": "所属小区"}}}, "LoginRequestParams": {"type": "object", "properties": {"token": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "point": {"type": "string"}}, "required": ["password", "point", "token", "userName"]}, "ResponseEntityString": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "string"}}}, "Notice": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string"}, "imageUrl": {"type": "string"}, "content": {"type": "string"}, "sort": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "integer", "format": "int64"}, "updateBy": {"type": "integer", "format": "int64"}, "targetType": {"type": "string"}, "targetIds": {"type": "string"}, "top": {"type": "boolean"}}}, "ResponseEntityNotice": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Notice"}}}, "NoticeDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string"}, "imageUrl": {"type": "string"}, "content": {"type": "string"}, "sort": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "integer", "format": "int64"}, "updateBy": {"type": "integer", "format": "int64"}, "targetType": {"type": "string"}, "targetIds": {"type": "string"}, "top": {"type": "boolean"}, "read": {"type": "boolean"}}}, "PageInfoNoticeDTO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/NoticeDTO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoNoticeDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoNoticeDTO"}}}, "CommunityVehicle": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区车辆id"}, "plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "parkingType": {"type": "string", "description": "车位类型"}, "parkingNumber": {"type": "string", "description": "车位编号"}, "validBeginTime": {"type": "string", "format": "date-time", "description": "有效期开始时间"}, "validEndTime": {"type": "string", "format": "date-time", "description": "有效期结束时间"}, "note": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "状态"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "ResponseEntityCommunityVehicle": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityVehicle"}}}, "CommunityVehicleSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "plateNumber": {"type": "string", "description": "车牌号"}, "vehicleColor": {"type": "string", "description": "车辆颜色（关联字典）"}, "mainUse": {"type": "boolean", "description": "主要使用"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}}}, "PageInfoCommunityVehicle": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityVehicle"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityVehicle": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityVehicle"}}}, "CommunityResidentRoomSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}, "roomId": {"type": "integer", "format": "int64", "description": "房间ID"}, "status": {"type": "string", "description": "状态"}, "residentType": {"type": "string", "description": "住户类型（业主，租客，家属）"}}}, "CommunityResidentRoomDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "人房关系ID"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "communityName": {"type": "string", "description": "小区名称"}, "buildingId": {"type": "integer", "format": "int64", "description": "楼栋ID"}, "buildingNumber": {"type": "string", "description": "楼栋编号"}, "unitNumber": {"type": "string", "description": "单元编号"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}, "roomId": {"type": "integer", "format": "int64", "description": "房间ID"}, "roomNumber": {"type": "string", "description": "房间编号"}, "status": {"type": "string", "description": "状态"}, "residentType": {"type": "string", "description": "住户类型（业主，租客，家属）"}, "isDefault": {"type": "boolean", "description": "默认值"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}}}, "PageInfoCommunityResidentRoomDTO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityResidentRoomDTO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityResidentRoomDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityResidentRoomDTO"}}}, "GoodStuff": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID"}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}, "amount": {"type": "number", "format": "double", "description": "金额"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "soldStock": {"type": "integer", "format": "int32", "description": "已售库存"}, "points": {"type": "number", "format": "double", "description": "积分"}, "media": {"type": "string", "description": "媒体文件（JSON或URL）"}, "status": {"type": "string", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "address": {"type": "string", "description": "地址"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "views": {"type": "integer", "format": "int32", "description": "浏览量"}}}, "ResponseEntityGoodStuff": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/GoodStuff"}}}, "MyGoodStuffSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}}}, "PageInfoGoodStuff": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodStuff"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoGoodStuff": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoGoodStuff"}}}, "PageInfoGoodStuffOrder": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodStuffOrder"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoGoodStuffOrder": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoGoodStuffOrder"}}}, "CollectGoodStuffSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}}}, "ResidentFamilyDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "家人id"}, "residentId": {"type": "integer", "format": "int64", "description": "家人住户ID"}, "roomId": {"type": "integer", "format": "int64", "description": "关联房间ID"}, "residentName": {"type": "string", "description": "家人姓名"}, "certificateType": {"type": "string", "description": "家人证件类型"}, "idCardNumber": {"type": "string", "description": "家人证件编号"}, "phone": {"type": "string", "description": "家人手机号"}}}, "ResponseEntityResidentFamilyDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/ResidentFamilyDTO"}}}, "ResidentFamilySearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "familyName": {"type": "string", "description": "家人姓名"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}}}, "PageInfoResidentFamilyDTO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/ResidentFamilyDTO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoResidentFamilyDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoResidentFamilyDTO"}}}, "Imagetext": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "图文ID"}, "title": {"type": "string", "description": "标题"}, "imageUrl": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "link": {"type": "string", "description": "跳转链接"}, "sort": {"type": "integer", "format": "int64", "description": "排序"}, "createBy": {"type": "integer", "format": "int64", "description": "创建者"}, "type": {"type": "string", "description": "类型（banner：轮播图,notice：通知,about_us：关于我们,privacy_policy：隐私政策）"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}}}, "ResponseEntityImagetext": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Imagetext"}}}, "PageInfoImagetext": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Imagetext"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoImagetext": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoImagetext"}}}, "GoodStuffVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID"}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}, "amount": {"type": "number", "format": "double", "description": "金额"}, "stock": {"type": "integer", "format": "int32", "description": "库存"}, "soldStock": {"type": "integer", "format": "int32", "description": "已售库存"}, "points": {"type": "number", "format": "double", "description": "积分"}, "media": {"type": "string", "description": "媒体文件（JSON或URL）"}, "status": {"type": "string", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "address": {"type": "string", "description": "地址"}, "views": {"type": "integer", "format": "int32", "description": "浏览量"}, "isMy": {"type": "boolean", "description": "是否是我发布的"}}}, "ResponseEntityGoodStuffVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/GoodStuffVO"}}}, "NearGoodStuffSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "address": {"type": "string", "description": "地址"}, "stuffDescribe": {"type": "string", "description": "描述"}, "categoryCode": {"type": "string", "description": "分类编码"}, "type": {"type": "string", "description": "类型"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}}}, "PageInfoGoodStuffVO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodStuffVO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoGoodStuffVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoGoodStuffVO"}}}, "Dict": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "字典ID"}, "nameEn": {"type": "string", "description": "英文名(代码)"}, "nameCn": {"type": "string", "description": "中文名"}, "cssClass": {"type": "string", "description": "样式"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}}}, "ResponseEntityListDict": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Dict"}}}}, "DictVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "字典ID"}, "nameEn": {"type": "string", "description": "英文名(代码)"}, "nameCn": {"type": "string", "description": "中文名"}, "cssClass": {"type": "string", "description": "样式"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "note": {"type": "string", "description": "备注"}}}, "PageInfoDictVO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/DictVO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoDictVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoDictVO"}}}, "CommunityVisitor": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区访客id"}, "visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "note": {"type": "string", "description": "备注"}, "stayDuration": {"type": "integer", "format": "int32", "description": "停留时长"}, "timeUnit": {"type": "string", "description": "时间单位"}, "visitTime": {"type": "string", "format": "date-time", "description": "来访时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "communityId": {"type": "integer", "format": "int64", "description": "小区id"}, "status": {"type": "string", "description": "访客状态"}, "isUsual": {"type": "boolean", "description": "是否常用"}, "verifyBy": {"type": "boolean", "description": "核验人"}}}, "ResponseEntityCommunityVisitor": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityVisitor"}}}, "CommunityVisitorSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "visitorName": {"type": "string", "description": "访客姓名"}, "phone": {"type": "string", "description": "手机号"}, "vehicleNumber": {"type": "string", "description": "车牌号"}, "status": {"type": "string", "description": "访客状态"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentId": {"type": "integer", "format": "int64", "description": "住户ID"}}}, "PageInfoCommunityVisitor": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityVisitor"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityVisitor": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityVisitor"}}}, "CommunityRoomQuery": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "communityId": {"type": "integer", "format": "int64"}, "buildingId": {"type": "integer", "format": "int64"}, "buildingNumber": {"type": "string"}, "roomNumber": {"type": "string"}}}, "CommunityRoom": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "房间ID"}, "roomNumber": {"type": "string", "description": "房间号"}, "type": {"type": "string", "description": "户型"}, "unitNumber": {"type": "string", "description": "单元号"}, "buildingId": {"type": "integer", "format": "int64", "description": "楼栋id"}, "area": {"type": "number", "format": "double", "description": "房间面积㎡"}, "expandData": {"type": "string", "description": "扩展数据"}, "note": {"type": "string", "description": "备注"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}}}, "PageInfoCommunityRoom": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityRoom"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityRoom": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityRoom"}}}, "CommunitySearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "communityName": {"type": "string"}}}, "Community": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区ID"}, "communityName": {"type": "string", "description": "小区名称"}, "note": {"type": "string", "description": "备注"}, "lng": {"type": "number", "format": "double", "description": "经度"}, "lat": {"type": "number", "format": "double", "description": "纬度"}, "address": {"type": "string", "description": "地址"}, "expandData": {"type": "string", "description": "扩展数据"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "orgId": {"type": "integer", "format": "int64", "description": "分组ID"}}}, "PageInfoCommunity": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Community"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunity": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunity"}}}, "CommunityBuildingSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "communityId": {"type": "integer", "format": "int64"}, "buildingNumber": {"type": "string"}, "type": {"type": "string"}}}, "CommunityBuilding": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "小区楼房ID"}, "buildingNumber": {"type": "string", "description": "建筑编号"}, "type": {"type": "string", "description": "类型"}, "lng": {"type": "number", "format": "double", "description": "纬度"}, "lat": {"type": "number", "format": "double", "description": "经度"}, "alt": {"type": "number", "format": "double", "description": "海拔"}, "expandData": {"type": "string", "description": "扩展数据"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "note": {"type": "string", "description": "备注"}, "sort": {"type": "integer", "format": "int32", "description": "序号"}, "communityId": {"type": "integer", "format": "int64", "description": "所属小区"}}}, "PageInfoCommunityBuilding": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityBuilding"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityBuilding": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityBuilding"}}}, "ResponseEntityUserVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/UserVO"}}}, "UserVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "userName": {"type": "string", "description": "用户名"}, "nickName": {"type": "string", "description": "昵称"}, "email": {"type": "string", "description": "邮箱"}, "phone": {"type": "string", "description": "手机号"}, "gender": {"type": "string", "description": "性别"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "status": {"type": "string", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "note": {"type": "string", "description": "备注"}, "orgId": {"type": "integer", "format": "int64", "description": "组织ID"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}}}, "PageInfoUserVO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserVO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoUserVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoUserVO"}}}, "ResponseEntityUserDetail": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/UserDetail"}}}, "UserDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "nickName": {"type": "string"}, "password": {"type": "string"}, "status": {"type": "string"}, "sex": {"type": "integer", "format": "int32"}, "orgId": {"type": "integer", "format": "int64"}, "avatarUrl": {"type": "string"}, "roleCode": {"type": "string"}, "phone": {"type": "string"}, "roleId": {"type": "integer", "format": "int64"}, "permissions": {"type": "string"}}}, "ResponseEntityRoleVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/RoleVO"}}}, "RoleVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "roleCode": {"type": "string", "description": "角色编码"}, "dataScope": {"type": "string", "description": "数据权限"}, "sort": {"type": "integer", "format": "int32", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "note": {"type": "string", "description": "备注"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "permissions": {"type": "array", "description": "权限ID（菜单id）", "items": {"type": "integer", "format": "int64"}}}}, "PageInfoRole": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoRole": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoRole"}}}, "Role": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "roleCode": {"type": "string", "description": "角色编码"}, "dataScope": {"type": "string", "description": "数据权限"}, "sort": {"type": "integer", "format": "int32", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "note": {"type": "string", "description": "备注"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}}}, "ResponseEntityResident": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Resident"}}}, "PageInfoResident": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Resident"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoResident": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoResident"}}}, "ResponseEntityPropertyPerson": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PropertyPerson"}}}, "PageInfoPropertyPerson": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyPerson"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoPropertyPerson": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoPropertyPerson"}}}, "ResponseEntityPropertyPaymentItems": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PropertyPaymentItems"}}}, "PageInfoPropertyPaymentItems": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyPaymentItems"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoPropertyPaymentItems": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoPropertyPaymentItems"}}}, "ResponseEntityPropertyBill": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PropertyBill"}}}, "PageInfoPropertyBill": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyBill"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoPropertyBill": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoPropertyBill"}}}, "Org": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "组织ID"}, "orgName": {"type": "string", "description": "组织名称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "status": {"type": "integer", "format": "int32", "description": "状态"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "note": {"type": "string", "description": "备注"}, "ancestors": {"type": "string", "description": "祖级列表/分割"}}}, "ResponseEntityOrg": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Org"}}}, "OrgVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "组织ID"}, "orgName": {"type": "string", "description": "组织名称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "children": {"description": "子级"}}}, "PageInfoOrgVO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/OrgVO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoOrgVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoOrgVO"}}}, "NoticeSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "title": {"type": "string", "description": "标题"}, "type": {"type": "string", "description": "类型"}, "targetType": {"type": "string", "description": "目标类型（all：全体，building：楼栋，resident：住户）"}, "top": {"type": "boolean", "description": "置顶"}}}, "PageInfoNotice": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Notice"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoNotice": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoNotice"}}}, "Menu": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "菜单ID"}, "menuName": {"type": "string", "description": "菜单名称"}, "menuType": {"type": "string", "description": "菜单类型"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "path": {"type": "string", "description": "路径"}, "componentPath": {"type": "string", "description": "组件路径"}, "icon": {"type": "string", "description": "图标"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "note": {"type": "string", "description": "备注"}}}, "ResponseEntityMenu": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Menu"}}}, "MenuDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "菜单ID"}, "menuName": {"type": "string", "description": "菜单名"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "path": {"type": "string", "description": "菜单路径（显示路径如/index）"}, "permission": {"type": "string", "description": "权限"}, "componentPath": {"type": "string", "description": "组件路径"}, "menuType": {"type": "string", "description": "菜单类型"}, "icon": {"type": "string", "description": "图标"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "note": {"type": "string", "description": "备注"}, "children": {"description": "子级"}}}, "PageInfoMenuDTO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDTO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoMenuDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoMenuDTO"}}}, "MenuVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "菜单ID"}, "menuName": {"type": "string", "description": "菜单名称"}, "menuType": {"type": "string", "description": "菜单类型"}, "parentId": {"type": "integer", "format": "int64", "description": "父级ID"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "path": {"type": "string", "description": "路径"}, "componentPath": {"type": "string", "description": "组件路径"}, "icon": {"type": "string", "description": "图标"}, "note": {"type": "string", "description": "备注"}}}, "ResponseEntityListMenuVO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MenuVO"}}}}, "Member": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "微信用户ID"}, "openid": {"type": "string", "description": "微信小程序用户openid"}, "unionid": {"type": "string", "description": "微信用户统一ID"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "userName": {"type": "string", "description": "用户名"}, "nickName": {"type": "string", "description": "昵称"}, "avatarUrl": {"type": "string", "description": "头像"}, "gender": {"type": "string", "description": "性别(man：男；woman：女)"}, "phone": {"type": "string", "description": "手机号"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "role": {"type": "string", "description": "角色"}}}, "ResponseEntityMember": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Member"}}}, "PageInfoMember": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/Member"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoMember": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoMember"}}}, "ResponseEntityDict": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Dict"}}}, "ResponseEntityCommunity": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Community"}}}, "PageInfoCommunityWorkOrder": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityWorkOrder"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityWorkOrder": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityWorkOrder"}}}, "ResponseEntityCommunityRoom": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityRoom"}}}, "CommunityResidentDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "人房ID"}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentId": {"type": "integer", "format": "int64", "description": "住户id"}, "residentName": {"type": "string", "description": "住户姓名"}, "birthday": {"type": "string", "format": "date-time", "description": "生日"}, "gender": {"type": "string", "description": "性别"}, "certificateType": {"type": "string", "description": "证件类型"}, "idCardNumber": {"type": "string", "description": "证件号码"}, "nativePlace": {"type": "string", "description": "籍贯"}, "note": {"type": "string", "description": "备注"}, "phone": {"type": "string", "description": "手机号"}, "address": {"type": "string", "description": "住址"}, "tags": {"type": "string", "description": "标签"}}}, "ResponseEntityCommunityResidentDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityResidentDTO"}}}, "CommunityResidentSearch": {"type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32", "minimum": 1}, "pageSize": {"type": "integer", "format": "int32", "maximum": 500}, "communityId": {"type": "integer", "format": "int64", "description": "小区ID"}, "residentName": {"type": "integer", "format": "int64", "description": "住户姓名"}, "idCardNumber": {"type": "string", "description": "证件号码"}}}, "PageInfoCommunityResidentDTO": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CommunityResidentDTO"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}}, "ResponseEntityPageInfoCommunityResidentDTO": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/PageInfoCommunityResidentDTO"}}}, "ResponseEntityCommunityBuilding": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/CommunityBuilding"}}}, "Document": {"type": "object", "properties": {"content": {"type": "string", "writeOnly": true}, "id": {"type": "string"}, "text": {"type": "string"}, "media": {"$ref": "#/components/schemas/Media"}, "metadata": {"type": "object", "additionalProperties": {"type": "object"}}, "score": {"type": "number", "format": "double"}}}, "Media": {"type": "object", "properties": {"id": {"type": "string"}, "mimeType": {"$ref": "#/components/schemas/MimeType"}, "data": {"type": "object"}, "name": {"type": "string"}, "dataAsByteArray": {"type": "string", "format": "byte"}}}, "MimeType": {"type": "object", "properties": {"type": {"type": "string"}, "subtype": {"type": "string"}, "parameters": {"type": "object", "additionalProperties": {"type": "string"}}, "charset": {"type": "string"}, "concrete": {"type": "boolean"}, "wildcardType": {"type": "boolean"}, "wildcardSubtype": {"type": "boolean"}, "subtypeSuffix": {"type": "string"}}}, "ResponseEntityListDocument": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}}, "ResponseEntityVerify": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "code": {"type": "integer", "format": "int32", "description": "状态码（0：正常，1：失败）"}, "data": {"$ref": "#/components/schemas/Verify"}}}, "Verify": {"type": "object", "properties": {"token": {"type": "string"}, "oriImage": {"type": "string", "format": "byte"}, "blockImage": {"type": "string", "format": "byte"}, "secretKey": {"type": "string", "format": "byte"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}