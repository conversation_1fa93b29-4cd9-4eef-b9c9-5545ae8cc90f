/* 居民信息审核列表样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 40rpx;
}

/* 搜索栏 */
.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 24rpx 32rpx;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #000000;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.filter-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  background: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='4' y1='21' x2='4' y2='14'%3E%3C/line%3E%3Cline x1='4' y1='10' x2='4' y2='3'%3E%3C/line%3E%3Cline x1='12' y1='21' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12' y2='3'%3E%3C/line%3E%3Cline x1='20' y1='21' x2='20' y2='16'%3E%3C/line%3E%3Cline x1='20' y1='12' x2='20' y2='3'%3E%3C/line%3E%3Cline x1='1' y1='14' x2='7' y2='14'%3E%3C/line%3E%3Cline x1='9' y1='8' x2='15' y2='8'%3E%3C/line%3E%3Cline x1='17' y1='16' x2='23' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 分类标签样式 */
.category-tabs {
  display: flex;
  padding: 16rpx 32rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
  overflow-x: auto;
  white-space: nowrap;
}

.tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  padding: 0 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
  transition: all 0.3s;
}

.tab.active {
  background: #FF8C00;
  color: #FFFFFF;
}

/* 审核列表样式 */
.review-list {
  padding: 24rpx 32rpx;
}

.review-card {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;

}

.review-content {
  padding: 32rpx;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.review-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.review-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-identity {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='16' rx='2'%3E%3C/rect%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Ccircle cx='12' cy='11' r='3'%3E%3C/circle%3E%3Cpath d='M17 18.5c-1.4-1-3.1-1.5-5-1.5s-3.6.5-5 1.5'%3E%3C/path%3E%3C/svg%3E");
}

.icon-house {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF453A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

.icon-vehicle {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='1' y='6' width='22' height='12' rx='2'%3E%3C/rect%3E%3Cpath d='M4 12h16'%3E%3C/path%3E%3Cpath d='M7 18v2'%3E%3C/path%3E%3Cpath d='M17 18v2'%3E%3C/path%3E%3Cpath d='M7 6v2'%3E%3C/path%3E%3Cpath d='M17 6v2'%3E%3C/path%3E%3C/svg%3E");
}

.review-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  padding: 0 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.review-tag.pending {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.review-tag.approved {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.review-tag.rejected {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.review-info {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

.review-actions {
  display: flex;
  justify-content: flex-end;
}

.btn-secondary {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
  height: 64rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  border: none;
}



/* 空状态样式 */
.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  background: rgba(142, 142, 147, 0.1);
  border-radius: 60rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='8.5' cy='7' r='4'%3E%3C/circle%3E%3Cline x1='18' y1='8' x2='23' y2='13'%3E%3C/line%3E%3Cline x1='23' y1='8' x2='18' y2='13'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 48rpx 48rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 32rpx 0;
  color: #8E8E93;
  font-size: 28rpx;
}

.loading-indicator {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.1);
  border-top: 4rpx solid #FF8C00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.modal-body {
  padding: 32rpx;
}

.reject-reason {
  width: 100%;
  height: 200rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  box-sizing: border-box;
}

.confirm-text {
  display: block;
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  padding: 24rpx 0;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  flex: 1;
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
}

.modal-footer button::after {
  border: none;
}

.modal-footer .btn-cancel {
  color: #8E8E93;
  background: transparent;
}

.modal-footer .btn-confirm {
  color: #FF8C00;
  font-weight: 600;
  background: transparent;
}
