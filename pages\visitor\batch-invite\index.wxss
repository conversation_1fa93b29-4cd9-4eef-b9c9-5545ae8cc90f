/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 批量邀请页面特定样式 */

/* 来访目的选择器样式 */
.visitor-purpose-options {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.visitor-purpose-option {
  width: 100%;
  padding: 16px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.visitor-purpose-option:last-child {
  border-bottom: none;
}

.visitor-purpose-option:active {
  background-color: #f5f5f5;
}
.batch-invite-intro {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-invite-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.batch-invite-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.batch-invite-title text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.batch-invite-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.batch-invite-common-info,
.batch-invite-visitors {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-invite-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.batch-invite-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
  margin-left: 8px;
}

.batch-visitor-list {
  margin-bottom: 16px;
}

.batch-visitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.batch-visitor-info {
  flex: 1;
}

.batch-visitor-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.batch-visitor-phone {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.batch-visitor-car {
  font-size: 14px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.batch-visitor-actions {
  display: flex;
  align-items: center;
}

.batch-visitor-edit,
.batch-visitor-delete {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.batch-visitor-action-icon {
  width: 20px;
  height: 20px;
}

.batch-visitor-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #999;
  font-size: 14px;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.add-visitor-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f5f7fa;
  border-radius: 8px;
  color: #4f46e5;
  font-size: 14px;
}

.add-visitor-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* 批量邀请结果弹窗样式 */
.batch-result-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.batch-result-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.batch-result-icon.success {
  background-color: #e6f7ef;
}

.batch-result-icon.error {
  background-color: #fff1f0;
}

.batch-result-icon image {
  width: 32px;
  height: 32px;
}

.batch-result-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.batch-result-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.batch-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.batch-result-item-info {
  display: flex;
  align-items: center;
}

.batch-result-item-name {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.batch-result-item-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.batch-result-item-status.success {
  background-color: #e6f7ef;
  color: #10b981;
}

.batch-result-item-status.error {
  background-color: #fff1f0;
  color: #ef4444;
}

.batch-result-view-btn {
  font-size: 12px;
  padding: 4px 8px;
  background-color: #4f46e5;
  color: #fff;
  border-radius: 4px;
  line-height: 1.5;
  min-height: 0;
}