const request = require('../utils/request.js')
const paymentApi = {

	//分页查询我的缴费账单列表
	queryMyPaymentList: function (params) {
		return request.request('/users-api/v1/property/payment/bill/page', 'GET', params, true)
	},

	//账单支付
	pay: function (params) {
		return request.request('/users-api/v1/property/payment/bill/pay', 'POST', params, true)
	},

	//账单支付通知
	payNotice: function (params) {
		return request.request('/users-api/v1/property/payment/bill/pay-notice', 'POST', params, true)
	},


	//根据id查询缴费详情
	getPaymentDetail: function (id) {
		return request.request('/users-api/v1/property/payment/bill?id='+id, 'GET', {}, true)
	},


}

module.exports = paymentApi