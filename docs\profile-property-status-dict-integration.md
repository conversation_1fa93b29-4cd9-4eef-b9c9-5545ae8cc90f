# 我的页面物业员工认证状态字典集成

## 概述
将我的页面中的物业员工认证状态显示改为使用`person_status`字典，实现动态状态显示和样式匹配。

## 修改内容

### 1. 数据结构增强

#### 添加字典数据字段
```javascript
// pages/profile/profile.js - data部分
data: {
  // ... 其他字段
  personStatusOptions: [], // 物业员工状态字典选项
}
```

### 2. 字典数据加载

#### 新增字典加载方法
```javascript
// 加载字典数据
loadDictionaries: function () {
  try {
    // 获取物业员工状态字典
    const personStatusDict = util.getDictByNameEn('person_status');
    
    this.setData({
      personStatusOptions: personStatusDict && personStatusDict.length > 0 && personStatusDict[0].children ? personStatusDict[0].children : []
    });

    console.log('我的页面字典数据加载完成:', {
      personStatusOptions: this.data.personStatusOptions
    });
  } catch (error) {
    console.error('加载我的页面字典数据失败:', error);
    this.setData({
      personStatusOptions: []
    });
  }
}
```

#### 页面初始化调用
```javascript
onLoad: function () {
  // 初始化页面数据
  this.loadDictionaries();
}
```

### 3. 认证状态检查逻辑重构

#### 原逻辑（简单判断）
```javascript
// 原代码 - 只是简单的已认证/未认证
if (isPropertyStaff) {
  personAuthBadgeText = '已认证';
  personAuthBadgeClass = 'property-verified';
}
```

#### 新逻辑（字典驱动，统一样式）
```javascript
// 设置物业员工认证状态文本和样式 - 使用person_status字典
let personAuthBadgeText = '未认证';
let personAuthBadgeClass = '';

if (isPropertyStaff && propertyInfo.personStatus) {
  // 使用person_status字典匹配状态
  const statusOption = this.data.personStatusOptions.find(option => option.nameEn === propertyInfo.personStatus);

  if (statusOption) {
    // 直接使用字典的nameCn作为显示文本
    personAuthBadgeText = statusOption.nameCn;
    // 统一使用物业员工认证样式，不区分具体状态
    personAuthBadgeClass = 'property-verified';
  } else {
    // 如果字典中找不到对应状态，显示原始状态值
    personAuthBadgeText = propertyInfo.personStatus || '未知状态';
    personAuthBadgeClass = 'property-verified';
  }
} else if (isPropertyStaff) {
  // 有物业员工标记但没有状态信息
  personAuthBadgeText = '已认证';
  personAuthBadgeClass = 'property-verified';
}
```

### 4. 样式类扩展

#### 统一样式类
```css
/* pages/profile/profile.wxss */

.auth-badge.property-verified {
  background-color: #007AFF; /* 物业员工认证统一颜色 - 蓝色 */
}
```

## 数据流程

### 1. 数据获取流程
```
页面加载 → loadDictionaries() → util.getDictByNameEn('person_status') → 设置personStatusOptions
```

### 2. 状态显示流程
```
checkAuthStatus() → 获取propertyInfo.personStatus → 在字典中查找匹配项 → 设置显示文本和样式类
```

### 3. 数据来源
- **字典数据**: `person_status`字典，通过`util.getDictByNameEn()`获取
- **状态数据**: `propertyInfo.personStatus`，从`wx.getStorageSync('propertyInfo')`获取
- **认证标记**: `isPropertyStaff`，从`wx.getStorageSync('isPropertyStaff')`获取

## 状态映射

### 字典状态与样式映射
| 状态值 (nameEn) | 显示文本 (nameCn) | 样式类 | 颜色 | 说明 |
|----------------|------------------|--------|------|------|
| 任何字典状态 | 对应的nameCn | `property-verified` | 蓝色 | 统一物业员工认证样式 |
| 字典外状态 | 原始状态值 | `property-verified` | 蓝色 | 兜底显示 |

### 兜底逻辑
1. **有认证标记但无状态**: 显示"已认证"，使用蓝色样式
2. **字典中找不到状态**: 显示原始状态值，使用蓝色样式
3. **无认证标记**: 显示"未认证"，使用默认样式

## 技术要点

### 1. 字典数据处理
```javascript
// 标准字典数据获取模式
const personStatusDict = util.getDictByNameEn('person_status');
const options = personStatusDict && personStatusDict.length > 0 && personStatusDict[0].children ? personStatusDict[0].children : [];
```

### 2. 状态匹配查找
```javascript
// 在字典选项中查找匹配的状态
const statusOption = this.data.personStatusOptions.find(option => option.nameEn === propertyInfo.personStatus);
```

### 3. 统一样式设置
```javascript
// 所有物业员工认证状态统一使用相同样式类
personAuthBadgeClass = 'property-verified';
```

### 4. 错误处理
- 字典加载失败时设置空数组
- 状态匹配失败时显示原始值
- 数据缺失时使用默认显示

## 用户体验改进

### 1. 状态可视化
- **统一样式**: 所有物业员工认证状态使用统一的蓝色样式，保持视觉一致性
- **文本清晰**: 使用字典中的中文描述，用户友好

### 2. 实时更新
- 页面显示时重新检查状态
- 支持状态变化的实时反映

### 3. 兼容性
- 向后兼容原有的简单认证标记
- 渐进式增强，不影响现有功能

## 测试验证

### 测试场景
1. **正常状态**: 有字典数据，有状态值，正确匹配显示字典nameCn
2. **字典缺失**: 字典加载失败，使用兜底逻辑
3. **状态缺失**: 有认证标记但无状态值，显示默认"已认证"
4. **状态未知**: 状态值不在字典中，显示原始值
5. **完全未认证**: 无认证标记，显示"未认证"

### 验证要点
- 字典数据正确加载
- 状态文本正确显示（使用nameCn而非硬编码）
- 样式类统一应用（都是property-verified）
- 颜色统一显示蓝色
- 兜底逻辑正常工作

## 总结

这个修改实现了：

1. **数据驱动**: 使用`person_status`字典动态显示状态
2. **视觉统一**: 所有物业员工认证状态使用统一的蓝色样式
3. **用户友好**: 显示中文状态描述而非英文代码
4. **健壮性**: 完善的错误处理和兜底逻辑
5. **可扩展**: 支持字典中新增状态类型，无需修改代码
6. **无硬编码**: 不依赖特定的状态值，完全由字典驱动

通过这个改进，我的页面能够更准确地显示物业员工的认证状态，同时保持视觉一致性，提升了用户体验和系统的可维护性。
