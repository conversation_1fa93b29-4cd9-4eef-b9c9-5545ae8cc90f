<!--recycle.wxml-->
<view class="container">
  <view class="page-header">
    <view class="page-title">废品回收</view>
    <view class="page-subtitle">变废为宝，保护环境</view>
  </view>
  
  <view class="recycle-banner">
    <image class="banner-image" src="https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" mode="aspectFill"></image>
    <view class="banner-content">
      <view class="banner-title">预约上门回收</view>
      <view class="banner-desc">专业回收，变废为宝</view>
    </view>
  </view>
  
  <view class="recycle-categories">
    <view class="category-title">回收品类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="selectCategory" data-id="{{item.id}}">
        <view class="category-icon {{item.selected ? 'selected' : ''}}">
          <icon class="iconfont {{item.icon}}"></icon>
        </view>
        <view class="category-name">{{item.name}}</view>
      </view>
    </view>
  </view>
  
  <view class="recycle-form">
    <view class="form-title">回收信息</view>
    
    <view class="form-group">
      <view class="form-label">回收地址</view>
      <view class="address-selector" bindtap="selectAddress">
        <view class="address-info" wx:if="{{address}}">
          <view class="address-detail">{{address.detail}}</view>
          <view class="address-contact">{{address.name}} {{address.phone}}</view>
        </view>
        <view class="address-placeholder" wx:else>请选择回收地址</view>
        <view class="address-arrow">></view>
      </view>
    </view>
    
    <view class="form-group">
      <view class="form-label">预约时间</view>
      <view class="time-selector">
        <picker mode="date" value="{{date}}" start="{{minDate}}" end="{{maxDate}}" bindchange="bindDateChange">
          <view class="picker-item">
            <view class="picker-value">{{date || '请选择日期'}}</view>
            <view class="picker-arrow">></view>
          </view>
        </picker>
        <picker mode="selector" value="{{timeIndex}}" range="{{timeSlots}}" bindchange="bindTimeChange">
          <view class="picker-item">
            <view class="picker-value">{{timeSlots[timeIndex] || '请选择时间段'}}</view>
            <view class="picker-arrow">></view>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="form-group">
      <view class="form-label">备注信息</view>
      <textarea class="form-textarea" placeholder="请填写备注信息，如物品数量、重量等" bindinput="inputRemark" value="{{remark}}"></textarea>
    </view>
  </view>
  
  <view class="recycle-tips">
    <view class="tips-title">回收须知</view>
    <view class="tips-content">
      <view class="tips-item">1. 请确保回收物品干净整洁，无污染</view>
      <view class="tips-item">2. 大件物品请提前告知，便于安排车辆</view>
      <view class="tips-item">3. 回收员上门后会根据物品实际情况进行称重计价</view>
      <view class="tips-item">4. 如有特殊情况需要取消预约，请提前2小时联系客服</view>
    </view>
  </view>
  
  <view class="submit-btn-wrapper">
    <button class="submit-btn" bindtap="submitOrder" disabled="{{!canSubmit}}">提交预约</button>
  </view>
</view>

<!-- 地址选择弹窗 -->
<view class="address-modal {{showAddressModal ? 'show' : ''}}" bindtap="closeAddressModal">
  <view class="address-modal-content" catchtap="stopPropagation">
    <view class="address-modal-header">
      <view class="address-modal-title">选择地址</view>
      <view class="address-modal-close" bindtap="closeAddressModal">×</view>
    </view>
    <view class="address-modal-body">
      <view class="address-list">
        <view class="address-item {{selectedAddressId === item.id ? 'selected' : ''}}" 
              wx:for="{{addressList}}" 
              wx:key="id" 
              bindtap="selectAddressItem" 
              data-id="{{item.id}}">
          <view class="address-item-info">
            <view class="address-item-detail">{{item.detail}}</view>
            <view class="address-item-contact">{{item.name}} {{item.phone}}</view>
          </view>
          <view class="address-item-check" wx:if="{{selectedAddressId === item.id}}">✓</view>
        </view>
      </view>
      <view class="add-address-btn" bindtap="addNewAddress">
        <icon class="iconfont icon-add"></icon>
        <text>新增地址</text>
      </view>
    </view>
    <view class="address-modal-footer">
      <button class="address-confirm-btn" bindtap="confirmAddress">确认选择</button>
    </view>
  </view>
</view>

<!-- 提交成功弹窗 -->
<view class="success-modal {{showSuccessModal ? 'show' : ''}}" bindtap="closeSuccessModal">
  <view class="success-modal-content" catchtap="stopPropagation">
    <view class="success-icon">
      <icon class="iconfont icon-success"></icon>
    </view>
    <view class="success-title">预约成功</view>
    <view class="success-desc">您的废品回收预约已提交成功</view>
    <view class="success-info">
      <view class="success-info-item">
        <view class="info-label">预约编号：</view>
        <view class="info-value">{{orderNo}}</view>
      </view>
      <view class="success-info-item">
        <view class="info-label">预约时间：</view>
        <view class="info-value">{{date}} {{timeSlots[timeIndex]}}</view>
      </view>
    </view>
    <view class="success-tips">回收员将在预约时间上门服务，请保持电话畅通</view>
    <button class="success-btn" bindtap="closeSuccessModal">我知道了</button>
  </view>
</view>
