/**
 * 公告详情页
 * 用于预览公告最终效果
 */

const noticeApi = require('../../../../../api/noticeApi');
const util = require('../../../../../utils/util');

Page({
  data: {
    darkMode: false,
    announcement: null, // 公告详情
    isLoading: true, // 是否正在加载
    isPreview: false, // 是否是预览模式
    noticeTypeDict: [], // 通知类型字典
    shouldRefreshList: false, // 是否需要刷新列表
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '公告详情'
    });

    // 初始化字典数据
    this.initDictData();

    // 判断是否是预览模式
    const isPreview = options.preview === 'true';
    this.setData({ isPreview });

    console.log('公告详情页参数', options);

    // 如果是预览模式，从缓存获取数据
    if (isPreview) {
      this.loadPreviewData();
    } else if (options.id) {
      // 否则从API获取数据
      this.loadAnnouncementDetail(options.id);

      // 记录查看的公告ID，用于调试
      wx.setStorage({
        key: 'last_viewed_announcement_id',
        data: options.id
      });
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    // 页面显示时检查是否需要刷新上级页面
    // 这里不做任何操作，刷新逻辑在onUnload中处理
  },

  onUnload: function() {
    // 页面卸载时，如果需要刷新列表，则通知上级页面
    if (this.data.shouldRefreshList) {
      const pages = getCurrentPages();
      if (pages.length >= 2) {
        const prevPage = pages[pages.length - 2];

        // 如果上一个页面是公告管理页面
        if (prevPage.route.includes('announcement/announcement')) {
          if (typeof prevPage.refreshAnnouncements === 'function') {
            prevPage.refreshAnnouncements();
          }
        }
      }
    }
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const noticeTypeDict = util.getDictByNameEn('notice_type')[0].children;
      this.setData({ noticeTypeDict });
    } catch (error) {
      console.error('初始化字典数据失败', error);
      // 设置默认字典数据
      this.setData({
        noticeTypeDict: [
          { nameEn: 'property_notice', nameCn: '物业通知' },
          { nameEn: 'emer_notice', nameCn: '紧急通知' },
          { nameEn: 'user_message', nameCn: '用户消息' }
        ]
      });
    }
  },

  // 加载公告详情
  loadAnnouncementDetail: function(id) {
    this.setData({ isLoading: true });

    console.log('加载公告详情', id);

    noticeApi.getNotice(id)
      .then(res => {
        console.log('公告详情数据', res);
        const announcement = this.processAnnouncementData(res.data || res);
        console.log('处理后的公告详情', announcement);

        this.setData({
          announcement,
          isLoading: false
        });
      })
      .catch(err => {
        console.error('加载公告详情失败', err);

        this.setData({ isLoading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载预览数据
  loadPreviewData: function() {
    wx.getStorage({
      key: 'announcement_preview',
      success: (res) => {
        const announcement = this.processAnnouncementData(res.data);

        this.setData({
          announcement,
          isLoading: false
        });
      },
      fail: () => {
        wx.showToast({
          title: '预览数据不存在',
          icon: 'none'
        });

        this.setData({ isLoading: false });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 处理公告数据
  processAnnouncementData: function(data) {
    // 格式化日期
    let publishTime = '';
    const createTime = data.createTime || data.publishTime;
    if (createTime) {
      if (typeof createTime === 'string') {
        if (createTime.includes('/')) {
          // 如果是"YYYY/MM/DD HH:MM:SS"格式
          const dateParts = createTime.split(' ')[0].split('/');
          const timeParts = createTime.split(' ')[1].split(':');
          publishTime = `${dateParts[0]}年${dateParts[1]}月${dateParts[2]}日 ${timeParts[0]}:${timeParts[1]}`;
        } else {
          // 如果是ISO格式
          const date = new Date(createTime);
          publishTime = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        }
      }
    }

    // 获取类型文本
    const typeText = this.getTypeText(data.type);

    // 处理图片 - 支持逗号分隔的多张图片
    let images = [];
    if (data.imageUrl) {
      const apiUrl = wx.getStorageSync('apiUrl');

      // 分割逗号分隔的图片URL
      const imageUrls = data.imageUrl.split(',').filter(url => url.trim());

      images = imageUrls.map(url => {
        const trimmedUrl = url.trim();
        // 如果已经是完整URL，直接使用；否则拼接API地址
        if (trimmedUrl.startsWith('http')) {
          return trimmedUrl;
        } else {
          return apiUrl + '/common-api/v1/file/' + trimmedUrl;
        }
      });
    }

    // 处理富文本内容
    const content = data.content || '';

    console.log('公告富文本内容', content);

    return {
      ...data,
      publishTime,
      typeText,
      images,
      content
    };
  },

  // 获取公告类型文本
  getTypeText: function(type) {
    const typeDict = this.data.noticeTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === type);
    return typeItem ? typeItem.nameCn : '通知';
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.announcement.images;

    if (!images || images.length === 0) {
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
      return;
    }

    // 确保索引有效
    const currentIndex = Math.max(0, Math.min(index, images.length - 1));

    wx.previewImage({
      current: images[currentIndex],
      urls: images,
      fail: (error) => {
        console.error('图片预览失败:', error);
        wx.showToast({
          title: '图片预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 图片加载失败处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    console.error(`图片加载失败，索引: ${index}`, e.detail);

    // 可以在这里设置默认图片或移除失败的图片
    // 暂时只记录错误，不做特殊处理
  },

  // 返回按钮点击
  onBackTap: function() {
    wx.navigateBack();
  }
});
