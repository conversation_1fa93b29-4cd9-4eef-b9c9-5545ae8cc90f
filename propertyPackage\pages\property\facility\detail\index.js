// pages/property/facility/detail/index.js
const util = require('../../../../../utils/util.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,

    // 设施ID
    facilityId: '',

    // 设施详情
    facility: null,

    // 维护历史
    maintenanceHistory: [],

    // 图片查看相关
    currentImageIndex: 0,
    showImageViewer: false,

    // 二维码相关
    showQRCode: false,

    // 详细信息标签页
    detailTabs: [
      { id: 'basic', name: '基本参数', active: true },
      { id: 'operation', name: '运行数据', active: false },
      { id: 'document', name: '关联文档', active: false }
    ],
    currentDetailTab: 'basic',

    // 用户权限
    userPermissions: {
      canEdit: true,
      canRepair: true,
      canMaintain: true,
      canInspect: true,
      canUpdateStatus: true
    }
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 获取设施ID
    if (options.id) {
      this.setData({
        facilityId: options.id
      });
      this.loadFacilityDetail(options.id);
    } else {
      wx.showToast({
        title: '设施ID无效',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载设施详情
  loadFacilityDetail: function(facilityId) {
    this.setData({ isLoading: true });

    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const facility = this.getMockFacilityDetail(facilityId);
      const maintenanceHistory = this.getMockMaintenanceHistory(facilityId);

      this.setData({
        facility: facility,
        maintenanceHistory: maintenanceHistory,
        isLoading: false
      });
    }, 500);
  },

  // 获取模拟设施详情
  getMockFacilityDetail: function(facilityId) {
    const facilities = {
      '1': {
        id: '1',
        name: '小区正门监控',
        code: 'CAM-001',
        category: 'monitor',
        categoryText: '监控设施',
        location: '小区正门',
        status: 'normal',
        statusText: '正常',
        qrCode: '/images/facility/qrcode.png',
        responsiblePerson: '张工',
        installDate: '2022-05-15',
        warrantyEndDate: '2025-05-15',
        lastMaintenance: '2023-10-15',
        nextMaintenance: '2024-01-15',
        brand: '海康威视',
        model: 'DS-2CD2T85G1-I8',
        supplier: '广州安防科技有限公司',
        contactPhone: '020-12345678',
        operationData: {
          onlineTime: '99.8%',
          storageUsage: '75%',
          resolution: '4MP'
        },
        documents: [
          { name: '产品说明书', url: 'https://example.com/docs/manual.pdf' },
          { name: '安装证明', url: 'https://example.com/docs/install.pdf' }
        ],
        images: [
          '/images/facility/camera1.png',
          '/images/facility/camera2.png'
        ]
      },
      '6': {
        id: '6',
        name: '小区正门门禁',
        code: 'ACC-001',
        category: 'door',
        categoryText: '门禁设施',
        location: '小区正门',
        status: 'normal',
        statusText: '正常',
        qrCode: '/images/facility/qrcode.png',
        responsiblePerson: '李工',
        installDate: '2022-06-10',
        warrantyEndDate: '2025-06-10',
        lastMaintenance: '2023-10-01',
        nextMaintenance: '2024-01-01',
        brand: '大华',
        model: 'DHI-ASI7213X-T1',
        supplier: '广州安防科技有限公司',
        contactPhone: '020-12345678',
        operationData: {
          openCount: '1253次/月',
          failureRate: '0.1%',
          batteryLevel: '85%'
        },
        documents: [
          { name: '产品说明书', url: 'https://example.com/docs/door_manual.pdf' }
        ],
        images: [
          '/images/facility/door1.png'
        ]
      },
      '8': {
        id: '8',
        name: '1号楼单元门',
        code: 'ACC-003',
        category: 'door',
        categoryText: '门禁设施',
        location: '1号楼',
        status: 'fault',
        statusText: '故障',
        qrCode: '/images/facility/qrcode.png',
        responsiblePerson: '李工',
        installDate: '2022-04-20',
        warrantyEndDate: '2025-04-20',
        lastMaintenance: '2023-08-15',
        nextMaintenance: '2023-11-15',
        brand: '大华',
        model: 'DHI-ASI7213X-T1',
        supplier: '广州安防科技有限公司',
        contactPhone: '020-12345678',
        operationData: {
          openCount: '856次/月',
          failureRate: '2.5%',
          batteryLevel: '45%'
        },
        documents: [
          { name: '产品说明书', url: 'https://example.com/docs/door_manual.pdf' }
        ],
        images: [
          '/images/facility/door2.png'
        ],
        faultDescription: '读卡器故障，无法识别门禁卡'
      },
      '9': {
        id: '9',
        name: '中央空调系统',
        code: 'HVAC-001',
        category: 'hvac',
        categoryText: '暖通设施',
        location: '小区公共区域',
        status: 'normal',
        statusText: '正常',
        qrCode: '/images/facility/qrcode.png',
        responsiblePerson: '王工',
        installDate: '2021-07-15',
        warrantyEndDate: '2026-07-15',
        lastMaintenance: '2023-09-15',
        nextMaintenance: '2023-12-15',
        brand: '格力',
        model: 'GMV-H450WL/A',
        supplier: '广州制冷设备有限公司',
        contactPhone: '020-87654321',
        operationData: {
          temperature: '25°C',
          humidity: '45%',
          energyConsumption: '125kWh/天'
        },
        documents: [
          { name: '产品说明书', url: 'https://example.com/docs/hvac_manual.pdf' },
          { name: '维护手册', url: 'https://example.com/docs/hvac_maintenance.pdf' }
        ],
        images: [
          '/images/facility/hvac1.png',
          '/images/facility/hvac2.png',
          '/images/facility/hvac3.png'
        ]
      }
    };

    return facilities[facilityId] || facilities['1'];
  },

  // 获取模拟维护历史
  getMockMaintenanceHistory: function(facilityId) {
    const histories = {
      '1': [
        {
          id: '101',
          type: 'maintenance',
          typeText: '保养',
          date: '2023-10-15',
          description: '定期保养，清洁镜头，检查连接线路',
          operator: '张工',
          result: '正常'
        },
        {
          id: '102',
          type: 'inspection',
          typeText: '巡检',
          date: '2023-09-20',
          description: '日常巡检，检查设备运行状态',
          operator: '李工',
          result: '正常'
        },
        {
          id: '103',
          type: 'repair',
          typeText: '维修',
          date: '2023-07-05',
          description: '设备离线，重启设备并更换电源适配器',
          operator: '张工',
          result: '已修复'
        }
      ],
      '8': [
        {
          id: '201',
          type: 'repair',
          typeText: '维修',
          date: '2023-11-02',
          description: '读卡器故障，无法识别门禁卡',
          operator: '李工',
          result: '处理中'
        },
        {
          id: '202',
          type: 'maintenance',
          typeText: '保养',
          date: '2023-08-15',
          description: '定期保养，清洁读卡器，检查门锁',
          operator: '李工',
          result: '正常'
        },
        {
          id: '203',
          type: 'inspection',
          typeText: '巡检',
          date: '2023-07-10',
          description: '日常巡检，检查设备运行状态',
          operator: '王工',
          result: '发现异常'
        }
      ]
    };

    return histories[facilityId] || histories['1'];
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 导航到编辑页面
  navigateToEdit: function() {
    const { facilityId } = this.data;
    wx.navigateTo({
      url: `/pages/property/facility/add/index?id=${facilityId}`
    });
  },

  // 切换详细信息标签页
  switchDetailTab: function(e) {
    const tabId = e.currentTarget.dataset.id;

    // 更新标签页状态
    const detailTabs = this.data.detailTabs.map(item => {
      return {
        ...item,
        active: item.id === tabId
      };
    });

    this.setData({
      detailTabs: detailTabs,
      currentDetailTab: tabId
    });
  },

  // 查看图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const { facility } = this.data;

    if (facility && facility.images && facility.images.length > 0) {
      this.setData({
        currentImageIndex: index,
        showImageViewer: true
      });
    }
  },

  // 关闭图片查看器
  closeImageViewer: function() {
    this.setData({
      showImageViewer: false
    });
  },

  // 显示二维码
  showQRCode: function() {
    this.setData({
      showQRCode: true
    });
  },

  // 关闭二维码
  closeQRCode: function() {
    this.setData({
      showQRCode: false
    });
  },

  // 添加图片
  addImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
      success: (res) => {
        // 这里应该是上传图片到服务器
        // 目前只是模拟添加到本地数据
        const tempFilePaths = res.tempFilePaths;
        const { facility } = this.data;

        if (facility) {
          const updatedFacility = {
            ...facility,
            images: [...(facility.images || []), tempFilePaths[0]]
          };

          this.setData({
            facility: updatedFacility
          });

          wx.showToast({
            title: '图片添加成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 发起报修
  initiateRepair: function() {
    const { facilityId } = this.data;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/repair/index?id=${facilityId}`
    });
  },

  // 记录维修/保养
  recordMaintenance: function() {
    const { facilityId } = this.data;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/maintenance/index?id=${facilityId}`
    });
  },

  // 扫码巡检
  scanInspection: function() {
    // 导航到巡检任务列表页
    wx.navigateTo({
      url: `/propertyPackage/pages/property/facility/tasks/index`
    });
  },

  // 更新状态
  updateStatus: function() {
    // 显示提示信息
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 查看监控
  viewMonitor: function() {
    // 显示提示信息
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 设备控制
  controlDevice: function() {
    // 显示提示信息
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    const { facilityId } = this.data;

    // 重新加载设施详情
    this.loadFacilityDetail(facilityId);

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  }
})
