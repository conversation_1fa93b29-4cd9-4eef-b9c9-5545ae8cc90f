// garbage.js
Page({
  data: {
    searchKeyword: '',
    currentCategory: 'all',
    showGuide: false,
    guideCategory: '',
    items: [
      { id: 1, name: '纸箱', category: 'recyclable' },
      { id: 2, name: '塑料瓶', category: 'recyclable' },
      { id: 3, name: '玻璃瓶', category: 'recyclable' },
      { id: 4, name: '易拉罐', category: 'recyclable' },
      { id: 5, name: '旧衣物', category: 'recyclable' },
      { id: 6, name: '废电池', category: 'harmful' },
      { id: 7, name: '过期药品', category: 'harmful' },
      { id: 8, name: '废荧光灯', category: 'harmful' },
      { id: 9, name: '废油漆桶', category: 'harmful' },
      { id: 10, name: '废水银温度计', category: 'harmful' },
      { id: 11, name: '剩菜剩饭', category: 'kitchen' },
      { id: 12, name: '果皮', category: 'kitchen' },
      { id: 13, name: '蛋壳', category: 'kitchen' },
      { id: 14, name: '茶叶渣', category: 'kitchen' },
      { id: 15, name: '骨头', category: 'kitchen' },
      { id: 16, name: '卫生纸', category: 'other' },
      { id: 17, name: '烟蒂', category: 'other' },
      { id: 18, name: '尿不湿', category: 'other' },
      { id: 19, name: '陶瓷碎片', category: 'other' },
      { id: 20, name: '一次性餐具', category: 'other' }
    ],
    filteredItems: []
  },

  onLoad: function() {
    this.filterItems('all')
  },

  onSearchInput: function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterItems(this.data.currentCategory, keyword)
  },

  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    })
    this.filterItems(this.data.currentCategory)
  },

  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category
    
    if (category !== 'all') {
      this.setData({
        showGuide: true,
        guideCategory: category
      })
    }
    
    this.setData({
      currentCategory: category
    })
    this.filterItems(category, this.data.searchKeyword)
  },

  filterItems: function(category, keyword = '') {
    let filtered = this.data.items
    
    // 按分类筛选
    if (category !== 'all') {
      filtered = filtered.filter(item => item.category === category)
    }
    
    // 按关键词搜索
    if (keyword) {
      filtered = filtered.filter(item => 
        item.name.indexOf(keyword) !== -1
      )
    }
    
    this.setData({
      filteredItems: filtered
    })
  },

  showDetail: function(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showModal({
      title: item.name,
      content: `${item.name}属于${this.getCategoryName(item.category)}，${this.getCategoryDescription(item.category)}`,
      showCancel: false
    })
  },

  closeGuide: function() {
    this.setData({
      showGuide: false
    })
  },

  getCategoryIcon: function(category) {
    switch(category) {
      case 'recyclable': return 'icon-recyclable'
      case 'harmful': return 'icon-harmful'
      case 'kitchen': return 'icon-kitchen'
      case 'other': return 'icon-other'
      default: return 'icon-all'
    }
  },

  getCategoryName: function(category) {
    switch(category) {
      case 'recyclable': return '可回收物'
      case 'harmful': return '有害垃圾'
      case 'kitchen': return '厨余垃圾'
      case 'other': return '其他垃圾'
      default: return '全部'
    }
  },

  getCategoryDescription: function(category) {
    switch(category) {
      case 'recyclable':
        return '可回收物是指适宜回收和资源利用的生活垃圾，如纸类、塑料、金属、玻璃、织物等。'
      case 'harmful':
        return '有害垃圾是指对人体健康或自然环境造成直接或潜在危害的生活垃圾，如废电池、废荧光灯管、废水银温度计等。'
      case 'kitchen':
        return '厨余垃圾是指居民日常生活中产生的食物残渣、果皮等易腐烂的生物质生活废弃物。'
      case 'other':
        return '其他垃圾是指除可回收物、有害垃圾、厨余垃圾以外的其他生活垃圾。'
      default:
        return ''
    }
  },

  getCategoryExamples: function(category) {
    switch(category) {
      case 'recyclable':
        return '报纸、纸箱、塑料瓶、玻璃瓶、易拉罐、金属制品、旧衣物等'
      case 'harmful':
        return '废电池、过期药品、废荧光灯管、废水银温度计、废油漆桶等'
      case 'kitchen':
        return '剩菜剩饭、果皮、蛋壳、茶叶渣、骨头等'
      case 'other':
        return '卫生纸、烟蒂、尿不湿、陶瓷碎片、一次性餐具等'
      default:
        return ''
    }
  },

  getCategoryTips: function(category) {
    switch(category) {
      case 'recyclable':
        return '轻投轻放，清洁干燥，避免污染；纸张尽量平整；立体包装物请清空内容物，清洁后压扁投放。'
      case 'harmful':
        return '投放时请注意轻放；易破损的请连同包装或包裹后投放；如易挥发，请密封后投放。'
      case 'kitchen':
        return '沥干水分后再投放；有包装物的厨余垃圾应将包装物去除后分类投放；盛放厨余垃圾的容器，如塑料袋等，应投放到其他垃圾容器。'
      case 'other':
        return '尽量沥干水分；难以辨识类别的生活垃圾投入其他垃圾容器内。'
      default:
        return ''
    }
  }
})
