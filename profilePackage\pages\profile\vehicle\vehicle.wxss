/* 我的车辆页面样式 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0px 20px;
  margin-bottom: 80px;
  box-sizing: border-box;
  height: calc(100vh - 80px); /* 调整高度 */
}

/* 车辆列表样式 */
.vehicle-list {
  margin-top: 10px; /* 添加顶部间距 */
  margin-bottom: 20px;
}

.vehicle-card {
  background-color: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
}

.vehicle-plate {
  font-size: 20px;
  font-weight: 600;
  color: #1c1c1e;
}

.vehicle-status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 10px;
  background-color: #F2F2F7;
}

.vehicle-status.pending {
  color: #FF9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.vehicle-status.approved {
  color: #34C759;
  background-color: rgba(52, 199, 89, 0.1);
}

.vehicle-status.rejected {
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
}

.vehicle-info {
  padding: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 80px;
  color: #8E8E93;
  font-size: 15px;
}

.info-value {
  flex: 1;
  color: #1c1c1e;
  font-size: 15px;
  display: flex;
  align-items: center;
}

.primary-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  background-color: #007AFF;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.vehicle-actions {
  display: flex;
  border-top: 1px solid rgba(60, 60, 67, 0.1);
}

.vehicle-action {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  color: #007AFF;
  font-size: 15px;
  position: relative;
}

.vehicle-action:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 12px;
  bottom: 12px;
  width: 1px;
  background-color: rgba(60, 60, 67, 0.1);
}

.vehicle-action:active {
  background-color: #F2F2F7;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #8E8E93;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-icon image {
  width: 100%;
  height: 100%;
}



.empty-text {
  font-size: 17px;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 15px;
  margin-bottom: 24px;
}

/* 右下角固定添加按钮 */
.floating-add-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  background-color: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  z-index: 999;
  transition: all 0.3s ease;
}

.floating-add-button:active {
  transform: scale(0.95);
  background-color: #0062CC;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
}

.add-icon {
  color: white;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
}

/* 颜色点样式 */
.color-dot {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid rgba(60, 60, 67, 0.1);
}

/* 编辑模态框样式 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.edit-modal.show {
  opacity: 1;
  visibility: visible;
}

.edit-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.edit-modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.edit-modal.show .edit-modal-content {
  transform: translateY(0);
}

.edit-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1c1c1e;
  text-align: center;
  flex: 1;
}

.edit-modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  font-size: 24px;
  position: absolute;
  right: 16px;
  top: 16px;
}

.edit-modal-body {
  padding: 16px 20px;
}

.form-group {
  margin-bottom: 16px;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  color: #8E8E93;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(60, 60, 67, 0.3);
  border-radius: 10px;
  font-size: 16px;
  color: #1c1c1e;
  background-color: #F2F2F7;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-height: 44px;
}

.form-input.disabled {
  color: #666;
}

.plate-hint {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 4px;
}

.color-display {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(60, 60, 67, 0.3);
  border-radius: 10px;
  font-size: 16px;
  color: #1c1c1e;
  background-color: #F2F2F7;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.color-display.empty {
  color: #C7C7CC;
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 40px;
  color: #C7C7CC;
  font-size: 20px;
  pointer-events: none;
}

.form-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-modal-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.btn {
  padding: 12px 0;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  flex: 1;
  border: none;
  line-height: 1.5;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #F2F2F7;
  color: #8E8E93;
  margin-right: 8px;
}

.btn-save {
  background-color: #007AFF;
  color: white;
  margin-left: 8px;
}

.btn:active {
  transform: scale(0.98);
}

/* 颜色选择器弹窗样式 */
.color-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(5px);
}

.color-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.color-selector-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1101;
  box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.15);
}

.color-selector-modal.show .color-selector-content {
  transform: translateY(0);
}

.color-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
}

.color-selector-title {
  font-size: 18px;
  font-weight: 600;
  color: #1c1c1e;
}

.close-button {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  font-size: 24px;
}

.color-options {
  display: flex;
  flex-direction: column;
}

.color-option {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  font-size: 16px;
  color: #1c1c1e;
  border-bottom: 1px solid rgba(60, 60, 67, 0.1);
  transition: background-color 0.2s ease;
}

.color-option:last-child {
  border-bottom: none;
}

.color-option:active {
  background-color: #F2F2F7;
}

.color-option.selected {
  color: #007AFF;
}

.color-option.selected::after {
  content: "✓";
  margin-left: auto;
  color: #007AFF;
  font-weight: bold;
}
