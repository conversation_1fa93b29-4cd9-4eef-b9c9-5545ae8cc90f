
//获取消息未读统计
/users-api/v1/message/count-unread GET
响应
{
  "errorMessage": "string",
  "code": 1073741824, 
  "data": {
    "sysMessageCount": 1073741824,//系统消息未读角标数量
    "noticeMessageCount": 1073741824,//公告消息未读角标数量
    "privateMessageCount": 1073741824,//私信消息未读角标数量
    "totalMessageCount": 1073741824 //底部菜单未读消息角标总数量
  }
}

//标记系统消息已读
'/users-api/v1/message/sys-message/read?id='+id POST

//标记私信已读
'/users-api/v1/message/private-message/read?id='+id POST

//标记通知公告已读
'/users-api/v1/notice/read?id='+id POST

