// 访客统计页面
const propertyApi = require('@/api/propertyApi.js')
import * as echarts from "@/components/ec-canvas/echarts"

Page({
  data: {
    activeTab: 'trend', // 当前激活的标签页：trend-趋势分析，duration-滞留时长，purpose-来访目的，parking-停车占用
    timeRange: 'month', // 时间范围：month-月度，week-周度
    isLoading: false, // 是否正在加载
    chartRendered: false, // 图表是否已渲染
    timeLevel: 2,//0今日 1周度 2月度 3季度 4年度

    // API返回的原始数据
    averageStayDuration: 0,
    purposeDistribution: {},
    stayDurationDistribution: {},
    timeSlotDistribution: {},
    todayVisited: 0,
    totalVisitors: 0,
    vehicleParkingDuration: {},

    // 处理后的图表数据
    trendData: {},
    durationData: {},
    purposeData: {},
    parkingData: {},

    // ECharts配置
    ec: {
      lazyLoad: true
    },
    ecTrend: {
      lazyLoad: true
    },
    ecDuration: {
      lazyLoad: true
    },
    ecPurpose: {
      lazyLoad: true
    },
    ecParking: {
      lazyLoad: true
    },
    ecParkingDuration: {
      lazyLoad: true
    }
  },

  onLoad: function () {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '访客统计'
    });

    // 加载统计数据
    this.loadStatisticsData();
  },

  onShow: function () {
    // 如果图表未渲染，则渲染图表
    if (!this.data.chartRendered) {
      this.renderCharts();
    }
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });

    // 切换标签页后重新渲染图表
    this.renderCharts();
  },

  // 切换时间范围
  switchTimeRange: function (e) {
    const range = e.currentTarget.dataset.range;
    let timeLevel = 2; // 默认月度
    if (range === 'week') {
      timeLevel = 1; // 周度
    }

    this.setData({
      timeRange: range,
      timeLevel: timeLevel
    });

    // 切换时间范围后重新加载数据并渲染图表
    this.loadStatisticsData();
  },

  // 加载统计数据
  loadStatisticsData: function () {
    this.setData({ isLoading: true });

    const params = {
      timeLevel: this.data.timeLevel,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    propertyApi.getVisitorStatistics(params).then(res => {
      console.log('访客统计数据:', res);

      // 更新原始数据，包括API返回的trendData
      this.setData({
        averageStayDuration: res.averageStayDuration || 0,
        purposeDistribution: res.purposeDistribution || {},
        stayDurationDistribution: res.stayDurationDistribution || {},
        timeSlotDistribution: res.timeSlotDistribution || {},
        todayVisited: res.todayVisited || 0,
        totalVisitors: res.totalVisitors || 0,
        vehicleParkingDuration: res.vehicleParkingDuration || {},
        apiTrendData: res.trendData || [], // 保存API返回的趋势数据
        isLoading: false
      });

      // 处理数据并生成图表数据
      this.processStatisticsData();

      // 渲染图表
      setTimeout(() => {
        this.renderCharts();
      }, 500);

    }).catch(err => {
      console.error('获取访客统计数据失败:', err);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    });
  },

  // 处理统计数据
  processStatisticsData: function() {
    // 处理趋势数据
    const trendData = this.generateTrendData();

    // 处理滞留时长数据
    const durationData = this.generateDurationData();

    // 处理来访目的数据
    const purposeData = this.generatePurposeData();

    // 处理停车数据
    const parkingData = this.generateParkingData();

    this.setData({
      trendData,
      durationData,
      purposeData,
      parkingData
    });
  },

  // 生成趋势数据
  generateTrendData: function () {
    // 使用API返回的真实趋势数据
    const apiTrendData = this.data.apiTrendData || [];
    const timeLevel = this.data.timeLevel;
    const labels = [];
    const data = [];

    if (timeLevel === 1) {
      // 周度数据：7天
      const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      for (let i = 0; i < 7; i++) {
        labels.push(weekDays[i]);
        data.push(apiTrendData[i] || 0);
      }
    } else if (timeLevel === 2) {
      // 月度数据：31天
      const today = new Date();
      const currentMonth = today.getMonth() + 1;

      for (let i = 0; i < 31; i++) {
        const day = i + 1;
        labels.push(`${currentMonth}/${day}`);
        data.push(apiTrendData[i] || 0);
      }
    }

    // 计算总数和平均值
    const total = data.reduce((sum, value) => sum + value, 0);
    const avgDaily = data.length > 0 ? Math.round(total / data.length) : 0;

    return {
      labels,
      data,
      total: total || this.data.totalVisitors,
      avgDaily
    };
  },

  // 生成滞留时长数据
  generateDurationData: function () {
    // 使用API返回的滞留时长分布数据
    const stayDuration = this.data.stayDurationDistribution;
    const durations = [
      { label: '0-30分钟', value: stayDuration['0-30min'] || 0 },
      { label: '30-60分钟', value: stayDuration['30-60min'] || 0 },
      { label: '1-2小时', value: stayDuration['1-2hour'] || 0 },
      { label: '3-4小时', value: stayDuration['3-4hour'] || 0 },
      { label: '4小时以上', value: stayDuration['4hour+'] || 0 }
    ];

    // 基于时间段分布生成热力图数据
    const timeSlot = this.data.timeSlotDistribution;
    const heatmap = [];
    const timeSlots = ['morning', 'afternoon', 'evening'];
    const hours = [
      [6, 7, 8, 9, 10, 11], // 上午
      [12, 13, 14, 15, 16, 17], // 下午
      [18, 19, 20, 21, 22, 23] // 晚上
    ];

    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour++) {
        let value = 0;
        timeSlots.forEach((slot, slotIndex) => {
          if (hours[slotIndex].includes(hour)) {
            value = Math.floor((timeSlot[slot] || 0) / 6); // 平均分配到各小时
          }
        });
        heatmap.push({ hour, day, value });
      }
    }

    return {
      durations,
      heatmap,
      avgDuration: this.data.averageStayDuration
    };
  },

  // 生成来访目的数据
  generatePurposeData: function () {
    // 使用API返回的来访目的分布数据
    const purposeDistribution = this.data.purposeDistribution;
    const purposes = [];
    let total = 0;

    // 转换目的分布数据
    Object.keys(purposeDistribution).forEach(key => {
      const value = purposeDistribution[key] || 0;
      purposes.push({
        label: key,
        value: value
      });
      total += value;
    });

    // 如果没有数据，使用默认数据
    if (purposes.length === 0) {
      const defaultPurposes = [
        { label: '探亲访友', value: 0 },
        { label: '快递配送', value: 0 },
        { label: '家政服务', value: 0 },
        { label: '商务拜访', value: 0 },
        { label: '维修服务', value: 0 },
        { label: '其他', value: 0 }
      ];
      purposes.push(...defaultPurposes);
    }

    // 计算百分比
    const purposesWithPercentage = purposes.map(item => ({
      ...item,
      percentage: total > 0 ? Math.round((item.value / total) * 100) : 0
    }));

    return {
      purposes: purposesWithPercentage,
      total: total || this.data.totalVisitors
    };
  },

  // 生成停车占用数据
  generateParkingData: function () {
    // 使用API返回的车辆停车时长分布数据
    const vehicleParking = this.data.vehicleParkingDuration;
    const parkingDurations = [
      { label: '0-1小时', value: vehicleParking['0-1hour'] || 0 },
      { label: '1-2小时', value: vehicleParking['1-2hour'] || 0 },
      { label: '2-4小时', value: vehicleParking['2-4hour'] || 0 },
      { label: '4-8小时', value: vehicleParking['4-8hour'] || 0 },
      { label: '8小时以上', value: vehicleParking['8hour+'] || 0 }
    ];

    // 计算总车辆数
    const totalCars = parkingDurations.reduce((sum, item) => sum + item.value, 0);

    // 计算平均占用率（基于停车时长分布）
    const occupancyRate = totalCars > 0 ? Math.min(Math.round((totalCars / 100) * 100), 100) : 0;

    // 基于时间段分布生成每小时停车占用率
    const timeSlot = this.data.timeSlotDistribution;
    const hourlyOccupancy = [];
    for (let hour = 0; hour < 24; hour++) {
      let rate = 0;
      if (hour >= 6 && hour < 12) {
        // 上午时段
        rate = Math.floor((timeSlot.morning || 0) / 6 * 10); // 转换为百分比
      } else if (hour >= 12 && hour < 18) {
        // 下午时段
        rate = Math.floor((timeSlot.afternoon || 0) / 6 * 10);
      } else if (hour >= 18 && hour < 24) {
        // 晚上时段
        rate = Math.floor((timeSlot.evening || 0) / 6 * 10);
      }

      hourlyOccupancy.push({
        hour,
        rate: Math.min(rate, 100) // 确保不超过100%
      });
    }

    return {
      parkingDurations,
      occupancyRate,
      hourlyOccupancy,
      totalCars
    };
  },

  // 渲染图表
  renderCharts: function () {
    // 根据当前标签页渲染不同的图表
    switch (this.data.activeTab) {
      case 'trend':
        this.renderTrendChart();
        break;
      case 'duration':
        this.renderDurationChart();
        break;
      case 'purpose':
        this.renderPurposeChart();
        break;
      case 'parking':
        this.renderParkingChart();
        this.renderParkingDurationChart();
        break;
    }

    this.setData({ chartRendered: true });
  },

  // 渲染趋势图表
  renderTrendChart: function () {
    const trendData = this.data.trendData;
    if (!trendData.labels || !trendData.data) return;

    const ec_canvas = this.selectComponent('#trendChart');
    if (!ec_canvas) return;

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const option = {
        title: {
          text: '访客趋势',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}人'
        },
        xAxis: {
          type: 'category',
          data: trendData.labels,
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [{
          data: trendData.data,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#4f46e5'
          },
          itemStyle: {
            color: '#4f46e5'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
                { offset: 1, color: 'rgba(79, 70, 229, 0.1)' }
              ]
            }
          }
        }]
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 渲染滞留时长图表
  renderDurationChart: function () {
    const durationData = this.data.durationData;
    if (!durationData.durations) return;

    const ec_canvas = this.selectComponent('#durationChart');
    if (!ec_canvas) return;

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const option = {
        title: {
          text: '滞留时长分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}人 ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [{
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: durationData.durations.map(item => ({
            name: item.label,
            value: item.value
          })),
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            fontSize: 10
          }
        }]
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 渲染来访目的图表
  renderPurposeChart: function () {
    const purposeData = this.data.purposeData;
    if (!purposeData.purposes) return;

    const ec_canvas = this.selectComponent('#purposeChart');
    if (!ec_canvas) return;

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const option = {
        title: {
          text: '来访目的分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}人'
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'category',
          data: purposeData.purposes.map(item => item.label),
          axisLabel: {
            fontSize: 10
          }
        },
        series: [{
          type: 'bar',
          data: purposeData.purposes.map(item => item.value),
          itemStyle: {
            color: '#3b82f6'
          },
          barWidth: '60%'
        }]
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 渲染停车占用图表
  renderParkingChart: function () {
    const parkingData = this.data.parkingData;
    if (!parkingData.hourlyOccupancy) return;

    const ec_canvas = this.selectComponent('#parkingChart');
    if (!ec_canvas) return;

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const option = {
        title: {
          text: '停车占用率',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}时: {c}%'
        },
        xAxis: {
          type: 'category',
          data: parkingData.hourlyOccupancy.map(item => item.hour + '时'),
          axisLabel: {
            fontSize: 10,
            interval: 3 // 每4个小时显示一个标签
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            fontSize: 10,
            formatter: '{value}%'
          }
        },
        series: [{
          data: parkingData.hourlyOccupancy.map(item => item.rate),
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#10b981'
          },
          itemStyle: {
            color: '#10b981'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
              ]
            }
          }
        }]
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 渲染停车时长分布图表
  renderParkingDurationChart: function () {
    const parkingData = this.data.parkingData;
    if (!parkingData.parkingDurations) return;

    const ec_canvas = this.selectComponent('#parkingDurationChart');
    if (!ec_canvas) return;

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const option = {
        title: {
          text: '停车时长分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}辆 ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          data: parkingData.parkingDurations.map(item => ({
            name: item.label,
            value: item.value
          })),
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            fontSize: 10
          }
        }]
      };

      chart.setOption(option);
      return chart;
    });
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '数据导出功能开发中',
      icon: 'none'
    });
  }
});
