<!--便民信息黄页分类页面-->
<view class="container">

  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/icons/search.svg" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索{{categoryName}}服务" bindinput="onSearchInput" value="{{searchKeyword}}"/>
      <view class="search-clear" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="/images/icons/clear.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 服务列表 -->
  <view class="service-list">
    <view class="service-item" wx:for="{{filteredServices}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="service-content">
        <view class="service-name">{{item.name}}</view>
        <view class="service-desc">{{item.description}}</view>
        <view class="service-info">
          <view class="service-address" wx:if="{{item.address}}">
            <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
            <text>{{item.address}}</text>
          </view>
          <view class="service-phone" wx:if="{{item.phone}}">
            <image class="info-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
            <text>{{item.phone}}</text>
          </view>
        </view>
      </view>
      <view class="service-arrow">
        <image src="/images/icons/arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredServices.length === 0}}">
    <image class="empty-image" src="/images/illustrations/empty-search.svg" mode="aspectFit"></image>
    <view class="empty-text">暂无{{categoryName}}服务</view>
    <view class="empty-subtext">我们正在努力收集更多服务信息</view>
  </view>
</view>
