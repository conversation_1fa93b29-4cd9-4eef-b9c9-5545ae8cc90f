/* pages/property/workorder/batch-assign/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #ff8c00; /* 主品牌色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #ff8c00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 主要内容 */
.content {
  flex: 1;
  margin-top: 88px; /* 导航栏高度(44px) + 状态栏高度(~44px) */
  padding: 16px;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

/* 标题样式 */
.section-title {
  margin: 16px 0;
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 12px;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

/* 工单列表样式 */
.order-list {
  background-color: #fff;
  border-radius: 12px;
  padding: 8px 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.order-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.repair-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z'%3E%3C/path%3E%3C/svg%3E");
}

.complaint-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
}

.suggestion-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z'%3E%3C/path%3E%3C/svg%3E");
}

.other-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
}

.order-info {
  flex: 1;
  margin-right: 12px;
}

.order-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-meta {
  font-size: 12px;
  color: #999;
}

.order-id {
  margin-right: 12px;
}

.order-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 100px;
  background-color: #f5f5f5;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.status-pending .status-text {
  color: #ff9800;
}

.status-processing .status-text {
  color: #2196f3;
}

/* 分配表单样式 */
.assign-form {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 员工列表样式 */
.staff-list {
  margin-bottom: 16px;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 8px;
}

.staff-item:last-child {
  margin-bottom: 0;
}

.staff-item.selected {
  background-color: #fff7e6;
  border: 1px solid #ffcc80;
}

.staff-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.avatar-icon {
  width: 24px;
  height: 24px;
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.staff-position {
  font-size: 12px;
  color: #999;
}

.staff-check {
  width: 24px;
  height: 24px;
}

.check-icon {
  width: 24px;
  height: 24px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-textarea {
  width: 100%;
  height: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16px;
}

.btn-submit {
  background-color: #ff8c00;
  color: #fff;
}

.btn-cancel:active {
  background-color: #e0e0e0;
}

.btn-submit:active {
  background-color: #e67e00;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .order-list,
.darkMode .assign-form,
.darkMode .bottom-buttons {
  background-color: #2a2a2a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.darkMode .title-text,
.darkMode .order-title,
.darkMode .staff-name {
  color: #fff;
}

.darkMode .order-meta,
.darkMode .staff-position,
.darkMode .form-tips {
  color: #999;
}

.darkMode .order-item {
  border-bottom-color: #333;
}

.darkMode .staff-item {
  border-bottom-color: #333;
}

.darkMode .staff-item.selected {
  background-color: #3a3a3a;
  border-color: #555;
}

.darkMode .form-textarea {
  background-color: #333;
  border-color: #444;
  color: #fff;
}

.darkMode .btn-cancel {
  background-color: #333;
  color: #ccc;
}

.darkMode .btn-cancel:active {
  background-color: #444;
}
