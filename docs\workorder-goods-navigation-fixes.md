# 工单和商品页面导航问题修复

## 问题概述
1. 新增工单后返回列表页时需要刷新列表
2. 商品详情页跳转订单详情页时参数传递问题

## 问题1：工单新增后列表页刷新

### 当前实现状态
**已正确实现**，无需修改。

#### 工单新增页面 (`servicePackage/pages/repair/repair.js`)
```javascript
// 创建工单成功后
workOrderApi.createWorkOrder(workOrderData)
  .then(res => {
    // 设置刷新标记，确保返回工单列表时刷新
    wx.setStorageSync('workOrderListNeedRefresh', true)
    
    this.hideLoading()
    this.showAlert('工单提交成功', '提交成功')
    
    // 延迟返回上一页
    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  })
```

#### 工单列表页面 (`servicePackage/pages/workorder/list/index.js`)
```javascript
onShow() {
  // 页面显示时检查是否需要刷新
  const needRefresh = wx.getStorageSync('workOrderListNeedRefresh')
  if (needRefresh) {
    console.log('检测到工单状态变化，刷新列表')
    // 清除刷新标记
    wx.removeStorageSync('workOrderListNeedRefresh')
    // 刷新工单列表
    this.refreshWorkOrders()
  }
}
```

### 刷新机制说明
1. **设置标记**：工单创建成功后设置 `workOrderListNeedRefresh` 标记
2. **检测标记**：列表页面 `onShow` 时检查标记
3. **执行刷新**：如果标记存在，清除标记并刷新列表
4. **适用场景**：新增、取消、状态变更等操作

## 问题2：商品详情页跳转订单详情页参数问题

### 问题分析
订单ID可能是长字符串，在URL传递时可能包含特殊字符，导致参数解析失败。

### 解决方案

#### 商品详情页面修复 (`profilePackage/pages/goods/detail/detail.js`)

**原代码问题**：
```javascript
// 可能存在特殊字符或长度问题
wx.navigateTo({
  url: '/profilePackage/pages/goods/order/order?id='+res.data+'&searchType=buy'
});
```

**修复后代码**：
```javascript
// 跳转到订单详情页
setTimeout(() => {
  // 确保订单ID是字符串并进行URL编码
  const orderId = String(res.data);
  const encodedOrderId = encodeURIComponent(orderId);
  
  console.log('跳转参数：', {
    原始ID: res.data,
    字符串ID: orderId,
    编码后ID: encodedOrderId
  });
  
  wx.navigateTo({
    url: `/profilePackage/pages/goods/order/order?id=${encodedOrderId}&searchType=buy`
  });
}, 2500);
```

#### 订单详情页面修复 (`profilePackage/pages/goods/order/order.js`)

**原代码问题**：
```javascript
onLoad: function (options) {
  if (options.id && options.searchType) {
    this.setData({
      orderId: options.id,  // 直接使用，可能包含编码字符
      searchType: options.searchType
    })
    this.loadOrderDetail(options.id)
  }
}
```

**修复后代码**：
```javascript
onLoad: function (options) {
  console.log('订单详情页面onLoad，接收到的options：', options);
  
  // 获取暗黑模式设置
  this.setData({
    darkMode: app.globalData.darkMode,
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
  });

  // 加载订单状态字典
  this.loadOrderStatusDictionary();

  if (options.id && options.searchType) {
    // 解码订单ID，防止特殊字符问题
    const orderId = decodeURIComponent(options.id);
    
    console.log('订单ID处理：', {
      原始ID: options.id,
      解码后ID: orderId,
      搜索类型: options.searchType
    });
    
    this.setData({
      orderId: orderId,
      searchType: options.searchType
    })
    this.loadOrderDetail(orderId)
  } else {
    console.error('缺少必要参数：', {
      hasId: !!options.id,
      hasSearchType: !!options.searchType,
      options: options
    });
    
    wx.showToast({
      title: '订单参数错误',
      icon: 'none'
    });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
}
```

## 修复要点

### 1. URL参数编码处理
- **发送方**：使用 `encodeURIComponent()` 编码参数
- **接收方**：使用 `decodeURIComponent()` 解码参数
- **类型转换**：确保参数是字符串类型

### 2. 错误处理增强
- **参数验证**：检查必要参数是否存在
- **调试日志**：添加详细的调试信息
- **用户反馈**：参数错误时给出友好提示

### 3. 数据类型安全
- **强制转换**：`String(res.data)` 确保是字符串
- **空值检查**：验证参数存在性
- **类型日志**：记录参数类型信息

## 常见问题和解决方案

### 问题1：订单ID包含特殊字符
**解决方案**：使用 `encodeURIComponent` 和 `decodeURIComponent`

### 问题2：订单ID过长
**解决方案**：URL编码可以处理长字符串，微信小程序支持较长的URL

### 问题3：参数为undefined
**解决方案**：
- 检查API返回的数据结构
- 验证 `res.data` 是否存在
- 添加参数验证逻辑

### 问题4：页面跳转失败
**解决方案**：
- 检查页面路径是否正确
- 验证参数格式是否正确
- 添加跳转失败的错误处理

## 测试验证

### 测试场景1：正常订单ID
- 创建订单获得正常ID
- 验证跳转和参数传递
- 确认订单详情正确加载

### 测试场景2：长订单ID
- 模拟长字符串订单ID
- 验证编码解码过程
- 确认参数正确传递

### 测试场景3：特殊字符订单ID
- 模拟包含特殊字符的ID
- 验证URL编码处理
- 确认解码后ID正确

### 测试场景4：参数缺失
- 模拟缺少参数的情况
- 验证错误处理逻辑
- 确认用户友好提示

## 最佳实践

### 1. URL参数传递
```javascript
// 发送方
const encodedParam = encodeURIComponent(param);
wx.navigateTo({
  url: `/path/to/page?param=${encodedParam}`
});

// 接收方
onLoad: function(options) {
  const param = decodeURIComponent(options.param);
}
```

### 2. 参数验证
```javascript
onLoad: function(options) {
  if (!options.id) {
    wx.showToast({
      title: '参数错误',
      icon: 'none'
    });
    wx.navigateBack();
    return;
  }
  
  const id = decodeURIComponent(options.id);
  // 继续处理...
}
```

### 3. 调试日志
```javascript
console.log('参数处理：', {
  原始参数: options.param,
  解码后: decodedParam,
  类型: typeof decodedParam
});
```

这些修复确保了页面间参数传递的可靠性和用户体验的一致性。
