/* auth.wxss */
.container {
  padding: 40rpx;
}

.auth-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.auth-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.auth-subtitle {
  font-size: 28rpx;
  color: #666;
}

.auth-form {
  margin-bottom: 60rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.verification-code-group {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.verification-input {
  flex: 1;
}

.verification-btn {
  width: 220rpx;
  height: 88rpx;
  background: #007AFF;
  color: white;
  font-size: 28rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.verification-btn.sent {
  background: #ccc;
  color: #666;
}

.house-info-selector {
  width: 100%;
  height: 88rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}

.agreement-group {
  margin-top: 60rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.agreement-text {
  color: #666;
  margin-left: 8rpx;
}

.agreement-link {
  color: #007AFF;
}

.auth-footer {
  margin-top: 80rpx;
}

.auth-btn {
  width: 100%;
  height: 88rpx;
  background: #007AFF;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-btn.disabled {
  background: #ccc;
  color: #666;
}

.auth-tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
}

/* 房屋选择弹窗 */
.house-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.house-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.house-selector-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.house-selector-modal.show .house-selector-content {
  transform: scale(1);
}

.house-selector-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.house-selector-title {
  font-size: 32rpx;
  font-weight: 500;
}

.house-selector-close {
  font-size: 40rpx;
  color: #999;
}

.house-selector-body {
  max-height: 600rpx;
  overflow-y: auto;
}

.house-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.house-item.selected {
  background: #f0f9ff;
}

.house-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.house-address {
  font-size: 26rpx;
  color: #666;
}

.house-selector-footer {
  padding: 30rpx;
}

.house-selector-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: white;
  font-size: 30rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
