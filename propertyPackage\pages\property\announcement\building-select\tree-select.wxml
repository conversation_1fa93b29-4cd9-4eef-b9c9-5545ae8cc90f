<!-- 楼栋树形选择页 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 搜索框 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索楼栋/单元/房间..." value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
    </view>
  </view>

  <!-- 全体住户选项 -->
  <view class="global-option {{selectedAll ? 'selected' : ''}}" bindtap="toggleSelectAll">
    <view class="checkbox {{selectedAll ? 'checked' : ''}}"></view>
    <text class="option-text">全体住户</text>
  </view>

  <!-- 已选数量提示 -->
  <view class="selected-tip" wx:if="{{!selectedAll && Object.keys(selectedItems).length > 0}}">
    <text>已选择 {{Object.keys(selectedItems).length}} 个对象</text>
    <view class="view-selected" bindtap="toggleSelectedList">查看已选</view>
  </view>

  <!-- 穿梭选择区域 -->
  <view class="transfer-container" wx:if="{{!selectedAll}}">
    <!-- 左侧：树形列表 -->
    <view class="tree-container">
      <view class="tree-header">可选列表</view>
      <scroll-view scroll-y class="tree-content">
        <!-- 楼栋节点 -->
        <block wx:for="{{treeData}}" wx:key="id">
          <view class="tree-node building-node">
            <!-- 展开/折叠图标 -->
            <view class="expand-icon {{expandedKeys.includes(item.id) ? 'expanded' : ''}}"
                  catchtap="toggleExpand"
                  data-id="{{item.id}}"></view>

            <!-- 复选框 -->
            <view class="checkbox {{selectedItems[item.id] ? 'checked' : ''}}"
                  catchtap="toggleSelect"
                  data-node="{{item}}"></view>

            <!-- 节点名称 -->
            <view class="node-name" catchtap="toggleExpand" data-id="{{item.id}}">{{item.name}}</view>
          </view>

          <!-- 单元节点 -->
          <block wx:if="{{expandedKeys.includes(item.id) && item.children && item.children.length > 0}}">
            <block wx:for="{{item.children}}" wx:for-item="unit" wx:key="id">
              <view class="tree-node unit-node">
                <!-- 展开/折叠图标 -->
                <view class="expand-icon {{expandedKeys.includes(unit.id) ? 'expanded' : ''}}"
                      catchtap="toggleExpand"
                      data-id="{{unit.id}}"></view>

                <!-- 复选框 -->
                <view class="checkbox {{selectedItems[unit.id] ? 'checked' : ''}}"
                      catchtap="toggleSelect"
                      data-node="{{unit}}"></view>

                <!-- 节点名称 -->
                <view class="node-name" catchtap="toggleExpand" data-id="{{unit.id}}">{{unit.name}}</view>
              </view>

              <!-- 房间节点 -->
              <block wx:if="{{expandedKeys.includes(unit.id)}}">
                <block wx:if="{{unit.children && unit.children.length > 0}}">
                  <view class="tree-node room-node" wx:for="{{unit.children}}" wx:for-item="room" wx:key="id">
                    <!-- 占位 -->
                    <view class="expand-icon placeholder"></view>

                    <!-- 复选框 -->
                    <view class="checkbox {{selectedItems[room.id] ? 'checked' : ''}}"
                          catchtap="toggleSelect"
                          data-node="{{room}}"></view>

                    <!-- 节点名称 -->
                    <view class="node-name">{{room.name}}</view>
                  </view>
                </block>
                <!-- 调试信息 -->
                <view class="debug-info" wx:else>
                  <text style="color: red; font-size: 24rpx;">该单元没有房间数据</text>
                </view>
              </block>
            </block>
          </block>
        </block>
      </scroll-view>
    </view>

    <!-- 右侧：已选列表 -->
    <view class="selected-container {{showSelectedList ? 'show' : ''}}">
      <view class="selected-header">
        <text>已选对象</text>
        <view class="clear-selected" bindtap="clearSelectedItems">清空</view>
      </view>
      <scroll-view scroll-y class="selected-content">
        <block wx:if="{{Object.keys(selectedItems).length > 0}}">
          <view class="selected-item" wx:for="{{selectedItems}}" wx:key="id">
            <text class="selected-name">{{item.name}}</text>
            <view class="remove-icon" catchtap="removeSelectedItem" data-id="{{item.id}}"></view>
          </view>
        </block>
        <view class="empty-selected" wx:else>
          <text>暂无已选对象</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && treeData.length === 0}}">
    <view class="empty-icon"></view>
    <view class="empty-text">{{searchValue ? '未找到相关楼栋，试试其他关键词吧！' : '暂无楼栋数据'}}</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-actions">
    <view class="action-btn cancel" bindtap="cancelSelection">取消</view>
    <view class="action-btn confirm" bindtap="confirmSelection">确认</view>
  </view>
</view>
