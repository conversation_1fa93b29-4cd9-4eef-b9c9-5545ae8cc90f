/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 批量邀请页面特定样式 */

/* 来访目的选择器样式 */
.visitor-purpose-options {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.visitor-purpose-option {
  width: 100%;
  padding: 16px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.visitor-purpose-option:last-child {
  border-bottom: none;
}

.visitor-purpose-option:active {
  background-color: #f5f5f5;
}
.batch-invite-intro {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-invite-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.batch-invite-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.batch-invite-title text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.batch-invite-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.batch-invite-common-info,
.batch-invite-visitors {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-invite-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.batch-invite-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
  margin-left: 8px;
}

.batch-visitor-list {
  margin-bottom: 16px;
}

.batch-visitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.batch-visitor-info {
  flex: 1;
}

.batch-visitor-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.batch-visitor-phone {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.batch-visitor-car {
  font-size: 14px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.batch-visitor-actions {
  display: flex;
  align-items: center;
}

.batch-visitor-edit,
.batch-visitor-delete {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.batch-visitor-action-icon {
  width: 20px;
  height: 20px;
}

.batch-visitor-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #999;
  font-size: 14px;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

/* 添加访客按钮区域 */
.add-visitor-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.add-visitor-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f5f7fa;
  border-radius: 8px;
  color: #4f46e5;
  font-size: 14px;
}

.add-visitor-btn.frequent-visitor-btn {
  background-color: #fff7ed;
  color: #ea580c;
}

.add-visitor-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* 批量邀请结果弹窗样式 */
.batch-result-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.batch-result-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.batch-result-icon.success {
  background-color: #e6f7ef;
}

.batch-result-icon.error {
  background-color: #fff1f0;
}

.batch-result-icon image {
  width: 32px;
  height: 32px;
}

.batch-result-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.batch-result-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.batch-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.batch-result-item-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.batch-result-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.batch-result-item-name {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.batch-result-item-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.batch-result-item-status.success {
  background-color: #e6f7ef;
  color: #10b981;
}

.batch-result-item-status.error {
  background-color: #fff1f0;
  color: #ef4444;
}

.batch-result-item-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.batch-result-view-btn {
  font-size: 12px;
  padding: 4px 8px;
  background-color: #4f46e5;
  color: #fff;
  border-radius: 4px;
  line-height: 1.5;
  min-height: 0;
}

/* 常用访客选择弹窗样式 */
.frequent-visitors-content {
  max-height: 80vh;
}

.frequent-visitors-select-all {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.frequent-visitors-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: #fff;
  transition: all 0.2s ease;
}

.frequent-visitors-checkbox.checked {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

.frequent-visitors-check-icon {
  width: 10px;
  height: 10px;
}

.frequent-visitors-select-all-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.frequent-visitors-count {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.frequent-visitors-list {
  max-height: 50vh;
  overflow-y: auto;
}

.frequent-visitors-empty {
  text-align: center;
  padding: 40px 16px;
  color: #999;
  font-size: 14px;
}

.frequent-visitor-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  transition: background-color 0.2s;
}

.frequent-visitor-item:last-child {
  border-bottom: none;
}

.frequent-visitor-item.selected {
  background-color: #f8fafc;
}

.frequent-visitor-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: #fff;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.frequent-visitor-checkbox.checked {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

.frequent-visitor-check-icon {
  width: 10px;
  height: 10px;
}

.frequent-visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #4f46e5;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin-right: 12px;
  flex-shrink: 0;
}

.frequent-visitor-info {
  flex: 1;
  min-width: 0;
}

.frequent-visitor-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.frequent-visitor-phone {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.frequent-visitor-car {
  font-size: 12px;
  color: #999;
}

/* 弹窗底部按钮样式调整 */
.visitor-picker-footer {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.visitor-picker-cancel-btn {
  flex: 1;
  height: 44px;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 8px;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.visitor-picker-confirm-btn {
  flex: 2;
  height: 44px;
  background-color: #4f46e5;
  color: #fff;
  border-radius: 8px;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.visitor-picker-confirm-btn:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
}