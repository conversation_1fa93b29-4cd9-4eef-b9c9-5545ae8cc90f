<!--我的贡献页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <view class="header">
    <view class="title">我的贡献</view>
    <view class="subtitle">查看个人环保成就</view>
  </view>

  <!-- 贡献统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-title">环保贡献</view>
      <view class="stats-subtitle">您的环保行动正在改变世界</view>
    </view>
    
    <view class="stats-content">
      <view class="stat-item">
        <view class="stat-value">{{contribution.count}}</view>
        <view class="stat-label">已分类物品</view>
      </view>
      
      <view class="stat-item">
        <view class="stat-value">{{contribution.carbon}}<text class="unit">kg</text></view>
        <view class="stat-label">减少碳排放</view>
      </view>
      
      <view class="stat-item">
        <view class="stat-value">{{contribution.points}}</view>
        <view class="stat-label">碳积分</view>
      </view>
    </view>
  </view>

  <!-- 环保进度卡片 -->
  <view class="progress-card">
    <view class="progress-header">
      <view class="progress-title">环保进度</view>
      <view class="progress-level">Lv.{{contribution.level}}</view>
    </view>
    
    <view class="tree-animation">
      <view class="tree-container">
        <view class="tree tree-{{contribution.level}}"></view>
      </view>
    </view>
    
    <view class="progress-bar">
      <view class="progress-track">
        <view class="progress-fill" style="width: {{contribution.progress}}%;"></view>
      </view>
      <view class="progress-text">{{contribution.progress}}%</view>
    </view>
    
    <view class="level-info">
      <view class="level-text">再减少{{contribution.nextLevelCarbon}}kg碳排放升级到 Lv.{{contribution.level + 1}}</view>
    </view>
  </view>

  <!-- 环保成就卡片 -->
  <view class="achievements-card">
    <view class="card-title">环保成就</view>
    
    <view class="achievements-grid">
      <view class="achievement-item {{item.unlocked ? 'unlocked' : 'locked'}}" 
            wx:for="{{achievements}}" 
            wx:key="id"
            bindtap="showAchievementDetail"
            data-id="{{item.id}}">
        <view class="achievement-icon {{item.unlocked ? '' : 'locked'}} {{item.id}}-icon"></view>
        <view class="achievement-name">{{item.name}}</view>
        <view class="achievement-status">{{item.unlocked ? '已获得' : '未获得'}}</view>
      </view>
    </view>
  </view>

  <!-- 环保记录卡片 -->
  <view class="records-card">
    <view class="card-title">环保记录</view>
    
    <view class="records-list">
      <view class="record-item" wx:for="{{records}}" wx:key="id">
        <view class="record-icon {{item.type}}-icon"></view>
        <view class="record-info">
          <view class="record-title">{{item.title}}</view>
          <view class="record-time">{{item.time}}</view>
        </view>
        <view class="record-value {{item.valueType}}">{{item.prefix}}{{item.value}}</view>
      </view>
    </view>
  </view>

  <!-- 成就详情弹窗 -->
  <view class="achievement-detail" wx:if="{{selectedAchievement}}" bindtap="closeAchievementDetail">
    <view class="detail-card" catchtap="stopPropagation">
      <view class="detail-header">
        <view class="detail-icon {{selectedAchievement.id}}-icon {{selectedAchievement.unlocked ? '' : 'locked'}}"></view>
        <view class="detail-close" bindtap="closeAchievementDetail">×</view>
      </view>
      
      <view class="detail-content">
        <view class="detail-name">{{selectedAchievement.name}}</view>
        <view class="detail-status {{selectedAchievement.unlocked ? 'unlocked' : 'locked'}}">
          {{selectedAchievement.unlocked ? '已获得' : '未获得'}}
        </view>
        <view class="detail-desc">{{selectedAchievement.description}}</view>
        
        <view class="detail-progress" wx:if="{{!selectedAchievement.unlocked}}">
          <view class="detail-progress-text">完成进度: {{selectedAchievement.progress}}/{{selectedAchievement.target}}</view>
          <view class="detail-progress-bar">
            <view class="detail-progress-track">
              <view class="detail-progress-fill" style="width: {{selectedAchievement.progress / selectedAchievement.target * 100}}%;"></view>
            </view>
          </view>
        </view>
        
        <view class="detail-reward" wx:if="{{selectedAchievement.unlocked}}">
          <view class="reward-title">获得奖励</view>
          <view class="reward-value">{{selectedAchievement.reward}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
