<!-- 订单二维码页面 -->
<view class="qrcode-container {{darkMode ? 'dark' : ''}}">
  <view class="card">
    <!-- 头部 -->
    <view class="header">
      <view class="title">交易二维码</view>
      <view class="subtitle">请向卖家出示此二维码完成交易</view>
    </view>

    <!-- 二维码内容 -->
    <view class="qrcode-section">
      <view class="qrcode">
        <canvas wx:if="{{qrContent && !loadFailed}}" class="qr-canvas" canvas-id="qrCanvas"></canvas>
        <image wx:if="{{loadFailed}}" class="failed-icon" src="/images/icon/error.png"></image>
        <text wx:if="{{loadFailed}}" class="failed-text">二维码加载失败</text>
      </view>
      
      <view class="order-info">
        <view class="order-no">
          <text>订单号: {{orderNo}}</text>
          <view class="copy-btn" bindtap="copyOrderNo">复制</view>
        </view>
        
        <view class="help-tips" bindtap="showTradeTips">
          <image class="help-icon" src="/images/icon/help.png"></image>
          <text>交易流程说明</text>
        </view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="goods-info" wx:if="{{orderInfo}}">
      <view class="order-title">订单信息</view>
      <view class="goods-card">
        <view class="goods-header">
          <text class="goods-name">{{orderInfo.goodsName || '未知商品'}}</text>
          <text class="goods-price">¥ {{orderInfo.finalPrice}}</text>
        </view>
        
        <view class="goods-detail">
          <view class="detail-item">
            <text class="label">商品单价</text>
            <text class="value">¥ {{orderInfo.pricePerItem || '0.00'}}</text>
          </view>
          <view class="detail-item">
            <text class="label">购买数量</text>
            <text class="value">{{orderInfo.quantity || 1}}</text>
          </view>
          <view class="detail-item" wx:if="{{orderInfo.usePoints}}">
            <text class="label">积分抵扣</text>
            <text class="value">{{orderInfo.pointsUsed || 0}} 积分</text>
          </view>
          <view class="detail-item">
            <text class="label">交易状态</text>
            <text class="value status-pending">待核销</text>
          </view>
          <view class="detail-item">
            <text class="label">创建时间</text>
            <text class="value">{{orderInfo.createTime || '未知'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="action-btns">
      <button class="btn btn-secondary" bindtap="contactSeller">联系卖家</button>
      <button class="btn btn-primary" bindtap="closePage">返回</button>
    </view>
  </view>
</view>

<!-- 交易提示弹窗 -->
<view class="tips-modal" wx:if="{{showTips}}">
  <view class="tips-card">
    <view class="tips-header">
      <text class="tips-title">交易流程说明</text>
      <view class="close-icon" bindtap="closeTips">×</view>
    </view>
    
    <view class="tips-content">
      <view class="tip-item">
        <view class="tip-number">1</view>
        <view class="tip-text">将此二维码出示给卖家</view>
      </view>
      <view class="tip-item">
        <view class="tip-number">2</view>
        <view class="tip-text">卖家使用小程序"核销订单"功能扫描此码</view>
      </view>
      <view class="tip-item">
        <view class="tip-number">3</view>
        <view class="tip-text">扫码成功后，进行线下物品交接和付款</view>
      </view>
      <view class="tip-item">
        <view class="tip-number">4</view>
        <view class="tip-text">交易完成</view>
      </view>
      
      <view class="tip-warning">
        <text>注意：为保障交易安全，请当面交易，核验商品后再付款。平台不介入资金交易环节。</text>
      </view>
    </view>
  </view>
</view>
