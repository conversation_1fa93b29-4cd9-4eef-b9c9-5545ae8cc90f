// components/svg-icon/svg-icon.js
Component({
  properties: {
    name: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: '#007AFF'
    },
    size: {
      type: Number,
      value: 24
    }
  },
  data: {
    iconType: '',
    iconText: ''
  },
  lifetimes: {
    attached: function() {
      this.updateIcon();
    }
  },
  observers: {
    'name': function() {
      this.updateIcon();
    }
  },
  methods: {
    updateIcon: function() {
      const name = this.data.name;
      let iconType = 'info';
      let iconText = '';

      // 根据图标名称设置图标类型和文本
      switch(name) {
        // 消息图标
        case 'message-repair':
          iconType = 'edit';
          iconText = '维修';
          break;
        case 'message-water':
          iconType = 'location';
          iconText = '停水';
          break;
        case 'message-facility':
          iconType = 'info';
          iconText = '设施';
          break;

        // 快捷服务图标
        case 'payment':
          iconType = 'success';
          iconText = '缴费';
          break;
        case 'repair':
          iconType = 'warn';
          iconText = '报修';
          break;
        case 'vote':
          iconType = 'success';
          iconText = '投票';
          break;
        case 'goods':
          iconType = 'success';
          iconText = '好物';
          break;
        case 'visitor':
          iconType = 'info';
          iconText = '访客';
          break;
        case 'garbage':
          iconType = 'info';
          iconText = '垃圾';
          break;
        case 'service':
          iconType = 'info';
          iconText = '服务';
          break;
        case 'renovation':
          iconType = 'info';
          iconText = '装修';
          break;
        default:
          iconType = 'info';
          iconText = '';
      }

      this.setData({
        iconType: iconType,
        iconText: iconText
      });
    }
  }
})
