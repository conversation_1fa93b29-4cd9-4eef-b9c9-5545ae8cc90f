/* AI拍照识别页面样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 相机组件样式 */
.camera-container {
  width: 100%;
  height: 800rpx;
  position: relative;
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.camera {
  width: 100%;
  height: 100%;
}

.camera-tips {
  position: absolute;
  top: 30rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
}

.tip-text {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.camera-controls {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
}

.camera-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-btn-inner {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: white;
  border: 6rpx solid rgba(0, 0, 0, 0.2);
}

/* 预览图片样式 */
.preview-container {
  width: 100%;
  height: 800rpx;
  position: relative;
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  background-color: #000;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-controls {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.preview-btn {
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.cancel-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}

.confirm-btn {
  background-color: #2196f3;
  color: white;
}

/* 分析中样式 */
.analyzing-container {
  width: 100%;
  height: 800rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analyzing-text {
  font-size: 32rpx;
  color: #333;
}

/* 识别结果样式 */
.result-container {
  margin-bottom: 40rpx;
}

.result-header {
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.result-subtitle {
  font-size: 24rpx;
  color: #999;
}

.result-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.result-image-container {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
}

.result-image {
  width: 100%;
  height: 100%;
}

.result-info {
  padding: 30rpx;
}

.result-item {
  margin-bottom: 20rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.result-value {
  font-size: 32rpx;
  color: #333;
}

.result-type {
  display: inline-block;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

.result-controls {
  display: flex;
  gap: 20rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.again-btn {
  background-color: #f5f5f5;
  color: #666;
}

.detail-btn {
  background-color: #2196f3;
  color: white;
}

/* 历史记录样式 */
.history-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.clear-text {
  font-size: 26rpx;
  color: #999;
}

.history-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.history-info {
  flex: 1;
}

.history-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.history-type {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.no-history {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-history-icon {
  width: 100rpx;
  height: 100rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='100' height='100' fill='none' stroke='%23cccccc' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z'%3E%3C/path%3E%3Ccircle cx='12' cy='13' r='4'%3E%3C/circle%3E%3Cline x1='8' y1='21' x2='16' y2='21'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12' y2='21'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 20rpx;
}

.no-history-text {
  font-size: 28rpx;
  color: #999;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .title {
  color: #f5f5f7;
}

.darkMode .subtitle {
  color: #8e8e93;
}

.darkMode .analyzing-container {
  background-color: #2c2c2e;
}

.darkMode .analyzing-text {
  color: #f5f5f7;
}

.darkMode .result-title {
  color: #f5f5f7;
}

.darkMode .result-card {
  background-color: #2c2c2e;
}

.darkMode .result-image-container {
  background-color: #1c1c1e;
}

.darkMode .result-label {
  color: #8e8e93;
}

.darkMode .result-value {
  color: #f5f5f7;
}

.darkMode .again-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .history-section {
  background-color: #2c2c2e;
}

.darkMode .title-text {
  color: #f5f5f7;
}

.darkMode .history-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .history-name {
  color: #f5f5f7;
}

.darkMode .no-history-text {
  color: #8e8e93;
}
