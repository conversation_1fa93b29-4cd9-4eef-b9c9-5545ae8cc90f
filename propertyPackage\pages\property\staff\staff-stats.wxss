/* 员工统计页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 统计卡片 */
.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: block;
  width: 8rpx;
  height: 32rpx;
  background-color: #ff8c00;
  margin-right: 16rpx;
  border-radius: 4rpx;
}

/* 总览卡片 */
.overview-data {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.overview-value.active {
  color: #4caf50;
}

.overview-value.inactive {
  color: #f44336;
}

.overview-label {
  font-size: 26rpx;
  color: #999;
}

.overview-divider {
  width: 1rpx;
  height: 80rpx;
  background-color: #eee;
}

/* 部门分布 */
.stats-list {
  margin-top: 20rpx;
}

.stats-item {
  margin-bottom: 24rpx;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-item-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.stats-item-name {
  font-size: 28rpx;
  color: #666;
}

.stats-item-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.stats-progress-bg {
  width: 100%;
  height: 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
}

.stats-progress {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

/* 性别比例 */
.gender-chart {
  margin-top: 20rpx;
}

.gender-chart-container {
  display: flex;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.gender-chart-male, .gender-chart-female {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.5s ease;
}

.gender-chart-male {
  background-color: #2196f3;
}

.gender-chart-female {
  background-color: #e91e63;
}

.gender-chart-icon-male, .gender-chart-icon-female {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.gender-chart-icon-male {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='10.5' cy='10.5' r='7.5'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='15.8' y2='15.8'%3E%3C/line%3E%3C/svg%3E");
}

.gender-chart-icon-female {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cline x1='12' y1='21' x2='12' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='18' x2='15' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.gender-chart-labels {
  display: flex;
  justify-content: space-around;
}

.gender-chart-label {
  display: flex;
  align-items: center;
}

.gender-chart-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.gender-chart-color.male {
  background-color: #2196f3;
}

.gender-chart-color.female {
  background-color: #e91e63;
}

.gender-chart-label text {
  font-size: 26rpx;
  color: #666;
}

/* 年龄分布 */
.age-chart {
  margin-top: 20rpx;
}

.age-chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 300rpx;
}

.age-chart-bar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.age-chart-bar {
  width: 40rpx;
  border-radius: 20rpx 20rpx 0 0;
  transition: height 0.5s ease;
}

.age-chart-bar-value {
  font-size: 24rpx;
  color: #333;
  margin: 10rpx 0;
}

.age-chart-bar-label {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  width: 100rpx;
}

/* 底部按钮 */
.bottom-btn {
  width: 100%;
  height: 80rpx;
  background-color: #ff8c00;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-top: 30rpx;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .stats-card {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-title {
  color: #f5f5f7;
}

.darkMode .overview-value {
  color: #f5f5f7;
}

.darkMode .overview-label,
.darkMode .stats-item-name,
.darkMode .gender-chart-label text,
.darkMode .age-chart-bar-label {
  color: #8e8e93;
}

.darkMode .stats-item-value,
.darkMode .age-chart-bar-value {
  color: #f5f5f7;
}

.darkMode .overview-divider {
  background-color: #3a3a3c;
}

.darkMode .stats-progress-bg {
  background-color: #3a3a3c;
}

.darkMode .loading-text {
  color: #8e8e93;
}
