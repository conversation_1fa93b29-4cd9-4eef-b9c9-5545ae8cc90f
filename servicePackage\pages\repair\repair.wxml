<!--repair.wxml-->
<view class="container">
  <!-- 工单类型 -->
  <view class="form-card">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-repair"></view>
      </view>
      <view class="title-text">工单类型</view>
      <text class="required">*</text>
    </view>

    <view class="type-grid">
      <view class="type-option {{selectedWorkOrderType === 'repair' ? 'active' : ''}}" bindtap="selectType" data-type="repair">
        <view class="type-icon">
          <view class="icon-repair {{selectedWorkOrderType === 'repair' ? 'active' : ''}}"></view>
        </view>
        <view class="type-label">维修</view>
      </view>
      <view class="type-option {{selectedWorkOrderType === 'complaint' ? 'active' : ''}}" bindtap="selectType" data-type="complaint">
        <view class="type-icon">
          <view class="icon-complaint {{selectedWorkOrderType === 'complaint' ? 'active' : ''}}"></view>
        </view>
        <view class="type-label">投诉</view>
      </view>
      <view class="type-option {{selectedWorkOrderType === 'suggestion' ? 'active' : ''}}" bindtap="selectType" data-type="suggestion">
        <view class="type-icon">
          <view class="icon-suggestion {{selectedWorkOrderType === 'suggestion' ? 'active' : ''}}"></view>
        </view>
        <view class="type-label">建议</view>
      </view>
      <view class="type-option {{selectedWorkOrderType === 'other' ? 'active' : ''}}" bindtap="selectType" data-type="other">
        <view class="type-icon">
          <view class="icon-other {{selectedWorkOrderType === 'other' ? 'active' : ''}}"></view>
        </view>
        <view class="type-label">其他</view>
      </view>
    </view>
  </view>

  <!-- 工单信息 -->
  <view class="form-card">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-message"></view>
      </view>
      <view class="title-text">问题描述</view>
      <text class="required">*</text>
    </view>

    <!-- 问题描述 -->
    <view class="textarea-wrapper">
      <textarea class="form-textarea" placeholder="请详细描述您遇到的问题..." value="{{description}}" bindinput="inputDescription" maxlength="500" />
    </view>

    <!-- 图片上传 -->
    <view class="image-upload-section">
      <view class="section-label">
        <text class="label-text">问题图片</text>
        <text class="label-tip">(最多上传9张)</text>
      </view>

      <view class="image-list">
        <view class="upload-button" bindtap="chooseImage" wx:if="{{images.length < 9}}">
          <view class="upload-icon">
            <view class="icon-camera"></view>
          </view>
          <text class="upload-text">上传图片</text>
        </view>

        <view class="image-item" wx:for="{{images}}" wx:key="index">
          <image src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
          <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}">
            <view class="delete-icon-inner">×</view>
          </view>
        </view>
      </view>

      <view class="upload-tip">
        <view class="tip-icon">
          <view class="icon-info"></view>
        </view>
        <text class="tip-text">支持jpg、png、jpeg格式，单张图片不超过5MB</text>
      </view>
    </view>
  </view>

  <!-- 报修地址 -->
  <view class="form-card" wx:if="{{!isPropertyStaff}}">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-location"></view>
      </view>
      <view class="title-text">报修地址</view>
      <text class="required">*</text>
    </view>

    <!-- 区域类型选择 -->
    <view class="address-type-selector">
      <view class="radio-group">
        <view class="radio-option {{selectedRegionType === 'house' ? 'selected' : ''}}" bindtap="selectRegionType" data-type="house">
          <view class="radio-button {{selectedRegionType === 'house' ? 'selected' : ''}}">
            <view class="radio-inner" wx:if="{{selectedRegionType === 'house'}}"></view>
          </view>
          <view class="radio-label">我的房屋</view>
        </view>
        <view class="radio-option {{selectedRegionType === 'public_area' ? 'selected' : ''}}" bindtap="selectRegionType" data-type="public_area">
          <view class="radio-button {{selectedRegionType === 'public_area' ? 'selected' : ''}}">
            <view class="radio-inner" wx:if="{{selectedRegionType === 'public_area'}}"></view>
          </view>
          <view class="radio-label">公共区域</view>
        </view>
      </view>
    </view>

    <!-- 我的房屋选择 -->
    <view class="my-address-container" wx:if="{{selectedRegionType === 'house'}}">
      <picker bindchange="selectHouse" value="{{selectedHouseIndex}}" range="{{houses}}" range-key="fullAddress">
        <view class="picker-field">
          <text>{{selectedHouse ? selectedHouse.fullAddress : '请选择房屋'}}</text>
          <view class="icon-arrow-down"></view>
        </view>
      </picker>
      <view wx:if="{{selectedHouse}}" class="house-info">
        <!-- <text class="house-type">{{selectedHouse.residentTypeCn}}</text> -->
      </view>
    </view>

    <!-- 公共区域 -->
    <view class="public-area-container" wx:if="{{selectedRegionType === 'public_area'}}">
      <view class="address-input-wrapper">
        <input class="address-input" placeholder="请输入详细地址，如：1号楼电梯旁" value="{{publicAddress}}" bindinput="inputPublicAddress" />
        <view class="location-btn" bindtap="getLocation">
          <view class="icon-location"></view>
        </view>
      </view>
      <view class="address-tip">
        <view class="tip-icon">
          <view class="icon-info"></view>
        </view>
        <text class="tip-text">点击右侧定位图标可获取当前位置</text>
      </view>
    </view>
  </view>

  <!-- 物业人员专用地址输入 -->
  <view class="form-card" wx:if="{{isPropertyStaff}}">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-location"></view>
      </view>
      <view class="title-text">报修地址</view>
      <text class="required">*</text>
    </view>

    <view class="address-input-wrapper">
      <input class="address-input" placeholder="请输入详细地址" value="{{publicAddress}}" bindinput="inputPublicAddress" />
      <view class="location-btn" bindtap="getLocation">
        <view class="icon-location"></view>
      </view>
    </view>
  </view>

  <!-- 报修人信息 - 普通用户 -->
  <view class="form-card" wx:if="{{!isPropertyStaff && userInfo}}">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-person"></view>
      </view>
      <view class="title-text">报修人信息</view>
    </view>

    <!-- 用户基本信息 -->
    <view class="reporter-basic-info">
      <view class="reporter-avatar">
        <view class="icon-person"></view>
      </view>
      <view class="reporter-details">
        <view class="reporter-name">{{userInfo.realName}}</view>
        <view class="reporter-type">住户</view>
      </view>
    </view>

    <!-- 用户详细信息 -->
    <view class="reporter-detail-info">
      <view class="info-row">
        <view class="info-item">
          <view class="info-label">手机号码</view>
          <view class="info-value">{{userInfo.phone || '未设置'}}</view>
        </view>
        <view class="info-item" wx:if="{{selectedHouse}}">
          <view class="info-label">用户类型</view>
          <view class="info-value">{{selectedHouse.residentTypeCn}}</view>
        </view>
      </view>

      <view class="info-block" wx:if="{{selectedHouse}}">
        <view class="info-label">房屋地址</view>
        <view class="info-value">{{selectedHouse.fullAddress}}</view>
      </view>
    </view>
  </view>

  <!-- 报修人信息 - 物业人员 -->
  <view class="form-card" wx:if="{{isPropertyStaff}}">
    <view class="form-card-title">
      <view class="title-icon-wrapper">
        <view class="icon-person"></view>
      </view>
      <view class="title-text">报修人信息</view>
    </view>

    <view class="reporter-input-section">
      <view class="input-group">
        <view class="input-label">报修人姓名</view>
        <input class="input-field" placeholder="请输入报修人姓名" value="{{customReporterName}}" bindinput="inputCustomReporterName" />
      </view>

      <view class="input-group">
        <view class="input-label">联系电话</view>
        <input class="input-field" placeholder="请输入联系电话" value="{{customReporterPhone}}" bindinput="inputCustomReporterPhone" type="number" />
      </view>

      <view class="input-group">
        <view class="input-label">用户类型</view>
        <picker bindchange="selectCustomReporterRole" value="{{customReporterRoleIndex}}" range="{{reporterRoleOptions}}">
          <view class="picker-field">
            <text>{{reporterRoleOptions[customReporterRoleIndex] || '请选择用户类型'}}</text>
            <view class="icon-arrow-down"></view>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-btn-wrapper">
    <button class="submit-btn {{canSubmit ? '' : 'disabled'}}" bindtap="submitRepair" disabled="{{!canSubmit}}">
      <text>提交工单</text>
    </button>
  </view>
</view>

<!-- 加载中弹窗 -->
<view class="loading-modal" wx:if="{{showLoading}}">
  <view class="loading-content">
    <view class="loading-icon"></view>
    <view class="loading-text">{{loadingText}}</view>
  </view>
</view>

<!-- 提示弹窗 -->
<view class="alert-modal {{showAlert ? 'show' : ''}}" bindtap="closeAlert">
  <view class="alert-content" catchtap="stopPropagation">
    <view class="alert-icon">
      <view class="icon-info" style="width: 24px; height: 24px;"></view>
    </view>
    <view class="alert-title">{{alertTitle}}</view>
    <view class="alert-message">{{alertMessage}}</view>
    <view class="alert-footer">
      <button class="alert-btn" bindtap="closeAlert">确定</button>
    </view>
  </view>
</view>
