// components/icon-text/icon-text.js
Component({
  properties: {
    name: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: '#007AFF'
    },
    size: {
      type: Number,
      value: 24
    },
    bgColor: {
      type: String,
      value: '#f0f7ff'
    }
  },
  data: {
    iconText: '',
    iconColor: ''
  },
  lifetimes: {
    attached: function() {
      this.updateIcon();
    }
  },
  observers: {
    'name': function() {
      this.updateIcon();
    }
  },
  methods: {
    updateIcon: function() {
      const name = this.data.name;
      let iconText = '';
      let iconColor = this.data.color;
      
      // 根据图标名称设置文本和颜色
      switch(name) {
        // 消息图标
        case 'message-repair':
          iconText = '修';
          break;
        case 'message-water':
          iconText = '水';
          break;
        case 'message-facility':
          iconText = '告';
          break;
          
        // 快捷服务图标
        case 'payment':
          iconText = '费';
          break;
        case 'repair':
          iconText = '修';
          break;
        case 'vote':
          iconText = '票';
          break;
        case 'goods':
          iconText = '物';
          break;
        case 'visitor':
          iconText = '客';
          break;
        case 'garbage':
          iconText = '垃';
          break;
        case 'service':
          iconText = '服';
          break;
        case 'renovation':
          iconText = '装';
          break;
        default:
          iconText = '';
      }
      
      this.setData({
        iconText: iconText,
        iconColor: iconColor
      });
    }
  }
})
