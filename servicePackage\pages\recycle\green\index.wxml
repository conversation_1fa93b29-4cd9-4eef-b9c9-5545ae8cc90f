<!--绿色循环页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- Banner内容 -->
  <view class="banner-content">
    <swiper class="slogan-swiper" vertical="true" autoplay="{{false}}" circular="{{true}}" current="{{currentSloganIndex}}" duration="500">
      <swiper-item wx:for="{{slogans}}" wx:key="*this">
        <view class="banner-slogan">{{item}}</view>
      </swiper-item>
    </swiper>
  </view>

  <!-- Sticky搜索栏 -->
  <view class="sticky-search">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="16"></icon>
      <input class="search-input" placeholder="搜索垃圾分类信息" bindinput="searchWaste" value="{{searchKeyword}}"/>
      <view class="search-actions">
        <view class="voice-btn" bindlongpress="startVoiceSearch" bindtouchend="endVoiceSearch">
          <view class="voice-icon"></view>
        </view>
        <view class="camera-btn" bindtap="takePhotoForRecognition">
          <view class="camera-icon"></view>
        </view>
      </view>
      <icon wx:if="{{searchKeyword}}" class="clear-icon" type="clear" size="16" bindtap="clearSearch"></icon>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{searchKeyword && searchResults.length > 0}}">
    <view class="result-item" wx:for="{{searchResults}}" wx:key="name">
      <view class="result-name">{{item.name}}</view>
      <view class="result-type" style="background-color: {{item.typeColor}}10; color: {{item.typeColor}};">
        {{item.typeName}}
      </view>
    </view>
  </view>

  <view class="no-results" wx:if="{{searchKeyword && searchResults.length === 0}}">
    <icon class="no-results-icon" type="info" size="40" color="#ccc"></icon>
    <view class="no-results-text">未找到相关结果</view>
    <view class="upload-photo-btn" bindtap="takePhotoForRecognition">拍照识别</view>
  </view>

  <!-- 首屏核心区 -->
  <view class="core-section">
    <!-- 环保成就卡片 -->
    <view class="achievement-card">
      <view class="achievement-stats">
        <view class="stat-item">
          <view class="stat-value">{{userStats.points}}</view>
          <view class="stat-label">积分</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{userStats.recycleCount}}</view>
          <view class="stat-label">次数</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{userStats.carbonReduction}}</view>
          <view class="stat-label">减碳(kg)</view>
        </view>
      </view>
    </view>

    <!-- 三个核心功能卡片 -->
    <view class="core-feature-cards">
      <!-- 分类指南卡片 -->
      <view class="feature-card" bindtap="navigateToGuide">
        <view class="card-icon guide-icon"></view>
        <view class="card-content">
          <view class="card-title">分类指南</view>
          <view class="card-desc">查询垃圾分类知识</view>
        </view>
        <view class="card-arrow"></view>
      </view>

      <!-- 投放地图卡片 -->
      <view class="feature-card" bindtap="navigateToMap">
        <view class="card-icon map-icon"></view>
        <view class="card-content">
          <view class="card-title">投放地图</view>
          <view class="card-desc">查看附近清洁亭位置</view>
        </view>
        <view class="card-arrow"></view>
      </view>

      <!-- 清运时间卡片 -->
      <view class="feature-card" bindtap="showScheduleDetail">
        <view class="card-icon schedule-icon"></view>
        <view class="card-content">
          <view class="card-title">清运时间</view>
          <view class="card-desc">合理安排投放时间</view>
        </view>
        <view class="card-arrow"></view>
      </view>
    </view>
  </view>

  <!-- 二级信息区 - 垃圾分类知识 -->
  <view class="knowledge-section">
    <view class="section-header" bindtap="toggleKnowledgeSection">
      <view class="section-title">
        <view class="title-left">
          <view class="title-icon knowledge-icon-title"></view>
          <view class="title-text">分类知识</view>
        </view>
        <view class="toggle-icon {{isKnowledgeSectionExpanded ? 'expanded' : ''}}"></view>
      </view>
    </view>

    <!-- 四类垃圾Tabs - 简化版 -->
    <view class="waste-type-tabs" wx:if="{{isKnowledgeSectionExpanded}}">
      <view
        class="waste-type-tab {{currentTypeIndex === index ? 'active' : ''}}"
        wx:for="{{recycleTypes}}"
        wx:key="id"
        bindtap="switchTypeTab"
        data-index="{{index}}"
        style="{{currentTypeIndex === index ? 'color: ' + item.color + '; border-color: ' + item.color : ''}}"
      >
        <view class="waste-type-icon-small {{item.icon}}-icon-small"></view>
        {{item.name}}
      </view>
    </view>

    <!-- Tab内容区域 - 简化版 -->
    <view class="waste-type-content" wx:if="{{isKnowledgeSectionExpanded}}">
      <view class="waste-type-icon {{recycleTypes[currentTypeIndex].icon}}-icon" style="background-color: {{recycleTypes[currentTypeIndex].color}}10;"></view>

      <view class="waste-type-info">
        <view class="waste-type-description">{{recycleTypes[currentTypeIndex].description}}</view>

        <view class="waste-type-requirements">{{recycleTypes[currentTypeIndex].requirements}}</view>

        <view class="waste-type-examples">
          <view class="examples-title">常见物品：</view>
          <view class="examples-list">
            <text
              wx:for="{{recycleTypes[currentTypeIndex].examples}}"
              wx:key="*this"
              class="example-item"
              style="color: {{recycleTypes[currentTypeIndex].color}};"
            >{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 辅助区 - 容易混淆专区 -->
  <view class="auxiliary-section">
    <view class="confusion-section">
      <view class="section-header" bindtap="toggleConfusionSection">
        <view class="section-title">
          <view class="title-left">
            <view class="title-icon confusion-icon-title"></view>
            <view class="title-text">容易混淆专区</view>
          </view>
          <view class="toggle-icon {{isConfusionSectionExpanded ? 'expanded' : ''}}"></view>
        </view>
      </view>

      <view class="confusion-list" wx:if="{{isConfusionSectionExpanded}}">
        <view class="expand-all" bindtap="toggleExpandAllConfusionItems">
          {{areAllConfusionItemsExpanded ? '收起全部' : '展开全部'}}
        </view>
        <view
          class="confusion-item {{currentConfusionIndex === index || areAllConfusionItemsExpanded ? 'expanded' : ''}}"
          wx:for="{{confusionItems}}"
          wx:key="title"
        >
          <view class="confusion-header" bindtap="toggleConfusionItem" data-index="{{index}}">
            <view class="confusion-item-title">{{item.title}}</view>
            <view class="confusion-arrow {{currentConfusionIndex === index || areAllConfusionItemsExpanded ? 'expanded' : ''}}"></view>
          </view>

          <view class="confusion-content" wx:if="{{currentConfusionIndex === index || areAllConfusionItemsExpanded}}">
            <view class="confusion-text">{{item.content}}</view>
            <view class="confusion-types">
              <view
                class="confusion-type"
                wx:for="{{item.types}}"
                wx:for-item="type"
                wx:key="*this"
                wx:for-index="typeIndex"
              >
                <view class="type-badge {{type}}">
                  <view class="type-icon {{type}}-icon-badge"></view>
                </view>
                <view class="confusion-type-name">
                  {{type === 'recyclable' ? '可回收物' : (type === 'hazardous' ? '有害垃圾' : (type === 'kitchen' ? '厨余垃圾' : '其他垃圾'))}}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 社区互动区域 -->
    <view class="community-section">
      <view class="section-header" bindtap="toggleCommunitySection">
        <view class="section-title">
          <view class="title-left">
            <view class="title-icon community-icon-title"></view>
            <view class="title-text">社区互动</view>
          </view>
          <view class="toggle-icon {{isCommunityExpanded ? 'expanded' : ''}}"></view>
        </view>
      </view>

      <view class="community-content" wx:if="{{isCommunityExpanded}}">
        <view class="community-card" bindtap="navigateToCommunity">
          <view class="community-card-title">分享心得和参与活动</view>
          <view class="community-card-desc">与社区居民一起参与环保活动，共建绿色家园</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 清运时间详情 - 默认隐藏，点击卡片时显示 -->
  <view class="schedule-detail {{isScheduleDetailVisible ? 'visible' : ''}}" bindtap="hideScheduleDetail">
    <view class="schedule-detail-content" catchtap="stopPropagation">
      <view class="schedule-detail-header">
        <view class="schedule-detail-title">清运时间表</view>
        <view class="schedule-detail-close" bindtap="hideScheduleDetail">×</view>
      </view>
      <view class="schedule-detail-date">{{currentDate}}</view>
      <view class="schedule-detail-items">
        <view class="schedule-detail-item" wx:for="{{schedules}}" wx:key="type">
          <view class="schedule-type-badge">
            <view class="type-icon {{item.type === '可回收物' ? 'recyclable' : (item.type === '有害垃圾' ? 'hazardous' : (item.type === '厨余垃圾' ? 'kitchen' : 'other'))}}-icon-badge"></view>
            <view class="schedule-type-label" style="background-color: {{item.color}};">{{item.type}}</view>
          </view>
          <view class="schedule-detail-time">{{item.time}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部固定按钮区域 -->
  <view class="fixed-buttons">
    <!-- 我的预约按钮 -->
    <view class="my-appointment-btn" bindtap="navigateToMyAppointments">
      <view class="my-appointment-icon"></view>
      <view class="my-appointment-text">我的预约</view>
    </view>

    <!-- 预约回收按钮 -->
    <view class="appointment-btn" bindtap="navigateToAppointment">
      <view class="appointment-icon"></view>
      <view class="appointment-text">立即预约废品回收</view>
    </view>
  </view>

  <!-- 碳减排树可视化 -->
  <view class="carbon-tree-modal {{isCarbonTreeVisible ? 'visible' : ''}}" bindtap="hideCarbonTree">
    <view class="carbon-tree-content" catchtap="stopPropagation">
      <view class="carbon-tree-header">
        <view class="carbon-tree-title">我的减碳成就</view>
        <view class="carbon-tree-close" bindtap="hideCarbonTree">×</view>
      </view>
      <view class="carbon-tree-visualization">
        <view class="carbon-tree-image"></view>
        <view class="carbon-tree-stats">
          <view class="carbon-stat">
            <view class="carbon-stat-value">{{userStats.carbonReduction}}</view>
            <view class="carbon-stat-label">减碳量(kg)</view>
          </view>
          <view class="carbon-stat">
            <view class="carbon-stat-value">{{userStats.recycleCount}}</view>
            <view class="carbon-stat-label">投递次数</view>
          </view>
        </view>
        <view class="carbon-tree-progress">
          <view class="carbon-progress-bar">
            <view class="carbon-progress-fill" style="width: {{(userStats.recycleCount / 20) * 100}}%;"></view>
          </view>
          <view class="carbon-progress-text">再投递{{20 - userStats.recycleCount}}次，即可点亮下一片树叶</view>
        </view>
        <view class="carbon-tree-badges">
          <view class="carbon-badge {{userStats.recycleCount >= 5 ? 'unlocked' : 'locked'}}">
            <view class="carbon-badge-icon beginner-badge"></view>
            <view class="carbon-badge-name">日常级</view>
          </view>
          <view class="carbon-badge {{userStats.recycleCount >= 15 ? 'unlocked' : 'locked'}}">
            <view class="carbon-badge-icon intermediate-badge"></view>
            <view class="carbon-badge-name">启蒙级</view>
          </view>
          <view class="carbon-badge {{userStats.recycleCount >= 30 ? 'unlocked' : 'locked'}}">
            <view class="carbon-badge-icon expert-badge"></view>
            <view class="carbon-badge-name">专家级</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载骨架屏 -->
  <view class="skeleton-screen" wx:if="{{isLoading}}">
    <view class="skeleton-banner"></view>
    <view class="skeleton-search"></view>
    <view class="skeleton-card"></view>
    <view class="skeleton-features">
      <view class="skeleton-feature"></view>
      <view class="skeleton-feature"></view>
      <view class="skeleton-feature"></view>
    </view>
    <view class="skeleton-section"></view>
  </view>
</view>
