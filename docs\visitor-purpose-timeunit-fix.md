# 访客提交参数修复：purpose和timeUnit

## 问题描述

在访客登记页面和批量邀请页面提交时，发现了两个参数问题：

1. **批量邀请页面缺少purpose参数**：在`buildBatchVisitorData`方法中没有包含来访目的参数
2. **timeUnit参数不统一**：不同页面使用了不同的时间单位格式（'hour' vs 'hours'）

## 修复内容

### 1. 批量邀请页面添加purpose参数

#### 修复前
```javascript
// servicePackage/pages/visitor/batch-invite/index.js
return visitors.map(visitor => {
  return {
    visitorName: visitor.name,
    phone: visitor.phone,
    vehicleNumber: visitor.carNumber || '',
    note: formData.remarks || '',
    stayDuration: formData.duration,
    timeUnit: 'hours',
    visitTime: dateUtil.formatISOToDateTime(visitDateTime),
    communityId: communityId
  };
});
```

#### 修复后
```javascript
// servicePackage/pages/visitor/batch-invite/index.js
return visitors.map(visitor => {
  return {
    visitorName: visitor.name,
    phone: visitor.phone,
    vehicleNumber: visitor.carNumber || '',
    note: formData.remarks || '',
    purpose: formData.purpose || '', // 新增来访目的参数
    stayDuration: formData.duration,
    timeUnit: 'hours',
    visitTime: dateUtil.formatISOToDateTime(visitDateTime),
    communityId: communityId
  };
});
```

### 2. 统一timeUnit参数格式

将所有相关文件中的timeUnit参数统一为'hours'格式：

#### 访客登记页面
```javascript
// servicePackage/pages/visitor/registration/index.js
return {
  visitorName: visitorName,
  phone: visitorPhone,
  vehicleNumber: carNumber || '',
  note: remarks || '',
  purpose: purpose || '',
  stayDuration: duration,
  timeUnit: 'hours', // 统一为hours
  visitTime: dateUtil.formatISOToDateTime(visitDateTime),
  communityId: communityId
};
```

#### 批量邀请页面
```javascript
// servicePackage/pages/visitor/batch-invite/index.js
return visitors.map(visitor => {
  return {
    visitorName: visitor.name,
    phone: visitor.phone,
    vehicleNumber: visitor.carNumber || '',
    note: formData.remarks || '',
    purpose: formData.purpose || '',
    stayDuration: formData.duration,
    timeUnit: 'hours', // 统一为hours
    visitTime: dateUtil.formatISOToDateTime(visitDateTime),
    communityId: communityId
  };
});
```

#### 访客延期功能
```javascript
// servicePackage/pages/visitor/list/index.js
const params = {
  id: currentVisitorId,
  stayDuration: hours,
  timeUnit: "hours", // 统一为hours
  visitTime: currentVisitor.originalVisitTime
};
```

### 3. 更新API文档注释

```javascript
// api/visitorsApi.js
/**
 * 新增访客
 * @param {Object} visitorData - 访客信息
 * @param {string} visitorData.visitorName - 访客姓名
 * @param {string} visitorData.phone - 手机号码
 * @param {string} visitorData.vehicleNumber - 车牌号（可选）
 * @param {string} visitorData.note - 备注（可选）
 * @param {string} visitorData.purpose - 来访目的（可选）
 * @param {number} visitorData.stayDuration - 停留时长
 * @param {string} visitorData.timeUnit - 时间单位（hours/days）
 * @param {string} visitorData.visitTime - 访问时间（ISO格式）
 * @param {number} visitorData.communityId - 社区ID
 * @returns {Promise} 返回新增结果
 */
```

## 修复的文件列表

1. **servicePackage/pages/visitor/batch-invite/index.js**
   - 在`buildBatchVisitorData`方法中添加`purpose: formData.purpose || ''`
   - 确保timeUnit使用'hours'

2. **servicePackage/pages/visitor/registration/index.js**
   - 确保timeUnit使用'hours'

3. **servicePackage/pages/visitor/list/index.js**
   - 在延期功能中确保timeUnit使用'hours'

4. **api/visitorsApi.js**
   - 更新API注释，将timeUnit说明从'hour/day'改为'hours/days'

## 影响范围

### 功能影响
1. **批量邀请功能**：现在会正确提交来访目的参数
2. **访客登记功能**：时间单位格式统一
3. **访客延期功能**：时间单位格式统一

### 数据一致性
1. **后端接收**：所有访客相关接口现在都会收到统一格式的timeUnit参数
2. **数据存储**：来访目的信息会被正确保存
3. **数据查询**：访客列表中的来访目的信息会正确显示

## 测试建议

### 1. 批量邀请测试
- 创建批量邀请，填写来访目的
- 提交后检查后端是否收到purpose参数
- 在访客列表中查看来访目的是否正确显示

### 2. 访客登记测试
- 创建单个访客，填写来访目的
- 提交后检查后端是否收到正确的timeUnit和purpose参数
- 验证访客凭证页面显示的信息是否完整

### 3. 访客延期测试
- 对现有访客进行延期操作
- 检查延期接口是否收到正确的timeUnit参数
- 验证延期后的时间计算是否正确

## 注意事项

1. **向后兼容**：修改后的参数格式需要确保与后端API兼容
2. **数据验证**：来访目的字段在前端已有必填验证，确保数据完整性
3. **时间单位**：统一使用'hours'格式，如果后端期望其他格式需要相应调整
4. **错误处理**：如果后端不支持新的参数格式，需要适当的错误处理机制

## 相关文档

- [访客管理API使用说明](./visitor-api-usage.md)
- [批量邀请常用访客功能](./batch-invite-frequent-visitors.md)
- [访客延期和收藏功能API接口更新](./visitor-api-interface-update.md)
