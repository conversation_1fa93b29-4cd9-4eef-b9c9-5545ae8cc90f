# 部门选择多层级展开功能修复

## 问题描述

原来的部门选择器只支持两层结构（公司-部门），但是实际数据结构可能有更多层级，比如：
- 网信科技公司（第一层公司）
  - 智慧物业发展规划公司（第二层公司）
    - 具体部门（第三层部门）

第二层的公司"智慧物业发展规划公司"有 `children` 数据，但是原来的模板没有为它添加展开功能。

## 解决方案

### 1. 扩展WXML模板结构

将原来的两层结构扩展为四层结构，支持：
- Level 0: 顶级公司
- Level 1: 子公司/部门  
- Level 2: 子公司的子公司/部门
- Level 3: 最深层的部门

### 2. 为所有层级的公司添加展开图标

```xml
<!-- 第一层公司展开图标 -->
<text wx:if="{{item.type === 'company' && item.children && item.children.length > 0}}"
      class="expand-icon {{item.expanded ? 'expanded' : ''}}">▼</text>

<!-- 第二层公司展开图标 -->
<text wx:if="{{childItem.type === 'company' && childItem.children && childItem.children.length > 0}}"
      class="expand-icon {{childItem.expanded ? 'expanded' : ''}}">▼</text>

<!-- 第三层公司展开图标 -->
<text wx:if="{{grandChildItem.type === 'company' && grandChildItem.children && grandChildItem.children.length > 0}}"
      class="expand-icon {{grandChildItem.expanded ? 'expanded' : ''}}">▼</text>
```

### 3. 保持现有的JavaScript逻辑

`toggleCompanyExpanded` 函数已经是递归的，可以处理任意层级的展开/收起：

```javascript
toggleCompanyExpanded: function(companyId) {
  const updateExpanded = (orgList) => {
    return orgList.map(org => {
      if (org.id === companyId && org.type === 'company') {
        // 找到目标公司，切换展开状态
        const newExpanded = !org.expanded;
        console.log(`${org.orgName} 展开状态: ${org.expanded} -> ${newExpanded}`);
        return {
          ...org,
          expanded: newExpanded
        };
      }

      // 递归处理子级
      if (org.children && org.children.length > 0) {
        return {
          ...org,
          children: updateExpanded(org.children)
        };
      }

      return org;
    });
  };

  const updatedDepartments = updateExpanded(this.data.filteredDepartments);
  this.setData({
    filteredDepartments: updatedDepartments
  });
}
```

### 4. 层级样式支持

CSS已经支持多层级的样式：

```css
.level-0 .selector-item { background-color: #ffffff; }
.level-1 .selector-item { background-color: #f9fafb; }
.level-2 .selector-item { background-color: #f3f4f6; }
.level-3 .selector-item { background-color: #f1f3f4; }

.org-children {
  margin-left: 20px;
  border-left: 2px solid #e5e7eb;
}
```

## 当前支持的层级结构

```
网信科技公司 (level-0, company) ✅ 可展开
├── 智慧物业发展规划公司 (level-1, company) ✅ 可展开  
│   ├── 技术部 (level-2, dept) ✅ 可选择
│   └── 运营部 (level-2, dept) ✅ 可选择
├── 直属部门A (level-1, dept) ✅ 可选择
└── 子公司B (level-1, company) ✅ 可展开
    ├── 子公司B的子公司 (level-2, company) ✅ 可展开
    │   ├── 深层部门1 (level-3, dept) ✅ 可选择
    │   └── 深层部门2 (level-3, dept) ✅ 可选择
    └── 子公司B的直属部门 (level-2, dept) ✅ 可选择
```

## 功能特点

1. **支持四层嵌套**：公司-子公司-子公司-部门
2. **递归展开**：任意层级的公司都可以展开/收起
3. **视觉区分**：不同层级有不同的背景色和缩进
4. **交互一致**：所有层级的交互逻辑保持一致
5. **只能选择部门**：只有 `type === 'dept'` 的项目才能被选中

## 测试验证

1. 点击"网信科技公司"应该能展开显示子级
2. 点击"智慧物业发展规划公司"应该能展开显示其子级部门
3. 只有部门类型的项目才显示选中状态（对勾）
4. 确认按钮只有在选择了部门时才启用

## 注意事项

- 如果数据结构超过四层，需要进一步扩展WXML模板
- 当前实现避免了递归模板的复杂性，使用直接嵌套确保事件绑定正常工作
- 保持了原有的搜索和过滤功能不受影响
