// pages/property/resident/statistics/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')
import * as echarts from "@/components/ec-canvas/echarts";


Page({
  data: {
    // 统计数据
    statistics: {
      genderCount: {},
      buildingResidentCount: {},
      ageCount: {},
      typeCount: {},
      trendCount: [],
      roomTypeCount: {}
    },

    // 当前激活的标签
    activeTab: 'overview',

    // 是否正在加载
    isLoading: false,

    // 字典数据
    residentTypeDict: [],
    genderDict: [],
    roomTypeDict: [],

    // 图表实例
    residentTypeChart: null,
    ageChart: null,
    trendChart: null,
    roomTypeChart: null,
    buildingChart: null,

    // echart配置
    ec: {
      lazyLoad: true // 懒加载
    }
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民统计'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载数据
    this.loadData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取居民类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type');
      const residentTypeOptions = residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children ? residentTypeDict[0].children : [];

      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender');
      const genderOptions = genderDict && genderDict.length > 0 && genderDict[0].children ? genderDict[0].children : [];

      // 获取房屋类型字典
      const roomTypeDict = util.getDictByNameEn('room_type');
      const roomTypeOptions = roomTypeDict && roomTypeDict.length > 0 && roomTypeDict[0].children ? roomTypeDict[0].children : [];

      this.setData({
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });
    } catch (error) {
      console.error('初始化字典数据失败:', error);
    }
  },

  // 加载数据
  loadData: function() {
    this.setData({
      isLoading: true
    });

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }

    const params = {
      communityId: selectedCommunity.id
    };

    console.log('加载居民统计数据，参数:', params);

    propertyApi.getResidentStatistics(params)
      .then(res => {
        console.log('居民统计API响应:', res);
        if (res) {
          this.processStatisticsData(res);
        } else {
          console.log('居民统计数据为空');
          this.setData({ isLoading: false });
        }
      })
      .catch(error => {
        console.error('获取居民统计数据失败:', error);
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      });
  },

  // 处理统计数据
  processStatisticsData: function(data) {
    console.log('处理统计数据:', data);

    // 处理居民类型分布数据
    const typeData = this.processTypeData(data.typeCount || {});

    // 处理性别分布数据
    const genderData = this.processGenderData(data.genderCount || {});

    // 处理年龄分布数据
    const ageData = this.processAgeData(data.ageCount || {});

    // 处理房屋类型分布数据
    const roomTypeData = this.processRoomTypeData(data.roomTypeCount || {});

    // 处理楼栋分布数据
    const buildingData = this.processBuildingData(data.buildingResidentCount || {});

    // 处理趋势数据
    const trendData = data.trendCount || [];

    // 计算年度总数
    const yearlyTotal = trendData.reduce((sum, count) => sum + count, 0);

    // 计算性别总数
    const genderTotal = Object.values(data.genderCount || {}).reduce((sum, count) => sum + count, 0);

    this.setData({
      'statistics.typeCount': data.typeCount,
      'statistics.genderCount': data.genderCount,
      'statistics.ageCount': data.ageCount,
      'statistics.roomTypeCount': data.roomTypeCount,
      'statistics.buildingResidentCount': data.buildingResidentCount,
      'statistics.trendCount': trendData,
      'statistics.yearlyTotal': yearlyTotal,
      'statistics.genderTotal': genderTotal,
      'statistics.typeData': typeData,
      'statistics.genderData': genderData,
      'statistics.ageData': ageData,
      'statistics.roomTypeData': roomTypeData,
      'statistics.buildingData': buildingData,
      isLoading: false
    });

    console.log('统计数据处理完成');
    console.log('处理后的数据:', {
      typeData: typeData,
      genderData: genderData,
      ageData: ageData,
      roomTypeData: roomTypeData,
      buildingData: buildingData,
      trendData: trendData
    });

    // 延迟渲染图表，确保DOM已更新
    setTimeout(() => {
      this.renderCharts();
    }, 300);
  },

  // 处理居民类型分布数据
  processTypeData: function(typeCount) {
    const typeData = [];
    const residentTypeDict = this.data.residentTypeDict;

    Object.keys(typeCount).forEach(key => {
      const count = typeCount[key];
      const dictItem = residentTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      typeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return typeData;
  },

  // 处理性别分布数据
  processGenderData: function(genderCount) {
    const genderData = [];
    const genderDict = this.data.genderDict;

    // 计算总数
    const total = Object.values(genderCount).reduce((sum, count) => sum + count, 0);

    Object.keys(genderCount).forEach(key => {
      const count = genderCount[key];
      const dictItem = genderDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;
      const percent = total > 0 ? Math.round(count * 100 / total) : 0;

      genderData.push({
        name: name,
        value: count,
        percent: percent,
        nameEn: key
      });
    });

    return genderData;
  },

  // 处理年龄分布数据
  processAgeData: function(ageCount) {
    const ageData = [];
    const ageMapping = {
      'under20': '20岁以下',
      'age20to30': '20-30岁',
      'age30to40': '30-40岁',
      'age40to50': '40-50岁',
      'over50': '50岁以上'
    };

    // 计算总数
    const total = Object.values(ageCount).reduce((sum, count) => sum + count, 0);

    Object.keys(ageCount).forEach(key => {
      const count = ageCount[key];
      const name = ageMapping[key] || key;
      const percent = total > 0 ? Math.round(count * 100 / total) : 0;

      ageData.push({
        name: name,
        value: count,
        percent: percent,
        nameEn: key
      });
    });

    return ageData;
  },

  // 处理房屋类型分布数据
  processRoomTypeData: function(roomTypeCount) {
    const roomTypeData = [];
    const roomTypeDict = this.data.roomTypeDict;

    Object.keys(roomTypeCount).forEach(key => {
      const count = roomTypeCount[key];
      const dictItem = roomTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      roomTypeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return roomTypeData;
  },

  // 处理楼栋分布数据
  processBuildingData: function(buildingResidentCount) {
    const buildingData = [];

    Object.keys(buildingResidentCount).forEach(key => {
      const count = buildingResidentCount[key];

      buildingData.push({
        name: key,
        value: count
      });
    });

    return buildingData;
  },

  // 渲染图表
  renderCharts: function() {
    const activeTab = this.data.activeTab;

    // 根据当前激活的标签渲染相应的图表
    switch(activeTab) {
      case 'overview':
        this.renderResidentTypeChart();
        break;
      case 'trend':
        setTimeout(() => {
          this.renderTrendChart();
        }, 1500);
      
        break;
      case 'age':
        this.renderAgeChart();
        break;
      case 'house':
        this.renderRoomTypeChart();
        this.renderBuildingChart();
        break;
    }
  },

  // 渲染居民类型分布饼图 - 使用EChart
  renderResidentTypeChart: function() {
    try {
      const typeData = this.data.statistics.typeData || [];
      if (typeData.length === 0) return;

      console.log('居民类型数据:', typeData);

      // 使用EChart渲染居民类型饼图
      this.initResidentTypeEChart(typeData);
    } catch (e) {
      console.error('渲染居民类型图失败:', e);
    }
  },

  // 初始化居民类型EChart
  initResidentTypeEChart: function(data) {
    this.loadResidentTypeChart(data);
  },

  // 加载居民类型饼图
  loadResidentTypeChart: function(data) {
    let ec_canvas = this.selectComponent('#residentTypeChart');
    if (!ec_canvas) {
      console.error('无法找到居民类型图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const chartData = data.map(item => ({
        name: item.name,
        value: item.value
      }));

      var myoption = {
        title: {
          text: '居民类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}人 ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            name: '居民类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: '{b}: {c}人',
              fontSize: 10
            },
            itemStyle: {
              color: function(params) {
                const colors = ['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'];
                return colors[params.dataIndex % colors.length];
              }
            }
          }
        ]
      };

      chart.setOption(myoption);
      return chart;
    });
  },

  // 渲染趋势分析柱状图 - 使用EChart
  renderTrendChart: function() {
    try {
      const trendData = this.data.statistics.trendCount || [];
      if (trendData.length === 0) return;

      console.log('趋势数据:', trendData);

      // 使用EChart渲染趋势图
      this.initTrendEChart(trendData);
    } catch (e) {
      console.error('渲染趋势图失败:', e);
    }
  },

  // 初始化趋势分析EChart
  initTrendEChart: function(data) {
    // 传递后台数据到图表中，进行懒加载图表
    this.loadTrendChart(data);
  },

  // 加载趋势图表
  loadTrendChart: function(data) {
    // 绑定组件（ec-canvas标签的id）
    let ec_canvas = this.selectComponent('#trendChart');
    if (!ec_canvas) {
      console.error('无法找到趋势图表组件');
      return;
    }
    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr // 解决模糊显示的问题
      });

      // echart表格的内容配置
      var myoption = {
        title: {
          text: '居民增长趋势',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}人'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}人',
            fontSize: 10
          },
          minInterval: 1
        },
        series: [
          {
            name: '新增居民',
            type: 'bar',
            data: data,
            itemStyle: {
              color: '#FF8C00'
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 10
            }
          }
        ]
      };

      chart.setOption(myoption);
      return chart;
    });
  },

  // 渲染年龄分布柱状图 - 使用EChart
  renderAgeChart: function() {
    try {
      const ageData = this.data.statistics.ageData || [];
      if (ageData.length === 0) return;

      console.log('年龄数据:', ageData);

      // 使用EChart渲染年龄分布图
      this.initAgeEChart(ageData);
    } catch (e) {
      console.error('渲染年龄图失败:', e);
    }
  },

  // 初始化年龄分布EChart
  initAgeEChart: function(data) {
    this.loadAgeChart(data);
  },

  // 加载年龄分布柱状图
  loadAgeChart: function(data) {
    let ec_canvas = this.selectComponent('#ageChart');
    if (!ec_canvas) {
      console.error('无法找到年龄分布图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const categories = data.map(item => item.name);
      const values = data.map(item => item.value);

      var myoption = {
        title: {
          text: '年龄分布统计',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}人'
        },
        grid: {
          left: '15%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            fontSize: 10,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}人',
            fontSize: 10
          },
          minInterval: 1
        },
        series: [
          {
            name: '人数',
            type: 'bar',
            data: values,
            itemStyle: {
              color: '#007AFF'
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 10
            }
          }
        ]
      };

      chart.setOption(myoption);
      return chart;
    });
  },

  // 渲染房屋类型分布饼图 - 使用EChart
  renderRoomTypeChart: function() {
    try {
      const roomTypeData = this.data.statistics.roomTypeData || [];
      if (roomTypeData.length === 0) return;

      console.log('房屋类型数据:', roomTypeData);

      // 使用EChart渲染房屋类型饼图
      this.initRoomTypeEChart(roomTypeData);
    } catch (e) {
      console.error('渲染房屋类型图失败:', e);
    }
  },

  // 初始化房屋类型EChart
  initRoomTypeEChart: function(data) {
    this.loadRoomTypeChart(data);
  },

  // 加载房屋类型饼图
  loadRoomTypeChart: function(data) {
    let ec_canvas = this.selectComponent('#roomTypeChart');
    if (!ec_canvas) {
      console.error('无法找到房屋类型图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const chartData = data.map(item => ({
        name: item.name,
        value: item.value
      }));

      var myoption = {
        title: {
          text: '房屋类型分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: ' {b}: {c}户 ({d}%)'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            name: '房屋类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: '{b}: {c}户',
              fontSize: 10
            },
            itemStyle: {
              color: function(params) {
                const colors = ['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6', '#FF9500', '#30D158', '#5AC8FA', '#AF52DE', '#FF2D92'];
                return colors[params.dataIndex % colors.length];
              }
            }
          }
        ]
      };

      chart.setOption(myoption);
      return chart;
    });
  },

  // 渲染楼栋分布柱状图 - 使用EChart
  renderBuildingChart: function() {
    try {
      const buildingData = this.data.statistics.buildingData || [];
      if (buildingData.length === 0) return;

      console.log('楼栋数据:', buildingData);

      // 使用EChart渲染楼栋分布图
      this.initBuildingEChart(buildingData);
    } catch (e) {
      console.error('渲染楼栋图失败:', e);
    }
  },

  // 初始化楼栋分布EChart
  initBuildingEChart: function(data) {
    this.loadBuildingChart(data);
  },

  // 加载楼栋分布柱状图
  loadBuildingChart: function(data) {
    let ec_canvas = this.selectComponent('#buildingChart');
    if (!ec_canvas) {
      console.error('无法找到楼栋分布图表组件');
      return;
    }

    ec_canvas.init((canvas, width, height, dpr) => {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      const categories = data.map(item => item.name);
      const values = data.map(item => item.value);

      var myoption = {
        title: {
          text: '楼栋居民分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}人'
        },
        grid: {
          left: '15%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}人',
            fontSize: 10
          },
          minInterval: 1
        },
        series: [
          {
            name: '居民数',
            type: 'bar',
            data: values,
            itemStyle: {
              color: '#34C759'
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 10
            }
          }
        ]
      };

      chart.setOption(myoption);
      return chart;
    });
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (this.data.activeTab !== tab) {
      this.setData({
        activeTab: tab
      });

      // 延迟渲染图表，确保DOM已更新
      setTimeout(() => {
        this.renderCharts();
      }, 100);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }
})