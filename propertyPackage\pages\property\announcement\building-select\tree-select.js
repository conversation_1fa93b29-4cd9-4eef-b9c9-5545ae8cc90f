/**
 * 楼栋树形选择页
 * 实现多层级穿梭选择器
 */

const announcementApi = require('../../../../utils/announcement-api');
const util = require('../../../../utils/util');

Page({
  data: {
    darkMode: false,
    searchValue: '', // 搜索关键词
    isLoading: true, // 是否正在加载

    // 树形结构相关
    treeData: [], // 树形数据
    expandedKeys: [], // 展开的节点ID列表

    // 已选项相关
    selectedAll: false, // 是否选择全体住户
    selectedItems: {}, // 已选项 {id: {id, name, type, parentId}}

    // 显示控制
    showSelectedList: false, // 是否显示已选列表
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '选择发布对象'
    });

    // 获取已选项
    if (options.selected) {
      try {
        const selectedItems = JSON.parse(options.selected);
        this.setData({ selectedItems });
      } catch (e) {
        console.error('解析已选项失败', e);
      }
    }

    // 加载树形数据
    this.loadTreeData();
  },

  onReady: function() {
    // 页面渲染完成后，自动展开第一个楼栋和单元
    setTimeout(() => {
      const treeData = this.data.treeData;
      if (treeData && treeData.length > 0) {
        const firstBuilding = treeData[0];
        const expandedKeys = [firstBuilding.id];

        if (firstBuilding.children && firstBuilding.children.length > 0) {
          const firstUnit = firstBuilding.children[0];
          expandedKeys.push(firstUnit.id);

          console.log('自动展开第一个楼栋和单元', expandedKeys);
          console.log('第一个单元的房间数量', firstUnit.children ? firstUnit.children.length : 0);
        }

        this.setData({ expandedKeys });
      }
    }, 500);
  },

  // 加载树形数据
  loadTreeData: function() {
    this.setData({ isLoading: true });

    // 调用API获取楼栋列表
    announcementApi.getBuildingList()
      .then(res => {
        const buildings = res.data || this.getMockTreeData();

        // 打印树形数据结构
        console.log('树形数据结构:', JSON.stringify(buildings));

        // 检查每个楼栋和单元是否有子节点
        buildings.forEach(building => {
          console.log(`楼栋 ${building.name} 有 ${building.children ? building.children.length : 0} 个单元`);

          if (building.children && building.children.length > 0) {
            building.children.forEach(unit => {
              console.log(`单元 ${building.name}-${unit.name} 有 ${unit.children ? unit.children.length : 0} 个房间`);
            });
          }
        });

        this.setData({
          treeData: buildings,
          isLoading: false
        });
      })
      .catch(err => {
        console.error('加载楼栋列表失败', err);

        // 使用模拟数据
        const mockData = this.getMockTreeData();

        // 打印模拟数据结构
        console.log('使用模拟数据，树形数据结构:', JSON.stringify(mockData));

        this.setData({
          treeData: mockData,
          isLoading: false
        });

        wx.showToast({
          title: '加载失败，使用模拟数据',
          icon: 'none'
        });
      });
  },

  // 获取模拟树形数据
  getMockTreeData: function() {
    // 生成模拟树形数据
    const treeData = [];

    // 直接创建固定的模拟数据，确保结构正确
    const buildingA = {
      id: 'building_A',
      name: 'A栋',
      type: 'building',
      children: []
    };

    const buildingB = {
      id: 'building_B',
      name: 'B栋',
      type: 'building',
      children: []
    };

    const buildingC = {
      id: 'building_C',
      name: 'C栋',
      type: 'building',
      children: []
    };

    // 为A栋添加单元
    for (let j = 1; j <= 5; j++) {
      const unit = {
        id: `unit_A${j}`,
        name: `${j}单元`,
        type: 'unit',
        parentId: buildingA.id,
        children: []
      };

      // 为每个单元添加房间
      for (let k = 1; k <= 5; k++) {
        const roomNumber = j * 100 + k;
        const room = {
          id: `room_A${roomNumber}`,
          name: `${roomNumber}室`,
          type: 'room',
          parentId: unit.id
        };

        // 确保房间被添加到单元的children数组中
        unit.children.push(room);
      }

      // 确保单元被添加到楼栋的children数组中
      buildingA.children.push(unit);
      console.log(`A栋${j}单元有${unit.children.length}个房间`);
    }

    // 为B栋添加单元
    for (let j = 1; j <= 5; j++) {
      const unit = {
        id: `unit_B${j}`,
        name: `${j}单元`,
        type: 'unit',
        parentId: buildingB.id,
        children: []
      };

      // 为每个单元添加房间
      for (let k = 1; k <= 5; k++) {
        const roomNumber = j * 100 + k;
        const room = {
          id: `room_B${roomNumber}`,
          name: `${roomNumber}室`,
          type: 'room',
          parentId: unit.id
        };

        // 确保房间被添加到单元的children数组中
        unit.children.push(room);
      }

      // 确保单元被添加到楼栋的children数组中
      buildingB.children.push(unit);
      console.log(`B栋${j}单元有${unit.children.length}个房间`);
    }

    // 为C栋添加单元
    for (let j = 1; j <= 5; j++) {
      const unit = {
        id: `unit_C${j}`,
        name: `${j}单元`,
        type: 'unit',
        parentId: buildingC.id,
        children: []
      };

      // 为每个单元添加房间
      for (let k = 1; k <= 5; k++) {
        const roomNumber = j * 100 + k;
        const room = {
          id: `room_C${roomNumber}`,
          name: `${roomNumber}室`,
          type: 'room',
          parentId: unit.id
        };

        // 确保房间被添加到单元的children数组中
        unit.children.push(room);
      }

      // 确保单元被添加到楼栋的children数组中
      buildingC.children.push(unit);
      console.log(`C栋${j}单元有${unit.children.length}个房间`);
    }

    // 将楼栋添加到树形数据中
    treeData.push(buildingA);
    treeData.push(buildingB);
    treeData.push(buildingC);

    // 打印树形数据
    console.log('树形数据生成完成，共有楼栋数：', treeData.length);
    console.log('A栋有', buildingA.children.length, '个单元');
    console.log('B栋有', buildingB.children.length, '个单元');
    console.log('C栋有', buildingC.children.length, '个单元');

    // 检查第一个单元是否有房间
    if (buildingA.children.length > 0) {
      const firstUnit = buildingA.children[0];
      console.log('A栋第一个单元有', firstUnit.children.length, '个房间');
      console.log('第一个房间是：', firstUnit.children[0]);
    }

    return treeData;
  },

  // 搜索框输入
  onSearchInput: function(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value });
    this.debounceSearch(value);
  },

  // 防抖搜索
  debounceSearch: util.debounce(function(value) {
    if (!value) {
      // 清空搜索，恢复原始数据
      this.loadTreeData();
      return;
    }

    // 搜索逻辑
    this.searchTreeData(value);
  }, 300),

  // 搜索树形数据
  searchTreeData: function(keyword) {
    if (!keyword) {
      this.loadTreeData();
      return;
    }

    const originalData = this.getMockTreeData();
    const lowercaseKeyword = keyword.toLowerCase();

    // 搜索结果
    const filteredData = [];

    // 遍历楼栋
    originalData.forEach(building => {
      // 检查楼栋名称是否匹配
      const buildingMatch = building.name.toLowerCase().includes(lowercaseKeyword);

      // 过滤匹配的单元
      const matchedUnits = [];

      if (building.children) {
        building.children.forEach(unit => {
          // 检查单元名称是否匹配
          const unitMatch = unit.name.toLowerCase().includes(lowercaseKeyword);

          // 过滤匹配的房间
          const matchedRooms = [];

          if (unit.children) {
            unit.children.forEach(room => {
              // 检查房间名称是否匹配
              const roomMatch = room.name.toLowerCase().includes(lowercaseKeyword);

              if (roomMatch) {
                matchedRooms.push(room);
              }
            });
          }

          // 如果单元名称匹配或者有匹配的房间，则添加单元
          if (unitMatch || matchedRooms.length > 0) {
            const unitCopy = { ...unit, children: matchedRooms };
            matchedUnits.push(unitCopy);
          }
        });
      }

      // 如果楼栋名称匹配或者有匹配的单元，则添加楼栋
      if (buildingMatch || matchedUnits.length > 0) {
        const buildingCopy = { ...building, children: matchedUnits };
        filteredData.push(buildingCopy);
      }
    });

    // 更新数据
    this.setData({
      treeData: filteredData,
      isLoading: false
    });

    // 如果有匹配项，自动展开
    if (filteredData.length > 0) {
      const expandedKeys = [];

      filteredData.forEach(building => {
        expandedKeys.push(building.id);

        if (building.children && building.children.length > 0) {
          building.children.forEach(unit => {
            expandedKeys.push(unit.id);
          });
        }
      });

      this.setData({ expandedKeys });
    }
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({ searchValue: '' });
    this.loadTreeData();
  },

  // 展开/折叠节点
  toggleExpand: function(e) {
    const nodeId = e.currentTarget.dataset.id;
    const expandedKeys = [...this.data.expandedKeys];

    const index = expandedKeys.indexOf(nodeId);
    if (index === -1) {
      // 展开节点
      expandedKeys.push(nodeId);

      // 打印日志，帮助调试
      console.log('展开节点', nodeId);

      // 查找该节点是否有子节点
      let hasChildren = false;

      // 检查是否是楼栋节点
      const building = this.data.treeData.find(b => b.id === nodeId);
      if (building && building.children && building.children.length > 0) {
        hasChildren = true;
        console.log('楼栋节点有子节点', building.children.length);
      }

      // 检查是否是单元节点
      if (!hasChildren) {
        for (const building of this.data.treeData) {
          if (building.children) {
            const unit = building.children.find(u => u.id === nodeId);
            if (unit && unit.children && unit.children.length > 0) {
              hasChildren = true;
              console.log('单元节点有子节点', unit.children.length);
              break;
            }
          }
        }
      }

      console.log('节点是否有子节点', hasChildren);
    } else {
      // 折叠节点
      expandedKeys.splice(index, 1);
      console.log('折叠节点', nodeId);
    }

    this.setData({ expandedKeys });
  },

  // 选择/取消选择节点
  toggleSelect: function(e) {
    const node = e.currentTarget.dataset.node;
    const selectedItems = { ...this.data.selectedItems };

    if (selectedItems[node.id]) {
      // 取消选择
      this.unselectNode(node, selectedItems);
    } else {
      // 选择节点
      this.selectNode(node, selectedItems);
    }

    this.setData({ selectedItems });
  },

  // 选择节点及其子节点
  selectNode: function(node, selectedItems) {
    // 添加当前节点到已选项
    selectedItems[node.id] = {
      id: node.id,
      name: node.name,
      type: node.type,
      parentId: node.parentId
    };

    // 如果有子节点，递归选择所有子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        this.selectNode(child, selectedItems);
      });
    }
  },

  // 取消选择节点及其子节点
  unselectNode: function(node, selectedItems) {
    // 从已选项中移除当前节点
    delete selectedItems[node.id];

    // 如果有子节点，递归取消选择所有子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        this.unselectNode(child, selectedItems);
      });
    }
  },

  // 切换选择全体住户
  toggleSelectAll: function() {
    const selectedAll = !this.data.selectedAll;

    if (selectedAll) {
      // 选择全体住户，清空已选项
      this.setData({
        selectedAll,
        selectedItems: {}
      });
    } else {
      // 取消选择全体住户
      this.setData({ selectedAll });
    }
  },

  // 显示/隐藏已选列表
  toggleSelectedList: function() {
    this.setData({
      showSelectedList: !this.data.showSelectedList
    });
  },

  // 从已选列表中移除项
  removeSelectedItem: function(e) {
    const itemId = e.currentTarget.dataset.id;
    const selectedItems = { ...this.data.selectedItems };

    delete selectedItems[itemId];

    this.setData({ selectedItems });
  },

  // 清空已选项
  clearSelectedItems: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已选项吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ selectedItems: {} });
        }
      }
    });
  },

  // 确认选择
  confirmSelection: function() {
    if (this.data.selectedAll) {
      // 选择全体住户
      this.returnSelection({
        targetScope: 'all_residents',
        buildings: [],
        selectedAll: true
      });
      return;
    }

    const selectedItems = this.data.selectedItems;
    const selectedIds = Object.keys(selectedItems);

    if (selectedIds.length === 0) {
      wx.showToast({
        title: '请至少选择一个发布对象',
        icon: 'none'
      });
      return;
    }

    // 整理选择结果
    const result = this.organizeSelection(selectedItems);

    // 返回选择结果
    this.returnSelection({
      targetScope: 'specific_buildings',
      buildings: result.buildings,
      selectedItems: selectedItems,
      selectedAll: false
    });
  },

  // 整理选择结果
  organizeSelection: function(selectedItems) {
    const buildings = [];
    const buildingIds = [];

    // 遍历已选项
    Object.values(selectedItems).forEach(item => {
      if (item.type === 'building') {
        // 如果是楼栋，直接添加
        buildings.push({
          id: item.id,
          name: item.name,
          address: ''
        });
        buildingIds.push(item.id);
      } else if (item.type === 'unit') {
        // 如果是单元，添加单元信息
        const buildingId = item.parentId;
        const building = this.data.treeData.find(b => b.id === buildingId);

        if (building) {
          buildings.push({
            id: item.id,
            name: `${building.name}-${item.name}`,
            address: ''
          });
          buildingIds.push(item.id);
        }
      } else if (item.type === 'room') {
        // 如果是房间，添加房间信息
        const unitId = item.parentId;
        let unit = null;
        let building = null;

        // 查找单元和楼栋
        for (const b of this.data.treeData) {
          if (b.children) {
            for (const u of b.children) {
              if (u.id === unitId) {
                unit = u;
                building = b;
                break;
              }
            }
            if (unit) break;
          }
        }

        if (unit && building) {
          buildings.push({
            id: item.id,
            name: `${building.name}-${unit.name}-${item.name}`,
            address: ''
          });
          buildingIds.push(item.id);
        }
      }
    });

    return {
      buildings,
      buildingIds
    };
  },

  // 返回选择结果
  returnSelection: function(result) {
    // 将选择结果返回给上一页
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 上一页

    // 调用上一页的方法，传递选择结果
    if (prevPage && prevPage.onBuildingsSelected) {
      prevPage.onBuildingsSelected({
        detail: result
      });
    }

    // 返回上一页
    wx.navigateBack();
  },

  // 取消选择
  cancelSelection: function() {
    wx.navigateBack();
  }
});
