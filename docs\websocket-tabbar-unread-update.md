# WebSocket消息推送时底部Tab未读数量更新功能

## 功能概述

当用户不在消息中心页面时，接收到WebSocket推送的未读消息，系统会自动更新底部tab栏的未读消息数量角标，确保用户能够及时看到新消息提醒。

## 实现原理

### 1. 问题分析
- 原有实现：只有在消息中心页面时，接收到WebSocket消息会更新列表和底部未读数量
- 存在问题：在其他tab页面时，接收到WebSocket消息推送，底部tab的未读数量角标不会实时更新

### 2. 解决方案
在app.js的WebSocket消息处理方法中，增加判断逻辑：
- 如果当前页面不是消息中心页面，则主动调用TabbarManager更新底部tab的未读数量
- 使用TabbarManager.getTotalUnreadCount()获取最新的未读数量并更新角标

## 代码修改

### 修改文件：app.js

#### 1. 私聊消息处理方法 (handlePrivateMessageReceived)
```javascript
// 如果当前不在消息中心页面，需要手动更新底部tab的未读数量
if (currentPage && currentPage.route !== 'pages/messages/messages') {
  console.log('App: 不在消息中心页面，更新底部tab未读数量');
  this.updateTabbarUnreadCount();
}
```

#### 2. 系统消息处理方法 (handleSystemMessageReceived)
```javascript
// 如果当前不在消息中心页面，需要手动更新底部tab的未读数量
if (currentPage && currentPage.route !== 'pages/messages/messages') {
  console.log('App: 不在消息中心页面，更新底部tab未读数量');
  this.updateTabbarUnreadCount();
}
```

#### 3. 通知公告处理方法 (handleNoticeMessageReceived)
```javascript
// 如果当前不在消息中心页面，需要手动更新底部tab的未读数量
if (currentPage && currentPage.route !== 'pages/messages/messages') {
  console.log('App: 不在消息中心页面，更新底部tab未读数量');
  this.updateTabbarUnreadCount();
}
```

#### 4. 新增更新方法 (updateTabbarUnreadCount)
```javascript
/**
 * 更新底部tab的未读数量
 * 当接收到WebSocket消息但不在消息中心页面时调用
 */
updateTabbarUnreadCount: function () {
  console.log('App: 开始更新底部tab未读数量');
  
  // 使用TabbarManager获取最新的未读数量并更新
  TabbarManager.getTotalUnreadCount().then(count => {
    console.log('App: 获取到最新未读数量:', count);
    // TabbarManager.getTotalUnreadCount()内部已经会调用updateUnreadCount更新tab
  }).catch(err => {
    console.error('App: 更新底部tab未读数量失败:', err);
  });
},
```

## 工作流程

### 1. 在消息中心页面时
1. 接收到WebSocket消息推送
2. 消息中心页面处理消息，更新列表
3. 消息中心页面调用TabbarManager.updateUnreadCount()更新底部角标
4. 不触发app.js中的额外更新逻辑

### 2. 在其他页面时
1. 接收到WebSocket消息推送
2. app.js分发消息到消息中心页面（如果存在）
3. 检测到当前页面不是消息中心页面
4. 调用updateTabbarUnreadCount()方法
5. 通过TabbarManager.getTotalUnreadCount()获取最新未读数量
6. TabbarManager自动更新底部tab角标

## 测试方法

### 测试场景1：在首页接收消息
1. 打开小程序，停留在首页
2. 通过其他设备或后台发送消息到当前用户
3. 观察底部消息tab是否显示未读数量角标
4. 预期结果：角标数量实时更新

### 测试场景2：在物业管理页面接收消息
1. 切换到物业管理tab页面
2. 通过其他设备或后台发送消息到当前用户
3. 观察底部消息tab是否显示未读数量角标
4. 预期结果：角标数量实时更新

### 测试场景3：在消息中心页面接收消息
1. 切换到消息中心tab页面
2. 通过其他设备或后台发送消息到当前用户
3. 观察消息列表和底部角标是否同时更新
4. 预期结果：列表和角标都实时更新

### 测试场景4：多种消息类型
1. 分别测试私聊消息、系统消息、通知公告
2. 在不同tab页面接收不同类型的消息
3. 观察底部角标是否都能正确更新
4. 预期结果：所有消息类型都能触发角标更新

## 注意事项

1. **避免重复更新**：在消息中心页面时不会触发额外的更新，避免重复API调用
2. **错误处理**：如果获取未读数量失败，会在控制台输出错误日志，不影响正常功能
3. **性能考虑**：只在必要时调用API获取未读数量，避免频繁请求
4. **兼容性**：保持与现有消息处理逻辑的兼容性，不影响原有功能

## 相关文件

- `app.js` - 主要修改文件，WebSocket消息处理逻辑
- `utils/tabbar-manager.js` - 底部tab管理工具，提供未读数量更新功能
- `pages/messages/messages.js` - 消息中心页面，原有的消息处理逻辑
- `custom-tab-bar/index.wxml` - 底部tab栏模板，显示未读数量角标
- `custom-tab-bar/index.wxss` - 底部tab栏样式，角标样式定义
