<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>延期图标</title>
    <g id="延期" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="椭圆形" stroke="#f59e0b" stroke-width="1.5" cx="12" cy="12" r="10"></circle>
        <polyline id="路径" stroke="#f59e0b" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="12 7 12 12 15 15"></polyline>
        <line x1="8" y1="4" x2="16" y2="4" id="路径" stroke="#f59e0b" stroke-width="1.5" stroke-linecap="round"></line>
    </g>
</svg>
