# 工单受理页面和详情页面改进

## 修改内容

### 1. 工单受理页面员工显示优化

#### 文件：`propertyPackage/pages/property/workorder/process/index.js`

**问题**：员工显示信息不完整，缺少员工管理列表中的详细信息

**解决方案**：
- 参考员工管理列表的显示格式
- 添加员工编号、组织、职位、性别、年龄等信息
- 统一员工信息字段映射

```javascript
// 为员工添加选中状态和工作状态显示，参考员工管理列表的显示格式
const staffList = (res || []).map(staff => ({
  ...staff,
  selected: false,
  workStatusText: this.getWorkStatusText(staff.workStatus),
  // 添加员工管理列表中的显示字段
  name: staff.personName || staff.name || '',
  employeeId: staff.personNumber || staff.employeeId || '',
  organization: staff.orgName || staff.organization || '',
  position: staff.positionName || staff.position || '',
  phone: staff.phone || '',
  gender: staff.gender || '',
  age: staff.age || 0
}));
```

#### 文件：`propertyPackage/pages/property/workorder/process/index.wxml`

**修改**：更新员工信息显示结构
```xml
<view class="staff-info">
  <view class="staff-name-id">
    <text class="staff-name">{{item.name}}</text>
    <text class="staff-id" wx:if="{{item.employeeId}}">{{item.employeeId}}</text>
  </view>
  <view class="staff-position" wx:if="{{item.organization || item.position}}">{{item.organization}} | {{item.position}}</view>
  <view class="staff-details">
    <text class="staff-gender-age" wx:if="{{item.gender || item.age}}">{{item.gender}} | {{item.age}}岁</text>
    <text class="staff-status status-{{item.workStatus}}">{{item.workStatusText}}</text>
  </view>
</view>
```

#### 文件：`propertyPackage/pages/property/workorder/process/index.wxss`

**添加样式**：
```css
.staff-name-id {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.staff-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.staff-id {
  font-size: 24rpx;
  color: #999;
}

.staff-gender-age {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}
```

### 2. 受理成功后更新详情页面

#### 文件：`propertyPackage/pages/property/workorder/process/index.js`

**问题**：受理成功后，工单详情页面的按钮状态没有更新

**解决方案**：
```javascript
setTimeout(() => {
  // 设置标记，通知详情页面需要刷新
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2]; // 获取上一个页面
  if (prevPage && prevPage.route.includes('workorder/detail')) {
    // 如果上一个页面是工单详情页，调用其刷新方法
    prevPage.loadWorkOrderDetail(this.data.workOrder.id);
  }
  wx.navigateBack();
}, 1500);
```

### 3. 工单详情页面按钮样式修复

#### 文件：`propertyPackage/pages/property/workorder/detail/index.wxss`

**问题**：
1. 按钮文字不居中
2. 按钮大小不固定
3. 右边距显示不正确

**解决方案**：

**按钮容器样式**：
```css
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin: 16px;
  margin-bottom: 16px;
  gap: 8px;
}
```

**按钮样式**：
```css
.action-button {
  flex: 1;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  border-radius: 22px;
  text-align: center;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}
```

**移除默认边框**：
```css
.action-button.primary::after,
.action-button.secondary::after,
.action-button.danger::after {
  border: none;
}
```

**页面边距修复**：
```css
.detail-content {
  flex: 1;
  margin-top: 88px;
  padding: 0;
  padding-bottom: 80px;
}

.detail-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.status-card {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
}
```

## 测试要点

1. **员工显示测试**：
   - 验证员工信息显示完整（姓名、编号、组织、职位、性别、年龄、工作状态）
   - 验证员工选择功能正常

2. **受理流程测试**：
   - 受理工单成功后，返回详情页面
   - 验证详情页面按钮状态正确更新（从"受理工单"变为"处理工单"）

3. **样式测试**：
   - 验证按钮文字居中显示
   - 验证按钮大小固定
   - 验证页面右边距正确
   - 验证卡片间距统一

## 相关文件

- `propertyPackage/pages/property/workorder/process/index.js`
- `propertyPackage/pages/property/workorder/process/index.wxml`
- `propertyPackage/pages/property/workorder/process/index.wxss`
- `propertyPackage/pages/property/workorder/detail/index.wxss`
