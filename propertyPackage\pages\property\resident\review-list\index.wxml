<!--居民信息审核列表-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索申请人、ID、手机号等" value="{{searchText}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchText}}"></view>
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <view class="filter-icon"></view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view class="tab {{activeTab === 'resident_room' ? 'active' : ''}}" bindtap="switchTab" data-tab="resident_room">房产审核</view>
    <view class="tab {{activeTab === 'resident_vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="resident_vehicle">车辆审核</view>
  </view>

  <!-- 审核列表 -->
  <view class="review-list">
    <block wx:if="{{reviews.length > 0}}">
      <view class="review-card" wx:for="{{reviews}}" wx:key="id" data-index="{{index}}" data-id="{{item.id}}" data-type="{{item.type}}" style="{{item.style}}">
        <view class="review-content" bindtap="viewDetail" data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">
          <view class="review-header">
            <view class="review-title">
              <view class="review-icon {{item.iconClass}}"></view>
              <text>{{item.title}}</text>
            </view>
            <view class="review-tag {{item.status}}">{{item.statusText}}</view>
          </view>

          <view class="review-info">
            <view class="info-row">
              <view class="info-label">申请人</view>
              <view class="info-value">{{item.applicant}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{item.phone}}</view>
            </view>

            <!-- 根据类型显示不同信息 -->
            <block wx:if="{{item.type === 'resident_room'}}">
              <view class="info-row">
                <view class="info-label">房产地址</view>
                <view class="info-value">{{item.addressText}}</view>
              </view>
              <view class="info-row" wx:if="{{item.residentTypeName}}">
                <view class="info-label">居民类型</view>
                <view class="info-value">{{item.residentTypeName}}</view>
              </view>
            </block>

            <block wx:elif="{{item.type === 'resident_vehicle'}}">
              <view class="info-row">
                <view class="info-label">车牌号</view>
                <view class="info-value">{{item.plateNumber}}</view>
              </view>
              <view class="info-row" wx:if="{{item.residentTypeName}}">
                <view class="info-label">居民类型</view>
                <view class="info-value">{{item.residentTypeName}}</view>
              </view>
            </block>

            <view class="info-row">
              <view class="info-label">申请时间</view>
              <view class="info-value">{{item.submitTime}}</view>
            </view>
          </view>

          <view class="review-actions">
            <button class="btn-secondary" catchtap="viewDetail" data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">查看详情</button>
          </view>
        </view>


      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{reviews.length === 0}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无{{activeTab === 'all' ? '' : activeTabName}}审核信息</view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view class="loading-indicator" wx:if="{{isLoading}}"></view>
    <text wx:else bindtap="loadMore">加载更多</text>
  </view>


</view>
