/* pages/ws-test/websocket-test.wxss */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态区域 */
.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-value.connected {
  color: #07c160;
}

.status-value.disconnected {
  color: #fa5151;
}

/* 按钮区域 */
.button-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-button {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #07c160;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.test-button[disabled] {
  background: #c0c0c0;
  color: #999;
}

/* 发送区域 */
.send-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.input-field:focus {
  border-color: #07c160;
  background: white;
}

.send-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.send-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #1989fa;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.send-button[disabled] {
  background: #c0c0c0;
  color: #999;
}

/* 消息区域 */
.messages-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.messages-header {
  padding: 30rpx 40rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.messages-list {
  height: 600rpx;
  padding: 20rpx 40rpx;
}

.message-item {
  margin-bottom: 30rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 8rpx solid #e0e0e0;
}

.message-item:last-child {
  margin-bottom: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.message-sender {
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
}

.message-time {
  font-size: 20rpx;
  color: #999;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  word-break: break-all;
}

/* 不同类型消息的样式 */
.message-system {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.message-system .message-content {
  color: #1976d2;
}

.message-sent {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.message-sent .message-content {
  color: #2e7d32;
}

.message-received {
  background: #fff3e0;
  border-left-color: #ff9800;
}

.message-received .message-content {
  color: #f57c00;
}

.message-error {
  background: #ffebee;
  border-left-color: #f44336;
}

.message-error .message-content {
  color: #c62828;
}

.empty-messages {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 80rpx 0;
}
