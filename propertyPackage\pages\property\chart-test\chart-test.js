// 引入图表库
const wxCharts = require('../../../../utils/wxcharts/wxcharts.js');
import CanvasContext from '@/utils/canvas_context.js';


import * as echarts from "@/components/ec-canvas/echarts"

// 页面实例
Page({
  data: {
    windowWidth: 320,
    ec: {
      lazyLoad: true // 懒加载
    }
  },

  // 页面加载时
  onLoad: function() {
    // 获取设备信息
    try {
      const res = wx.getSystemInfoSync();
      this.setData({
        windowWidth: res.windowWidth
      });
    } catch (e) {
      console.error('获取设备信息失败:', e);
    }
  },

  // 页面显示时
  onShow: function() {
    // 初始化图表
    this.initCharts();
  },

  // 初始化所有图表
  initCharts: function() {
    // 初始化饼图
    this.initPieChart();

    setTimeout(() => {
        // 初始化柱状图
    // this.initColumnChart();

      let data = [2, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      this.initchart(data);
    }, 1500);
  

    // 初始化折线图
    this.initLineChart();
  },

  // 初始化饼图
  initPieChart: function() {

    

    try {
      // 饼图数据
      const pieData = [
        { name: '业主', data: 60 },
        { name: '租户', data: 30 },
        { name: '家属', data: 10 }
      ];

      // 创建饼图
      const ctx = wx.createCanvasContext('pieCanvas');
      if (!ctx) {
        console.error('无法获取饼图Canvas上下文');
        return;
      }

      wxCharts.create({
        type: 'pie',
        canvas: ctx,
        series: pieData,
        width: this.data.windowWidth - 40,
        height: 200,
        padding: 10,
        colors: ['#7cb5ec', '#f7a35c', '#434348'],
        showLegend: true
      });

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化饼图失败:', e);
    }
  },

  initchart(data){
    // 传递后台数据到图表中，进行懒加载图表
    this.loadchart(data);
  },
 
  loadchart(data){
    // 绑定组件（ec-canvas标签的id）
    let ec_canvas = this.selectComponent('#myChart');
    debugger
    ec_canvas.init((canvas,width,height,dpr)=>{
      const chart =echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr // 解决模糊显示的问题
      })
      // echart表格的内容配置
      var myoption = {
        title: {},
        tooltip: {
          trigger: 'axis'
        },
        legend: {},
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} '
          }
        },
        series: [
          {
            name: 'price',
            type: 'line',
            data: data, // 动态修改的数据
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            }
          }
        ]
      }
      chart.setOption(myoption);
      return chart;
    })
  },



  // 初始化柱状图
  initColumnChart: function() {
    this.initChart('columnCanvas',300,300)
    // wx.createSelectorQuery().select('#columnCanvas').fields({ size: true, node: true },res=>{
    //   const ctx  = new CanvasContext(res);

    //   console.log('ctx',ctx)

    // try {
    //   // 柱状图数据
    //   const columnData = [
    //     { name: '待处理', data: ['15'] },
    //     { name: '处理中', data: ['12'] },
    //     { name: '已完成', data: ['98'] }
    //   ];


    //   if (!ctx) {
    //     console.error('无法获取柱状图Canvas上下文');
    //     return;
    //   }

    //   wxCharts.create({
    //     type: 'column',
    //     canvas: ctx,
    //     series: columnData,
    //     categories: ['工单状态'],
    //     width: 300,
    //     height: 200,
    //     padding: 10,
    //     colors: [ '#10b981'],
    //     showLegend: false
    //   });

    //   // 绘制到画布
    //   // ctx.draw();
    // } catch (e) {
    //   console.error('初始化柱状图失败:', e);
    // }

    // }).exec()








    // try {
    //   // 柱状图数据
    //   const columnData = [
    //     { name: '待处理', data: ['15'] },
    //     { name: '处理中', data: ['12'] },
    //     { name: '已完成', data: ['98'] }
    //   ];

    //   // 创建柱状图
    //   const ctx = wx.createCanvasContext('columnCanvas');
    //   if (!ctx) {
    //     console.error('无法获取柱状图Canvas上下文');
    //     return;
    //   }

    //   wxCharts.create({
    //     type: 'column',
    //     canvas: ctx,
    //     series: columnData,
    //     categories: ['工单状态'],
    //     width: 300,
    //     height: 200,
    //     padding: 10,
    //     colors: [ '#10b981'],
    //     showLegend: false
    //   });

    //   // 绘制到画布
    //   ctx.draw();
    // } catch (e) {
    //   console.error('初始化柱状图失败:', e);
    // }
  },

  // 初始化折线图
  initLineChart: function() {
    try {
      // 折线图数据
      const lineData = [
        { name: '访客数量', data: [10, 15, 20, 25, 30, 25, 20] }
      ];

      // 创建折线图
      const ctx = wx.createCanvasContext('lineCanvas');
      if (!ctx) {
        console.error('无法获取折线图Canvas上下文');
        return;
      }

      wxCharts.create({
        type: 'line',
        canvas: ctx,
        series: lineData,
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        width: this.data.windowWidth - 40,
        height: 200,
        padding: 10,
        colors: ['#4f46e5'],
        showLegend: true
      });

      // 绘制到画布
      ctx.draw();
    } catch (e) {
      console.error('初始化折线图失败:', e);
    }
  }
});
