# 实名认证页面隐私政策弹窗实现

## 修改概述

在实名认证页面中添加了隐私政策弹窗功能，当用户点击蓝色高亮的"《隐私政策》"链接时，会弹出隐私政策内容弹窗，与设置页面保持一致的交互体验。

## 实现内容

### 1. API引入

在页面顶部添加imagetext API引入：
```javascript
const imagetext = require('../../../api/imagetext.js');
```

### 2. 数据结构扩展

在data对象中添加隐私政策弹窗相关数据：
```javascript
data: {
  // ... 其他数据
  
  // 隐私政策弹窗
  showPrivacyModal: false,
  privacyContent: '',
  privacyLoading: false
}
```

### 3. 方法实现

#### 修改goToPrivacyPolicy方法
将原来的页面跳转改为弹窗显示：

```javascript
// 显示隐私政策弹窗
goToPrivacyPolicy: function () {
  this.setData({
    privacyLoading: true,
    showPrivacyModal: true
  });

  // 获取隐私政策内容
  imagetext.getImagetextList({
    type: 'privacy_policy',
    pageNum: 1,
    pageSize: 1
  }).then(res => {
    if (res && res.list && res.list.length > 0) {
      const privacyPolicy = res.list[0]; // 取最新的第一个
      
      this.setData({
        privacyContent: privacyPolicy.content || '暂无隐私政策内容',
        privacyLoading: false
      });
    } else {
      this.setData({
        privacyContent: '暂无隐私政策内容',
        privacyLoading: false
      });
    }
  }).catch(error => {
    console.error('获取隐私政策失败:', error);
    this.setData({
      privacyContent: '加载隐私政策失败，请稍后再试',
      privacyLoading: false
    });
  });
}
```

#### 新增弹窗控制方法
```javascript
// 关闭隐私政策弹窗
closePrivacyModal: function() {
  this.setData({
    showPrivacyModal: false
  });
},

// 确认已阅读隐私政策
confirmPrivacyPolicy: function() {
  this.setData({
    showPrivacyModal: false
  });
  
  wx.showToast({
    title: '已确认',
    icon: 'success'
  });
},

// 阻止事件冒泡
stopPropagation: function() {
  // 空方法，用于阻止事件冒泡
}
```

### 4. WXML模板

在页面底部添加隐私政策弹窗：
```xml
<!-- 隐私政策弹窗 -->
<view class="modal-mask" wx:if="{{showPrivacyModal}}" bindtap="closePrivacyModal">
  <view class="modal-container" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">隐私政策</view>
      <view class="modal-close" bindtap="closePrivacyModal">×</view>
    </view>
    <view class="modal-content">
      <view wx:if="{{privacyLoading}}" class="loading-text">加载中...</view>
      <scroll-view wx:else scroll-y="true" class="content-scroll">
        <rich-text nodes="{{privacyContent}}" class="rich-content"></rich-text>
      </scroll-view>
    </view>
    <view class="modal-footer">
      <button class="confirm-btn" bindtap="confirmPrivacyPolicy">我已阅读</button>
    </view>
  </view>
</view>
```

### 5. CSS样式

添加与设置页面一致的弹窗样式：
```css
/* 隐私政策弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-container {
  width: 80%;
  max-width: 600rpx;
  max-height: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ... 其他样式 */
```

## 功能特点

### 1. 一致的用户体验
- 与设置页面的隐私政策弹窗保持完全一致的设计和交互
- 相同的弹窗样式、布局和操作方式
- 统一的加载状态和错误处理

### 2. 便捷的访问方式
- 用户在实名认证过程中可以直接查看隐私政策
- 无需跳转到其他页面，保持认证流程的连续性
- 点击蓝色高亮链接即可弹出内容

### 3. 动态内容管理
- 通过API获取最新的隐私政策内容
- 支持富文本格式显示
- 内容可通过后台管理系统随时更新

### 4. 完善的交互设计
- 弹窗遮罩层，点击可关闭
- 右上角关闭按钮
- 底部"我已阅读"确认按钮
- 内容区域可滚动查看

## 原有链接位置

在实名认证页面的隐私提示区域：
```xml
<!-- 隐私提示 -->
<view class="privacy-tip">
  提交认证信息即表示您同意我们根据<text class="link" bindtap="goToPrivacyPolicy">《隐私政策》</text>收集、使用和保护您的个人信息，包括您的身份信息、房屋信息、联系方式及人脸照片等。我们将严格保护您的隐私安全，不会向无关第三方泄露您的个人信息。
</view>
```

## API调用

### 获取隐私政策内容
```javascript
imagetext.getImagetextList({
  type: 'privacy_policy',
  pageNum: 1,
  pageSize: 1
})
```

### 数据处理
- 取返回列表中的第一个item（最新的）
- 使用item.content作为富文本内容
- 处理空数据和错误情况

## 用户流程

```
用户在实名认证页面
    ↓
阅读隐私提示文本
    ↓
点击蓝色"《隐私政策》"链接
    ↓
弹出隐私政策内容弹窗
    ↓
查看富文本内容（可滚动）
    ↓
点击"我已阅读"按钮
    ↓
弹窗关闭，显示确认提示
    ↓
继续实名认证流程
```

## 相关文件

- `pages/auth/real-name/real-name.js` - 页面逻辑
- `pages/auth/real-name/real-name.wxml` - 页面模板
- `pages/auth/real-name/real-name.wxss` - 页面样式
- `api/imagetext.js` - 图文内容API

## 测试要点

1. **链接点击** - 测试点击隐私政策链接能正确弹出弹窗
2. **内容加载** - 测试API内容的正确获取和显示
3. **弹窗交互** - 测试各种关闭方式的正确性
4. **滚动功能** - 测试富文本内容的滚动功能
5. **确认功能** - 测试"我已阅读"按钮的功能
6. **错误处理** - 测试网络错误时的处理
7. **样式一致性** - 确保与设置页面弹窗样式一致

## 注意事项

1. **z-index层级** - 弹窗使用z-index: 1000确保在最上层显示
2. **事件冒泡** - 使用catchtap阻止弹窗内容区域的点击事件冒泡
3. **响应式设计** - 弹窗在不同屏幕尺寸下都能正常显示
4. **内容安全** - 富文本内容经过安全处理，防止XSS攻击

现在实名认证页面的隐私政策链接已经实现了弹窗功能，与设置页面保持一致的用户体验。
