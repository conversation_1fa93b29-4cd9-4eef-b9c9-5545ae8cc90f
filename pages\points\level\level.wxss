/* level.wxss */
/* 容器样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #222;
  color: #fff;
}

.dark-mode .level-card,
.dark-mode .privilege-item,
.dark-mode .level-item {
  background-color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.dark-mode .level-name,
.dark-mode .privilege-title,
.dark-mode .level-item-name,
.dark-mode .highest-text {
  color: #fff;
}

.dark-mode .level-points,
.dark-mode .privilege-desc,
.dark-mode .level-item-range,
.dark-mode .level-privilege-text,
.dark-mode .tip-text {
  color: #aaa;
}

.dark-mode .progress-bar {
  background-color: #444;
}

.dark-mode .tabs {
  background-color: #444;
}

.dark-mode .tab {
  color: #aaa;
}

.dark-mode .tab.active {
  background-color: #555;
  color: #ff8c00;
}

/* 等级卡片 */
.level-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 等级头部 */
.level-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.level-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.3);
}

.level-icon-img {
  width: 80rpx;
  height: 80rpx;
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.level-points {
  font-size: 28rpx;
  color: #666;
}

/* 等级进度 */
.level-progress {
  border-top: 1px solid #f0f0f0;
  padding-top: 30rpx;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.progress-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  border-radius: 8rpx;
}

.next-level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.next-level-text {
  font-size: 28rpx;
  color: #666;
}

.earn-points-btn {
  width: 200rpx;
  height: 70rpx;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.3);
  padding: 0;
}

/* 最高等级 */
.highest-level {
  border-top: 1px solid #f0f0f0;
  padding-top: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.crown-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.highest-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

/* 标签页 */
.tabs {
  display: flex;
  margin-bottom: 30rpx;
  background-color: #f0f0f0;
  border-radius: 40rpx;
  padding: 6rpx;
}

.tab {
  flex: 1;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 35rpx;
}

.tab.active {
  background-color: #fff;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 标签内容 */
.tab-content {
  margin-bottom: 30rpx;
}

/* 特权列表 */
.privileges-list {
  margin-bottom: 30rpx;
}

.privilege-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.privilege-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 140, 0, 0.1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.privilege-emoji {
  font-size: 40rpx;
}

.privilege-content {
  flex: 1;
}

.privilege-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.privilege-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 等级列表 */
.all-levels {
  margin-bottom: 30rpx;
}

.level-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.level-item.current-level {
  border: 2rpx solid #ff8c00;
}

.level-item-header {
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f0f0f0;
}

.level-item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.level-item-icon-img {
  width: 50rpx;
  height: 50rpx;
}

.level-item-info {
  flex: 1;
}

.level-item-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.level-item-range {
  font-size: 24rpx;
  color: #666;
}

.level-item-tag {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  background-color: #ff8c00;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.level-item-privileges {
  padding: 20rpx 24rpx;
}

.level-privilege-item {
  display: flex;
  margin-bottom: 12rpx;
}

.level-privilege-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #ff8c00;
  border-radius: 6rpx;
  margin-top: 10rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.level-privilege-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 底部提示 */
.bottom-tips {
  text-align: center;
  margin-top: 40rpx;
  margin-bottom: 30rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.8;
}

.privilege-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
