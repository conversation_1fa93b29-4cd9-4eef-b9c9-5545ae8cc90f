# 访客登记和批量添加功能API更新

## 修改概述
1. 访客登记页面：将常用访客功能改为调用API接口获取数据
2. 批量添加访客页面：移除VisitorManager模拟数据调用，改为循环请求添加访客接口

## 1. 访客登记页面常用访客功能更新

### 文件：`servicePackage/pages/visitor/registration/index.js`

#### 原实现
```javascript
// 获取常用访客列表
getFrequentVisitors: function() {
  const frequentVisitors = VisitorManager.getFrequentVisitors();
  this.setData({
    frequentVisitors: frequentVisitors
  });
}
```

#### 新实现
```javascript
// 获取常用访客列表
getFrequentVisitors: function() {
  // 获取社区信息
  const selectedCommunity = wx.getStorageSync('selectedCommunity') || {};
  const communityId = selectedCommunity.id;

  // 调用我的访客列表接口获取常用访客
  const params = {
    pageNum: 1,
    pageSize: 500,
    communityId: communityId,
    isUsual: true
  };

  visitorsApi.getVisitorList(params)
    .then(res => {
      if (res && res.list) {
        // 处理常用访客数据
        const frequentVisitors = res.list.map(visitor => ({
          id: visitor.id,
          visitorName: visitor.visitorName,
          phone: visitor.phone,
          vehicleNumber: visitor.vehicleNumber || '',
          note: visitor.note || ''
        }));

        this.setData({
          frequentVisitors: frequentVisitors
        });
      }
    })
    .catch(err => {
      console.error('获取常用访客失败：', err);
      this.setData({
        frequentVisitors: []
      });
    });
}
```

#### API参数说明
| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| pageNum | Number | 页码 | 1 |
| pageSize | Number | 每页数量 | 500 |
| communityId | Number | 社区ID | 2 |
| isUsual | Boolean | 是否常用访客 | true |

#### 选择常用访客功能
```javascript
// 选择常用访客
selectFrequentVisitor: function(e) {
  const id = e.currentTarget.dataset.id;
  const frequentVisitor = this.data.frequentVisitors.find(v => v.id === id);

  if (frequentVisitor) {
    // 填充表单数据 - 只填充访客姓名和手机号码
    this.setData({
      'formData.visitorName': frequentVisitor.visitorName,
      'formData.visitorPhone': frequentVisitor.phone,
      'errors.visitorName': '',
      'errors.visitorPhone': '',
      showFrequentVisitorsModal: false
    });

    wx.showToast({
      title: '已填充常用访客信息',
      icon: 'success'
    });
  }
}
```

## 2. 批量添加访客页面API更新

### 文件：`servicePackage/pages/visitor/batch-invite/index.js`

#### 移除的依赖
```javascript
// 移除
const VisitorManager = require('@/utils/visitor-manager');

// 添加
const visitorsApi = require('@/api/visitorsApi');
```

#### 原批量保存实现
```javascript
// 批量保存访客数据
VisitorManager.saveBatchVisitors(batchVisitorData)
  .then(result => {
    // 显示结果
  })
```

#### 新循环请求实现
```javascript
// 循环添加访客
addVisitorsBatch: function(batchVisitorData) {
  const results = [];
  let completedCount = 0;
  const totalCount = batchVisitorData.length;

  // 循环请求添加访客接口
  batchVisitorData.forEach((visitorData, index) => {
    visitorsApi.addVisitor(visitorData)
      .then(res => {
        results.push({
          success: true,
          data: res,
          visitor: visitorData
        });
        completedCount++;
        this.checkBatchComplete(completedCount, totalCount, results);
      })
      .catch(err => {
        results.push({
          success: false,
          error: err.message || '添加失败',
          visitor: visitorData
        });
        completedCount++;
        this.checkBatchComplete(completedCount, totalCount, results);
      });
  });
}
```

#### 批量完成检查
```javascript
// 检查批量添加是否完成
checkBatchComplete: function(completedCount, totalCount, results) {
  if (completedCount === totalCount) {
    wx.hideLoading();

    // 统计成功数量
    const successCount = results.filter(r => r.success).length;
    
    // 显示结果
    this.setData({
      batchResult: {
        success: successCount > 0,
        successCount: successCount,
        totalCount: totalCount,
        results: results
      },
      showResultModal: true
    });

    // 显示提示
    if (successCount === totalCount) {
      wx.showToast({
        title: `成功添加${successCount}位访客`,
        icon: 'success'
      });
    } else if (successCount > 0) {
      wx.showToast({
        title: `成功添加${successCount}位访客，${totalCount - successCount}位失败`,
        icon: 'none'
      });
    } else {
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  }
}
```

#### 访客数据构建
```javascript
// 构建批量访客数据
buildBatchVisitorData: function() {
  const { formData, visitors, visitorType } = this.data;

  // 获取社区信息
  const selectedCommunity = wx.getStorageSync('selectedCommunity') || {};
  const communityId = selectedCommunity.id;

  // 计算访问时间（ISO格式）
  const visitDateTime = dateUtil.parseDateTime(formData.visitDate, formData.visitTime);

  // 构建访客数据数组
  return visitors.map(visitor => {
    return {
      visitorName: visitor.name,
      phone: visitor.phone,
      vehicleNumber: visitor.carNumber || '',
      note: formData.remarks || '',
      stayDuration: formData.duration,
      timeUnit: 'hour',
      visitTime: visitDateTime.toISOString(),
      communityId: communityId
    };
  });
}
```

## 3. UI界面更新

### 批量添加结果显示更新

#### 文件：`servicePackage/pages/visitor/batch-invite/index.wxml`

```xml
<!-- 结果摘要 -->
<view class="batch-result-text">
  <text wx:if="{{batchResult.success}}">成功添加 {{batchResult.successCount}}/{{batchResult.totalCount}} 位访客</text>
  <text wx:else>添加失败</text>
</view>

<!-- 结果列表 -->
<view class="batch-result-list" wx:if="{{batchResult.results.length > 0}}">
  <view class="batch-result-item" wx:for="{{batchResult.results}}" wx:key="index">
    <view class="batch-result-item-info">
      <view class="batch-result-item-header">
        <view class="batch-result-item-name">{{item.visitor.visitorName}}</view>
        <view class="batch-result-item-status {{item.success ? 'success' : 'error'}}">
          {{item.success ? '成功' : '失败'}}
        </view>
      </view>
      <view class="batch-result-item-error" wx:if="{{!item.success}}">
        <text>{{item.error}}</text>
      </view>
    </view>
    <view class="batch-result-item-action" wx:if="{{item.success}}">
      <button class="batch-result-view-btn" bindtap="viewVisitorCredential" data-id="{{item.data.id}}">查看凭证</button>
    </view>
  </view>
</view>
```

#### 文件：`servicePackage/pages/visitor/batch-invite/index.wxss`

```css
.batch-result-item-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.batch-result-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.batch-result-item-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}
```

## 4. 功能特点

### 常用访客功能
1. **API驱动**: 从服务器获取真实的常用访客数据
2. **社区隔离**: 只显示当前社区的常用访客
3. **字段映射**: 正确映射API返回的字段名
4. **快速填充**: 点击常用访客自动填充姓名和手机号

### 批量添加功能
1. **并发请求**: 同时发起多个添加访客请求
2. **结果统计**: 统计成功和失败的数量
3. **错误处理**: 显示具体的失败原因
4. **用户反馈**: 提供详细的操作结果反馈
5. **页面导航**: 完成后返回访客列表页面

## 5. 错误处理

### 常用访客
- 网络错误时显示空列表
- 社区ID缺失时不发起请求
- API失败时记录错误日志

### 批量添加
- 单个访客添加失败不影响其他访客
- 显示具体的失败原因
- 提供重试机制（用户可重新操作）

## 6. 用户体验

### 加载状态
- 常用访客加载时的状态提示
- 批量添加时的进度提示

### 操作反馈
- 成功/失败的明确提示
- 详细的结果统计信息
- 便捷的后续操作入口

这些更新使访客管理功能完全基于API接口，提供了更真实和可靠的数据处理能力。
