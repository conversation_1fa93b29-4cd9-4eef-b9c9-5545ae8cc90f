/* settings.wxss */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式样式 */
.darkMode, page[data-darkmode="true"] {
  background-color: #1c1c1e;
  color: #f5f5f7;
}

.settings-header {
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.darkMode .header-title, page[data-darkmode="true"] .header-title {
  color: #f5f5f7;
}

.settings-section {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.darkMode .settings-section, page[data-darkmode="true"] .settings-section {
  background: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.darkMode .settings-item, page[data-darkmode="true"] .settings-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 32rpx;
  color: #333;
}

.darkMode .item-label, page[data-darkmode="true"] .item-label {
  color: #f5f5f7;
}

.item-value {
  font-size: 28rpx;
  color: #999;
}

.darkMode .item-value, page[data-darkmode="true"] .item-value {
  color: #8e8e93;
}

.item-arrow {
  font-size: 28rpx;
  color: #999;
}

.darkMode .item-arrow, page[data-darkmode="true"] .item-arrow {
  color: #8e8e93;
}
