/* settings.wxss */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式样式 */
.darkMode, page[data-darkmode="true"] {
  background-color: #1c1c1e;
  color: #f5f5f7;
}

.settings-header {
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.darkMode .header-title, page[data-darkmode="true"] .header-title {
  color: #f5f5f7;
}

.settings-section {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.darkMode .settings-section, page[data-darkmode="true"] .settings-section {
  background: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.darkMode .settings-item, page[data-darkmode="true"] .settings-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 32rpx;
  color: #333;
}

.darkMode .item-label, page[data-darkmode="true"] .item-label {
  color: #f5f5f7;
}

.item-value {
  font-size: 28rpx;
  color: #999;
}

.darkMode .item-value, page[data-darkmode="true"] .item-value {
  color: #8e8e93;
}

.item-arrow {
  font-size: 28rpx;
  color: #999;
}

.darkMode .item-arrow, page[data-darkmode="true"] .item-arrow {
  color: #8e8e93;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-container {
  width: 80%;
  max-width: 600rpx;
  height: 80vh;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  flex: 1;
  padding: 30rpx;
  overflow: hidden;
}

.content-scroll {
  height: 100%;

}

.rich-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.loading-text {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  background-color: #ff8c00;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.confirm-btn:active {
  background-color: #e67a00;
}

/* 富文本内容样式 */
.rich-content :global(p) {
  margin-bottom: 16rpx;
}

.rich-content :global(h1), .rich-content :global(h2), .rich-content :global(h3) {
  font-weight: bold;
  margin: 20rpx 0 16rpx;
}

.rich-content :global(img) {
  max-width: 100%;
  height: auto;
  margin: 16rpx 0;
}

.rich-content :global(a) {
  color: #ff8c00;
  text-decoration: none;
}

.rich-content :global(ul), .rich-content :global(ol) {
  padding-left: 30rpx;
  margin: 16rpx 0;
}

.rich-content :global(li) {
  margin-bottom: 8rpx;
}
