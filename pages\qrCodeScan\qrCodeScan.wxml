<!--扫码核销页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 扫码中 -->
  <view class="scanning-container" wx:if="{{!scanResult && !showOrderConfirm}}">
    <view class="scanning-icon"></view>
    <view class="scanning-text">正在扫描...</view>
    <view class="scanning-tip">请将二维码放入框内</view>
  </view>

  <!-- 订单确认 -->
  <view class="confirm-container" wx:elif="{{showOrderConfirm}}">
    <view class="confirm-icon"></view>
    <view class="confirm-title">确认完成订单</view>

    <view class="order-details">
      <view class="detail-item">
        <view class="detail-label">订单号</view>
        <view class="detail-value">{{orderInfo.orderNo}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">商品</view>
        <view class="detail-value">{{orderInfo.stuffDescribe}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">数量</view>
        <view class="detail-value">{{orderInfo.quantity}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">金额</view>
        <view class="detail-value">¥{{orderInfo.totalAmount}}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">下单时间</view>
        <view class="detail-value">{{orderInfo.createTime}}</view>
      </view>
    </view>

    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="cancelConfirm">取消</view>
      <view class="action-btn primary" bindtap="confirmCompleteOrder">确认完成</view>
    </view>
  </view>
  
  <!-- 验证成功 -->
  <view class="result-container success" wx:elif="{{verificationSuccess}}">
    <view class="result-icon success"></view>
    <view class="result-title">核销成功</view>
    <view class="result-time">{{orderInfo.verifyTime}}</view>
    
    <view class="order-card">
      <view class="order-header">
        <view class="order-id">订单号: {{orderInfo.orderSn}}</view>
        <view class="order-time">{{orderInfo.createTime}}</view>
      </view>
      <view class="order-content">
        <image class="buyer-avatar" src="{{orderInfo.buyerAvatar}}" mode="aspectFill"></image>
        <view class="order-info">
          <view class="buyer-name">{{orderInfo.buyerName}}</view>
          <view class="goods-title">{{orderInfo.goodsTitle}}</view>
          <view class="order-detail">
            <view class="order-quantity">数量: {{orderInfo.quantity}}</view>
            <view class="order-amount">金额: ¥{{orderInfo.totalAmount}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="rescan">继续扫码</view>
      <view class="action-btn primary" bindtap="viewOrderDetail">查看详情</view>
    </view>
  </view>
  
  <!-- 验证失败 -->
  <view class="result-container error" wx:elif="{{verificationFailed}}">
    <view class="result-icon error"></view>
    <view class="result-title">核销失败</view>
    <view class="error-message">{{errorMessage}}</view>
    
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="backToList">返回列表</view>
      <view class="action-btn primary" bindtap="rescan">重新扫码</view>
    </view>
  </view>
</view>


<!-- 邀请码绑定弹窗 -->
<view class="invite-modal {{showInviteModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="invite-modal-mask" bindtap="hideInviteModal"></view>
  <view class="invite-modal-content">
    <!-- 弹窗头部 -->
    <view class="invite-modal-header">
      <text class="invite-modal-title">住户邀请</text>
      <view class="invite-modal-close" bindtap="hideInviteModal">×</view>
    </view>

    <!-- 住户信息区域 -->
    <view class="invite-resident-info" wx:if="{{inviteData}}">
      <view class="resident-avatar">
        <image wx:if="{{inviteData.avatar}}" src="{{inviteData.avatar}}" mode="aspectFill"></image>
        <text wx:else class="avatar-text">{{inviteData.nameInitial || '?'}}</text>
      </view>
      <view class="resident-details">
        <view class="resident-name">{{inviteData.residentName || '未知用户'}}</view>
           <view class="resident-name"> {{inviteData.communityName}} </view>
        <view class="resident-type">{{inviteData.phone||''}}</view>
      </view>
    </view>

    <!-- 房产列表区域 -->
    <view class="invite-house-list" wx:if="{{inviteData && inviteData.roomList}}">
      <view class="house-list-title">邀请您绑定以下房产：</view>
      <scroll-view class="house-scroll" scroll-y="true">
        <view 
          class="house-item {{item.isBind ? 'bound' : ''}}" 
          wx:for="{{inviteData.roomList}}" 
          wx:key="id"
        >
          <view class="house-info">
            <view class="house-address">{{item.fullAddress || item.address}}</view>
            <view class="house-status">
              <text  class="status-pending">{{item.residentTypeName}}</text>
            </view>
          </view>
          <view class="house-icon">
            <view class="home-icon"></view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="invite-actions">
      <button class="invite-btn invite-btn-cancel" bindtap="hideInviteModal">取消</button>
      <button 
        class="invite-btn invite-btn-confirm" 
        bindtap="confirmBinding"
        disabled="{{isBinding}}"
      >
        {{isBinding ? '绑定中...' : '确认绑定'}}
      </button>
    </view>
  </view>
</view>

<!-- 加载提示 -->
<view class="loading-overlay {{isLoading ? 'show' : ''}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>