// auth.js
Page({
  data: {
    name: '',
    idCard: '',
    phone: '',
    verificationCode: '',
    houseInfo: '',
    agreed: false,
    canSubmit: false,
    codeSent: false,
    countDown: 60,
    showHouseSelector: false,
    houseList: [
      { id: 1, name: '星河湾1栋1单元101', address: '广州市天河区珠江新城星河湾小区1栋1单元101' },
      { id: 2, name: '星河湾1栋1单元102', address: '广州市天河区珠江新城星河湾小区1栋1单元102' },
      { id: 3, name: '星河湾1栋2单元201', address: '广州市天河区珠江新城星河湾小区1栋2单元201' },
      { id: 4, name: '星河湾2栋1单元101', address: '广州市天河区珠江新城星河湾小区2栋1单元101' },
      { id: 5, name: '星河湾2栋1单元102', address: '广州市天河区珠江新城星河湾小区2栋1单元102' }
    ],
    selectedHouse: -1
  },

  onLoad: function (options) {
    // 如果有from参数，记录来源页面
    if (options.from) {
      this.setData({
        fromPage: options.from
      })
    }
  },

  // 输入姓名
  inputName: function (e) {
    this.setData({
      name: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 输入身份证号
  inputIdCard: function (e) {
    this.setData({
      idCard: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 输入手机号
  inputPhone: function (e) {
    this.setData({
      phone: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 输入验证码
  inputVerificationCode: function (e) {
    this.setData({
      verificationCode: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 发送验证码
  sendVerificationCode: function () {
    const phone = this.data.phone
    if (!phone) {
      wx.showToast({
        title: '请先输入手机号',
        icon: 'none'
      })
      return
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    // 模拟发送验证码
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    })

    // 开始倒计时
    this.setData({
      codeSent: true,
      countDown: 60
    })

    this.countDownTimer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(this.countDownTimer)
        this.setData({
          codeSent: false
        })
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        })
      }
    }, 1000)
  },

  // 选择房屋
  selectHouse: function () {
    this.setData({
      showHouseSelector: true
    })
  },

  // 关闭房屋选择器
  closeHouseSelector: function () {
    this.setData({
      showHouseSelector: false
    })
  },

  // 选择房屋项
  selectHouseItem: function (e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedHouse: index
    })
  },

  // 确认房屋选择
  confirmHouseSelection: function () {
    const index = this.data.selectedHouse
    if (index >= 0) {
      const house = this.data.houseList[index]
      this.setData({
        houseInfo: house.name,
        showHouseSelector: false
      })
      this.checkCanSubmit()
    } else {
      wx.showToast({
        title: '请选择房屋',
        icon: 'none'
      })
    }
  },

  // 勾选协议
  checkboxChange: function (e) {
    this.setData({
      agreed: e.detail.value.includes('agree')
    })
    this.checkCanSubmit()
  },

  // 显示用户协议
  showAgreement: function () {
    wx.showModal({
      title: '用户服务协议',
      content: '这是用户服务协议内容...',
      showCancel: false
    })
  },

  // 显示隐私政策
  showPrivacyPolicy: function () {
    wx.showModal({
      title: '隐私政策',
      content: '这是隐私政策内容...',
      showCancel: false
    })
  },

  // 检查是否可以提交
  checkCanSubmit: function () {
    const { name, idCard, phone, verificationCode, houseInfo, agreed } = this.data
    const canSubmit = name && idCard && phone && verificationCode && houseInfo && agreed
    this.setData({
      canSubmit
    })
  },

  // 提交认证
  submitAuth: function () {
    if (!this.data.canSubmit) return

    const { name, idCard, phone, verificationCode, houseInfo } = this.data

    // 简单的身份证号格式验证
    if (!/^\d{17}[\dXx]$/.test(idCard)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return
    }

    // 简单的手机号格式验证
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    // 模拟验证码验证
    if (verificationCode !== '123456' && verificationCode !== '666666') {
      wx.showToast({
        title: '验证码错误',
        icon: 'none'
      })
      return
    }

    // 显示加载中
    wx.showLoading({
      title: '提交中...',
      mask: true
    })

    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading()
      
      // 保存认证信息
      wx.setStorageSync('isAuthenticated', true)
      wx.setStorageSync('userName', name)
      
      // 显示认证成功
      wx.showToast({
        title: '认证成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            // 如果有来源页面，返回来源页面
            if (this.data.fromPage === 'home') {
              wx.switchTab({
                url: '/pages/index/index'
              })
            } else {
              wx.navigateBack()
            }
          }, 1500)
        }
      })
    }, 2000)
  },

  onUnload: function () {
    // 清除定时器
    if (this.countDownTimer) {
      clearInterval(this.countDownTimer)
    }
  }
})
