// pages/property/workorder/templates/index.js
const templateManager = require('../../../../utils/workorder-template');

Page({
  data: {
    darkMode: false,
    currentTab: 0,
    tabs: [
      { id: templateManager.TEMPLATE_TYPES.PROCESS, name: '处理备注' },
      { id: templateManager.TEMPLATE_TYPES.ASSIGN, name: '分配备注' },
      { id: templateManager.TEMPLATE_TYPES.COMPLETE, name: '完成结果' },
      { id: templateManager.TEMPLATE_TYPES.RECORD, name: '处理记录' }
    ],
    templates: {
      [templateManager.TEMPLATE_TYPES.PROCESS]: [],
      [templateManager.TEMPLATE_TYPES.ASSIGN]: [],
      [templateManager.TEMPLATE_TYPES.COMPLETE]: [],
      [templateManager.TEMPLATE_TYPES.RECORD]: []
    },
    showAddModal: false,
    showEditModal: false,
    editingTemplate: null,
    newTemplate: {
      content: '',
      remark: ''
    },
    statusBarHeight: 20
  },

  onLoad: function() {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight
    });
    
    // 初始化模板数据
    templateManager.initTemplates();
    
    // 加载模板数据
    this.loadTemplates();
  },
  
  // 加载模板数据
  loadTemplates: function() {
    const templates = {};
    
    this.data.tabs.forEach(tab => {
      templates[tab.id] = templateManager.getTemplates(tab.id);
    });
    
    this.setData({ templates });
  },
  
  // 切换标签页
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ currentTab: index });
  },
  
  // 显示添加模板对话框
  showAddModal: function() {
    this.setData({
      showAddModal: true,
      newTemplate: {
        content: '',
        remark: ''
      }
    });
  },
  
  // 隐藏添加模板对话框
  hideAddModal: function() {
    this.setData({ showAddModal: false });
  },
  
  // 显示编辑模板对话框
  showEditModal: function(e) {
    const { id, type } = e.currentTarget.dataset;
    const templates = this.data.templates[type];
    const template = templates.find(t => t.id === id);
    
    if (template) {
      this.setData({
        showEditModal: true,
        editingTemplate: {
          id,
          type,
          content: template.content,
          remark: template.remark || ''
        }
      });
    }
  },
  
  // 隐藏编辑模板对话框
  hideEditModal: function() {
    this.setData({ showEditModal: false });
  },
  
  // 输入新模板内容
  inputNewContent: function(e) {
    this.setData({
      'newTemplate.content': e.detail.value
    });
  },
  
  // 输入新模板备注
  inputNewRemark: function(e) {
    this.setData({
      'newTemplate.remark': e.detail.value
    });
  },
  
  // 输入编辑模板内容
  inputEditContent: function(e) {
    this.setData({
      'editingTemplate.content': e.detail.value
    });
  },
  
  // 输入编辑模板备注
  inputEditRemark: function(e) {
    this.setData({
      'editingTemplate.remark': e.detail.value
    });
  },
  
  // 添加模板
  addTemplate: function() {
    const { currentTab, tabs, newTemplate } = this.data;
    const type = tabs[currentTab].id;
    
    if (!newTemplate.content) {
      wx.showToast({
        title: '请输入模板内容',
        icon: 'none'
      });
      return;
    }
    
    // 处理记录模板需要同时填写动作和备注
    if (type === templateManager.TEMPLATE_TYPES.RECORD && !newTemplate.remark) {
      wx.showToast({
        title: '请输入处理备注',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '添加中...' });
    
    templateManager.addTemplate(type, newTemplate)
      .then(templates => {
        wx.hideLoading();
        
        // 更新模板列表
        const updatedTemplates = { ...this.data.templates };
        updatedTemplates[type] = templates;
        
        this.setData({
          templates: updatedTemplates,
          showAddModal: false
        });
        
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('添加模板失败', error);
        
        wx.showToast({
          title: error.message || '添加失败',
          icon: 'none'
        });
      });
  },
  
  // 编辑模板
  editTemplate: function() {
    const { editingTemplate } = this.data;
    
    if (!editingTemplate.content) {
      wx.showToast({
        title: '请输入模板内容',
        icon: 'none'
      });
      return;
    }
    
    // 处理记录模板需要同时填写动作和备注
    if (editingTemplate.type === templateManager.TEMPLATE_TYPES.RECORD && !editingTemplate.remark) {
      wx.showToast({
        title: '请输入处理备注',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '更新中...' });
    
    templateManager.editTemplate(
      editingTemplate.type,
      editingTemplate.id,
      {
        content: editingTemplate.content,
        remark: editingTemplate.remark
      }
    )
      .then(templates => {
        wx.hideLoading();
        
        // 更新模板列表
        const updatedTemplates = { ...this.data.templates };
        updatedTemplates[editingTemplate.type] = templates;
        
        this.setData({
          templates: updatedTemplates,
          showEditModal: false
        });
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('编辑模板失败', error);
        
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none'
        });
      });
  },
  
  // 删除模板
  deleteTemplate: function(e) {
    const { id, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个模板吗？',
      confirmText: '删除',
      confirmColor: '#f44336',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          
          templateManager.deleteTemplate(type, id)
            .then(templates => {
              wx.hideLoading();
              
              // 更新模板列表
              const updatedTemplates = { ...this.data.templates };
              updatedTemplates[type] = templates;
              
              this.setData({ templates: updatedTemplates });
              
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(error => {
              wx.hideLoading();
              console.error('删除模板失败', error);
              
              wx.showToast({
                title: error.message || '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },
  
  // 重置模板
  resetTemplates: function() {
    wx.showModal({
      title: '重置模板',
      content: '确定要重置所有模板为默认值吗？这将删除所有自定义模板。',
      confirmText: '重置',
      confirmColor: '#f44336',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '重置中...' });
          
          templateManager.resetTemplates()
            .then(() => {
              wx.hideLoading();
              
              // 重新加载模板数据
              this.loadTemplates();
              
              wx.showToast({
                title: '重置成功',
                icon: 'success'
              });
            })
            .catch(error => {
              wx.hideLoading();
              console.error('重置模板失败', error);
              
              wx.showToast({
                title: error.message || '重置失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },
  
  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
