/**index.wxss**/
@import './index.skeleton.wxss';

/* 通用样式 */
.container {

  padding-bottom:180rpx;
}

.card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 32rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 积分中心样式 */
.points-center {
  padding: 30rpx;
}

.points-title-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff8c00;
}

.points-info {
  margin-top: 20rpx;
}

.points-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.points-value-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.points-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff8c00;
}

.points-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.points-level-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.points-level {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
  background: rgba(255, 140, 0, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.points-next-level {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.points-progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.points-progress-bg {
  flex: 1;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.points-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  border-radius: 8rpx;
}

.points-progress-text {
  font-size: 24rpx;
  color: #999;
  width: 60rpx;
  text-align: right;
}

.points-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.points-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.points-action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  background-size: 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.earn-icon {
  background-color: rgba(76, 217, 100, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234cd964' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='5' x2='12' y2='19'%3E%3C/line%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.mall-icon {
  background-color: rgba(255, 140, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z'%3E%3C/path%3E%3Cline x1='3' y1='6' x2='21' y2='6'%3E%3C/line%3E%3Cpath d='M16 10a4 4 0 0 1-8 0'%3E%3C/path%3E%3C/svg%3E");
}

.rules-icon {
  background-color: rgba(90, 200, 250, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%235ac8fa' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.points-action-text {
  font-size: 24rpx;
  color: #666;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: normal;
}
/* 顶部状态栏背景 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240rpx; /* 覆盖状态栏和导航栏 */
  background: linear-gradient(to bottom, #ff8c00, #f8f8f8);
  z-index: -1;
}

/* 欢迎区域 */
.welcome-section {
  padding: 10rpx 40rpx 10rpx; /* 减少顶部内边距 */
  margin-bottom: 10rpx; /* 减少底部外边距 */
  margin-top: -10rpx; /* 负边距使其上移 */
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx; /* 减少底部间距 */
  margin-top: 0; /* 移除顶部间距 */
}

.scan-code-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}

.scan-code-icon {
  width: 60rpx;
  height: 60rpx;
}

.community-selector {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 36rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.community-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
}

.community-arrow {
  font-size: 20rpx;
  margin-left: 10rpx;
  color: #ffffff;
}

.weather-mood {
  display: flex;
  align-items: center;
  margin-top: 10rpx; /* 减少顶部间距 */
  margin-bottom: 10rpx; /* 增加底部间距 */
}

.weather-display {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.weather-temp {
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 10rpx;
  color: #666666;
}

.weather-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666666;
}

/* 天气图标 */
.icon-weather-sunny, .icon-weather-cloudy, .icon-weather-rainy, .icon-weather-snowy {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-weather-sunny {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='5'%3E%3C/circle%3E%3Cline x1='12' y1='1' x2='12' y2='3'%3E%3C/line%3E%3Cline x1='12' y1='21' x2='12' y2='23'%3E%3C/line%3E%3Cline x1='4.22' y1='4.22' x2='5.64' y2='5.64'%3E%3C/line%3E%3Cline x1='18.36' y1='18.36' x2='19.78' y2='19.78'%3E%3C/line%3E%3Cline x1='1' y1='12' x2='3' y2='12'%3E%3C/line%3E%3Cline x1='21' y1='12' x2='23' y2='12'%3E%3C/line%3E%3Cline x1='4.22' y1='19.78' x2='5.64' y2='18.36'%3E%3C/line%3E%3Cline x1='18.36' y1='5.64' x2='19.78' y2='4.22'%3E%3C/line%3E%3C/svg%3E");
}

.icon-weather-cloudy {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z'%3E%3C/path%3E%3C/svg%3E");
}

.icon-weather-rainy {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='16' y1='13' x2='16' y2='21'%3E%3C/line%3E%3Cline x1='8' y1='13' x2='8' y2='21'%3E%3C/line%3E%3Cline x1='12' y1='15' x2='12' y2='23'%3E%3C/line%3E%3Cpath d='M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25'%3E%3C/path%3E%3C/svg%3E");
}

.icon-weather-snowy {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25'%3E%3C/path%3E%3Cline x1='8' y1='16' x2='8.01' y2='16'%3E%3C/line%3E%3Cline x1='8' y1='20' x2='8.01' y2='20'%3E%3C/line%3E%3Cline x1='12' y1='18' x2='12.01' y2='18'%3E%3C/line%3E%3Cline x1='12' y1='22' x2='12.01' y2='22'%3E%3C/line%3E%3Cline x1='16' y1='16' x2='16.01' y2='16'%3E%3C/line%3E%3Cline x1='16' y1='20' x2='16.01' y2='20'%3E%3C/line%3E%3C/svg%3E");
}

.mood-message {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

/* 轮播Banner */
.banner-carousel {
  margin: 0 32rpx 30rpx 32rpx; /* 与卡片相同的边距 */
}

/* 轮播图样式 */
.banner-swiper {
  width: 100%;
  /* 使用宽高比16:9，通过aspect-ratio设置 */
  aspect-ratio: 16/9;
  /* 设置最大高度，适配平板等大屏设备 */
  max-height: 400rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(255, 140, 0, 0.08), 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover; /* 确保图片覆盖整个区域并保持比例 */
}

/* 轮播图标题 */
.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  color: white;
  padding: 40rpx 30rpx 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 0 0 20rpx 20rpx;
}

/* 轮播图加载状态 */
.banner-loading {
  width: 100%;
  aspect-ratio: 16/9;
  max-height: 400rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(255, 140, 0, 0.08), 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 轮播图空状态 */
.banner-empty {
  width: 100%;
  aspect-ratio: 16/9;
  max-height: 400rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(255, 140, 0, 0.08), 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 描述文字样式 */
.community-desc {
  font-size: 30rpx; /* 增大字号 */
  color: #888888; /* 浅灰色 */
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400; /* 正常字重 */
}

.payment-desc, .property-desc {
  font-size: 26rpx; /* 增大字号 */
  color: #888888; /* 浅灰色 */
  line-height: 1.3; /* 减小行高 */
  font-weight: 400; /* 正常字重 */
  margin-top: -4rpx; /* 负边距使其上移 */
}

/* 图标样式 */
.community-icon {
  position: absolute;
  right: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon, .property-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.community-icon image, .payment-icon image, .property-icon image {
  width: 100%;
  height: 100%;
  filter: invert(56%) sepia(75%) saturate(1582%) hue-rotate(360deg) brightness(103%) contrast(106%);
  background-color: transparent !important; /* 确保背景透明 */
}

.feature-banner-content {
  position: relative;
  z-index: 2;
}

.feature-banner-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

/* 消息中心 */
.message-center {
  margin-bottom: 40rpx;
}

.message-tabs-container {
  background: #F8F8F8;
  border-radius: 20rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  white-space: nowrap;
}

.message-tabs {
  display: flex;
  padding: 8rpx;
}

.message-tab {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}

.message-tab.active {
  color: #ff9500;
  font-weight: 600;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-badge {
  background: #FF3B30;
  color: white;
  font-size: 22rpx;
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
  padding: 0 8rpx;
}

.message-panel {
  display: none;
}

.message-panel.active {
  display: block;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.message-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.message-item:first-child {
  padding-top: 0;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #ff9500;
  background: rgba(255, 149, 0, 0.1);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
}

.message-info {
  flex: 1;
}

.message-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.icon-megaphone {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 11l18-5v12L3 14v-3z'%3E%3C/path%3E%3Cpath d='M6 10.6V14H3'%3E%3C/path%3E%3Cpath d='M11.6 16.8a3 3 0 1 1-5.8-1.6'%3E%3C/path%3E%3C/svg%3E");
}

.message-title {
  font-size: 32rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.message-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.message-status {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  margin-left: 16rpx;
}

.message-status.unread {
  background: #FF3B30;
}

.message-item.emergency {
  background-color: rgba(255, 59, 48, 0.05);
  border-left: 6rpx solid #FF3B30;
  padding-left: 24rpx;
  border-radius: 8rpx;
}

/* 社区活动 */
.community-events {
  padding: 0 32rpx; /* 调整为与卡片相同的左右内边距 */
  margin-bottom: 40rpx;
}

.event-card {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.event-image {
  height: 320rpx;
  background-size: cover;
  background-position: center;
}

.event-content {
  padding: 24rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.event-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  align-items: center;
}

.event-info-item {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.icon-calendar-event,
.icon-people-event {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar-event {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.icon-people-event {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

/* 消息详情弹窗 */
.message-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  backdrop-filter: blur(10px);
}

.message-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.message-detail-content {
  width: 85%;
  max-width: 640rpx;
  background-color: white;
  border-radius: 32rpx;
  overflow: hidden;
  transform: scale(0.9) translateY(40rpx);
  transition: transform 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  box-shadow: 0 24rpx 60rpx rgba(0, 0, 0, 0.15);
}

.message-detail-modal.show .message-detail-content {
  transform: scale(1) translateY(0);
}

.message-detail-header {
  padding: 40rpx 48rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: 32rpx;
}

.message-detail-icon {
  width: 84rpx;
  height: 84rpx;
  border-radius: 50%;
  background-color: rgba(255, 149, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.message-detail-title-container {
  flex: 1;
}

.message-detail-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 8rpx;
}

.message-detail-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.message-detail-body {
  padding: 32rpx 48rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.message-detail-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #3A3A3C;
  white-space: pre-line;
}

.message-detail-footer {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.08);
}

.message-detail-close-btn {
  flex: 1;
  height: 108rpx;
  border: none;
  background: none;
  font-size: 32rpx;
  font-weight: 500;
  color: #ff9500;
}

/* 快捷服务样式 */
.quick-services {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20rpx 0;
  padding: 20rpx 0;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160rpx;
  position: relative;
}

.service-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 16rpx;
  display: block;
}

/* 服务图标样式 */

.service-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  width: 100%;
  margin-top: 8rpx;
  display: block;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.2;
}

.visitor-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background-color: #FF3B30;
  color: white;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

/* 物业管理登录弹窗样式 */
.property-auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  backdrop-filter: blur(8px);
  transform: translateY(5000);
  /* iOS兼容性修复 */
  -webkit-overflow-scrolling: touch;
  overflow: hidden;
}

.property-auth-modal.show {
  opacity: 1;
  visibility: visible;
}

.property-auth-content {
  width: 85%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 32rpx;
  overflow: hidden;
  transform: scale(0.9) translateY(40rpx);
  transition: transform 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.15);
  padding: 40rpx 0 40rpx;
  /* iOS兼容性修复 */
  position: relative;
  z-index: 10000;
  isolation: isolate;
}

.property-auth-modal.show .property-auth-content {
  transform: scale(1) translateY(0);
  animation: modal-show 0.5s forwards;
}

@keyframes modal-show {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(40rpx);
  }
  50% {
    opacity: 1;
    transform: scale(1.02) translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-top: 20rpx;
  margin-bottom: 40rpx;
  color: #333;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-title::before {
  content: "";
  display: block;
  width: 48rpx;
  height: 48rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='11' width='18' height='11' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M7 11V7a5 5 0 0 1 10 0v4'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 16rpx;
}

.input-group {
  padding: 0 40rpx;
  margin-bottom: 32rpx;
  position: relative;
  /* iOS兼容性修复 */
  z-index: 100;
  isolation: isolate;
  overflow: hidden;
}

.input-group::before {
  content: "";
  display: block;
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 52rpx;
  top: 50%;
  transform: translateY(-50%);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1;
  opacity: 0.8;
}

.input-group:nth-child(2)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
}

.input-group:nth-child(3)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='11' width='18' height='11' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M7 11V7a5 5 0 0 1 10 0v4'%3E%3C/path%3E%3C/svg%3E");
}

.input-group:nth-child(4)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='13 2 3 14 12 14 11 22 21 10 12 10 13 2'%3E%3C/polygon%3E%3C/svg%3E");
}

.captcha-container {
  padding: 0 40rpx;
  margin-bottom: 32rpx;
  position: relative;
}

.remember-account {
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #555;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  border: 2rpx solid #ddd;
  margin-right: 16rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox.checked {
  background-color: #ff9500;
  border-color: #ff9500;
}

.checkbox-inner {
  width: 20rpx;
  height: 12rpx;
  border-left: 2rpx solid white;
  border-bottom: 2rpx solid white;
  transform: rotate(-45deg) scale(0);
  transition: all 0.2s;
  opacity: 0;
}

.checkbox.checked .checkbox-inner {
  transform: rotate(-45deg) scale(1);
  opacity: 1;
}

.modal-input {
  width: 100%;
  height: 104rpx;
  padding: 0 24rpx 0 72rpx;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  background-color: #F7F7F7;
  transition: all 0.3s;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  opacity: 0;
  /* iOS兼容性修复 */
  position: relative;
  z-index: 10;
  -webkit-appearance: none;
  -webkit-user-select: auto;
  outline: none;
}

.modal-input.show {
  opacity: 1;
}

.modal-input:focus {
  background-color: #FFF8EF;
  box-shadow: 0 0 0 2rpx rgba(255, 149, 0, 0.2);
  /* iOS焦点状态修复 */
  z-index: 11;
}

.modal-input::placeholder {
  color: #AEAEB2;
  /* iOS placeholder修复 */
  opacity: 1;
  -webkit-text-fill-color: #AEAEB2;
}

.input-clear, .input-action {
  position: absolute;
  right: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 36rpx;
  z-index: 2;
}

.icon-eye, .icon-eye-off {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-eye {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.icon-eye-off {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24'%3E%3C/path%3E%3Cline x1='1' y1='1' x2='23' y2='23'%3E%3C/line%3E%3C/svg%3E");
}

.modal-buttons {
  display: flex;
  margin: 40rpx 40rpx 20rpx;
}

.modal-button {
  flex: 1;
  height: 96rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  margin: 0;
  padding: 0;
  line-height: 96rpx;
  transition: all 0.3s;
}

.modal-button::after {
  border: none;
}

.cancel-button {
  color: #666;
  background-color: #F7F7F7;
  margin-right: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.cancel-button:active {
  background-color: #EFEFEF;
}

.confirm-button {
  color: white;
  background: linear-gradient(135deg, #ff9500, #ff8c00);
  font-weight: 600;
  box-shadow: 0 8rpx 16rpx rgba(255, 149, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.confirm-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.3);
}

.confirm-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(100, 100);
    opacity: 0;
  }
}

.confirm-button:active::after {
  animation: ripple 0.6s ease-out;
}

/* 暗黑模式下积分中心样式 */
.darkMode .points-value {
  color: #ff8c00;
}

.darkMode .points-label,
.darkMode .points-next-level,
.darkMode .points-progress-text,
.darkMode .points-action-text {
  color: #8e8e93;
}

.darkMode .points-level {
  color: #ff8c00;
  background: rgba(255, 140, 0, 0.2);
}

.darkMode .points-progress-bg {
  background: #3a3a3c;
}

.darkMode .earn-icon {
  background-color: rgba(76, 217, 100, 0.2);
}

.darkMode .mall-icon {
  background-color: rgba(255, 140, 0, 0.2);
}

.darkMode .rules-icon {
  background-color: rgba(90, 200, 250, 0.2);
}

.darkMode .points-title-icon {
  color: #ff8c00;
}



/* iOS特定修复 - 防止placeholder文字溢出 */
@supports (-webkit-touch-callout: none) {
  .modal-input {
    /* iOS WebKit特定修复 */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: text;
    -webkit-appearance: none;
    transform: translateZ(0);
    will-change: transform;
    /* 强制重绘 */
    -webkit-transform: translate3d(0, 0, 0);
    -webkit-font-smoothing: antialiased;
    /* 防止文字溢出 */
    text-overflow: clip;
    white-space: nowrap;
    overflow: hidden;
  }

  .modal-input::placeholder {
    /* iOS placeholder特定修复 */
    -webkit-text-fill-color: #AEAEB2 !important;
    opacity: 1 !important;
    color: #AEAEB2 !important;
    transform: translateZ(0);
    /* 强制placeholder位置 */
    position: relative;
    z-index: 1;
    /* 防止文字溢出 */
    text-overflow: clip;
    white-space: nowrap;
    overflow: hidden;
    /* 强制重绘 */
    -webkit-transform: translate3d(0, 0, 0);
  }

  .input-group {
    /* iOS输入组修复 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    /* 强制重绘 */
    -webkit-transform: translate3d(0, 0, 0);
    /* 防止内容溢出 */
    contain: layout style paint;
  }

  .property-auth-content {
    /* iOS弹窗内容修复 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    /* 强制重绘 */
    -webkit-transform: translate3d(0, 0, 0);
    /* 防止内容溢出 */
    contain: layout style paint;
  }

  /* 防止iOS自动填充样式干扰 */
  .modal-input:-webkit-autofill,
  .modal-input:-webkit-autofill:hover,
  .modal-input:-webkit-autofill:focus,
  .modal-input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px #F7F7F7 inset !important;
    -webkit-text-fill-color: #333 !important;
    transition: background-color 5000s ease-in-out 0s;
    /* 强制placeholder样式 */
    -webkit-text-security: none !important;
  }

  /* 强制隐藏可能的浮动placeholder */
  .modal-input::-webkit-input-placeholder {
    -webkit-text-fill-color: #AEAEB2 !important;
    opacity: 1 !important;
    color: #AEAEB2 !important;
    position: relative !important;
    z-index: 1 !important;
    transform: translateZ(0) !important;
  }

  .modal-input::-moz-placeholder {
    color: #AEAEB2 !important;
    opacity: 1 !important;
  }

  .modal-input:-ms-input-placeholder {
    color: #AEAEB2 !important;
    opacity: 1 !important;
  }

  .modal-input::placeholder {
    color: #AEAEB2 !important;
    opacity: 1 !important;
    -webkit-text-fill-color: #AEAEB2 !important;
  }
}


.welcome-text {
  font-size: 44rpx;
  font-weight: 600;
  color: #ffffff;
  margin-right: 35rpx;
}

.welcome-user{
  display: flex;
  align-items: center;
}