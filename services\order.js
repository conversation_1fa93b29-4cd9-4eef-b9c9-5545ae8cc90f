/**
 * 订单服务
 * 提供订单创建、查询等功能
 */

// 模拟订单数据
const mockOrders = [];

// 生成唯一的订单号
const generateOrderNo = () => {
  return 'ORD' + Date.now().toString() + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
};

// 生成唯一的二维码内容
const generateQrContent = (orderId) => {
  return `ORDER:${orderId}:${Date.now()}:${Math.random().toString(36).substring(2, 10)}`;
};

/**
 * 订单服务
 */
const OrderService = {
  /**
   * 创建订单
   * @param {Object} orderParams - 订单参数
   * @param {number|string} orderParams.goodsId - 商品ID
   * @param {number} orderParams.quantity - 购买数量
   * @param {string} orderParams.remark - 备注
   * @param {number|string} orderParams.finalPrice - 最终价格
   * @param {boolean} orderParams.usePoints - 是否使用积分
   * @param {number} orderParams.pointsUsed - 使用的积分数
   * @returns {Promise} - 返回创建结果
   */
  createOrder(orderParams) {
    console.log('OrderService.createOrder', orderParams);
    
    return new Promise((resolve, reject) => {
      try {
        // 参数校验
        if (!orderParams || !orderParams.goodsId) {
          reject(new Error('商品ID不能为空'));
          return;
        }

        // 创建新订单
        const orderNo = generateOrderNo();
        const newOrder = {
          _id: Date.now().toString(),
          orderNo: orderNo,
          ...orderParams,
          status: 'pending_pickup', // 待取货状态
          qrCodeContent: generateQrContent(orderNo),
          createTime: new Date().toISOString()
        };

        // 模拟存储
        mockOrders.push(newOrder);
        console.log('订单创建成功', newOrder);

        // 模拟网络延迟
        setTimeout(() => {
          resolve({
            _id: newOrder._id,
            orderNo: newOrder.orderNo,
            qrCodeContent: newOrder.qrCodeContent
          });
        }, 500);
      } catch (error) {
        console.error('订单创建失败', error);
        reject(error);
      }
    });
  },

  /**
   * 获取用户订单列表
   * @returns {Promise} - 返回订单列表
   */
  getMyOrders() {
    return Promise.resolve([...mockOrders]);
  },

  /**
   * 根据订单号获取订单
   * @param {string} orderNo - 订单号
   * @returns {Promise} - 返回订单详情
   */
  getOrderByNo(orderNo) {
    return new Promise((resolve, reject) => {
      const order = mockOrders.find(o => o.orderNo === orderNo);
      if (order) {
        resolve(order);
      } else {
        reject(new Error('订单不存在'));
      }
    });
  },

  /**
   * 核销订单
   * @param {string} qrCodeContent - 二维码内容
   * @returns {Promise} - 返回核销结果
   */
  verifyOrder(qrCodeContent) {
    return new Promise((resolve, reject) => {
      const order = mockOrders.find(o => o.qrCodeContent === qrCodeContent);
      if (!order) {
        reject(new Error('订单不存在'));
        return;
      }

      if (order.status !== 'pending_pickup') {
        reject(new Error('订单状态不正确，无法核销'));
        return;
      }

      order.status = 'completed';
      order.verifyTime = new Date().toISOString();
      
      resolve({
        success: true,
        orderNo: order.orderNo
      });
    });
  }
};

module.exports = OrderService;
