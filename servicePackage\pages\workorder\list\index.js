// pages/workorder/list/index.js
const workOrderApi = require('@/api/workOrderApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    statusTabs: [],
    currentTab: 0,
    statusMap: {},
    workOrderStatus: [],
    workOrderType: [],

    // 类型筛选
    typeTabs: [],
    currentTypeTab: 0,
    typeMap: {},

    workOrders: [],
    searchValue: '',
    currentPage: 1,
    pageSize: 10,
    total: 0,
    loading: false,
    refreshing: false,
    showEmpty: false
  },

  onLoad() {
    this.getWorkOrderStatusDict()
    this.getWorkOrderTypeDict()
    // 加载工单数据
    this.loadWorkOrders()
  },

  onShow() {
    // 页面显示时检查是否需要刷新
    const needRefresh = wx.getStorageSync('workOrderListNeedRefresh')
    if (needRefresh) {
      console.log('检测到工单状态变化，刷新列表')
      // 清除刷新标记
      wx.removeStorageSync('workOrderListNeedRefresh')
      // 刷新工单列表
      this.refreshWorkOrders()
    }
  },

  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    // 工单类型 work_order_type
    // --nameCn维修 nameEn repair
    // --nameCn投诉 nameEn complaint‌
    // --nameCn建议 nameEn suggestion
    // --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children

    // 构建类型标签和映射
    const typeTabs = ['全部']
    const typeMap = { 0: '' } // 全部类型不传参数

    workOrderType.forEach((type, index) => {
      typeTabs.push(type.nameCn)
      typeMap[index + 1] = type.nameEn
    })

    this.setData({
      workOrderType,
      typeTabs,
      typeMap
    })
  },

  // 获取工单状态字典
  getWorkOrderStatusDict: function () {
    // 工单状态 work_order_status
    // --nameCn待处理 nameEn pending
    // --nameCn处理中 nameEn processing
    // --nameCn已完成 nameEn completed
    // --nameCn已取消 nameEn cancelled

    var workOrderStatus = util.getDictByNameEn('work_order_status')[0].children

    // 构建状态标签和映射
    const statusTabs = ['全部']
    const statusMap = { 0: '' } // 全部状态不传参数

    workOrderStatus.forEach((status, index) => {
      statusTabs.push(status.nameCn)
      statusMap[index + 1] = status.nameEn
    })

    this.setData({
      workOrderStatus,
      statusTabs,
      statusMap
    })
  },

  onShow() {
   
  },

  // 页面相关事件处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.loadWorkOrders(true);
  },

  // 页面上拉触底事件的处理函数
  onReachBottom() {
    if (this.data.currentPage < this.data.totalPages) {
      this.loadMoreOrders();
    }
  },

  // 加载工单数据
  loadWorkOrders(refresh = false) {
    this.setData({ loading: true })

    // 准备查询参数
    const params = {
      pageNum: refresh ? 1 : this.data.currentPage,
      pageSize: this.data.pageSize,
      communityId: wx.getStorageSync('selectedCommunity').id
    }

    // 如果有状态筛选，添加状态参数
    if (this.data.currentTab > 0) {
      params.status = this.data.statusMap[this.data.currentTab]
    }

    // 如果有类型筛选，添加类型参数
    if (this.data.currentTypeTab > 0) {
      params.type = this.data.typeMap[this.data.currentTypeTab]
    }

    // 如果有关键字搜索，添加关键字参数
    if (this.data.searchValue && this.data.searchValue.trim()) {
      params.keyword = this.data.searchValue.trim()
    }
    
    // 调用API获取工单数据
    workOrderApi.getWorkOrderList(params)
      .then(res => {

        if (refresh) {
          wx.stopPullDownRefresh()
        }

        const { list, total } = res
        console.log('res',res)
        const processedOrders =[]
        if(list&&list.length>0)
        // 处理图片显示
          processedOrders = list.map(order => {
          return {
            ...order,
            // 处理图片字段，可能是单个图片或逗号分隔的多个图片
            imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
            // 获取状态显示名称
            statusName: this.getStatusName(order.status),
            // 获取类型显示名称
            typeName: this.getTypeName(order.type)
          }
        })

        this.setData({
          workOrders: refresh ? (processedOrders?processedOrders:[]) : [...this.data.workOrders, ...processedOrders?processedOrders:[]],
          total,
          currentPage: refresh ? 1 : this.data.currentPage,
          loading: false,
          refreshing: false,
          showEmpty: total === 0
        })

        
      })
      .catch(error => {
        console.error('加载工单失败', error)
        this.setData({ loading: false, refreshing: false })
        if (refresh) {
          wx.stopPullDownRefresh()
        }
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      })
  },

  // 刷新工单列表
  refreshWorkOrders: function() {
    this.setData({
      currentPage: 1,
      workOrders: [],
      refreshing: true
    })
    this.loadWorkOrders(true)
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshWorkOrders()
  },

  // 获取状态显示名称
  getStatusName: function (statusEn) {
    const status = this.data.workOrderStatus.find(item => item.nameEn === statusEn)
    return status ? status.nameCn : statusEn
  },

  // 获取类型显示名称
  getTypeName: function (typeEn) {
    // 先尝试精确匹配
    let type = this.data.workOrderType.find(item => item.nameEn === typeEn)
    console.log(this.data.workOrderType)
    // 如果精确匹配失败，尝试去除特殊字符后匹配
    // if (!type) {
    //   type = this.data.workOrderType.find(item => {
    //     // 清理可能的特殊字符
    //     const cleanNameEn = item.nameEn.replace(/[\u200B-\u200D\uFEFF]/g, '').trim()
    //     const cleanTypeEn = typeEn.replace(/[\u200B-\u200D\uFEFF]/g, '').trim()
    //     return cleanNameEn === cleanTypeEn
    //   })
    // }

    // 如果还是找不到，使用备用映射
    // if (!type) {
    //   const fallbackMap = {
    //     'repair': '维修',
    //     'complaint': '投诉',
    //     'suggestion': '建议',
    //     'other': '其他'
    //   }
    //   return fallbackMap[typeEn] || typeEn
    // }

    return type ? type.nameCn : typeEn
  },

  // 切换状态标签
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab)
    this.setData({
      currentTab: tab,
      currentPage: 1,
      workOrders: [] // 清空当前数据
    })
    this.loadWorkOrders(true) // 重新加载数据
  },

  // 切换类型标签
  switchTypeTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab)
    this.setData({
      currentTypeTab: tab,
      currentPage: 1,
      workOrders: [] // 清空当前数据
    })
    this.loadWorkOrders(true) // 重新加载数据
  },

  // 搜索工单
  searchWorkOrders(e) {
    this.setData({
      searchValue: e.detail.value,
      currentPage: 1,
      workOrders: [] // 清空当前数据
    })
    // 实时搜索，支持关键字参数
    this.loadWorkOrders(true)
  },

  // 搜索确认
  onSearchConfirm(e) {
    this.setData({
      searchValue: e.detail.value,
      currentPage: 1,
      workOrders: [] // 清空当前数据
    })
    this.loadWorkOrders(true)
  },

  // 加载更多工单
  loadMoreOrders() {
    const { currentPage, total, pageSize } = this.data
    const totalPages = Math.ceil(total / pageSize)

    if (currentPage >= totalPages) {
      wx.showToast({
        title: '已加载全部数据',
        icon: 'none'
      })
      return
    }

    this.setData({ currentPage: currentPage + 1 })
    this.loadWorkOrders() // 加载下一页
  },

  // 查看工单详情
  navigateToDetail(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/detail/index?id=${orderId}`
    })

    // 阻止事件冒泡
    return false
  },

  // 取消工单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认取消',
      content: '确定要取消该工单吗？取消后将无法恢复。',
      success: res => {
        if (res.confirm) {
          // 调用API取消工单
          workOrderApi.cancelWorkOrder(orderId)
            .then(() => {
              // 设置刷新标记，确保从详情页返回时也能刷新
              wx.setStorageSync('workOrderListNeedRefresh', true)

              // 立即刷新当前列表
              this.refreshWorkOrders()

              wx.showToast({
                title: '工单已取消',
                icon: 'success'
              })
            })
            .catch(error => {
              console.error('取消工单失败', error)
              wx.showToast({
                title: error.message || '操作失败，请重试',
                icon: 'none'
              })
            })
        }
      }
    })

    // 阻止事件冒泡
    return false
  },

  // 创建新工单
  createNewOrder() {
    wx.navigateTo({
      url: '/servicePackage/pages/repair/repair'
    });
  },



  // 导航到评价页面
  navigateToEvaluate(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/evaluate/index?id=${orderId}`
    });

    // 阻止事件冒泡
    return false;
  }
});
