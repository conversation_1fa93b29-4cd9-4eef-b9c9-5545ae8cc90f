<!--pages/points/mall/detail.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <image class="loading-icon" src="/images/loading.gif" mode="aspectFit"></image>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 商品详情 -->
  <block wx:else>
    <!-- 商品图片轮播 -->
    <swiper class="product-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="5000" duration="500" circular="{{true}}">
      <block wx:if="{{product && product.images && product.images.length > 0}}">
        <swiper-item wx:for="{{product.images}}" wx:key="index">
          <image class="product-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
        </swiper-item>
      </block>
      <block wx:else>
        <swiper-item>
          <image class="product-image" src="/images/placeholder.png" mode="aspectFill"></image>
        </swiper-item>
      </block>
    </swiper>

    <!-- 商品信息 -->
    <view class="product-info">
      <view class="product-title">{{product.title}}</view>
      <view class="product-price">
        <text class="points">{{product.points}}积分</text>
        <text class="cash" wx:if="{{product.cashPrice && product.cashPrice > 0}}">+ ¥{{product.cashPrice}}</text>
      </view>
      <view class="product-stock">库存: {{product.stock}}</view>
    </view>

    <!-- 商品描述 -->
    <view class="product-description">
      <view class="section-title">商品详情</view>
      <text class="description-text">{{product.description}}</text>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="quantity-control">
        <view class="quantity-btn minus {{quantity <= 1 ? 'disabled' : ''}}" bindtap="decreaseQuantity">-</view>
        <view class="quantity-value">{{quantity}}</view>
        <view class="quantity-btn plus {{quantity >= product.stock ? 'disabled' : ''}}" bindtap="increaseQuantity">+</view>
      </view>
      <view class="exchange-btn {{userPoints < totalPoints || product.stock <= 0 ? 'disabled' : ''}}" bindtap="showExchangeModal">
        {{userPoints < totalPoints ? '积分不足' : (product.stock <= 0 ? '已售罄' : '立即兑换')}}
      </view>
    </view>

    <!-- 兑换确认弹窗 -->
    <view class="modal {{showBuyModal ? 'show' : ''}}">
      <view class="modal-mask" bindtap="closeBuyModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">确认兑换</view>
          <view class="modal-close" bindtap="closeBuyModal">×</view>
        </view>
        <view class="modal-body">
          <view class="modal-product">
            <image class="modal-product-image" src="{{product.images[0]}}" mode="aspectFill"></image>
            <view class="modal-product-info">
              <view class="modal-product-title">{{product.title}}</view>
              <view class="modal-product-price">
                <text class="points">{{product.points}}积分</text>
                <text class="cash" wx:if="{{product.cashPrice && product.cashPrice > 0}}">+ ¥{{product.cashPrice}}</text>
              </view>
            </view>
          </view>
          <view class="modal-detail">
            <view class="modal-detail-item">
              <text>数量</text>
              <text>{{quantity}}</text>
            </view>
            <view class="modal-detail-item">
              <text>总积分</text>
              <text>{{totalPoints}}积分</text>
            </view>
            <view class="modal-detail-item" wx:if="{{cashAmount > 0}}">
              <text>总金额</text>
              <text>¥{{cashAmount}}</text>
            </view>
            <view class="modal-detail-item">
              <text>我的积分</text>
              <text>{{userPoints}}积分</text>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <view class="modal-btn cancel" bindtap="closeBuyModal">取消</view>
          <view class="modal-btn confirm" bindtap="confirmExchange">确认兑换</view>
        </view>
      </view>
    </view>
  </block>
</view>