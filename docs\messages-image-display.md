# 消息中心图片显示功能

## 功能概述

在消息中心页面的消息详情弹窗中，除了显示富文本内容外，还支持显示图片。图片数据来自消息的`imageUrl`字段，支持多图格式（逗号分隔）。

## 实现功能

### 1. 图片数据处理
- 支持单图和多图显示
- 多图格式：逗号分隔的URL字符串
- 自动添加API前缀处理相对路径
- 过滤空值和无效URL

### 2. 图片展示
- 网格布局显示多张图片
- 图片缩略图预览
- 点击图片可放大查看
- 支持图片滑动浏览

### 3. 用户交互
- 点击图片触发微信原生图片预览
- 支持多图片间滑动切换
- 图片加载失败时显示占位背景

## 代码实现

### 1. JavaScript处理逻辑

#### 图片数据处理
```javascript
// 在showMessageDetail方法中添加图片处理
// 处理图片数据
let imageList = [];
if (message.imageUrl) {
  // 处理逗号分隔的多图格式
  const imageUrls = message.imageUrl.split(',').filter(url => url.trim());
  imageList = imageUrls.map(url => {
    const trimmedUrl = url.trim();
    // 如果URL不是完整的HTTP地址，则添加apiUrl前缀
    if (trimmedUrl && !trimmedUrl.startsWith('http')) {
      return this.data.apiUrl + trimmedUrl;
    }
    return trimmedUrl;
  }).filter(url => url); // 过滤空值
}

this.setData({
  showMessageModal: true,
  currentMessage: {
    ...message,
    content,
    imageList // 添加处理后的图片列表
  },
  currentMessageIcon: icon
});
```

#### 图片预览方法
```javascript
// 预览图片
previewImage: function (e) {
  const current = e.currentTarget.dataset.src;
  const urls = this.data.currentMessage.imageList || [];
  
  if (urls.length === 0) return;

  wx.previewImage({
    current: current,
    urls: urls
  });
}
```

### 2. WXML模板结构

```xml
<!-- 弹窗内容 -->
<view class="modal-body">
  <view class="modal-time">{{currentMessage.time}}</view>
  <rich-text class="modal-content">{{currentMessage.content || '暂无内容'}}</rich-text>
  
  <!-- 图片展示 -->
  <view class="modal-images" wx:if="{{currentMessage.imageList && currentMessage.imageList.length > 0}}">
    <view class="images-title">相关图片</view>
    <view class="images-grid">
      <view class="image-item" wx:for="{{currentMessage.imageList}}" wx:key="index" bindtap="previewImage" data-src="{{item}}">
        <image src="{{item}}" mode="aspectFill" class="message-image"></image>
      </view>
    </view>
  </view>
</view>
```

### 3. CSS样式设计

```css
/* 图片展示区域 */
.modal-images {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f5f5f5;
}

.images-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f5f5f5;
  position: relative;
}

.message-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.image-item:active {
  opacity: 0.8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}
```

## 数据格式

### 输入数据格式
```javascript
// 单图格式
message.imageUrl = "image1.jpg"

// 多图格式（逗号分隔）
message.imageUrl = "image1.jpg,image2.png,image3.jpeg"

// 完整URL格式
message.imageUrl = "https://example.com/image1.jpg,https://example.com/image2.png"

// 混合格式
message.imageUrl = "image1.jpg,https://example.com/image2.png,image3.jpeg"
```

### 处理后的数据格式
```javascript
// 处理后的imageList数组
currentMessage.imageList = [
  "https://api.example.com/common-api/v1/file/image1.jpg",
  "https://api.example.com/common-api/v1/file/image2.png",
  "https://api.example.com/common-api/v1/file/image3.jpeg"
]
```

## 功能特点

### 1. 智能URL处理
- 自动识别相对路径和绝对路径
- 相对路径自动添加API前缀
- 绝对路径保持不变
- 过滤空值和无效URL

### 2. 响应式布局
- 图片网格自适应布局
- 固定尺寸缩略图显示
- 支持多行显示
- 图片间距统一

### 3. 用户体验
- 图片点击反馈效果
- 原生图片预览体验
- 多图滑动浏览
- 加载失败占位显示

### 4. 性能优化
- 图片懒加载
- 缩略图模式显示
- 按需加载原图
- 内存占用优化

## 使用场景

### 1. 系统消息
- 系统通知配图
- 功能说明图片
- 操作指引截图

### 2. 通知公告
- 活动宣传图片
- 公告配图
- 重要通知图片

### 3. 私信消息
- 用户发送的图片
- 聊天记录图片
- 分享图片内容

## 注意事项

1. **图片格式支持**：支持常见的图片格式（jpg、png、gif等）
2. **图片大小限制**：建议单张图片不超过5MB
3. **网络处理**：图片加载失败时显示占位背景
4. **性能考虑**：大量图片时建议分页加载
5. **安全性**：确保图片URL的安全性，防止恶意链接

## 相关文件

- `pages/messages/messages.js` - 消息中心主逻辑
- `pages/messages/messages.wxml` - 页面模板
- `pages/messages/messages.wxss` - 样式文件
- `api/messageApi.js` - 消息相关API接口
