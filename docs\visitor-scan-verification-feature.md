# 访客码扫描核销功能实现

## 功能概述
实现了访客码扫描识别和核销功能，允许物业人员通过扫码快速核销访客。

## 实现内容

### 1. 扫码页面功能扩展 (`pages/qrCodeScan/qrCodeScan.js`)

#### 新增访客码识别
- 在 `handleScanResult` 方法中添加了访客码格式识别
- 访客码规则：`visitor:访客数据id`
- 识别到访客码后跳转到访客凭证页面，并传递扫码标识

```javascript
// 处理访客二维码
handleVisitorCode: function (qrCodeContent) {
  // 解析访客码：visitor:id
  const parts = qrCodeContent.split(':');
  const visitorId = parts[1];
  
  // 跳转到访客凭证页面，添加扫码标识
  wx.navigateTo({
    url: `/servicePackage/pages/visitor/credential/index?id=${visitorId}&fromScan=true`
  });
}
```

### 2. 访客凭证页面核销功能 (`servicePackage/pages/visitor/credential/index.js`)

#### 新增数据字段
```javascript
data: {
  // 核销相关
  fromScan: false,        // 是否从扫码进入
  showVerifyButton: false, // 是否显示核销按钮
  isVerifying: false,     // 是否正在核销
  verifySuccess: false    // 核销是否成功
}
```

#### 核销条件检测
- **用户身份检测**：必须是已认证的物业人员
- **访客状态检测**：访客状态必须为"待到访"
- **时效性检测**：访客码未过失效时间

```javascript
checkShowVerifyButton: function () {
  // 检查用户是否是已认证的物业人员
  const propertyResult = util.checkPropertyAuthenticated();
  
  if (!propertyResult.isPropertyAuthenticated) {
    return;
  }
  
  // 检查访客是否过期和状态
  this.checkVisitorExpiration();
}
```

#### 核销功能实现
```javascript
// 核销访客
verifyVisitor: function () {
  wx.showModal({
    title: '确认核销',
    content: '确定要核销此访客吗？核销后访客状态将变为已到访。',
    success: (res) => {
      if (res.confirm) {
        this.performVerify();
      }
    }
  });
}

// 执行核销操作
performVerify: function () {
  visitorsApi.verifyVisitor(this.data.visitorId)
    .then(res => {
      // 核销成功，更新状态
      this.setData({
        verifySuccess: true,
        showVerifyButton: false,
        'visitorData.status': 'visited',
        statusText: '已到访'
      });
    })
    .catch(err => {
      // 处理核销失败
    });
}
```

### 3. UI界面更新 (`servicePackage/pages/visitor/credential/index.wxml`)

#### 核销按钮区域
```xml
<!-- 核销按钮 (仅在扫码进入且满足条件时显示) -->
<view class="visitor-verify-section" wx:if="{{showVerifyButton}}">
  <button class="visitor-verify-btn {{isVerifying ? 'verifying' : ''}}" 
          bindtap="verifyVisitor" 
          disabled="{{isVerifying}}">
    <image src="/images/icons/check-circle.svg" class="verify-icon" wx:if="{{!isVerifying}}"></image>
    <image src="/images/icons/loading.svg" class="verify-icon loading" wx:if="{{isVerifying}}"></image>
    <text>{{isVerifying ? '核销中...' : '核销访客'}}</text>
  </button>
  <view class="verify-tip" wx:if="{{!verifySuccess}}">
    <text>确认访客身份后点击核销</text>
  </view>
  <view class="verify-success" wx:if="{{verifySuccess}}">
    <image src="/images/icons/check-circle-fill.svg" class="success-icon"></image>
    <text>核销成功</text>
  </view>
</view>
```

### 4. 样式设计 (`servicePackage/pages/visitor/credential/index.wxss`)

#### 核销按钮样式
- 渐变背景设计，突出核销功能
- 按钮状态变化（正常/核销中/已核销）
- 加载动画效果
- 成功状态提示

```css
.visitor-verify-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.visitor-verify-btn {
  background-color: #fff;
  color: #667eea;
  border-radius: 25px;
  transition: all 0.3s ease;
}
```

## 功能流程

### 扫码核销流程
1. **扫码识别**：物业人员点击右上角扫码按钮
2. **格式检测**：系统识别访客码格式 `visitor:id`
3. **页面跳转**：跳转到访客凭证页面，传递 `fromScan=true` 参数
4. **权限检测**：检查用户是否为已认证物业人员
5. **状态检测**：检查访客状态和有效期
6. **显示按钮**：满足条件时显示核销按钮
7. **执行核销**：点击核销按钮，调用API更新访客状态
8. **状态更新**：核销成功后更新UI显示

### 核销条件
- ✅ 从扫码页面进入 (`fromScan=true`)
- ✅ 用户是已认证的物业人员
- ✅ 访客状态为"待到访" (`wait_visit`)
- ✅ 访客码未过失效时间

### API调用
使用现有的 `visitorsApi.verifyVisitor(id)` 接口进行核销操作。

## 测试场景

### 场景1：正常核销流程
1. 物业人员已完成认证
2. 扫描有效的访客二维码
3. 访客状态为"待到访"且未过期
4. 显示核销按钮，点击核销成功

### 场景2：权限不足
1. 未认证用户或非物业人员扫码
2. 不显示核销按钮
3. 正常显示访客凭证信息

### 场景3：访客已过期
1. 物业人员扫描已过期的访客码
2. 不显示核销按钮
3. 显示过期状态

### 场景4：访客已核销
1. 扫描已核销的访客码
2. 不显示核销按钮
3. 显示"已到访"状态

## 技术特点

1. **权限控制**：严格的用户身份验证
2. **状态管理**：完整的访客状态流转
3. **用户体验**：直观的UI设计和交互反馈
4. **错误处理**：完善的异常处理机制
5. **实时更新**：核销后立即更新页面状态

## 安全考虑

1. **身份验证**：只有已认证物业人员才能核销
2. **状态检查**：防止重复核销和过期核销
3. **确认机制**：核销前弹窗确认，防止误操作
4. **API安全**：使用现有的安全API接口

这个功能完善了访客管理系统的闭环，提供了便捷的扫码核销体验。
