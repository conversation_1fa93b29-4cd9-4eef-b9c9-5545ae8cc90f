# 房屋管理功能测试指南

## 测试前准备

### 1. 确保接口可用
- 确认后端接口 `http://10.37.13.5:8080` 可访问
- 确认用户已登录并有有效token
- 确认已选择小区（`selectedCommunity` 存在）

### 2. 测试数据准备
- 确保测试小区有楼栋数据
- 确保楼栋下有房间数据
- 准备不同角色的测试场景

## 核心功能测试

### 1. 房屋列表页面测试

#### 测试路径
```
/profilePackage/pages/profile/house/house
```

#### 测试要点
- ✅ 页面正常加载，显示房屋列表
- ✅ 悬浮添加按钮在右下角显示
- ✅ 空状态时显示友好提示
- ✅ 房屋卡片显示完整信息（地址、角色、状态）
- ✅ 编辑和删除按钮正常工作

#### 预期结果
```
页面布局：
┌─────────────────────────┐
│ 我的房屋                │
├─────────────────────────┤
│ 房屋卡片1               │
│ 房屋卡片2               │
│ ...                     │
│                    [+]  │ ← 悬浮按钮
└─────────────────────────┘
```

### 2. 新增房屋功能测试

#### 测试路径
```
点击悬浮按钮 → /profilePackage/pages/profile/house/add/add?mode=add
```

#### 分步测试流程

##### 第一步：选择楼栋
- ✅ 页面显示"第一步：选择楼栋"
- ✅ 楼栋列表正常加载
- ✅ 点击楼栋项有选中效果
- ✅ 选中楼栋后显示第二步

##### 第二步：选择房间
- ✅ 页面显示"第二步：选择房间"
- ✅ 根据选中楼栋加载房间列表
- ✅ 房间以网格形式展示
- ✅ 点击房间项有选中效果
- ✅ 选中房间后显示第三步

##### 第三步：选择身份
- ✅ 页面显示"第三步：选择身份"
- ✅ 显示三种角色选项：业主、租户、家庭成员
- ✅ 点击角色有选中效果
- ✅ 选择角色后底部按钮变为可用状态

#### 提交测试
- ✅ 点击"添加房屋"按钮
- ✅ 显示加载状态
- ✅ 成功后显示成功弹窗
- ✅ 确认后返回房屋列表
- ✅ 新房屋出现在列表中

### 3. 编辑房屋功能测试

#### 测试路径
```
房屋列表 → 点击编辑按钮 → /profilePackage/pages/profile/house/add/add?mode=edit&houseId=xxx
```

#### 测试要点
- ✅ 页面标题显示"编辑房屋"
- ✅ 按钮文字显示"保存修改"
- ✅ 现有数据正确预填充
- ✅ 可以修改角色选择
- ✅ 保存后返回列表页面
- ✅ 修改内容正确更新

### 4. 删除房屋功能测试

#### 测试流程
- ✅ 点击删除按钮
- ✅ 显示确认弹窗
- ✅ 确认删除后房屋从列表移除
- ✅ 取消删除时保持原状

### 5. 设置默认房屋测试

#### 测试流程
- ✅ 点击"设为默认"按钮
- ✅ 该房屋显示"默认"标识
- ✅ 其他房屋的默认标识被移除

## 异常情况测试

### 1. 网络异常测试

#### 测试场景
- 断网状态下操作
- 接口返回错误状态码
- 接口响应超时

#### 预期行为
- ✅ 显示友好的错误提示
- ✅ 提供重试机制
- ✅ 新增失败时保存到本地存储
- ✅ 不会出现白屏或崩溃

### 2. 数据异常测试

#### 测试场景
- 楼栋列表为空
- 房间列表为空
- 接口返回格式错误

#### 预期行为
- ✅ 显示相应的空状态提示
- ✅ 提供重新加载按钮
- ✅ 错误信息清晰易懂

### 3. 用户操作异常测试

#### 测试场景
- 快速连续点击
- 页面切换时的状态保持
- 返回按钮的正确处理

#### 预期行为
- ✅ 防止重复提交
- ✅ 状态正确保持
- ✅ 页面跳转正常

## 接口对接验证

### 1. 请求参数验证

#### 新增房屋请求
```javascript
POST /users-api/v1/member/room
{
  "buildingId": 123,
  "roomId": 456,
  "role": "owner"
}
```

#### 更新房屋请求
```javascript
PUT /users-api/v1/member/room
{
  "id": 789,
  "role": "tenant"
}
```

#### 删除房屋请求
```javascript
DELETE /users-api/v1/member/room/789
```

### 2. 响应数据验证

#### 检查要点
- ✅ 响应状态码正确
- ✅ 数据格式符合预期
- ✅ 错误信息正确处理
- ✅ 分页数据正确解析

## 用户体验测试

### 1. 交互流畅性
- ✅ 页面切换动画自然
- ✅ 按钮点击反馈及时
- ✅ 加载状态提示清晰
- ✅ 选择状态变化明显

### 2. 视觉一致性
- ✅ 色彩搭配协调
- ✅ 字体大小合适
- ✅ 间距布局合理
- ✅ 图标使用统一

### 3. 操作便利性
- ✅ 分步流程清晰
- ✅ 错误提示有用
- ✅ 返回操作方便
- ✅ 悬浮按钮易用

## 性能测试

### 1. 加载性能
- ✅ 页面初始化时间 < 2秒
- ✅ 数据加载时间 < 3秒
- ✅ 页面切换时间 < 1秒

### 2. 内存使用
- ✅ 无明显内存泄漏
- ✅ 页面销毁时正确清理
- ✅ 图片资源合理使用

## 兼容性测试

### 1. 设备兼容性
- ✅ iPhone (iOS 12+)
- ✅ Android (Android 7+)
- ✅ 不同屏幕尺寸适配

### 2. 微信版本兼容性
- ✅ 微信 7.0+
- ✅ 小程序基础库 2.10+

## 测试检查清单

### 基础功能 ✅
- [ ] 房屋列表正常显示
- [ ] 悬浮按钮正确位置
- [ ] 新增房屋三步流程
- [ ] 编辑房屋数据预填充
- [ ] 删除房屋确认机制
- [ ] 设置默认房屋功能

### 异常处理 ✅
- [ ] 网络异常提示
- [ ] 数据为空处理
- [ ] 接口错误处理
- [ ] 用户操作异常

### 用户体验 ✅
- [ ] 交互动画流畅
- [ ] 视觉设计统一
- [ ] 操作逻辑清晰
- [ ] 错误提示友好

### 性能表现 ✅
- [ ] 加载速度合理
- [ ] 内存使用正常
- [ ] 设备兼容良好

## 测试完成标准

所有测试项目通过后，房屋管理功能即可发布使用。如发现问题，请记录具体的复现步骤和错误信息，便于快速定位和修复。
