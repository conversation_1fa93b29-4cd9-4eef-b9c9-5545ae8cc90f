
// 家人管理API
const request = require('../utils/request.js')

/**
 * 家人管理API模块
 */
const familyApi = {

  /**
   * 分页查询家属列表
   * @param {Object} params - 查询参数
   * @param {number} params.pageNum - 页码，默认1
   * @param {number} params.pageSize - 每页大小，默认10
   * @returns {Promise} 返回家属列表
   */
  getFamilyList: function (params) {
    const defaultParams = {
      pageNum: 1,
      pageSize: 10
    }

    return request.request('/users-api/v1/member/resident/children/page', 'GET',params, true)
  },

  /**
   * 新增家属
   * @param {Object} familyData - 家属信息
   * @param {string} familyData.residentName - 家属姓名
   * @param {string} familyData.certificateType - 证件类型，默认"idCard"
   * @param {string} familyData.idCardNumber - 身份证号码
   * @param {string} familyData.phone - 手机号码
   * @param {number} familyData.roomId - 房间ID
   * @param {string} familyData.residentType - 住户身份类型（tenant/tenant_family等）
   * @returns {Promise} 返回新增结果
   */
  addFamily: function (params) {

    return request.request('/users-api/v1/member/resident/children', 'POST', params, true)
  },

  /**
   * 编辑家属
   * @param {Object} familyData - 家属信息
   * @param {number} familyData.id - 家属ID
   * @param {number} familyData.familyResidentId - 家属居民ID
   * @param {string} familyData.residentName - 家属姓名
   * @param {string} familyData.certificateType - 证件类型
   * @param {string} familyData.idCardNumber - 身份证号码
   * @param {string} familyData.phone - 手机号码
   * @param {number} familyData.roomId - 房间ID
   * @param {string} familyData.residentType - 住户身份类型（tenant/tenant_family等）
   * @returns {Promise} 返回编辑结果
   */
  updateFamily: function (familyData) {
    const params = {
      id: familyData.id,
      familyResidentId: familyData.familyResidentId || familyData.residentId,
      residentName: familyData.residentName || familyData.name,
      certificateType: familyData.certificateType,
      idCardNumber: familyData.idCardNumber || familyData.idCard,
      phone: familyData.phone,
      roomId: familyData.roomId,
      residentType: familyData.residentType  // 添加住户身份类型字段
    }

    return request.request('/users-api/v1/member/resident/children', 'PUT', params, true)
  },

  /**
   * 删除家属
   * @param {number} id - 家属ID
   * @returns {Promise} 返回删除结果
   */
  deleteFamily: function (id) {
    return request.request(`/users-api/v1/member/resident/children?id=${id}`, 'DELETE', {}, true)
  },

  /**
   * 获取家属详情
   * @param {number} id - 家属ID
   * @returns {Promise} 返回家属详情
   */
  getFamilyDetail: function (id) {
    return request.request(`/users-api/v1/member/resident/children/${id}`, 'GET', {}, true)
  },

  /**
   * 审核租客
   * @param {Object} params - 审核参数
   * @param {string} params.id - 租客ID
   * @param {string} params.status - 审核状态 (normal: 通过, no_pass: 不通过)
   * @returns {Promise} 返回审核结果
   */
  examineResident: function (params) {
    return request.request('/users-api/v1/member/resident/children/examine', 'PUT', params, true)
  }
}

module.exports = familyApi


