# 添加居民页面最终修改说明

## 修改概述

根据最新要求对添加居民页面进行了进一步优化，主要包括房屋选择方式改进、人员标签弹窗选择和输入框样式调整。

## 主要修改内容

### 1. 房屋选择方式改进

#### 改为楼栋→房间选择方式
参考 `profilePackage/pages/profile/house/house` 页面的新增房屋弹窗：
- 使用 `houseApi.getBuildingsByCommunity` 获取楼栋列表
- 使用 `houseApi.getRoomsByBuilding` 获取房间列表
- 实现楼栋→房间的分步选择流程

#### 选择流程
1. **楼栋选择**: 显示楼栋网格，支持搜索
2. **房间选择**: 选择楼栋后显示该楼栋下的房间
3. **已选信息**: 显示已选择的楼栋和房间，支持重新选择
4. **搜索功能**: 支持楼栋和房间的关键词搜索

#### API接口
```javascript
// 获取楼栋列表
houseApi.getBuildingsByCommunity({
  pageNum: 1,
  pageSize: 500,
  communityId: communityId
})

// 获取房间列表
houseApi.getRoomsByBuilding(buildingId)
```

### 2. 人员标签弹窗选择

#### 改为弹窗多选方式
- 移除复选框组，改为点击弹窗选择
- 支持多选标签，选中状态有视觉反馈
- 显示已选标签的中文名称，用顿号分隔

#### 选择流程
1. 点击人员标签输入框
2. 弹出标签选择弹窗
3. 点击标签进行多选/取消选择
4. 确认选择后更新表单数据

### 3. 输入框样式调整

#### 背景色和边框调整
- **背景色**: 从 `#F2F2F7` 改为 `#FFFFFF` (白色)
- **边框**: 添加 `2rpx solid #F2F2F7` 边框
- **适用组件**: 
  - 文本输入框 (`.form-input`)
  - 文本域 (`.form-textarea`) 
  - 选择器 (`.picker-value`)
  - 房屋选择器 (`.house-selector`)
  - 标签选择器 (`.tag-selector`)

#### 错误状态
- 错误时边框颜色变为 `#FF3B30` (红色)

### 4. 字典数据修正

根据要求使用正确的字典：
- **人员标签**: `util.getDictByNameEn('vehicle_status')[0].children`
- **证件类型**: `util.getDictByNameEn('certificate_type')[0].children`
- **居民类型**: `util.getDictByNameEn('resident_type')[0].children`
- **性别**: `util.getDictByNameEn('gender')[0].children`

## 新增方法

### JavaScript方法
```javascript
// 房屋选择相关
showHouseSelector()          // 显示房屋选择弹窗
hideHouseSelector()          // 隐藏房屋选择弹窗
loadBuildings()              // 加载楼栋列表
loadRooms(buildingId)        // 加载房间列表
selectBuilding(e)            // 选择楼栋
selectRoom(e)                // 选择房间
confirmHouseSelection()      // 确认房屋选择
onSearchInput(e)             // 搜索输入
filterBuildings(keyword)     // 筛选楼栋
filterRooms(keyword)         // 筛选房间
backToBuilding()             // 返回楼栋选择

// 人员标签相关
showTagSelector()            // 显示标签选择弹窗
hideTagSelector()            // 隐藏标签选择弹窗
toggleTag(e)                 // 切换标签选择
confirmTagSelection()        // 确认标签选择
```

### 样式类
```css
/* 房屋选择弹窗 */
.add-house-modal            // 弹窗容器
.selection-info             // 已选信息区域
.selection-grid             // 选择网格
.selection-item             // 选择项
.search-container           // 搜索容器
.content-scroll             // 内容滚动区域

/* 人员标签弹窗 */
.tag-modal                  // 标签弹窗容器
.tag-list                   // 标签列表
.tag-item                   // 标签项
.tag-selector               // 标签选择器
```

## 数据结构

### 房屋选择数据
```javascript
{
  currentStep: 'building',        // 当前步骤
  selectedBuildingId: '',         // 选中楼栋ID
  selectedBuildingName: '',       // 选中楼栋名称
  selectedRoomId: '',             // 选中房间ID
  selectedRoomName: '',           // 选中房间名称
  selectedUnitNumber: '',         // 选中单元号
  buildings: [],                  // 楼栋列表
  filteredBuildings: [],          // 筛选后楼栋列表
  rooms: [],                      // 房间列表
  filteredRooms: []               // 筛选后房间列表
}
```

### 标签选择数据
```javascript
{
  selectedTags: [],               // 选中的标签对象数组
  showTagSelector: false          // 是否显示标签选择弹窗
}
```

## 注意事项

1. 房屋选择必须先选楼栋再选房间，不能跳步
2. 人员标签支持多选，可以选择0个或多个标签
3. 所有输入框都使用白色背景和灰色边框
4. 弹窗支持点击遮罩层关闭
5. 搜索功能实时筛选，不需要点击搜索按钮
