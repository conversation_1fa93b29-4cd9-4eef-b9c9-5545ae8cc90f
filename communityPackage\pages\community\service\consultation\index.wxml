<!--物业/服务咨询页面-->
<view class="container">

  <!-- 联系方式卡片 -->
  <info-card title="联系方式">
    <view class="contact-list">
      <view class="contact-item" wx:for="{{contactList}}" wx:key="id">
        <view class="contact-info">
          <view class="contact-name">{{item.name}}</view>
          <view class="contact-phone">{{item.phone}}</view>
        </view>
        <view class="contact-action" bindtap="callPhone" data-phone="{{item.phone}}">
          <view class="call-btn">
            <image class="call-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <view class="property-info">
      <view class="info-item">
        <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
        <text>{{propertyInfo.address}}</text>
      </view>
      <view class="info-item">
        <image class="info-icon" src="/images/icons/clock.svg" mode="aspectFit"></image>
        <text>{{propertyInfo.hours}}</text>
      </view>
    </view>
  </info-card>

  <!-- 常见问题FAQ -->
  <info-card title="常见问题">
    <view class="faq-list">
      <view class="faq-item" wx:for="{{faqList}}" wx:key="id">
        <view class="faq-header" bindtap="toggleFaq" data-id="{{item.id}}">
          <view class="faq-question">{{item.question}}</view>
          <view class="faq-arrow {{item.expanded ? 'expanded' : ''}}">
            <image src="/images/icons/arrow-down.svg" mode="aspectFit"></image>
          </view>
        </view>
        <view class="faq-answer {{item.expanded ? 'expanded' : ''}}">
          {{item.answer}}
        </view>
      </view>
    </view>
  </info-card>

  <!-- 在线客服和留言 -->
  <info-card title="在线服务">
    <view class="service-buttons">
      <button class="service-btn" open-type="contact">
        <image class="btn-icon" src="/images/icons/customer-service.svg" mode="aspectFit"></image>
        <text>在线客服</text>
      </button>

      <button class="service-btn" bindtap="showMessageForm">
        <image class="btn-icon" src="/images/icons/message.svg" mode="aspectFit"></image>
        <text>留言反馈</text>
      </button>
    </view>
  </info-card>

  <!-- 留言表单弹窗 -->
  <view class="message-modal {{showMessageModal ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideMessageForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">留言反馈</view>
        <view class="modal-close" bindtap="hideMessageForm">
          <image src="/images/icons/close.svg" mode="aspectFit"></image>
        </view>
      </view>

      <view class="modal-body">
        <view class="form-item">
          <view class="form-label">留言类型</view>
          <picker bindchange="onTypeChange" value="{{messageTypeIndex}}" range="{{messageTypes}}">
            <view class="form-picker">
              <text>{{messageTypes[messageTypeIndex]}}</text>
              <image class="picker-arrow" src="/images/icons/arrow-down.svg" mode="aspectFit"></image>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">留言内容</view>
          <textarea class="form-textarea" placeholder="请输入您的留言内容" bindinput="onContentInput" value="{{messageContent}}"></textarea>
        </view>

        <view class="form-item">
          <view class="form-label">联系方式</view>
          <input class="form-input" type="text" placeholder="请输入您的联系方式" bindinput="onContactInput" value="{{messageContact}}"/>
          <view class="form-tip">您的联系方式仅用于物业回复，不会用于其他用途</view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-btn" bindtap="hideMessageForm">取消</button>
        <button class="submit-btn" bindtap="submitMessage" disabled="{{!canSubmit}}">提交</button>
      </view>
    </view>
  </view>
</view>
