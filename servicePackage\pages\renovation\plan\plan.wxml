<!--pages/renovation/plan/plan.wxml-->
<view class="container">
  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="progress-bar-container">
      <view class="progress-bar-active" style="right: calc(50% + 40px);"></view>
    </view>
    <view class="step step-complete">
      <view class="step-circle">
        <icon type="success" size="12" color="#fff"></icon>
      </view>
      <view class="step-label">基本信息</view>
    </view>
    <view class="step step-active">
      <view class="step-circle">2</view>
      <view class="step-label">施工方案</view>
    </view>
    <view class="step">
      <view class="step-circle">3</view>
      <view class="step-label">材料上传</view>
    </view>
    <view class="step">
      <view class="step-circle">4</view>
      <view class="step-label">承诺签署</view>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <view class="form-section-title">施工范围</view>
    <view class="form-group">
      <view class="form-label">
        <text>施工区域</text>
        <text class="required">*</text>
      </view>
      <view class="option-cards">
        <view class="option-card {{areaSelected.livingRoom ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="livingRoom"
              data-timestamp="{{lastUpdated}}">
          <text>客厅</text>
        </view>
        <view class="option-card {{areaSelected.bedroom ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="bedroom"
              data-timestamp="{{lastUpdated}}">
          <text>卧室</text>
        </view>
        <view class="option-card {{areaSelected.kitchen ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="kitchen"
              data-timestamp="{{lastUpdated}}">
          <text>厨房</text>
        </view>
        <view class="option-card {{areaSelected.bathroom ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="bathroom"
              data-timestamp="{{lastUpdated}}">
          <text>卫生间</text>
        </view>
        <view class="option-card {{areaSelected.balcony ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="balcony"
              data-timestamp="{{lastUpdated}}">
          <text>阳台</text>
        </view>
        <view class="option-card {{areaSelected.other ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="areas"
              data-value="other"
              data-timestamp="{{lastUpdated}}">
          <text>其他</text>
        </view>
      </view>
    </view>
    <view class="form-group" wx:if="{{areaSelected.other}}">
      <view class="form-label">
        <text>其他区域说明</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="text" placeholder="请输入其他区域说明" value="{{formData.otherAreaDesc}}" bindinput="onOtherAreaInput" />
      </view>
    </view>
  </view>

  <view class="form-section">
    <view class="form-section-title">施工内容</view>
    <view class="form-group">
      <view class="form-label">
        <text>施工项目</text>
        <text class="required">*</text>
      </view>
      <view class="option-cards">
        <view class="option-card {{itemSelected.wall ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="wall"
              data-timestamp="{{lastUpdated}}">
          <text>墙面处理</text>
        </view>
        <view class="option-card {{itemSelected.floor ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="floor"
              data-timestamp="{{lastUpdated}}">
          <text>地面处理</text>
        </view>
        <view class="option-card {{itemSelected.ceiling ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="ceiling"
              data-timestamp="{{lastUpdated}}">
          <text>天花板处理</text>
        </view>
        <view class="option-card {{itemSelected.water ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="water"
              data-timestamp="{{lastUpdated}}">
          <text>水路改造</text>
        </view>
        <view class="option-card {{itemSelected.electric ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="electric"
              data-timestamp="{{lastUpdated}}">
          <text>电路改造</text>
        </view>
        <view class="option-card {{itemSelected.furniture ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="furniture"
              data-timestamp="{{lastUpdated}}">
          <text>家具安装</text>
        </view>
        <view class="option-card {{itemSelected.other ? 'selected' : ''}}"
              bindtap="toggleOption"
              data-type="items"
              data-value="other"
              data-timestamp="{{lastUpdated}}">
          <text>其他</text>
        </view>
      </view>
    </view>
    <view class="form-group" wx:if="{{itemSelected.other}}">
      <view class="form-label">
        <text>其他项目说明</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="text" placeholder="请输入其他项目说明" value="{{formData.otherItemDesc}}" bindinput="onOtherItemInput" />
      </view>
    </view>
  </view>

  <view class="form-section">
    <view class="form-section-title">施工说明</view>
    <view class="form-group">
      <view class="form-label">
        <text>施工详情</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <textarea class="form-textarea" placeholder="请详细描述施工内容、方法和注意事项" value="{{formData.description}}" bindinput="onDescriptionInput" maxlength="500" />
        <view class="textarea-counter">{{formData.description.length}}/500</view>
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>是否涉及承重墙改造</text>
        <text class="required">*</text>
      </view>
      <view class="radio-cards">
        <view class="radio-card {{formData.hasBearingWall === false ? 'selected' : ''}}"
              bindtap="toggleBearingWall"
              data-value="{{false}}">
          <text>否</text>
        </view>
        <view class="radio-card {{formData.hasBearingWall === true ? 'selected' : ''}}"
              bindtap="toggleBearingWall"
              data-value="{{true}}">
          <text>是</text>
        </view>
      </view>
    </view>
    <view class="form-group" wx:if="{{formData.hasBearingWall}}">
      <view class="form-label">
        <text>承重墙改造说明</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <textarea class="form-textarea" placeholder="请详细说明承重墙改造方案，并确保有专业的结构评估报告" value="{{formData.bearingWallDesc}}" bindinput="onBearingWallDescInput" maxlength="300" />
        <view class="textarea-counter">{{formData.bearingWallDesc.length}}/300</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="back-button" bindtap="goToPrevStep">上一步</button>
    <button class="next-button" bindtap="goToNextStep" disabled="{{!isFormValid}}">下一步</button>
  </view>
</view>