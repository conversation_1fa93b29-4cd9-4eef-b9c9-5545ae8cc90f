// 订单二维码页面
const util = require('@/utils/util.js');
const app = getApp();
const OrderService = require('@/services/order');
// 引入微信小程序二维码库
const QR = require('@/utils/weapp-qrcode.js');

Page({
  data: {
    darkMode: false,
    orderNo: '',
    qrContent: '',
    orderInfo: null,
    loadFailed: false,
    showTips: false
  },

  onLoad: function(options) {
    // 检查暗黑模式
    this.setData({
      darkMode: app.globalData.darkMode || false
    });

    if (options.orderNo && options.qrContent) {
      const qrContent = decodeURIComponent(options.qrContent);
      
      this.setData({
        orderNo: options.orderNo,
        qrContent: qrContent
      });

      // 生成二维码
      this.generateQRCode(qrContent);
      
      // 加载订单信息
      this.loadOrderInfo(options.orderNo);
    } else {
      this.setData({
        loadFailed: true
      });
      wx.showToast({
        title: '缺少订单信息',
        icon: 'none'
      });
    }
  },
  
  // 生成二维码
  generateQRCode: function(content) {
    try {
      console.log('开始生成二维码:', content);
      
      // 使用 setTimeout 确保画布已经准备好
      setTimeout(() => {
        new QR('qrCanvas', {
          // usingIn: this,
          text: content,
          width: 180,
          height: 180,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QR.CorrectLevel.H
        });
        
        console.log('二维码生成成功');
      }, 100);
      
    } catch (error) {
      console.error('二维码生成失败:', error);
      this.setData({
        loadFailed: true
      });
    }
  },

  // 加载订单信息
  loadOrderInfo: function(orderNo) {
    wx.showLoading({
      title: '加载中...'
    });

    OrderService.getOrderByNo(orderNo)
      .then(order => {
        console.log('订单信息加载成功:', order);
        this.setData({
          orderInfo: order
        });
        wx.hideLoading();
      })
      .catch(error => {
        console.error('订单信息加载失败:', error);
        this.setData({
          loadFailed: true
        });
        wx.hideLoading();
        wx.showToast({
          title: '订单信息加载失败',
          icon: 'none'
        });
      });
  },

  // 关闭页面
  closePage: function() {
    wx.navigateBack();
  },

  // 分享订单
  onShareAppMessage: function() {
    const { orderNo } = this.data;
    return {
      title: '交易凭证',
      path: `/pages/goods/order/qrcode?orderNo=${orderNo}`
    };
  },

  // 复制订单号
  copyOrderNo: function() {
    const { orderNo } = this.data;
    wx.setClipboardData({
      data: orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 查看交易提示
  showTradeTips: function() {
    this.setData({
      showTips: true
    });
  },

  // 关闭提示
  closeTips: function() {
    this.setData({
      showTips: false
    });
  },

  // 联系卖家
  contactSeller: function() {
    const { orderInfo } = this.data;
    if (!orderInfo || !orderInfo.sellerInfo) {
      wx.showToast({
        title: '卖家信息不可用',
        icon: 'none'
      });
      return;
    }

    // 根据卖家设置的联系方式，提供相应的联系选项
    wx.showActionSheet({
      itemList: ['发送站内消息', '复制微信号', '拨打电话'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 发送站内消息 - 跳转到聊天页面
            wx.navigateTo({
              url: `/pages/message/chat/chat?userId=${orderInfo.sellerInfo.id}&goodsId=${orderInfo.goodsId}`
            });
            break;
          case 1:
            // 复制微信号
            if (orderInfo.sellerInfo.wechat) {
              wx.setClipboardData({
                data: orderInfo.sellerInfo.wechat,
                success: () => {
                  wx.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '卖家未提供微信号',
                icon: 'none'
              });
            }
            break;
          case 2:
            // 拨打电话
            if (orderInfo.sellerInfo.phone) {
              wx.makePhoneCall({
                phoneNumber: orderInfo.sellerInfo.phone,
                fail: (err) => {
                  console.error('拨打电话失败:', err);
                  wx.showToast({
                    title: '拨打电话失败',
                    icon: 'none'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '卖家未提供电话',
                icon: 'none'
              });
            }
            break;
        }
      }
    });
  }
});
