网络 /WebSocket /wx.connectSocket
SocketTask wx.connectSocket(Object object)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

以 Promise 风格 调用：不支持

小程序插件：支持，需要小程序基础库版本不低于 1.9.6

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
创建一个 WebSocket 连接。使用前请注意阅读相关说明。

参数
Object object
属性	类型	默认值	必填	说明	最低版本
url	string		是	开发者服务器 wss 接口地址	
header	Object		否	HTTP Header，Header 中不能设置 Referer	
protocols	Array.<string>		否	子协议数组	1.4.0
tcpNoDelay	boolean	false	否	建立 TCP 连接的时候的 TCP_NODELAY 设置	2.4.0
perMessageDeflate	boolean	false	否	是否开启压缩扩展	2.8.0
timeout	number		否	超时时间，单位为毫秒	2.10.0
forceCellularNetwork	boolean	false	否	强制使用蜂窝网络发送请求	2.29.0
success	function		否	接口调用成功的回调函数	
fail	function		否	接口调用失败的回调函数	
complete	function		否	接口调用结束的回调函数（调用成功、失败都会执行）	
返回值
SocketTask
基础库 1.7.0 开始支持，低版本需做兼容处理。

WebSocket 任务

并发数
1.7.0 及以上版本，最多可以同时存在 5 个 WebSocket 连接。
1.7.0 以下版本，一个小程序同时只能有一个 WebSocket 连接，如果当前已存在一个 WebSocket 连接，会自动关闭该连接，并重新创建一个 WebSocket 连接。
示例代码
wx.connectSocket({
  url: 'wss://example.qq.com',
  header:{
    'content-type': 'application/json'
  }
})








网络 /WebSocket /wx.onSocketOpen
wx.onSocketOpen(function listener)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
监听 WebSocket 连接打开事件。

参数
function listener
WebSocket 连接打开事件的监听函数

参数
Object res
属性	类型	说明	最低版本
header	object	连接成功的 HTTP 响应 Header	2.0.0


网络 /WebSocket /wx.onSocketClose
wx.onSocketClose(function listener)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
监听 WebSocket 连接关闭事件。

参数
function listener
WebSocket 连接关闭事件的监听函数

参数
Object res
属性	类型	说明
code	number	一个数字值表示关闭连接的状态号，表示连接被关闭的原因。
reason	string	一个可读的字符串，表示连接被关闭的原因。



网络 /WebSocket /wx.closeSocket
wx.closeSocket(Object object)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

以 Promise 风格 调用：支持

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
关闭 WebSocket 连接。

参数
Object object
属性	类型	默认值	必填	说明
code	number	1000（表示正常关闭连接）	否	一个数字值表示关闭连接的状态号，表示连接被关闭的原因。
reason	string		否	一个可读的字符串，表示连接被关闭的原因。这个字符串必须是不长于 123 字节的 UTF-8 文本（不是字符）。
success	function		否	接口调用成功的回调函数
fail	function		否	接口调用失败的回调函数
complete	function		否	接口调用结束的回调函数（调用成功、失败都会执行）
示例代码
wx.connectSocket({
  url: 'test.php'
})

//注意这里有时序问题，
//如果 wx.connectSocket 还没回调 wx.onSocketOpen，而先调用 wx.closeSocket，那么就做不到关闭 WebSocket 的目的。
//必须在 WebSocket 打开期间调用 wx.closeSocket 才能关闭。
wx.onSocketOpen(function() {
  wx.closeSocket()
})

wx.onSocketClose(function(res) {
  console.log('WebSocket 已关闭！')
})

网络 /WebSocket /wx.sendSocketMessage
wx.sendSocketMessage(Object object)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

以 Promise 风格 调用：支持

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
通过 WebSocket 连接发送数据。需要先 wx.connectSocket，并在 wx.onSocketOpen 回调之后才能发送。

参数
Object object
属性	类型	默认值	必填	说明
data	string/ArrayBuffer		是	需要发送的内容
success	function		否	接口调用成功的回调函数
fail	function		否	接口调用失败的回调函数
complete	function		否	接口调用结束的回调函数（调用成功、失败都会执行）
示例代码
let socketOpen = false
let socketMsgQueue = []
wx.connectSocket({
  url: 'test.php'
})

wx.onSocketOpen(function(res) {
  socketOpen = true
  for (let i = 0; i < socketMsgQueue.length; i++){
    sendSocketMessage(socketMsgQueue[i])
  }
  socketMsgQueue = []
})

function sendSocketMessage(msg) {
  if (socketOpen) {
    wx.sendSocketMessage({
      data:msg
    })
  } else {
    socketMsgQueue.push(msg)
  }
}

网络 /WebSocket /wx.onSocketMessage
wx.onSocketMessage(function listener)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
监听 WebSocket 接收到服务器的消息事件。

参数
function listener
WebSocket 接收到服务器的消息事件的监听函数

参数
Object res
属性	类型	说明
data	string/ArrayBuffer	服务器返回的消息

网络 /WebSocket /wx.onSocketError
wx.onSocketError(function listener)
推荐使用 SocketTask 的方式去管理 webSocket 链接，每一条链路的生命周期都更加可控，同时存在多个 webSocket 的链接的情况下使用 wx 前缀的方法可能会带来一些和预期不一致的情况。

小程序插件：不支持

微信 Windows 版：支持

微信 Mac 版：支持

微信 鸿蒙 OS 版：支持

相关文档: 网络使用说明、局域网通信

功能描述
监听 WebSocket 错误事件。

参数
function listener
WebSocket 错误事件的监听函数

参数
Object res
属性	类型	说明
errMsg	string	错误信息





WebSocket长连接在小程序中的实践：消息推送与断线重连机制设计
原创
于 2025-06-23 09:56:42 发布
·
430 阅读
·

4
·
 8
·
CC 4.0 BY-SA版权
文章标签：
#websocket
#小程序
#网络协议


微信小程序
专栏收录该内容
13 篇文章
订阅专栏
一、引言：为什么需要WebSocket长连接？
传统方案的痛点：HTTP轮询的低效性（高延迟、高资源消耗）
小程序场景需求：实时消息推送（如IM、直播弹幕、IoT设备状态同步）
技术选型对比：WebSocket vs. Server-Sent Events（SSE）在小程序端的适用性
二、WebSocket技术原理与小程序限制
1. WebSocket核心机制
握手过程：HTTP Upgrade 头升级协议
数据帧格式：二进制与文本消息的传输差异
心跳机制：Ping/Pong 帧维持长连接活性
2. 小程序端的特殊限制
后台存活时间：iOS/Android平台差异（如微信小程序后台最多保持5分钟连接）
域名白名单：需配置request合法域名与ws:///wss://协议支持
连接数限制：单小程序实例最多同时保持5个WebSocket连接
三、核心实现：从连接建立到消息推送
1. 环境准备
// app.js 全局配置
App({
  globalData: {
    socketTask: null,
    reconnectCount: 0,
    MAX_RECONNECT: 5
  }
})
AI写代码
javascript
运行
 

2. 连接建立与事件监听
// pages/chat/chat.js
Page({
  onLoad() {
    const app = getApp();
    app.globalData.socketTask = wx.connectSocket({
      url: 'wss://api.example.com/ws',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      protocols: ['chat']
    });
 
    // 监听连接成功
    app.globalData.socketTask.onOpen(() => {
      console.log('WebSocket连接已建立');
      this.sendAuthMessage(); // 发送认证消息
    });
 
    // 监听消息接收
    app.globalData.socketTask.onMessage((res) => {
      const data = JSON.parse(res.data);
      this.handleMessage(data);
    });
  }
})
AI写代码
javascript
运行

 

3. 消息推送与序列化
// 发送消息（含重试机制）
function sendMessage(data, retry = 3) {
  const app = getApp();
  if (app.globalData.socketTask.readyState === WebSocket.OPEN) {
    app.globalData.socketTask.send({
      data: JSON.stringify(data)
    });
  } else if (retry > 0) {
    setTimeout(() => sendMessage(data, retry - 1), 1000);
  }
}
AI写代码
javascript
运行

 

四、断线重连机制设计
1. 异常场景分类
主动关闭：wx.closeSocket() 调用
被动断开：网络切换、服务端主动断开、进程被杀
协议错误：非法数据帧、鉴权失败
2. 智能重连策略
// 全局重连管理器
function handleReconnect() {
  const app = getApp();
  if (app.globalData.reconnectCount >= app.globalData.MAX_RECONNECT) {
    showErrorToast('连接失败，请检查网络');
    return;
  }
 
  setTimeout(() => {
    app.globalData.reconnectCount++;
    wx.showLoading({ title: `重连中 (${app.globalData.reconnectCount}/5)` });
    establishConnection(); // 重新建立连接
  }, Math.min(1000 * 2 ** app.globalData.reconnectCount, 30000)); // 指数退避算法
}
 
// 监听连接关闭
app.globalData.socketTask.onClose(() => {
  wx.hideLoading();
  handleReconnect();
});
 
// 监听错误事件
app.globalData.socketTask.onError((err) => {
  console.error('WebSocket错误:', err);
  handleReconnect();
});
AI写代码
javascript
运行

 

3. 状态同步与界面更新
连接状态枚举：{ CONNECTING: 0, OPEN: 1, CLOSING: 2, CLOSED: 3 }
界面层绑定：通过setData更新连接状态UI（如显示重连提示）
五、关键优化策略
1. 心跳保活
// 发送心跳包（客户端）
setInterval(() => {
  if (socketTask.readyState === WebSocket.OPEN) {
    socketTask.send({
      data: JSON.stringify({ type: 'heartbeat' })
    });
  }
}, 30000);
 
// 服务端响应心跳（示例Node.js代码）
wss.on('connection', (ws) => {
  const heartbeatInterval = setInterval(() => {
    if (ws.isAlive === false) return ws.terminate();
    ws.isAlive = false;
    ws.ping();
  }, 30000);
 
  ws.on('pong', () => { ws.isAlive = true; });
});
AI写代码
javascript
运行

 

2. 消息队列缓冲
未连接时缓存消息到Storage，连接恢复后批量发送
消息去重：通过messageId或时间戳避免重复处理
3. 性能优化
分包加载：将WebSocket逻辑拆分到独立分包
代码压缩：使用terser等工具减小JS体积
内存管理：及时清理onUnload生命周期中的引用
六、注意事项与常见问题
安全风险
必须使用wss://协议防止中间人劫持
消息体需做签名校验（如HMAC-SHA256）
兼容性处理
基础库版本要求：wx.connectSocket需要基础库1.7.0+
降级方案：长轮询作为WebSocket的备用方案
日志监控
记录关键事件：connect/close/error时间戳、错误码
上报策略：重要错误立即上报，普通日志分批次上传
七、案例分析：实时聊天室实现
sequenceDiagram
  participant 用户A
  participant 小程序前端
  participant WebSocket服务
  participant 用户B
 
  用户A->>小程序前端: 发送消息
  小程序前端->>WebSocket服务: 发送消息体（含用户ID）
  WebSocket服务->>用户B: 广播消息（通过WebSocket帧）
  用户B-->>小程序前端: 触发onMessage事件
  小程序前端->>用户B: 渲染消息到界面
AI写代码
bash

 

八、总结与展望
关键点回顾：连接管理、断线重连、性能优化构成核心三角
未来方向：
结合WebTransport协议实现更低延迟
使用AI预测网络质量，动态调整心跳间隔
探索WebSocket over QUIC在弱网环境下的表现
————————————————
版权声明：本文为CSDN博主「即可皕」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/m0_75042480/article/details/148839899




微信小程序与WebSocket实时通信
原创
于 2025-02-16 20:42:29 发布
·
1.7k 阅读
·

5
·
 12
·
CC 4.0 BY-SA版权
文章标签：
#微信小程序
#websocket
#小程序

所属社区：2048 AI社区


小程序开发
同时被 2 个专栏收录
15 篇文章
订阅专栏

微信小程序
15 篇文章
订阅专栏
微信小程序中的 WebSocket 实时通信功能为开发者提供了强大的实时交互能力，适用于即时通讯、实时数据更新等场景。以下是实现 WebSocket 实时通信的完整指南，包括前端和后端的实现步骤。

一、微信小程序端的 WebSocket 实现
1. 初始化 WebSocket 连接
在小程序中，使用 wx.connectSocket 方法建立 WebSocket 连接。

wx.connectSocket({
  url: 'wss://example.com/websocket', // WebSocket 服务端地址
  header: {
    'content-type': 'application/json'
  },
  protocols: ['protocol1'], // 可选的协议
  success(res) {
    console.log('WebSocket连接创建成功', res);
  },
  fail(res) {
    console.error('WebSocket连接创建失败，请检查！', res);
  }
});
AI写代码

2. 设置 WebSocket 回调函数
在小程序中，需要设置以下回调函数来处理 WebSocket 的不同状态：

连接成功回调：wx.onSocketOpen

接收消息回调：wx.onSocketMessage

连接关闭回调：wx.onSocketClose

连接错误回调：wx.onSocketError

wx.onSocketOpen((res) => {
  console.log('WebSocket连接已打开', res);
});
 
wx.onSocketMessage((res) => {
  console.log('收到服务端消息：', res.data);
  // 处理接收到的消息
});
 
wx.onSocketClose((res) => {
  console.log('WebSocket连接已关闭', res);
});
 
wx.onSocketError((res) => {
  console.error('WebSocket连接发生错误', res);
});
AI写代码

3. 发送消息到服务端
使用 wx.sendSocketMessage 方法向服务端发送消息。

wx.sendSocketMessage({
  data: JSON.stringify({ type: 'chat', payload: 'Hello Server!' }),
  success(res) {
    console.log('消息发送成功', res);
  },
  fail(res) {
    console.error('消息发送失败', res);
  }
});
AI写代码
4. 关闭 WebSocket 连接
当不再需要 WebSocket 连接时，可以使用 wx.closeSocket 方法关闭连接。

wx.closeSocket({
  success(res) {
    console.log('WebSocket连接已关闭', res);
  }
});
AI写代码
二、服务端的 WebSocket 实现
1. 使用 Node.js 搭建 WebSocket 服务端
以下是一个基于 Node.js 的简单 WebSocket 服务端实现：

const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });
 
wss.on('connection', (ws) => {
  console.log('客户端连接成功');
 
  ws.on('message', (message) => {
    console.log('收到客户端消息：', message);
    // 广播消息给所有客户端
    wss.clients.forEach((client) => {
      if (client !== ws && client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  });
 
  ws.on('close', () => {
    console.log('客户端连接关闭');
  });
});
AI写代码

2. 处理客户端连接和消息转发
服务端需要维护客户端连接列表，并实现消息的接收和转发逻辑。

三、性能优化与稳定性增强
1. 心跳机制
为了保持连接的活跃性，可以实现心跳机制，定期向服务端发送心跳消息。

// 小程序端
setInterval(() => {
  wx.sendSocketMessage({ data: JSON.stringify({ type: 'ping' }) });
}, 30000); // 每30秒发送一次心跳
AI写代码
服务端收到心跳消息后，可以返回响应，确保连接正常。

2. 自动重连
在连接关闭或错误时，实现自动重连逻辑。

wx.onSocketClose(() => {
  console.log('连接关闭，尝试重新连接');
  wx.connectSocket({ url: 'wss://example.com/websocket' });
});
AI写代码
四、安全性与数据处理
1. 数据加密
在传输敏感数据时，建议对数据进行加密处理。

2. 验证客户端身份
服务端可以通过验证 appid 或 token 来确保连接的客户端是合法的。

五、实际案例：聊天室应用
以下是一个简单的聊天室应用实现：

小程序端：实现用户界面，发送和接收消息。

服务端：接收消息并广播给其他客户端。

通过上述步骤，开发者可以在微信小程序中实现高效的 WebSocket 实时通信功能，提升应用的交互性能和用户体验。
————————————————
版权声明：本文为CSDN博主「乐动心弦」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/w1149972666/article/details/145669801