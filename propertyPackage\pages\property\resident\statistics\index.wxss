/* 居民统计页面样式 */
.resident-stats-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 40rpx;
}

/* 标题栏 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 32rpx;
  background: linear-gradient(to right, #FF8C00, #FFA940);
  color: #FFFFFF;
}

.stats-title {
  font-size: 36rpx;
  font-weight: 600;
}

.stats-actions {
  display: flex;
}

.stats-action {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-left: 24rpx;
}

/* 时间范围选择器 */
.time-range-selector {
  display: flex;
  background: #FFFFFF;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.range-item {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.range-item.active {
  background-color: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
  font-weight: 500;
}

/* 标签页导航 */
.tab-header {
  display: flex;
  background: #FFFFFF;
  padding: 0 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  position: relative;
  padding: 24rpx 0;
  margin-right: 48rpx;
  font-size: 28rpx;
  color: #666666;
}

.tab-item.active {
  color: #FF8C00;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #FF8C00;
  border-radius: 2rpx;
}

/* 加载中 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.2);
  border-top: 4rpx solid #FF8C00;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 标签页内容 */
.tab-content {
  padding: 24rpx 0;
}

/* 统计卡片样式 */
.stats-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 32rpx 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

.card-body {
  padding: 24rpx 32rpx;
}

/* 摘要信息 */
.summary-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
}

.summary-value {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #FF8C00;
  margin-bottom: 8rpx;
}

.summary-label {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

/* 图表容器 */
.chart-container {
  margin-top: 24rpx;
  padding: 16rpx 0;
}

/* 模拟图表样式 */
.mock-chart {
  width: 100%;
  height: 400rpx;
  position: relative;
}

/* 饼图样式 */
.pie-chart {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
  position: relative;
}

.pie-segments {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: 50% 50%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
}

.pie-center {
  position: absolute;
  width: 50%;
  height: 50%;
  top: 25%;
  left: 25%;
  background-color: white;
  border-radius: 50%;
}

/* 环形图样式 */
.donut-chart {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
  position: relative;
}

.donut-segments {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.donut-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: 50% 50%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
}

.donut-center {
  position: absolute;
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  background-color: white;
  border-radius: 50%;
}

/* 图表图例 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 32rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 16rpx 16rpx 0;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.legend-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #999999;
}

/* 趋势图样式 */
.trend-chart {
  display: flex;
  height: 400rpx;
  padding: 20rpx 0;
}

.chart-y-axis {
  width: 60rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 16rpx;
}

.chart-y-axis text {
  font-size: 20rpx;
  color: #999999;
}

.chart-content {
  flex: 1;
  height: 100%;
  position: relative;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: calc(100% - 40rpx);
  padding-bottom: 10rpx;
}

.chart-bar {
  width: 30rpx;
  background-color: #FF8C00;
  border-radius: 30rpx 30rpx 0 0;
  transition: height 0.3s ease;
}

.chart-x-axis {
  height: 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.x-label {
  font-size: 20rpx;
  color: #999999;
  text-align: center;
  width: 60rpx;
}

/* 性别分布图表 */
.gender-chart {
  padding: 20rpx 0;
}

.gender-bar-container {
  margin-bottom: 24rpx;
}

.gender-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.gender-bar-wrapper {
  height: 40rpx;
  background-color: #F5F5F5;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.gender-bar {
  height: 100%;
  border-radius: 20rpx;
  transition: width 0.3s ease;
}

.gender-bar.male {
  background-color: #007AFF;
}

.gender-bar.female {
  background-color: #FF2D55;
}

.gender-value {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22rpx;
  color: #FFFFFF;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 年龄分布图表 */
.age-chart {
  padding: 20rpx 0;
}

.age-item {
  margin-bottom: 24rpx;
}

.age-bar-container {
  display: flex;
  align-items: center;
}

.age-label {
  width: 120rpx;
  font-size: 24rpx;
  color: #666666;
}

.age-bar-wrapper {
  flex: 1;
  height: 40rpx;
  background-color: #F5F5F5;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.age-bar {
  height: 100%;
  border-radius: 20rpx;
  transition: width 0.3s ease;
}

.age-value {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22rpx;
  color: #FFFFFF;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* EChart 趋势图表样式 */
.container {
  width: 100%;
  min-height: auto;
}
.echarts {
  width: 100%;
  height: 500rpx;
}
.ec-canvas {
  width: 100%;
  height: 100%;
}