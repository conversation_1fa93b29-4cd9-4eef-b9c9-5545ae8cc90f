/* 访客功能公共样式 */
.visitor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 状态栏背景 */
.visitor-status-bar-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100px; /* 足够高以覆盖状态栏和导航栏 */
  background: linear-gradient(to right, #4f46e5, #6366f1);
  z-index: 5;
}

/* 导航栏样式 */
.visitor-nav-bar {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  background-color: transparent; /* 改为透明背景 */
  z-index: 10;
  color: #fff; /* 文字改为白色 */
  margin-top: 20px; /* 为状态栏预留空间 */
}

.visitor-nav-back {
  display: flex;
  align-items: center;
}

.visitor-back-icon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
  /* 修改图标颜色为白色 */
  filter: brightness(0) invert(1);
}

.visitor-nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  color: #fff; /* 确保标题为白色 */
}

.visitor-nav-action {
  margin-left: auto;
}

.visitor-action-icon {
  width: 24px;
  height: 24px;
  /* 修改图标颜色为白色 */
  filter: brightness(0) invert(1);
}

/* 表单样式 */
.visitor-form-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-form-item {
  margin-bottom: 20px;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.visitor-required {
  color: #ff3b30;
  margin-left: 4px;
}

.visitor-form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background-color: #fff;
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-form-textarea {
  width: 100%;
  height: 80px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background-color: #fff;
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-error-tip {
  font-size: 12px;
  color: #ff3b30;
  margin-top: 4px;
}

/* 快捷操作区样式 */
.visitor-quick-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  width: 100%;
  box-sizing: border-box;
}

.visitor-quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 48%;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.visitor-quick-action:active {
  background-color: #f5f7fa;
  transform: scale(0.98);
}

.visitor-quick-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
  color: #4f46e5;
}

/* 常用访客列表样式 */
.visitor-frequent-visitors {
  max-height: 400px;
  overflow-y: auto;
}

.visitor-frequent-visitor-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.visitor-frequent-visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #4f46e5;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}

.visitor-frequent-visitor-info {
  flex: 1;
}

.visitor-frequent-visitor-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.visitor-frequent-visitor-phone {
  font-size: 14px;
  color: #666;
}

/* 访客类型选择器样式 */
.visitor-type-selector {
  display: flex;
  margin-bottom: 24px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  background-color: #f0f0f0;
  transition: background-color 0.3s;
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-type-option.active {
  background-color: #4f46e5;
  color: #fff;
}

.visitor-type-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

/* 特殊输入框样式 */
.visitor-phone-input-container {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-phone-input-container .visitor-form-input {
  flex: 1;
  border: none;
  width: 0; /* 让flex: 1生效 */
  min-width: 0; /* 防止内容溢出 */
}

.visitor-paste-btn {
  padding: 0 12px;
  height: 44px;
  line-height: 44px;
  color: #4f46e5;
  font-size: 14px;
  white-space: nowrap; /* 防止文字换行 */
}

.visitor-car-number-input-container {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-car-number-input-container .visitor-form-input {
  flex: 1;
  border: none;
  width: 0; /* 让flex: 1生效 */
  min-width: 0; /* 防止内容溢出 */
}

.visitor-history-btn {
  padding: 0 12px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visitor-history-icon {
  width: 20px;
  height: 20px;
}

/* 选择器样式 */
.visitor-datetime-picker, .visitor-duration-picker, .visitor-purpose-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  width: 100%; /* 确保宽度为100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.visitor-picker-icon {
  width: 20px;
  height: 20px;
  margin-left: 8px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 底部浮动步骤条 */
.visitor-step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.visitor-step {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #ddd;
  margin: 0 4px;
}

.visitor-step.active {
  width: 16px;
  background-color: #4f46e5;
}

.visitor-step-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

/* 提交按钮 */
.visitor-submit-btn-container {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.visitor-submit-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  color: #fff;
  font-size: 16px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗样式 */
.visitor-picker-modal, .visitor-share-modal, .visitor-extend-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
}

.visitor-picker-modal.show, .visitor-share-modal.show, .visitor-extend-modal.show {
  visibility: visible;
  opacity: 1;
}

.visitor-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.visitor-modal-content, .visitor-picker-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  transform: translateY(100%);
  transition: transform 0.3s;
}

/* 核验弹窗样式 */
.visitor-modal-body {
  padding: 16px;
}

.visitor-verify-info {
  margin-bottom: 16px;
}

.visitor-verify-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.visitor-verify-item {
  display: flex;
  margin-bottom: 8px;
}

.visitor-verify-label {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.visitor-verify-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.visitor-verify-tip {
  font-size: 12px;
  color: #999;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.visitor-modal-footer {
  display: flex;
  padding: 16px;
  border-top: 1px solid #eee;
}

.visitor-modal-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 16px;
  border-radius: 8px;
  margin: 0 8px;
}

.visitor-modal-cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.visitor-modal-confirm-btn {
  background-color: #4f46e5;
  color: #fff;
}

.visitor-picker-modal.show .visitor-picker-content,
.visitor-share-modal.show .visitor-modal-content,
.visitor-extend-modal.show .visitor-modal-content {
  transform: translateY(0);
}

.visitor-modal-header, .visitor-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.visitor-modal-title, .visitor-picker-title {
  font-size: 16px;
  font-weight: 500;
}

.visitor-modal-close, .visitor-picker-close {
  font-size: 14px;
  color: #666;
}

.visitor-close-icon {
  width: 20px;
  height: 20px;
}

.visitor-modal-body, .visitor-picker-body {
  padding: 16px;
}

.visitor-picker-footer {
  padding: 16px;
  border-top: 1px solid #eee;
}

.visitor-picker-confirm-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #4f46e5;
  color: #fff;
  font-size: 16px;
  border-radius: 8px;
  border: none;
}

/* 状态标签样式 */
.visitor-status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.visitor-status-badge.pending {
  background-color: #4f46e5;
}

.visitor-status-badge.visited {
  background-color: #10b981;
}

.visitor-status-badge.expired {
  background-color: #9ca3af;
}

.visitor-status-badge.canceled {
  background-color: #ef4444;
}

/* 访客列表样式 */
.visitor-list-container {
  flex: 1;
  overflow-y: auto;
}

.visitor-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 16px;
}

.visitor-empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.visitor-empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.visitor-empty-subtext {
  font-size: 14px;
  color: #666;
}

.visitor-add-btn {
  position: fixed;
  right: 16px;
  bottom: 24px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.visitor-add-icon {
  width: 24px;
  height: 24px;
}
