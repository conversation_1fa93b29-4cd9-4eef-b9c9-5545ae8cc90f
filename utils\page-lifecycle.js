// 页面生命周期函数
const util = require('./util.js')

// 重写Page函数，添加暗黑模式支持
const originalPage = Page

// 定义新的Page函数
Page = function(config) {
  // 保存原始的onLoad函数
  const originalOnLoad = config.onLoad

  // 重写onLoad函数
  config.onLoad = function(options) {
    // 应用当前的暗黑模式设置
    util.applyDarkMode()

    // 添加监听暗黑模式变化
    this.watchDarkModeChange = () => {
      try {
        const app = getApp()
        if (app && app.globalData && app.globalData.darkModeChangeEvent) {
          const { time, darkMode } = app.globalData.darkModeChangeEvent

          // 如果有暗黑模式变化事件，则应用暗黑模式样式
          if (this.lastDarkModeEventTime !== time) {
            this.lastDarkModeEventTime = time

            // 更新页面样式
            if (this.setData) {
              this.setData({
                darkMode: darkMode
              })
            }
          }
        }
      } catch (error) {
        console.log('监听暗黑模式变化失败：', error)
      }
    }

    // 初始化暗黑模式状态
    if (this.setData) {
      this.setData({
        darkMode: util.isDarkMode()
      })
    }

    // 调用原始的onLoad函数
    if (originalOnLoad) {
      originalOnLoad.call(this, options)
    }
  }

  // 保存原始的onShow函数
  const originalOnShow = config.onShow

  // 重写onShow函数
  config.onShow = function() {
    // 应用当前的暗黑模式设置
    util.applyDarkMode()

    // 检查暗黑模式状态
    if (this.watchDarkModeChange) {
      this.watchDarkModeChange()
    }

    // 更新暗黑模式状态
    if (this.setData) {
      this.setData({
        darkMode: util.isDarkMode()
      })
    }

    // 调用原始的onShow函数
    if (originalOnShow) {
      originalOnShow.call(this)
    }
  }

  // 保存原始的onUnload函数
  const originalOnUnload = config.onUnload

  // 重写onUnload函数
  config.onUnload = function() {
    // 清除监听器
    this.watchDarkModeChange = null

    // 调用原始的onUnload函数
    if (originalOnUnload) {
      originalOnUnload.call(this)
    }
  }

  // 调用原始的Page函数
  return originalPage(config)
}

module.exports = {
  // 导出一个空对象，表示已经加载了这个模块
}
