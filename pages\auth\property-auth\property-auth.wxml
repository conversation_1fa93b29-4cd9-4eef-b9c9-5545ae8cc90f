<!--物业员工登记页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 错误提示 -->
  <view class="error-message" wx:if="{{errorMsg}}">{{errorMsg}}</view>

  <!-- 物业员工基本信息 -->
  <view class="form-title">
    <text class="icon-property"></text>
    <text>物业员工基本信息</text>
  </view>

  <!-- 员工信息表单 -->
  <view class="auth-form">
    <!-- 姓名 -->
    <view class="form-item">
      <view class="form-label">
        <text>姓名</text>
        <text class="required">*</text>
      </view>
      <view class="form-input {{nameValid ? 'valid' : nameError ? 'error' : ''}}">
        <input type="text" placeholder="请输入您的真实姓名" value="{{name}}" bindinput="inputName" bindblur="validateName"/>
        <view class="check-icon" wx:if="{{nameValid}}"></view>
      </view>

    </view>

    <!-- 手机号码 -->
    <view class="form-item">
      <view class="form-label">
        <text>手机号码</text>
        <text class="required">*</text>
      </view>
      <view class="form-input {{phoneValid ? 'valid' : phoneError ? 'error' : ''}}">
        <input type="number" placeholder="请输入您的手机号码" value="{{phone}}" bindinput="inputPhone" bindblur="validatePhone"/>
        <view class="check-icon" wx:if="{{phoneValid}}"></view>
      </view>

    </view>

    <!-- 身份证号码 -->
    <view class="form-item">
      <view class="form-label">
        <text>身份证号码</text>
        <text class="required">*</text>
      </view>
      <view class="form-input {{idCardValid ? 'valid' : idCardError ? 'error' : ''}}">
        <input type="idcard" placeholder="请输入您的身份证号码" value="{{idCard}}" bindinput="inputIdCard" bindblur="validateIdCard"/>
        <view class="check-icon" wx:if="{{idCardValid}}"></view>
      </view>

    </view>

    <!-- 员工编号 -->
    <view class="form-item">
      <view class="form-label">
        <text>员工编号</text>
        <text class="required">*</text>
      </view>
      <view class="form-input {{employeeIdValid ? 'valid' : employeeIdError ? 'error' : ''}}">
        <input type="text" placeholder="请输入您的员工编号" value="{{employeeId}}" bindinput="inputEmployeeId" bindblur="validateEmployeeId"/>
        <view class="check-icon" wx:if="{{employeeIdValid}}"></view>
      </view>

    </view>

    <!-- 所属部门 -->
    <view class="form-item">
      <view class="form-label">
        <text>所属部门</text>
        <text class="required">*</text>
      </view>
      <view class="form-input picker {{departmentValid ? 'valid' : departmentError ? 'error' : ''}}">
        <picker bindchange="bindDepartmentChange" value="{{departmentIndex}}" range="{{departments}}">
          <view class="picker-text {{department ? '' : 'placeholder'}}">
            {{department || '请选择您所在的部门'}}
          </view>
        </picker>
        <view class="check-icon" wx:if="{{departmentValid}}"></view>
      </view>

    </view>

    <!-- 职位 -->
    <view class="form-item">
      <view class="form-label">
        <text>职位</text>
        <text class="required">*</text>
      </view>
      <view class="form-input picker {{positionValid ? 'valid' : positionError ? 'error' : ''}}">
        <picker bindchange="bindPositionChange" value="{{positionIndex}}" range="{{positions}}">
          <view class="picker-text {{position ? '' : 'placeholder'}}">
            {{position || '请选择您的职位'}}
          </view>
        </picker>
        <view class="check-icon" wx:if="{{positionValid}}"></view>
      </view>

    </view>

    <!-- 工作证照片 -->
    <view class="form-item">
      <view class="form-label">
        <text>工作证照片</text>
        <text class="required">*</text>
      </view>
      <view class="upload-area {{employeeCardPhotoPath ? 'has-image' : ''}}" bindtap="uploadEmployeeCard">
        <block wx:if="{{!employeeCardPhotoPath}}">
          <view class="upload-icon"></view>
          <view class="upload-text">点击上传工作证照片</view>

        </block>
        <image wx:else class="uploaded-image" src="{{employeeCardPhotoPath}}" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 人脸照片 -->
    <view class="form-item">
      <view class="form-label">
        <text>人脸照片</text>
      </view>
      <view class="upload-area {{facePhotoPath ? 'has-image' : ''}}" bindtap="uploadFacePhoto">
        <block wx:if="{{!facePhotoPath}}">
          <view class="upload-icon"></view>
          <view class="upload-text">点击上传照片</view>

        </block>
        <image wx:else class="uploaded-image" src="{{facePhotoPath}}" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 提交按钮 -->
    <button class="auth-btn" bindtap="submitAuth" disabled="{{isSubmitting}}">
      <text wx:if="{{!isSubmitting}}">提交登记</text>
      <view class="loading" wx:else></view>
    </button>

    <!-- 隐私政策提示 -->
    <view class="privacy-policy">
      <text>点击"提交登记"即表示您同意</text>
      <text class="policy-link">《隐私政策》</text>
      <text>和</text>
      <text class="policy-link">《用户协议》</text>
    </view>
  </view>
</view>
