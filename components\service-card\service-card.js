// 服务卡片组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片图标
    icon: {
      type: String,
      value: ''
    },
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    // 卡片描述
    description: {
      type: String,
      value: ''
    },
    // 是否显示箭头
    showArrow: {
      type: Boolean,
      value: true
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 卡片点击事件
    onCardTap: function() {
      this.triggerEvent('tap')
    }
  }
})
