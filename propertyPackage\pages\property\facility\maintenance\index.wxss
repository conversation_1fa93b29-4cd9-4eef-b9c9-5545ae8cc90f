/* 设施维修/保养记录页样式 */

/* 基本容器 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #ff8c00;
  color: #fff;
  z-index: 100;
  padding-left: 16px;
  padding-right: 16px;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.nav-placeholder {
  width: 40px;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding-top: 44px; /* 导航栏高度 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 设施信息 */
.facility-info {
  margin-top: 44px; /* 导航栏高度 */
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.facility-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.facility-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.facility-code {
  font-size: 28rpx;
  color: #666;
}

.facility-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.facility-status.normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.facility-status.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.facility-status.fault {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.facility-status.offline {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9E9E9E;
}

.facility-location {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 表单容器 */
.form-container {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.form-label.required::before {
  content: '*';
  color: #F44336;
  margin-right: 6rpx;
}

.form-control {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input.error {
  border: 1rpx solid #F44336;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea.error {
  border: 1rpx solid #F44336;
}

.error-message {
  font-size: 24rpx;
  color: #F44336;
  margin-top: 10rpx;
}

/* 工作类型选择器 */
.work-type-selector {
  display: flex;
}

.work-type-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.work-type-option:last-child {
  margin-right: 0;
}

.work-type-option.active {
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-weight: 500;
}

/* 状态选择器 */
.status-selector {
  display: flex;
}

.status-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.status-option:last-child {
  margin-right: 0;
}

.status-option.active {
  font-weight: 500;
}

/* 图片上传 */
.upload-container {
  margin-bottom: 20rpx;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 24rpx 24rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.upload-button {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 5V19M5 12H19' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.upload-button text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 日期输入 */
.date-input, .time-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.calendar-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.time-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 提交按钮 */
.submit-container {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button.disabled {
  background-color: #ccc;
}

/* 日期选择器弹窗 */
.date-picker-popup, .time-picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.date-picker-popup.active, .time-picker-popup.active {
  visibility: visible;
  opacity: 1;
}

.date-picker-mask, .time-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.date-picker-container, .time-picker-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.date-picker-popup.active .date-picker-container,
.time-picker-popup.active .time-picker-container {
  transform: translateY(0);
}

.date-picker-header, .time-picker-header {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-picker-cancel, .time-picker-cancel {
  font-size: 28rpx;
  color: #999;
}

.date-picker-title, .time-picker-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.date-picker-confirm, .time-picker-confirm {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
}

.date-picker, .time-picker {
  height: 400rpx;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .custom-nav {
  background-color: #ff8c00;
}

.darkMode .facility-info,
.darkMode .form-container,
.darkMode .submit-container,
.darkMode .date-picker-container,
.darkMode .time-picker-container {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .facility-name,
.darkMode .form-label,
.darkMode .date-picker-title,
.darkMode .time-picker-title {
  color: #f5f5f7;
}

.darkMode .facility-code,
.darkMode .facility-location,
.darkMode .upload-tip,
.darkMode .date-picker-cancel,
.darkMode .time-picker-cancel {
  color: #8e8e93;
}

.darkMode .form-input,
.darkMode .form-textarea,
.darkMode .upload-button,
.darkMode .work-type-option,
.darkMode .status-option {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .upload-button text {
  color: #8e8e93;
}

.darkMode .date-picker-header,
.darkMode .time-picker-header {
  border-bottom: 1rpx solid #3a3a3c;
}
