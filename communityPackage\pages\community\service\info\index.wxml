<!--社区信息展示页面-->
<view class="container">

  <!-- 分类选项卡 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'groups' ? 'active' : ''}}" bindtap="switchTab" data-tab="groups">
      社区兴趣群
      <view class="tab-line" wx:if="{{activeTab === 'groups'}}"></view>
    </view>
    <view class="tab {{activeTab === 'lostfound' ? 'active' : ''}}" bindtap="switchTab" data-tab="lostfound">
      失物招领
      <view class="tab-line" wx:if="{{activeTab === 'lostfound'}}"></view>
    </view>
  </view>

  <!-- 社区兴趣群列表 -->
  <view class="content-container" wx:if="{{activeTab === 'groups'}}">
    <view class="group-list">
      <view class="group-item" wx:for="{{groups}}" wx:key="id" bindtap="navigateToGroupDetail" data-id="{{item.id}}">
        <view class="group-content">
          <view class="group-header">
            <view class="group-name">{{item.name}}</view>
            <view class="group-members">
              <image class="member-icon" src="/images/icons/user-group.svg" mode="aspectFit"></image>
              <text>{{item.memberCount}}人</text>
            </view>
          </view>
          <view class="group-desc">{{item.description}}</view>
          <view class="group-owner">
            <image class="owner-avatar" src="{{item.ownerAvatar}}" mode="aspectFill"></image>
            <text>群主: {{item.ownerName}}</text>
          </view>
        </view>
        <view class="group-action">
          <button class="detail-btn">查看详情</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <empty-state
      wx:if="{{groups.length === 0}}"
      image="/images/illustrations/empty-groups.svg"
      text="暂无兴趣群"
      subtext="社区兴趣群正在筹备中"
    />
  </view>

  <!-- 失物招领列表 -->
  <view class="content-container" wx:if="{{activeTab === 'lostfound'}}">
    <view class="filter-tabs">
      <view class="filter-tab {{lostFoundFilter === 'all' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="all">全部</view>
      <view class="filter-tab {{lostFoundFilter === 'lost' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="lost">寻物启事</view>
      <view class="filter-tab {{lostFoundFilter === 'found' ? 'active' : ''}}" bindtap="switchLostFoundFilter" data-filter="found">招领启事</view>
    </view>

    <view class="lostfound-list">
      <view class="lostfound-item" wx:for="{{filteredLostFound}}" wx:key="id" bindtap="navigateToLostFoundDetail" data-id="{{item.id}}">
        <view class="lostfound-content">
          <view class="lostfound-header">
            <view class="lostfound-title">{{item.title}}</view>
            <view class="lostfound-tag {{item.type === 'lost' ? 'lost-tag' : 'found-tag'}}">
              {{item.type === 'lost' ? '寻找中' : '已拾到'}}
            </view>
          </view>

          <view class="lostfound-info">
            <view class="lostfound-time">
              <image class="info-icon" src="/images/icons/clock.svg" mode="aspectFit"></image>
              <text>{{item.time}}</text>
            </view>
            <view class="lostfound-location">
              <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
              <text>{{item.location}}</text>
            </view>
          </view>

          <view class="lostfound-desc">{{item.description}}</view>

          <!-- 图片预览 -->
          <view class="lostfound-images" wx:if="{{item.images && item.images.length > 0}}">
            <image
              class="lostfound-image"
              wx:for="{{item.images}}"
              wx:for-item="image"
              wx:key="index"
              src="{{image}}"
              mode="aspectFill"
              catchtap="previewImage"
              data-urls="{{item.images}}"
              data-current="{{image}}"
            ></image>
          </view>
        </view>

        <view class="lostfound-action">
          <button class="detail-btn">查看详情</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <empty-state
      wx:if="{{filteredLostFound.length === 0}}"
      image="/images/illustrations/empty-lostfound.svg"
      text="暂无{{lostFoundFilter === 'lost' ? '寻物启事' : lostFoundFilter === 'found' ? '招领启事' : '失物招领信息'}}"
      subtext="如有需要请联系物业"
    />
  </view>
</view>
