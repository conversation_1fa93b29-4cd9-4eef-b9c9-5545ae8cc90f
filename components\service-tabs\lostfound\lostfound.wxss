/* 失物招领组件样式 */
.lostfound-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: #f9fafb;
  padding-bottom: 70px; /* 为底部选项卡预留空间 */
}

/* 筛选选项卡样式 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.filter-tab {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  position: relative;
}

.filter-tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 2px;
  background-color: #ff8c00;
  border-radius: 1px;
}

/* 失物招领列表样式 */
.lostfound-list {
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

.lostfound-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.lostfound-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.lostfound-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.lostfound-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.lost-tag {
  background-color: #ff6b6b;
}

.found-tag {
  background-color: #4caf50;
}

.lostfound-info {
  display: flex;
  margin-bottom: 12px;
  gap: 16px;
}

.lostfound-time, .lostfound-location {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.info-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.lostfound-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.lostfound-images {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.lostfound-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.lostfound-contact {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.contact-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 底部安全区域 */
.safe-bottom {
  height: 20px;
}
