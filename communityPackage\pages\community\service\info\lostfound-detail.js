// 失物招领详情页面
const app = getApp()

Page({
  data: {
    itemId: null,
    item: {}
  },

  onLoad: function(options) {
    const { id } = options

    this.setData({
      itemId: parseInt(id)
    })

    // 加载物品详情
    this.loadItemDetail()
  },



  // 加载物品详情
  loadItemDetail: function() {
    const { itemId } = this.data

    // 模拟从服务器获取数据
    // 实际应该调用API获取数据
    const allItems = this.getAllItems()
    const item = allItems.find(i => i.id === itemId)

    if (item) {
      this.setData({
        item
      })
    } else {
      wx.showToast({
        title: '物品不存在',
        icon: 'none'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取所有物品数据
  getAllItems: function() {
    return [
      {
        id: 1,
        title: '寻找黑色钱包',
        type: 'lost',
        time: '2023-06-15 18:30',
        location: '小区中央广场',
        description: '昨天傍晚在中央广场散步时不慎遗失黑色钱包一个，内有身份证、银行卡等重要证件，如有拾到请联系物业。',
        features: [
          '黑色真皮钱包，长款',
          '内有身份证、银行卡、少量现金',
          '钱包侧面有一个小划痕'
        ],
        images: [
          'https://img.freepik.com/free-photo/black-wallet_74190-4488.jpg'
        ],
        contactPhone: '020-12345678'
      },
      {
        id: 2,
        title: '拾到一串钥匙',
        type: 'found',
        time: '2023-06-16 09:15',
        location: '3号楼电梯口',
        description: '今早在3号楼电梯口拾到一串钥匙，有房门钥匙和车钥匙，失主请联系物业认领。',
        features: [
          '一串钥匙，约5把',
          '有房门钥匙和汽车钥匙',
          '钥匙扣是一个小熊挂件'
        ],
        images: [
          'https://img.freepik.com/free-photo/house-keys_144627-12773.jpg'
        ],
        contactPhone: '020-12345678'
      },
      {
        id: 3,
        title: '寻找儿童玩具车',
        type: 'lost',
        time: '2023-06-14 16:45',
        location: '小区儿童游乐场',
        description: '孩子在游乐场玩耍时丢失了一辆红色遥控玩具车，是孩子很喜欢的玩具，如有拾到请联系物业。',
        features: [
          '红色遥控玩具车，约20cm长',
          '车身有"SPEED"字样',
          '遥控器是黑色的，有些磨损'
        ],
        images: [
          'https://img.freepik.com/free-photo/red-toy-car_144627-15043.jpg'
        ],
        contactPhone: '020-12345678'
      }
    ]
  },

  // 预览图片
  previewImage: function(e) {
    const { url } = e.currentTarget.dataset
    const { item } = this.data

    wx.previewImage({
      urls: item.images,
      current: url
    })
  },

  // 联系物业
  contactProperty: function() {
    const { item } = this.data

    if (item.contactPhone) {
      wx.makePhoneCall({
        phoneNumber: item.contactPhone
      })
    } else {
      wx.makePhoneCall({
        phoneNumber: '020-12345678' // 默认物业电话
      })
    }
  }
})
