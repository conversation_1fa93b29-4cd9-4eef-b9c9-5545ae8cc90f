// 物业服务组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    
  },

  /**
   * 组件的初始数据
   */
  data: {
    contactList: [
      {
        id: 1,
        name: '物业客服',
        phone: '020-12345678'
      },
      {
        id: 2,
        name: '报修热线',
        phone: '020-87654321'
      },
      {
        id: 3,
        name: '投诉建议',
        phone: '020-66668888'
      },
      {
        id: 4,
        name: '紧急联系',
        phone: '13800138000'
      }
    ],
    propertyInfo: {
      address: '广州市天河区珠江新城物业管理处',
      hours: '工作时间：周一至周日 08:30-18:00'
    },
    faqList: [
      {
        id: 1,
        question: '如何办理门禁卡？',
        answer: '业主可携带身份证和房产证明到物业管理处办理门禁卡，每户可免费办理2张，额外办理需支付工本费50元/张。',
        expanded: false
      },
      {
        id: 2,
        question: '如何申请装修？',
        answer: '业主需提前3天到物业管理处提交装修申请，并缴纳装修押金和垃圾清运费。装修时间为周一至周五9:00-18:00，周末及节假日不得进行噪音装修。',
        expanded: false
      },
      {
        id: 3,
        question: '访客如何进入小区？',
        answer: '访客可通过业主在小程序上提前登记，或在门岗处登记个人信息并由业主确认后进入小区。',
        expanded: false
      },
      {
        id: 4,
        question: '如何缴纳物业费？',
        answer: '业主可通过小程序"物业缴费"功能在线缴纳，也可到物业管理处现场缴纳。物业费按季度收取，提前缴纳可享受一定折扣。',
        expanded: false
      },
      {
        id: 5,
        question: '如何处理生活垃圾？',
        answer: '请按照垃圾分类要求，将垃圾分为可回收物、厨余垃圾、有害垃圾和其他垃圾，分别投放到小区指定的垃圾桶中。大件垃圾请提前联系物业安排清运。',
        expanded: false
      }
    ],
    showMessageModal: false,
    messageTypes: ['咨询问题', '投诉建议', '表扬反馈', '其他'],
    messageTypeIndex: 0,
    messageContent: '',
    messageContact: '',
    canSubmit: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 拨打电话
    callPhone: function(e) {
      const phone = e.currentTarget.dataset.phone
      wx.makePhoneCall({
        phoneNumber: phone
      })
    },

    // 切换FAQ展开/折叠状态
    toggleFaq: function(e) {
      const id = e.currentTarget.dataset.id
      const { faqList } = this.data

      const updatedFaqList = faqList.map(item => {
        if (item.id === id) {
          return {
            ...item,
            expanded: !item.expanded
          }
        }
        return item
      })

      this.setData({
        faqList: updatedFaqList
      })
    },

    // 显示留言表单
    showMessageForm: function() {
      this.setData({
        showMessageModal: true
      })
    },

    // 隐藏留言表单
    hideMessageForm: function() {
      this.setData({
        showMessageModal: false
      })
    },

    // 留言类型选择变化
    onTypeChange: function(e) {
      this.setData({
        messageTypeIndex: e.detail.value
      })
      this.checkCanSubmit()
    },

    // 留言内容输入
    onContentInput: function(e) {
      this.setData({
        messageContent: e.detail.value
      })
      this.checkCanSubmit()
    },

    // 联系方式输入
    onContactInput: function(e) {
      this.setData({
        messageContact: e.detail.value
      })
      this.checkCanSubmit()
    },

    // 检查是否可以提交
    checkCanSubmit: function() {
      const { messageContent, messageContact } = this.data
      const canSubmit = messageContent.trim() !== '' && messageContact.trim() !== ''

      this.setData({
        canSubmit
      })
    },

    // 提交留言
    submitMessage: function() {
      const { messageTypes, messageTypeIndex, messageContent, messageContact } = this.data

      if (!this.data.canSubmit) {
        return
      }

      // 模拟提交
      wx.showLoading({
        title: '提交中...'
      })

      setTimeout(() => {
        wx.hideLoading()
        
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })

        this.setData({
          showMessageModal: false,
          messageContent: '',
          messageContact: '',
          messageTypeIndex: 0,
          canSubmit: false
        })
      }, 1500)
    }
  }
})
