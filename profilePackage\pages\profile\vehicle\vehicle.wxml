<!-- 我的车辆页面 -->
<view class="container">
  <!-- 内容区域 -->
  <scroll-view scroll-y class="content-area">
    <view class="vehicle-list" wx:if="{{vehicles.length > 0}}">
      <view class="vehicle-card" wx:for="{{vehicles}}" wx:key="id">
        <view class="vehicle-header">
          <view class="vehicle-plate">{{item.plateNumber}}</view>
          <view class="vehicle-status" style="background-color: {{item.statusClass}}40;;color:  {{item.statusClass}};" >{{item.statusText}}</view>
        </view>
        <view class="vehicle-info">
          <view class="info-row">
            <view class="info-label">车辆颜色</view>
            <view class="info-value">
              <view class="color-dot" style="background-color: {{item.colorValue}};"></view>
              {{item.color}}
            </view>
          </view>
          <view class="info-row">
            <view class="info-label">车位类型</view>
            <view class="info-value">{{item.parkingTypeDisplay}}</view>
          </view>
          <view class="info-row">
            <view class="info-label">停车位</view>
            <view class="info-value">
              {{item.parkingSpace}}
              <text class="primary-badge" wx:if="{{item.isPrimaryVehicle}}">主要</text>
            </view>
          </view>
          <view class="info-row" wx:if="{{item.hasValidPeriod}}">
            <view class="info-label">有效期</view>
            <view class="info-value">{{item.formattedValidPeriod}}</view>
          </view>
        </view>
        <view class="vehicle-actions">
          <view class="vehicle-action" bindtap="editVehicle" data-id="{{item.id}}">编辑</view>
          <view class="vehicle-action" bindtap="deleteVehicle" data-id="{{item.id}}">删除</view>
        </view>
      </view>
    </view>
    <view class="empty-state" wx:else>
      <view class="empty-icon">
        <image src="/images/icons/car-empty.svg" mode="aspectFit"></image>
      </view>
      <view class="empty-text">暂无车辆</view>
      <view class="empty-subtext">添加您的车辆以便使用物业服务</view>
    </view>
  </scroll-view>

  <!-- 右下角固定添加按钮 -->
  <view class="floating-add-button" bindtap="addVehicle">
    <view class="add-icon">+</view>
  </view>
</view>


