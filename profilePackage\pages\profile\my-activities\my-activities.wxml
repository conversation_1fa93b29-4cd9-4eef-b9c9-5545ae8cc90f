<view class="container">
  <!-- 内容容器 -->
  <view class="content-container">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 活动列表 -->
    <block wx:if="{{!isLoading && enrolledActivities.length > 0}}">
      <view class="activities-list">
        <view class="activity-card" wx:for="{{enrolledActivities}}" wx:key="id" bindtap="navigateToActivityDetail" data-activity="{{item}}">
          <view class="activity-image" style="background-image: url('{{item.image}}');"></view>
          <view class="activity-content">
            <view class="activity-title">{{item.title}}</view>
            <view class="activity-description">{{item.description}}</view>
            <view class="activity-info">
              <view class="activity-info-item">
                <view class="icon-calendar"></view>
                <text>{{item.date}} {{item.time}}</text>
              </view>
              <view class="activity-info-item">
                <view class="icon-location"></view>
                <text>{{item.location}}</text>
              </view>
            </view>
            <view class="activity-status {{item.status}}">{{item.statusText}}</view>
            <view class="activity-actions" wx:if="{{item.status !== 'ended'}}">
              <button class="cancel-button" catchtap="showCancelConfirm" data-id="{{item.id}}">取消报名</button>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态提示 -->
    <view class="empty-state" wx:if="{{!isLoading && enrolledActivities.length === 0}}">
      <view class="empty-icon"></view>
      <view class="empty-title">暂无报名活动</view>
      <view class="empty-text">您还没有报名任何活动，快去看看有哪些精彩活动吧！</view>
      <button class="browse-button" bindtap="navigateToCommunity">浏览活动</button>
    </view>
  </view>

  <!-- 取消报名确认弹窗 -->
  <view class="modal {{showCancelModal ? 'show' : ''}}" bindtap="hideCancelModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-title">确认取消报名</view>
      <view class="modal-text">您确定要取消参加"{{currentActivity.title}}"活动吗？</view>
      <view class="modal-buttons">
        <button class="modal-button cancel" bindtap="hideCancelModal">再想想</button>
        <button class="modal-button confirm" bindtap="cancelEnrollment">确认取消</button>
      </view>
    </view>
  </view>
</view>
