// pages/renovation/status/status.js
const app = getApp();
const dateUtil = require('../../../../utils/dateUtil.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    application: {},
    timeline: [],
    isNewApplication: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const id = options.id;
    const isNew = options.new === '1';

    this.setData({
      isNewApplication: isNew
    });

    // 加载申请详情
    this.loadApplicationDetail(id);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新申请详情
    this.loadApplicationDetail(this.data.application.id, () => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载申请详情
   */
  loadApplicationDetail(id, callback) {
    // 模拟加载申请详情
    setTimeout(() => {
      // 根据ID获取不同的模拟数据
      let application = {};
      let timeline = [];

      if (id.startsWith('RN2023112')) {
        // 待审核申请
        application = {
          id: id,
          title: '紫荆花园3栋2单元1801',
          status: 'pending',
          statusText: '等待审核',
          type: '全屋装修',
          date: '2023-11-25',
          startDate: '2023-12-01',
          endDate: '2023-12-31',
          progress: '物业初审',
          contactName: '张三',
          contactPhone: '13800138000',
          constructionCompany: '优质装修公司',
          constructionManager: '李四',
          managerPhone: '13900139000'
        };

        timeline = [
          {
            step: 1,
            title: '提交申请',
            time: '2023-11-25 14:30',
            description: '您的装修申请已提交成功，等待物业审核。',
            status: 'completed'
          },
          {
            step: 2,
            title: '物业初审',
            description: '物业正在审核您的装修申请，预计1-2个工作日完成。',
            status: 'active'
          },
          {
            step: 3,
            title: '缴纳押金',
            description: '通过初审后，需缴纳装修押金。',
            status: 'pending'
          },
          {
            step: 4,
            title: '现场确认',
            description: '物业将安排工作人员上门确认装修情况。',
            status: 'pending'
          },
          {
            step: 5,
            title: '装修施工',
            description: '获得装修许可后，可以开始施工。',
            status: 'pending'
          },
          {
            step: 6,
            title: '竣工验收',
            description: '装修完成后，物业将进行验收。',
            status: 'pending'
          }
        ];
      } else if (id.startsWith('RN2023110')) {
        // 已通过申请
        application = {
          id: id,
          title: '紫荆花园1栋1单元502',
          status: 'approved',
          statusText: '已通过',
          type: '局部改造',
          date: '2023-11-03',
          startDate: '2023-11-10',
          endDate: '2023-11-30',
          progress: '施工监督中',
          contactName: '李四',
          contactPhone: '13900139000',
          constructionCompany: '专业装修公司',
          constructionManager: '王五',
          managerPhone: '13700137000'
        };

        timeline = [
          {
            step: 1,
            title: '提交申请',
            time: '2023-11-03 10:15',
            description: '您的装修申请已提交成功。',
            status: 'completed'
          },
          {
            step: 2,
            title: '物业初审',
            time: '2023-11-04 16:30',
            description: '您的装修申请已通过物业初审。',
            status: 'completed'
          },
          {
            step: 3,
            title: '缴纳押金',
            time: '2023-11-05 11:20',
            description: '您已缴纳装修押金2000元。',
            status: 'completed'
          },
          {
            step: 4,
            title: '现场确认',
            time: '2023-11-08 14:00',
            description: '物业已完成现场确认，允许开始装修。',
            status: 'completed'
          },
          {
            step: 5,
            title: '装修施工',
            description: '装修施工进行中，请遵守小区装修管理规定。',
            status: 'active'
          },
          {
            step: 6,
            title: '竣工验收',
            description: '装修完成后，请联系物业进行验收。',
            status: 'pending'
          }
        ];
      } else if (id.startsWith('RN2023082')) {
        // 已完成申请
        application = {
          id: id,
          title: '紫荆花园2栋2单元901',
          status: 'completed',
          statusText: '已完成',
          type: '设备安装',
          date: '2023-08-20',
          startDate: '2023-08-25',
          endDate: '2023-09-10',
          progress: '已完成',
          contactName: '王五',
          contactPhone: '13700137000',
          constructionCompany: '设备安装公司',
          constructionManager: '赵六',
          managerPhone: '13600136000',
          completeDate: '2023-09-15'
        };

        timeline = [
          {
            step: 1,
            title: '提交申请',
            time: '2023-08-20 09:45',
            description: '您的装修申请已提交成功。',
            status: 'completed'
          },
          {
            step: 2,
            title: '物业初审',
            time: '2023-08-21 15:10',
            description: '您的装修申请已通过物业初审。',
            status: 'completed'
          },
          {
            step: 3,
            title: '缴纳押金',
            time: '2023-08-22 10:30',
            description: '您已缴纳装修押金1000元。',
            status: 'completed'
          },
          {
            step: 4,
            title: '现场确认',
            time: '2023-08-24 11:00',
            description: '物业已完成现场确认，允许开始装修。',
            status: 'completed'
          },
          {
            step: 5,
            title: '装修施工',
            time: '2023-08-25 - 2023-09-10',
            description: '装修施工已完成。',
            status: 'completed'
          },
          {
            step: 6,
            title: '竣工验收',
            time: '2023-09-15 14:30',
            description: '物业已完成竣工验收，装修押金已退还。',
            status: 'completed'
          }
        ];
      } else if (id.startsWith('RN2023050')) {
        // 已驳回申请
        application = {
          id: id,
          title: '紫荆花园3栋2单元1801',
          status: 'rejected',
          statusText: '已驳回',
          type: '墙体拆改',
          date: '2023-05-05',
          startDate: '2023-05-10',
          endDate: '2023-05-30',
          progress: '申请被驳回',
          contactName: '张三',
          contactPhone: '13800138000',
          constructionCompany: '拆改公司',
          constructionManager: '李四',
          managerPhone: '13900139000',
          feedback: '您的装修申请涉及承重墙拆除，存在安全隐患，请调整方案后重新提交。',
          feedbackTime: '2023-05-07 16:45'
        };

        timeline = [
          {
            step: 1,
            title: '提交申请',
            time: '2023-05-05 11:20',
            description: '您的装修申请已提交成功。',
            status: 'completed'
          },
          {
            step: 2,
            title: '物业初审',
            time: '2023-05-07 16:45',
            description: '您的装修申请未通过物业初审，原因：涉及承重墙拆除，存在安全隐患。',
            status: 'rejected'
          }
        ];
      } else {
        // 新申请（刚提交的）
        application = {
          id: id,
          title: '紫荆花园3栋2单元1801',
          status: 'pending',
          statusText: '等待审核',
          type: '全屋装修',
          date: dateUtil.formatDate(new Date()),
          startDate: '2023-12-15',
          endDate: '2023-12-30',
          progress: '等待物业审核',
          contactName: '张三',
          contactPhone: '13800138000',
          constructionCompany: '优质装修公司',
          constructionManager: '李四',
          managerPhone: '13900139000'
        };

        timeline = [
          {
            step: 1,
            title: '提交申请',
            time: dateUtil.formatTime(new Date()),
            description: '您的装修申请已提交成功，等待物业审核。',
            status: 'completed'
          },
          {
            step: 2,
            title: '物业初审',
            description: '物业正在审核您的装修申请，预计1-2个工作日完成。',
            status: 'active'
          },
          {
            step: 3,
            title: '缴纳押金',
            description: '通过初审后，需缴纳装修押金。',
            status: 'pending'
          },
          {
            step: 4,
            title: '现场确认',
            description: '物业将安排工作人员上门确认装修情况。',
            status: 'pending'
          },
          {
            step: 5,
            title: '装修施工',
            description: '获得装修许可后，可以开始施工。',
            status: 'pending'
          },
          {
            step: 6,
            title: '竣工验收',
            description: '装修完成后，物业将进行验收。',
            status: 'pending'
          }
        ];
      }

      this.setData({
        application: application,
        timeline: timeline
      });

      // 如果是新申请，显示提交成功提示
      if (this.data.isNewApplication) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success',
          duration: 2000
        });

        // 重置标志，避免重复提示
        this.setData({
          isNewApplication: false
        });
      }

      if (callback) {
        callback();
      }
    }, 500);
  },

  /**
   * 处理操作
   */
  handleAction(e) {
    const action = e.currentTarget.dataset.action;

    switch (action) {
      case 'pay':
        this.payDeposit();
        break;
      case 'contact':
        this.contactProperty();
        break;
      case 'schedule':
        this.scheduleInspection();
        break;
    }
  },

  /**
   * 缴纳押金
   */
  payDeposit() {
    wx.showModal({
      title: '缴纳押金',
      content: '您需要缴纳2000元装修押金，是否前往支付？',
      confirmText: '去支付',
      confirmColor: '#FF9800',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/payment/payment?type=deposit&amount=2000&id=' + this.data.application.id
          });
        }
      }
    });
  },

  /**
   * 联系物业
   */
  contactProperty() {
    wx.showActionSheet({
      itemList: ['拨打物业电话', '发送消息'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        } else if (res.tapIndex === 1) {
          wx.navigateTo({
            url: '/pages/messages/chat?target=property'
          });
        }
      }
    });
  },

  /**
   * 预约验收
   */
  scheduleInspection() {
    wx.showModal({
      title: '预约验收',
      content: '请选择您方便的验收时间',
      confirmText: '去预约',
      confirmColor: '#FF9800',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/appointment/create?type=inspection&id=' + this.data.application.id
          });
        }
      }
    });
  },

  /**
   * 拨打电话
   */
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone;

    wx.makePhoneCall({
      phoneNumber: phone
    });
  },

  /**
   * 催办物业
   */
  notifyProperty() {
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '催办成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 重新申请
   */
  reapplyRenovation() {
    wx.navigateTo({
      url: '/pages/renovation/create/create?copy=' + this.data.application.id
    });
  },

  /**
   * 返回列表
   */
  goToList() {
    wx.navigateBack({
      delta: 1
    });
  }
})