/* 附近回收点地图页面样式 */
.container {
  padding: 0;
  min-height: 100vh;
  background-color: #f8f8f8;
}



.banner-content {
  text-align: center;
  padding: 20rpx 30rpx 40rpx;
  background: linear-gradient(135deg, #4caf50, #2e7d32);
  color: white;
  margin-bottom: 30rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 地图组件样式 */
.map-container {
  width: 100%;
  height: 500rpx;
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.map {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.location-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpoint cx='12' cy='12' r='3'%3E%3C/point%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.zoom-in-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3Cline x1='11' y1='8' x2='11' y2='14'%3E%3C/line%3E%3Cline x1='8' y1='11' x2='14' y2='11'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.zoom-out-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3Cline x1='8' y1='11' x2='14' y2='11'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 回收点列表样式 */
.list-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.list-filter {
  display: flex;
  align-items: center;
}

.filter-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.filter-icon {
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.recycle-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.recycle-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.recycle-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 40rpx;
  flex-shrink: 0;
}

.station-icon {
  background-color: rgba(76, 175, 80, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.bin-icon {
  background-color: rgba(33, 150, 243, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.item-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.tag {
  font-size: 22rpx;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.item-distance {
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 回收点详情弹窗样式 */
.point-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.detail-card {
  width: 100%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.detail-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.detail-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.detail-content {
  margin-bottom: 30rpx;
}

.detail-item {
  margin-bottom: 20rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.detail-value {
  font-size: 30rpx;
  color: #333;
}

.phone-value {
  color: #2196f3;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-tag {
  font-size: 26rpx;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

.detail-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.share-btn {
  background-color: #f5f5f5;
  color: #666;
}

.navigate-btn {
  background-color: #4caf50;
  color: white;
}

.share-icon, .navigate-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.share-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'%3E%3C/circle%3E%3Ccircle cx='6' cy='12' r='3'%3E%3C/circle%3E%3Ccircle cx='18' cy='19' r='3'%3E%3C/circle%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'%3E%3C/line%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'%3E%3C/line%3E%3C/svg%3E");
}

.navigate-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='3 11 22 2 13 21 11 13 3 11'%3E%3C/polygon%3E%3C/svg%3E");
}

.action-text {
  font-size: 28rpx;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .title {
  color: #f5f5f7;
}

.darkMode .subtitle {
  color: #8e8e93;
}

.darkMode .control-btn {
  background-color: #2c2c2e;
}

.darkMode .list-container {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .list-title {
  color: #f5f5f7;
}

.darkMode .filter-text {
  color: #8e8e93;
}

.darkMode .recycle-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .item-name {
  color: #f5f5f7;
}

.darkMode .item-address {
  color: #8e8e93;
}

.darkMode .detail-card {
  background-color: #2c2c2e;
}

.darkMode .detail-name {
  color: #f5f5f7;
}

.darkMode .detail-close {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .detail-value {
  color: #f5f5f7;
}

.darkMode .share-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
}
