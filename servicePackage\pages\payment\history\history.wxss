/* pages/payment/history/history.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}



/* 统计卡片 */
.stats-card {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.stats-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: #eee;
}

.stats-item.overdue .stats-value {
  color: #ff3b30;
}

/* 筛选区域 */
.filter-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.filter-group {
  margin-bottom: 24rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background-color: #f2f2f7;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}

.filter-option.active {
  background-color: #ff8c00;
  color: #fff;
}

.reset-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  color: #ff8c00;
  font-size: 26rpx;
}

.reset-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z' fill='%23ff8c00'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 缴费记录列表 */
.payment-records {
  padding: 0 30rpx;
}

.payment-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.payment-card:active {
  transform: scale(0.98);
}

.payment-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.payment-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.payment-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.payment-status.paid {
  background-color: #e8f5e9;
  color: #4caf50;
}

.payment-status.unpaid {
  background-color: #e3f2fd;
  color: #2196f3;
}

.payment-status.overdue {
  background-color: #ffebee;
  color: #f44336;
}

.payment-card-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.payment-info {
  flex: 1;
}

/* 新的主要信息布局 */
.payment-main-info {
  flex: 1;
}

.payment-main-info .payment-date,
.payment-main-info .payment-cycle,
.payment-main-info .payment-method {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.payment-main-info .label {
  color: #666;
  margin-right: 8rpx;
  min-width: 140rpx;
  font-size: 24rpx;
}

.payment-main-info .value {
  color: #333;
  font-weight: 500;
  font-size: 26rpx;
}

/* 保持向后兼容的旧样式 */
.payment-period {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.payment-date, .payment-method {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.payment-cycle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.payment-amount {
  text-align: right;
}

.payment-amount .amount-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.payment-amount .amount-total {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.payment-amount .amount-label {
  margin-right: 4rpx;
}

.payment-amount .amount-original {
  text-decoration: line-through;
  color: #999;
}

.payment-card-footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.payment-action {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f2f2f7;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.invoice-icon {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z' fill='%23666'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.2);
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='3' y='5' width='18' height='14' rx='2' stroke='%23999' stroke-width='1.5'/%3E%3Cpath d='M3 7l9 6 9-6' stroke='%23999' stroke-width='1.5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-action {
  font-size: 28rpx;
  color: #ff8c00;
  padding: 12rpx 30rpx;
  border: 1rpx solid #ff8c00;
  border-radius: 30rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more .loading-spinner {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  margin-bottom: 0;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
  color: #fff;
}

.darkMode .stats-card,
.darkMode .filter-section,
.darkMode .payment-card {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .stats-value,
.darkMode .payment-title,
.darkMode .payment-amount,
.darkMode .filter-label,
.darkMode .payment-period {
  color: #fff;
}

.darkMode .stats-label,
.darkMode .payment-date,
.darkMode .payment-method,
.darkMode .empty-text,
.darkMode .load-more {
  color: #8e8e93;
}

.darkMode .filter-option {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .filter-option.active {
  background-color: #ff8c00;
  color: #fff;
}

.darkMode .stats-divider {
  background-color: #3a3a3c;
}

.darkMode .payment-card-footer {
  border-top-color: #3a3a3c;
}

.darkMode .payment-action {
  background-color: #3a3a3c;
}

.darkMode .empty-icon {
  opacity: 0.3;
}
