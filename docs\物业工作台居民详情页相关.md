
可能用到的字典:
人员标签字典 residentRag=util.getDictByNameEn('resident_tag')[0].children 
证件类型字典certificateType=util.getDictByNameEn('certificate_type')[0].children 
居民住户类型字典residentType=util.getDictByNameEn('resident_type')[0].children 
性别字典genderDict=util.getDictByNameEn('resident_type')[0].children 
房间户型roomType=util.getDictByNameEn('room_type')[0].children 
房产审核状态字典residentStatus= util.getDictByNameEn('resident_status')[0].children 

propertyPackage/pages/property/resident/search/index居民列表页面
接口 propertyApi.propertyGetResidentList(params)
入参params=
{
pageNum:this.data.pageNum,
pageSize:this.data.pageSize,
phone:this.data.searchPhone,
residentName:this.data.searchResidentName,
communityId:wx.getStorageSync('selectedCommunity').id,
}

返回
{
        "total": 11,
        "list": [
            {
                "id": "118",
                "residentName": "卜凡傲",
                "birthday": "1990-06-06",
                "gender": "man",
                "certificateType": "id_card",
                "idCardNumber": "320323199006060298",
                "nativePlace": null,
                "phone": "13685156020",
                "tags": null,
                "note": null,
                "createTime": "2025-06-26 10:37:30",
                "updateTime": "2025-06-26 20:34:17",
                "communityId": "2"
            }
        ],
        "pageNum": 2,
        "pageSize": 10,
        "size": 1,
        "startRow": 11,
        "endRow": 11,
        "pages": 2,
        "prePage": 1,
        "nextPage": 0,
        "isFirstPage": false,
        "isLastPage": true,
        "hasPreviousPage": true,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1,
            2
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 2
    }


propertyPackage/pages/property/resident/detail/index居民详情页面完善
居民详情页的顶部编辑按钮去掉,基本信息,房屋信息,车辆信息的item要有独立的编辑按钮
1.获取基本信息propertyApi.getResidentDetail(id)
返回
{
        "id": "118",
        "residentName": "卜凡傲",
        "birthday": "1990-06-06",
        "gender": "man",
        "certificateType": "id_card",
        "idCardNumber": "320323199006060298",
        "nativePlace": null,
        "phone": "13685156020",
        "tags": null,
        "note": null,
        "createTime": "2025-06-26 10:37:30",
        "updateTime": "2025-06-26 20:34:17",
        "communityId": "2"
    }

编辑居民信息 propertyApi.editResident(params)


2.获取居民房产列表propertyApi.propertyGetResidentRoomList(params)
入参params={
pageNum1:this.data.pageNum,
pageSize:this.data.pageSize
}
返回
 {
        "total": 2,
        "list": [
            {
                "id": "142",
                "residentName": "卜凡傲",
                "phone": "13685156020",
                "buildingId": "5",
                "buildingNumber": "2栋",
                "unitNumber": null,
                "residentId": "118",
                "certificateType": "id_card",
                "idCardNumber": "320323199006060298",
                "roomId": "5",
                "roomNumber": "别墅1号",
                "roomType": "villa",
                "status": "normal",
                "residentType": "owner",
                "isDefault": false,
                "createTime": "2025-07-07 14:13:05"
            },
            {
                "id": "141",
                "residentName": "卜凡傲",
                "phone": "13685156020",
                "buildingId": "4",
                "buildingNumber": "1栋",
                "unitNumber": null,
                "residentId": "118",
                "certificateType": "id_card",
                "idCardNumber": "320323199006060298",
                "roomId": "14",
                "roomNumber": "104",
                "roomType": "duplex_loft",
                "status": "normal",
                "residentType": "family",
                "isDefault": false,
                "createTime": "2025-06-26 20:34:17"
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "size": 2,
        "startRow": 1,
        "endRow": 2,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
    }


编辑居民房产 propertyApi.propertyEditResidentRoom(params)
编辑房产时,先获取所有楼栋列表,communityApi.getPropertyBuildingList(params)
入参 params = {
      pageNum: 1,
      pageSize: 500,
      communityId: selectedCommunity.id
    };
楼栋列表返回
 {
        "total": 14,
        "list": [
            {
                "id": "18",
                "buildingNumber": "15栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 11:22:50",
                "updateTime": "2025-06-20 20:36:37",
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "17",
                "buildingNumber": "13栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:17:27",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "16",
                "buildingNumber": "12栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:17:06",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "15",
                "buildingNumber": "11栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:16:54",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "14",
                "buildingNumber": "10栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:16:00",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "13",
                "buildingNumber": "9栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:52",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "12",
                "buildingNumber": "8栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:46",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "11",
                "buildingNumber": "7栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:42",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "10",
                "buildingNumber": "6栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:34",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "9",
                "buildingNumber": "5栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:29",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "8",
                "buildingNumber": "4栋",
                "lng": null,
                "lat": null,
                "alt": null,
                "expandData": "",
                "createTime": "2025-06-13 10:15:21",
                "updateTime": null,
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "6",
                "buildingNumber": "3栋",
                "lng": 120.663263,
                "lat": 31.120825,
                "alt": 1.0,
                "expandData": "",
                "createTime": "2025-05-27 11:12:41",
                "updateTime": "2025-05-28 11:00:41",
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "5",
                "buildingNumber": "2栋",
                "lng": 120.663263,
                "lat": 31.120825,
                "alt": 1.0,
                "expandData": "",
                "createTime": "2025-05-27 11:12:25",
                "updateTime": "2025-05-27 21:18:28",
                "note": "",
                "sort": 0,
                "communityId": "2"
            },
            {
                "id": "4",
                "buildingNumber": "1栋",
                "lng": 120.663263,
                "lat": 31.120825,
                "alt": 20.0,
                "expandData": "",
                "createTime": "2025-05-27 11:10:11",
                "updateTime": "2025-05-28 11:00:25",
                "note": "",
                "sort": 0,
                "communityId": "2"
            }
        ],
        "pageNum": 1,
        "pageSize": 500,
        "size": 14,
        "startRow": 1,
        "endRow": 14,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
    }	
	
反显楼栋名称后,根据楼栋id查询房间列表communityApi.getPropertyRoomList(params)
入参params = {
      pageNum: 1,
      pageSize: 500,
      communityId: selectedCommunity.id
    };
房间列表返回
 {
        "total": 3,
        "list": [
            {
                "id": "7",
                "roomNumber": "别墅3号",
                "type": "villa",
                "unitNumber": null,
                "buildingId": "5",
                "area": null,
                "expandData": null,
                "note": null,
                "createTime": null,
                "updateTime": null
            },
            {
                "id": "6",
                "roomNumber": "别墅2号",
                "type": "villa",
                "unitNumber": null,
                "buildingId": "5",
                "area": null,
                "expandData": null,
                "note": null,
                "createTime": null,
                "updateTime": null
            },
            {
                "id": "5",
                "roomNumber": "别墅1号",
                "type": "villa",
                "unitNumber": null,
                "buildingId": "5",
                "area": null,
                "expandData": null,
                "note": null,
                "createTime": null,
                "updateTime": null
            }
        ],
        "pageNum": 1,
        "pageSize": 500,
        "size": 3,
        "startRow": 1,
        "endRow": 3,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
    }
反显单元和房间名称
	
	
	
3.获取居民车辆列表propertyApi.propertyGetResidentVehicleList(params)
入参params=

{

pageNum:this.data.pageNum,
pageSize:this.data.pageSize,
phone:this.data.searchPhone,
residentName:this.data.searchResidentName,
communityId:wx.getStorageSync('selectedCommunity').id,
  "residentId": 用户id
}

返回示例
{
        "total": 5,
        "list": [
            {
                "id": "26",
                "plateNumber": "苏AAAAAA",
                "vehicleColor": "红色",
                "parkingType": "固定车位",
                "parkingNumber": null,
                "validBeginTime": null,
                "validEndTime": null,
                "note": null,
                "status": "normal",
                "mainUse": false,
                "createTime": "2025-06-26 13:49:27",
                "updateTime": "2025-07-05 17:23:39",
                "residentId": "118",
                "communityId": "2"
            },
            {
                "id": "22",
                "plateNumber": "苏AKKEKER",
                "vehicleColor": "红色",
                "parkingType": "固定车位",
                "parkingNumber": "m",
                "validBeginTime": null,
                "validEndTime": null,
                "note": null,
                "status": "pending",
                "mainUse": true,
                "createTime": "2025-06-22 19:50:30",
                "updateTime": "2025-07-05 17:13:02",
                "residentId": "118",
                "communityId": "2"
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "size": 5,
        "startRow": 1,
        "endRow": 5,
        "pages": 1,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [
            1
        ],
        "navigateFirstPage": 1,
        "navigateLastPage": 1
    }

编辑居民车辆 propertyApi.propertyEditResidentVehicle(params)
编辑车辆可以参考新增车辆页面profilePackage/pages/profile/vehicle/add/add

所有用到的接口已经在相关api接口文件里定义好了

