<!-- components/virtual-list/index.wxml -->
<view class="virtual-list-container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{showLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
  
  <!-- 空列表提示 -->
  <view class="empty-container" wx:elif="{{list.length === 0 && showEmpty}}">
    <image wx:if="{{emptyIcon}}" class="empty-icon" src="{{emptyIcon}}" mode="aspectFit"></image>
    <view wx:else class="empty-icon-default"></view>
    <text class="empty-text">{{emptyText}}</text>
  </view>
  
  <!-- 虚拟列表 -->
  <scroll-view 
    wx:else
    class="virtual-list" 
    scroll-y="{{true}}" 
    scroll-top="{{scrollTop}}" 
    bindscroll="onScroll"
    enable-back-to-top="{{true}}"
    scroll-with-animation="{{false}}"
    enhanced="{{true}}"
    bounces="{{true}}"
    show-scrollbar="{{true}}"
  >
    <!-- 列表容器 -->
    <view class="list-container" style="height: {{listHeight}}px;">
      <!-- 顶部填充 -->
      <view class="top-fill" style="height: {{topFillHeight}}px;"></view>
      
      <!-- 可视区域列表项 -->
      <view 
        wx:for="{{visibleList}}" 
        wx:key="index" 
        class="list-item" 
        style="height: {{itemHeight}}px;"
        data-index="{{index}}"
        bindtap="onItemClick"
        bindlongpress="onItemLongPress"
      >
        <slot name="item" data="{{item}}"></slot>
      </view>
    </view>
  </scroll-view>
</view>
