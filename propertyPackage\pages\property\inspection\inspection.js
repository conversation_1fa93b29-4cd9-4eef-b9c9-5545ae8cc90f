// pages/property/inspection/inspection.js
const dateUtil = require('../../../../utils/dateUtil.js')

Page({
  data: {
    darkMode: false,
    activeTab: 'all', // 当前选中的标签：all, pending, completed, abnormal
    searchValue: '', // 搜索关键词
    inspections: [], // 巡检记录列表
    isLoading: true, // 是否正在加载
    isEmpty: false, // 是否为空状态
    isLoadingMore: false, // 是否正在加载更多
    hasMore: true, // 是否还有更多数据
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    
    // 统计数据
    statistics: {
      total: 0,
      pending: 0,
      completed: 0,
      abnormal: 0,
      todayCompleted: 0
    },
    
    // 日期筛选
    dateRange: {
      start: '',
      end: ''
    },
    showDatePicker: false,
    currentDatePickerType: 'start', // 'start' 或 'end'
    
    // 动画数据
    animationData: {}
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '巡检记录'
    });

    // 设置默认日期范围（最近7天）
    const today = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(today.getDate() - 7);
    
    this.setData({
      'dateRange.start': dateUtil.formatDate(sevenDaysAgo),
      'dateRange.end': dateUtil.formatDate(today)
    });

    // 添加页面进入动画
    this.animatePageEnter();

    // 加载巡检记录
    this.loadInspections();
  },
  
  onShow: function() {
    // 页面显示时刷新数据
    this.loadInspections();
  },
  
  // 页面进入动画
  animatePageEnter: function() {
    const animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
    });
    
    animation.opacity(1).step();
    
    this.setData({
      animationData: animation.export()
    });
  },
  
  // 加载巡检记录
  loadInspections: function(isLoadMore = false) {
    if (isLoadMore) {
      this.setData({ isLoadingMore: true });
    } else {
      this.setData({ isLoading: true, page: 1 });
    }
    
    // 模拟加载数据
    setTimeout(() => {
      // 模拟巡检记录数据
      const mockInspections = this.getMockInspections();
      
      // 根据当前标签筛选数据
      let filteredInspections = mockInspections;
      if (this.data.activeTab !== 'all') {
        filteredInspections = mockInspections.filter(item => item.status === this.data.activeTab);
      }
      
      // 根据搜索关键词筛选
      if (this.data.searchValue) {
        const keyword = this.data.searchValue.toLowerCase();
        filteredInspections = filteredInspections.filter(item => 
          item.name.toLowerCase().includes(keyword) || 
          item.location.toLowerCase().includes(keyword) ||
          item.inspector.toLowerCase().includes(keyword)
        );
      }
      
      // 根据日期范围筛选
      const startDate = new Date(this.data.dateRange.start).getTime();
      const endDate = new Date(this.data.dateRange.end).getTime() + 24 * 60 * 60 * 1000; // 包含结束日期
      
      filteredInspections = filteredInspections.filter(item => {
        const itemDate = new Date(item.date).getTime();
        return itemDate >= startDate && itemDate <= endDate;
      });
      
      // 更新统计数据
      const statistics = {
        total: mockInspections.length,
        pending: mockInspections.filter(item => item.status === 'pending').length,
        completed: mockInspections.filter(item => item.status === 'completed').length,
        abnormal: mockInspections.filter(item => item.status === 'abnormal').length,
        todayCompleted: mockInspections.filter(item => {
          const today = new Date().toISOString().split('T')[0];
          return item.status === 'completed' && item.date.includes(today);
        }).length
      };
      
      this.setData({
        inspections: filteredInspections,
        statistics: statistics,
        isEmpty: filteredInspections.length === 0,
        hasMore: false, // 模拟数据没有更多了
        isLoading: false,
        isLoadingMore: false
      });
    }, 500);
  },
  
  // 获取模拟巡检记录数据
  getMockInspections: function() {
    return [
      {
        id: 1,
        name: '小区围墙巡检',
        location: '小区北侧围墙',
        date: '2023-05-18',
        time: '09:30',
        inspector: '张工',
        status: 'completed',
        result: '正常',
        abnormalCount: 0,
        images: []
      },
      {
        id: 2,
        name: '电梯设备巡检',
        location: '1号楼电梯',
        date: '2023-05-18',
        time: '10:15',
        inspector: '李工',
        status: 'abnormal',
        result: '异常',
        abnormalCount: 2,
        images: ['https://example.com/image1.jpg']
      },
      {
        id: 3,
        name: '消防设施巡检',
        location: '2号楼消防通道',
        date: '2023-05-17',
        time: '14:00',
        inspector: '王工',
        status: 'completed',
        result: '正常',
        abnormalCount: 0,
        images: []
      },
      {
        id: 4,
        name: '绿化带巡检',
        location: '小区中心花园',
        date: '2023-05-17',
        time: '16:30',
        inspector: '赵工',
        status: 'completed',
        result: '正常',
        abnormalCount: 0,
        images: []
      },
      {
        id: 5,
        name: '停车场巡检',
        location: '地下停车场',
        date: '2023-05-16',
        time: '09:00',
        inspector: '张工',
        status: 'abnormal',
        result: '异常',
        abnormalCount: 1,
        images: ['https://example.com/image2.jpg']
      },
      {
        id: 6,
        name: '监控设备巡检',
        location: '小区各监控点',
        date: '2023-05-19',
        time: '11:00',
        inspector: '李工',
        status: 'pending',
        result: '',
        abnormalCount: 0,
        images: []
      }
    ];
  },
  
  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    this.loadInspections();
  },
  
  // 搜索输入
  onSearchInput: function(e) {
    this.setData({ searchValue: e.detail.value });
  },
  
  // 搜索确认
  onSearchConfirm: function() {
    this.loadInspections();
  },
  
  // 清除搜索
  clearSearch: function() {
    this.setData({ searchValue: '' });
    this.loadInspections();
  },
  
  // 显示日期选择器
  showDatePicker: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      showDatePicker: true,
      currentDatePickerType: type
    });
  },
  
  // 日期选择确认
  onDatePickerConfirm: function(e) {
    const date = dateUtil.formatDate(new Date(e.detail));
    
    if (this.data.currentDatePickerType === 'start') {
      this.setData({
        'dateRange.start': date,
        showDatePicker: false
      });
    } else {
      this.setData({
        'dateRange.end': date,
        showDatePicker: false
      });
    }
    
    this.loadInspections();
  },
  
  // 日期选择取消
  onDatePickerCancel: function() {
    this.setData({ showDatePicker: false });
  },
  
  // 查看巡检详情
  viewInspectionDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/inspection/detail/index?id=${id}`
    });
  },
  
  // 创建新巡检任务
  createNewInspection: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/inspection/create/index'
    });
  },
  
  // 加载更多
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.setData({ page: this.data.page + 1 });
      this.loadInspections(true);
    }
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadInspections();
    wx.stopPullDownRefresh();
  }
})
