# 设置页面关于我们功能修复

## 修改概述

修改了设置页面中的"关于我们"功能，使其与隐私政策保持一致，都通过`imagetext.getImagetextList`API获取内容并在富文本页面显示。

## 修改内容

### 1. 修改navigateToPage方法

**修改前：**
```javascript
navigateToPage: function(e) {
  const url = e.currentTarget.dataset.url
  wx.navigateTo({
    url: url
  })
}
```

**修改后：**
```javascript
navigateToPage: function(e) {
  const url = e.currentTarget.dataset.url
  
  // 如果是关于我们页面，使用API获取内容
  if (url === '/pages/about/about') {
    this.showAboutUs();
    return;
  }
  
  // 其他页面直接跳转
  wx.navigateTo({
    url: url
  })
}
```

### 2. 新增showAboutUs方法

```javascript
// 显示关于我们内容
showAboutUs: function() {
  wx.showLoading({
    title: '加载中...',
    mask: true
  });

  // 获取关于我们内容
  imagetext.getImagetextList({
    type: 'about_us',
    pageNum: 1,
    pageSize: 1
  }).then(res => {
    wx.hideLoading();
    
    if (res && res.list && res.list.length > 0) {
      const aboutUs = res.list[0]; // 取最新的第一个
      
      // 跳转到富文本显示页面
      wx.navigateTo({
        url: `/pages/webview/richtext?title=${encodeURIComponent('关于我们')}&content=${encodeURIComponent(aboutUs.content)}`
      });
    } else {
      wx.showModal({
        title: '关于我们',
        content: '暂无关于我们内容',
        showCancel: false
      });
    }
  }).catch(error => {
    wx.hideLoading();
    console.error('获取关于我们内容失败:', error);
    wx.showToast({
      title: '加载失败',
      icon: 'none'
    });
  });
}
```

## 功能特点

### 1. 统一的内容管理
- 隐私政策和关于我们都使用相同的API获取内容
- 都跳转到统一的富文本显示页面
- 保持用户体验的一致性

### 2. API调用规范
- 使用`imagetext.getImagetextList`接口
- 传递正确的type参数：`about_us`
- 取列表中最新的第一个item的content

### 3. 错误处理
- 完善的加载状态显示
- 网络错误处理
- 空数据处理

### 4. 用户体验
- 加载中状态提示
- 错误时的友好提示
- 富文本内容的良好展示

## 数据流程

```
用户点击"关于我们"
    ↓
检查URL是否为/pages/about/about
    ↓
调用imagetext.getImagetextList API
    ↓
type: 'about_us', pageNum: 1, pageSize: 1
    ↓
获取res.list[0]的content
    ↓
跳转到/pages/webview/richtext页面
    ↓
显示富文本内容
```

## 与原关于我们页面的区别

### 原关于我们页面 (/pages/about/about)
- 静态内容显示
- 需要修改代码才能更新内容
- 有固定的联系信息和版权信息

### 新的富文本页面 (/pages/webview/richtext)
- 动态内容获取
- 后台可以随时更新内容
- 纯富文本显示，更加灵活

## 兼容性考虑

- 保留了原有的`navigateToPage`方法的通用性
- 只对特定URL进行特殊处理
- 其他页面跳转不受影响

## 相关文件

- `pages/settings/settings.js` - 设置页面主逻辑
- `pages/webview/richtext.*` - 富文本显示页面
- `api/imagetext.js` - 图文内容API

## 测试要点

1. **正常流程** - 测试从设置页面点击关于我们能正确获取和显示内容
2. **空数据处理** - 测试API返回空数据时的处理
3. **网络错误** - 测试网络异常时的错误处理
4. **其他页面** - 确保其他页面的跳转不受影响
5. **富文本显示** - 验证富文本内容的正确渲染

现在设置页面的隐私政策和关于我们功能都统一使用API获取内容，保持了功能的一致性。
