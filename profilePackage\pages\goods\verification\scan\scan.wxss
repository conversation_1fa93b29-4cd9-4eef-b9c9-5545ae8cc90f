/* 扫码核销页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background: #f8f8f8;
}

/* 扫码中样式 */
.scanning-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.scanning-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M7 21H3a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpath d='M17 21h4a2 2 0 0 0 2-2v-4'%3E%3C/path%3E%3Cpath d='M7 3H3a2 2 0 0 0-2 2v4'%3E%3C/path%3E%3Cpath d='M17 3h4a2 2 0 0 1 2 2v4'%3E%3C/path%3E%3Cline x1='12' y1='7' x2='12' y2='17'%3E%3C/line%3E%3Cline x1='7' y1='12' x2='17' y2='12'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.scanning-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.scanning-tip {
  font-size: 28rpx;
  color: #666;
}

/* 验证结果样式 */
.result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.result-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.result-icon.success {
  background-color: #e8f5e9;
}

.result-icon.success::before {
  content: '';
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-icon.error {
  background-color: #ffebee;
}

.result-icon.error::before {
  content: '';
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.result-time {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.error-message {
  font-size: 28rpx;
  color: #f44336;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 订单卡片样式 */
.order-card {
  width: 100%;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 40rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 24rpx;
  color: #666;
}

.order-content {
  display: flex;
  padding: 20rpx;
  align-items: center;
}

.buyer-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.order-info {
  flex: 1;
}

.buyer-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-detail {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  width: 100%;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn.primary {
  background: #ff8c00;
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

/* 暗黑模式样式 */
.darkMode {
  background: #1c1c1e;
}

.darkMode .scanning-text {
  color: #f5f5f7;
}

.darkMode .scanning-tip {
  color: #8e8e93;
}

.darkMode .result-title {
  color: #f5f5f7;
}

.darkMode .result-time {
  color: #8e8e93;
}

.darkMode .order-card {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .order-header {
  border-bottom: 1rpx solid #3a3a3c;
  color: #8e8e93;
}

.darkMode .buyer-name {
  color: #f5f5f7;
}

.darkMode .goods-title,
.darkMode .order-detail {
  color: #8e8e93;
}

.darkMode .action-btn.secondary {
  background: #3a3a3c;
  color: #8e8e93;
}

/* 订单确认样式 */
.confirm-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 600rpx;
}

/* 商品图片展示样式 */
.goods-image-section {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 30rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.goods-main-image {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.image-count {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.confirm-icon {
  width: 120rpx;
  height: 120rpx;
  background: #ff8c00;
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
}

.confirm-icon::before {
  content: '?';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 60rpx;
  font-weight: bold;
}

.confirm-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.order-details {
  width: 100%;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  max-width: 60%;
  text-align: right;
  word-break: break-all;
}

/* 暗黑模式下的订单确认样式 */
.darkMode .confirm-title {
  color: #f5f5f7;
}

.darkMode .order-details {
  background: #2c2c2e;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .detail-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .detail-label {
  color: #8e8e93;
}

.darkMode .detail-value {
  color: #f5f5f7;
}

.darkMode .goods-image-section {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}
