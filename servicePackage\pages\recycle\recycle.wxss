/* recycle.wxss */
.container {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

.page-header {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

.page-subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 8rpx;
}

.recycle-banner {
  position: relative;
  height: 240rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0));
}

.banner-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
}

.banner-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.recycle-categories {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  background: #f5f5f5;
  color: #999;
  border: 2rpx solid transparent;
}

.category-icon.selected {
  background: #fff8e1;
  color: #ff8c00;
  border-color: #ff8c00;
}

.category-name {
  font-size: 24rpx;
  color: #666;
}

.recycle-form {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.address-selector {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
}

.address-info {
  flex: 1;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.address-contact {
  font-size: 24rpx;
  color: #999;
}

.address-placeholder {
  font-size: 28rpx;
  color: #999;
  flex: 1;
}

.address-arrow {
  font-size: 28rpx;
  color: #999;
}

.time-selector {
  display: flex;
  gap: 20rpx;
}

.picker-item {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-arrow {
  font-size: 28rpx;
  color: #999;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.recycle-tips {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.submit-btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #fff;
}

/* 地址选择弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.address-modal.show {
  opacity: 1;
  visibility: visible;
}

.address-modal-content {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.address-modal.show .address-modal-content {
  transform: translateY(0);
}

.address-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.address-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.address-modal-close {
  font-size: 40rpx;
  color: #999;
}

.address-modal-body {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 30rpx;
}

.address-list {
  margin-bottom: 30rpx;
}

.address-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.address-item:last-child {
  border-bottom: none;
}

.address-item.selected {
  background: #f8f8f8;
}

.address-item-info {
  flex: 1;
}

.address-item-detail {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.address-item-contact {
  font-size: 24rpx;
  color: #999;
}

.address-item-check {
  font-size: 32rpx;
  color: #ff8c00;
  margin-left: 20rpx;
}

.add-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #ff8c00;
}

.address-modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.address-confirm-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
}

/* 提交成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.success-modal.show {
  opacity: 1;
  visibility: visible;
}

.success-modal-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.success-modal.show .success-modal-content {
  transform: scale(1);
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: #e8f5e9;
  color: #4caf50;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.success-info {
  width: 100%;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.success-info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.success-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.success-tips {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  margin-bottom: 40rpx;
}

.success-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
}
