/* complete-info.wxss */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #252f3f;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

.content-area {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1b21;
  text-align: center;
  margin: 40rpx 0 16rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

/* 卡片样式 */
.info-card, .form-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  color: #1a1b21;
}

.icon-info, .icon-edit {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-info {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M12 16v-4'/%3E%3Cpath d='M12 8h.01'/%3E%3C/svg%3E");
}

.icon-edit {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'/%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'/%3E%3C/svg%3E");
}

/* 信息展示项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #1a1b21;
  font-weight: 500;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.optional {
  color: #6b7280;
  font-size: 24rpx;
  margin-left: 8rpx;
  font-weight: normal;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #e5e7eb;
  background: #f9fafb;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 48rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}

/* 输入框 */
.form-input {
  position: relative;
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #d2d6dc;
  border-radius: 16rpx;
  background-color: #f9fafb;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #1f2937;
  width: 100%;
}

.form-input.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input.error {
  border-color: #fa3e3e;
  background-color: rgba(250, 62, 62, 0.05);
}

/* 选择器 */
.form-selector {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #d2d6dc;
  border-radius: 16rpx;
  background-color: #f9fafb;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-selector.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-selector.error {
  border-color: #fa3e3e;
  background-color: rgba(250, 62, 62, 0.05);
}

.placeholder {
  color: #9fa6b2;
}

.arrow-icon {
  font-size: 24rpx;
  color: #9fa6b2;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.form-tip {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* 按钮区域 */
.button-section {
  padding: 40rpx 0;
}

.save-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #FF9500, #FF7800);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);
  transition: all 0.3s ease;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
}

/* 提示区域 */
.notice-section {
  background-color: rgba(255, 149, 0, 0.05);
  border: 1rpx solid rgba(255, 149, 0, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 40rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF9500;
  margin-bottom: 12rpx;
}

.notice-content {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.6;
}
