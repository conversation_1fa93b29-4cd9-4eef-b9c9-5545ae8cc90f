// pages/property/workorder/detail/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const commApi = require('@/api/commApi.js');
const util = require('@/utils/util.js');

Page({
  data: {
    workOrder: null,
    loading: true,
    statusBarHeight: 20, // 默认状态栏高度
    apiUrl: '', // API地址

    // 字典数据
    workOrderStatusDict: [], // 工单状态字典
    workOrderTypeDict: [], // 工单类型字典
    personWorkStatusDict: [], // 员工工作状态字典

    // 操作按钮显示控制
    showAcceptBtn: false, // 显示受理按钮
    showCancelBtn: false, // 显示取消按钮
    showProcessBtn: false, // 显示处理按钮
    showPendingBtn: false, // 显示挂起按钮
    showCompleteBtn: false, // 显示完成按钮

    // 弹窗控制
    showPendingModal: false, // 挂起工单弹窗
    showCompleteModal: false, // 完成工单弹窗
    showCancelModal: false, // 取消工单弹窗
    showProcessModal: false, // 处理工单弹窗

    // 弹窗表单数据
    pendingNote: '', // 挂起备注
    pendingImages: [], // 挂起图片
    completeNote: '', // 完成备注
    completeImages: [], // 完成图片
    cancelNote: '', // 取消备注
    cancelImages: [], // 取消图片
    processNote: '', // 处理备注
    processMinute: 0, // 预计处理时间（分钟）
    customMinute: '', // 自定义时间输入

    // 预设时间选项
    timeOptions: [
      { label: '1小时', value: 60 },
      { label: '2小时', value: 120 },
      { label: '4小时', value: 240 },
      { label: '8小时', value: 480 }
    ]
  },

  onLoad(options) {
    const { id } = options;

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 初始化API地址
    const apiUrl = wx.getStorageSync('apiUrl');

    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight,
      apiUrl: apiUrl
    });

    // 初始化字典数据
    this.initDictData();

    if (id) {
      this.loadWorkOrderDetail(id);
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化字典数据
  initDictData() {
    try {
      const workOrderStatusDict = util.getDictByNameEn('work_order_status')[0].children;
      const workOrderTypeDict = util.getDictByNameEn('work_order_type')[0].children;
      const personWorkStatusDict = util.getDictByNameEn('person_work_status')[0].children;

      this.setData({
        workOrderStatusDict,
        workOrderTypeDict,
        personWorkStatusDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载工单详情
  loadWorkOrderDetail(id) {
    this.setData({ loading: true });

    workOrderApi.getPropertyWorkOrderDetail(id).then(order => {
      console.log('工单详情数据：', order);
      if(!order) 
      {
        wx.showToast({
          title: '获取工单详情数据失败，请重试',
          icon: 'none'
        });
        this.setData({
          loading: false
        });
        return
      }
      // 处理工单数据
      const processedOrder = {
        ...order,
        // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
        imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
        // 获取状态显示名称
        statusName: this.getStatusText(order.status),
        // 获取类型显示名称
        typeName: this.getTypeText(order.type),
        // 处理报修人信息
        reporterInfo: {
          name: order.residentName || '未知',
          phone: order.residentPhone || '',
          address: order.residentAddress || order.region || ''
        },
        // 处理时间线数据
        processedTimeLine: this.processTimeLine(order.timeLine || [], order.personList || []),
        
        // 处理已分配人员列表
        assignedPersons: this.processAssignedPersons(order.workOrderPersonList || [], order.personList || [])
      };
      debugger
      // 根据工单状态设置按钮显示
      this.setButtonVisibility(order.status);

      this.setData({
        workOrder: processedOrder,
        loading: false
      });
    })
    .catch(error => {
      console.error('加载工单详情失败', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 根据工单状态设置按钮显示
  setButtonVisibility(status) {
    const buttonState = {
      showAcceptBtn: false,
      showCancelBtn: false,
      showProcessBtn: false,
      showPendingBtn: false,
      showCompleteBtn: false
    };

    // 除了取消状态和完成外，所有状态都显示取消按钮
    if (status !== 'cancelled' && status !== 'cancel'&&status !== 'complete') {
      buttonState.showCancelBtn = true;
    }

    switch (status) {
      case 'wait_process': // 待处理
        buttonState.showAcceptBtn = true;
        break;
      case 'accepted': // 已受理
        buttonState.showProcessBtn = true;
        break;
      case 'processing': // 处理中
        buttonState.showPendingBtn = true;
        buttonState.showCompleteBtn = true;
        break;
      case 'pending': // 挂起状态
        buttonState.showCompleteBtn = true;
        break;
      // 其他状态不显示特殊操作按钮
    }

    this.setData(buttonState);
  },


  // 受理工单
  acceptWorkOrder() {
    const workOrderId = this.data.workOrder.id;
    wx.navigateTo({
      url: `../process/index?id=${workOrderId}&mode=accept`
    });
  },

  // 取消工单
  cancelWorkOrder() {
    this.setData({
      showCancelModal: true,
      cancelNote: '',
      cancelImages: []
    });
  },

  // 执行取消工单
  doCancelWorkOrder(note = '', media = '') {
    const workOrderId = this.data.workOrder.id;

    wx.showLoading({
      title: '取消中...'
    });

    const params = {
      id: workOrderId,
      note: note,
      media: media
    };

    workOrderApi.propertyCancelWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ showCancelModal: false });
        wx.showToast({
          title: '工单已取消',
          icon: 'success'
        });

        // 刷新工单详情
        this.loadWorkOrderDetail(workOrderId);

        // 通知上级页面刷新数据
        this.notifyPreviousPageRefresh();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('取消工单失败', error);
        wx.showToast({
          title: '取消失败，请重试',
          icon: 'none'
        });
      });
  },

  // 处理工单
  processWorkOrder() {
    this.setData({
      showProcessModal: true,
      processNote: '',
      processMinute: 0,
      customMinute: ''
    });
  },

  // 执行处理工单
  doProcessWorkOrder(note = '', minute = 0) {
    const workOrderId = this.data.workOrder.id;

    wx.showLoading({
      title: '处理中...'
    });

    const params = {
      id: workOrderId,
      note: note,
      minute: minute
    };

    workOrderApi.propertyProcessingWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ showProcessModal: false });
        wx.showToast({
          title: '工单已开始处理',
          icon: 'success'
        });

        // 刷新工单详情
        this.loadWorkOrderDetail(workOrderId);

        // 通知上级页面刷新数据
        this.notifyPreviousPageRefresh();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('处理工单失败', error);
        wx.showToast({
          title: '处理失败，请重试',
          icon: 'none'
        });
      });
  },

  // 挂起工单
  pendingWorkOrder() {
    this.setData({
      showPendingModal: true,
      pendingNote: '',
      pendingImages: []
    });
  },

  // 执行挂起工单
  doPendingWorkOrder(note = '', media = '') {
    const workOrderId = this.data.workOrder.id;

    wx.showLoading({
      title: '挂起中...'
    });

    const params = {
      id: workOrderId,
      note: note,
      media: media
    };

    workOrderApi.propertyPendingWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ showPendingModal: false });
        wx.showToast({
          title: '工单已挂起',
          icon: 'success'
        });

        // 刷新工单详情
        this.loadWorkOrderDetail(workOrderId);

        // 通知上级页面刷新数据
        this.notifyPreviousPageRefresh();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('挂起工单失败', error);
        wx.showToast({
          title: '挂起失败，请重试',
          icon: 'none'
        });
      });
  },

  // 完成工单
  completeWorkOrder() {
    this.setData({
      showCompleteModal: true,
      completeNote: '',
      completeImages: []
    });
  },

  // 执行完成工单
  doCompleteWorkOrder(note = '', media = '') {
    const workOrderId = this.data.workOrder.id;

    wx.showLoading({
      title: '完成中...'
    });

    const params = {
      id: workOrderId,
      note: note,
      media: media
    };

    workOrderApi.propertyCompleteWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ showCompleteModal: false });
        wx.showToast({
          title: '工单已完成',
          icon: 'success'
        });

        // 刷新工单详情
        this.loadWorkOrderDetail(workOrderId);

        // 通知上级页面刷新数据
        this.notifyPreviousPageRefresh();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('完成工单失败', error);
        wx.showToast({
          title: '完成失败，请重试',
          icon: 'none'
        });
      });
  },

  // 获取工单状态文本
  getStatusText(status) {
    const statusDict = this.data.workOrderStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 获取工单类型文本
  getTypeText(type) {
    const typeDict = this.data.workOrderTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === type);
    return typeItem ? typeItem.nameCn : type;
  },

  // 处理时间线数据
  processTimeLine(timeLine, personList) {
    if (!timeLine || !Array.isArray(timeLine)) return [];

    return timeLine.map(item => {
      // 根据workOrderId从personList中找到对应的人员信息
      let operatorName = '系统';
      if (item.personId) {
        const person = personList.find(p => p.id === item.personId);
        operatorName = person ? person.personName : '未知操作员';
      }

      // 获取状态显示文本
      const statusText = this.getStatusText(item.status);

      // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
      const imageList = item.media ? item.media.split(',').filter(img => img.trim()) : [];

      return {
        ...item,
        operatorName: operatorName,
        statusText: statusText,
        formattedTime: this.formatTimelineTime(item.createTime),
        imageList: imageList
      };
    }).sort((a, b) => new Date(b.createTime) - new Date(a.createTime)); // 按时间倒序排列
  },

  // 处理已分配人员列表
  processAssignedPersons(workOrderPersonList, personList) {
    if (!workOrderPersonList || !Array.isArray(workOrderPersonList)) return [];

    return workOrderPersonList.map(item => {
      // 根据personId从personList中找到对应的人员信息
      const person = personList.find(p => p.id === item.personId);

      // 获取工单状态文本，使用work_order_status字典
      // const statusText = this.getStatusText(item.status);

      return {
        ...item,
        personName: person ? person.personName : '未知',
        personPhone: person ? person.phone : '',
        personPosition: person ? person.positionName : '',
        // statusText: statusText
      };
    });
  },

  // 格式化时间线时间显示
  formatTimelineTime(timeStr) {
    if (!timeStr) return '';

    const date = new Date(timeStr);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    const diffDays = Math.floor((today - itemDate) / (1000 * 60 * 60 * 24));

    const timeFormat = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    if (diffDays === 0) {
      return `今天 ${timeFormat}`;
    } else if (diffDays === 1) {
      return `昨天 ${timeFormat}`;
    } else if (diffDays < 7) {
      return `${diffDays}天前 ${timeFormat}`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },

  // 获取员工工作状态文本
  getWorkStatusText(workStatus) {
    const statusDict = this.data.personWorkStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === workStatus);
    return statusItem ? statusItem.nameCn : workStatus;
  },

  // 隐藏取消确认对话框
  hideCancelConfirm() {
    this.setData({ showCancelConfirm: false });
  },

  // 取消工单
  cancelOrder() {
    const { workOrder } = this.data;

    // 调用工单管理器取消工单
    // workOrderManager.cancelOrder(workOrder.id)
    //   .then(() => {
    //     this.hideCancelConfirm();

    //     // 重新加载工单详情
    //     this.loadWorkOrderDetail(workOrder.id);

    //     wx.showToast({
    //       title: '工单已取消',
    //       icon: 'success'
    //     });
    //   })
    //   .catch(error => {
    //     console.error('取消工单失败', error);
    //     this.hideCancelConfirm();

    //     wx.showToast({
    //       title: error.message || '操作失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 显示处理工单对话框
  showProcessModal() {
    this.setData({
      processingRemark: '',
      showProcessModal: true
    });
  },

  // 隐藏处理工单对话框
  hideProcessModal() {
    this.setData({ showProcessModal: false });
  },

  // 导航到工单处理页面
  navigateToProcessPage(e) {
    const { workOrder } = this.data;
    const type = e.currentTarget.dataset.type;

    wx.navigateTo({
      url: `/propertyPackage/pages/property/workorder/process/index?id=${workOrder.id}&type=${type}`
    });
  },

  // 输入处理备注
  inputProcessingRemark(e) {
    this.setData({ processingRemark: e.detail.value });
  },

  // 处理工单
  processOrder() {
    const { workOrder, processingRemark } = this.data;

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器处理工单
    // workOrderManager.processOrder(workOrder.id, processingRemark)
    //   .then(updatedOrder => {
    //     wx.hideLoading();

    //     this.setData({
    //       workOrder: updatedOrder,
    //       showProcessModal: false
    //     });

    //     wx.showToast({
    //       title: '工单已开始处理',
    //       icon: 'success'
    //     });
    //   })
    //   .catch(error => {
    //     wx.hideLoading();
    //     console.error('处理工单失败', error);

    //     wx.showToast({
    //       title: error.message || '处理失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 显示分配工单对话框
  showAssignModal() {
    this.setData({
      selectedStaffId: '',
      assignRemark: '',
      showAssignModal: true
    });
  },

  // 隐藏分配工单对话框
  hideAssignModal() {
    this.setData({ showAssignModal: false });
  },

  // 选择员工
  selectStaff(e) {
    this.setData({ selectedStaffId: e.currentTarget.dataset.id });
  },

  // 输入分配备注
  inputAssignRemark(e) {
    this.setData({ assignRemark: e.detail.value });
  },

  // 分配工单
  assignOrder() {
    const { workOrder, selectedStaffId, assignRemark, staffList } = this.data;

    if (!selectedStaffId) {
      wx.showToast({
        title: '请选择员工',
        icon: 'none'
      });
      return;
    }

    // 获取选中的员工信息
    const selectedStaff = staffList.find(staff => staff.id === selectedStaffId);

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器分配工单
    // workOrderManager.assignOrder(workOrder.id, selectedStaff, assignRemark)
    //   .then(updatedOrder => {
    //     wx.hideLoading();

    //     this.setData({
    //       workOrder: updatedOrder,
    //       showAssignModal: false
    //     });

    //     wx.showToast({
    //       title: '工单已分配',
    //       icon: 'success'
    //     });
    //   })
    //   .catch(error => {
    //     wx.hideLoading();
    //     console.error('分配工单失败', error);

    //     wx.showToast({
    //       title: error.message || '分配失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 显示完成工单对话框
  showCompleteModal() {
    this.setData({
      completeResult: '',
      completeImages: [],
      completeRemark: '',
      showCompleteModal: true
    });
  },

  // 隐藏完成工单对话框
  hideCompleteModal() {
    this.setData({ showCompleteModal: false });
  },

  // 输入完成结果
  inputCompleteResult(e) {
    this.setData({ completeResult: e.detail.value });
  },

  // 输入完成备注
  inputCompleteRemark(e) {
    this.setData({ completeRemark: e.detail.value });
  },

  // 上传完成图片
  uploadCompleteImages() {
    wx.chooseImage({
      count: 9 - this.data.completeImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 更新图片列表
        this.setData({
          completeImages: [...this.data.completeImages, ...res.tempFilePaths]
        });
      }
    });
  },

  // 删除完成图片
  removeCompleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.completeImages];
    images.splice(index, 1);
    this.setData({ completeImages: images });
  },

  // 完成工单
  completeOrder() {
    const { workOrder, completeResult, completeImages, completeRemark } = this.data;

    if (!completeResult) {
      wx.showToast({
        title: '请输入处理结果',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器完成工单
    // workOrderManager.completeOrder(workOrder.id, completeResult, completeImages, completeRemark)
    //   .then(updatedOrder => {
    //     wx.hideLoading();

    //     this.setData({
    //       workOrder: updatedOrder,
    //       showCompleteModal: false
    //     });

    //     wx.showToast({
    //       title: '工单已完成',
    //       icon: 'success'
    //     });
    //   })
    //   .catch(error => {
    //     wx.hideLoading();
    //     console.error('完成工单失败', error);

    //     wx.showToast({
    //       title: error.message || '操作失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 显示添加处理记录对话框
  showAddRecordModal() {
    this.setData({
      recordAction: '',
      recordRemark: '',
      showAddRecordModal: true
    });
  },

  // 隐藏添加处理记录对话框
  hideAddRecordModal() {
    this.setData({ showAddRecordModal: false });
  },

  // 输入处理记录动作
  inputRecordAction(e) {
    this.setData({ recordAction: e.detail.value });
  },

  // 输入处理记录备注
  inputRecordRemark(e) {
    this.setData({ recordRemark: e.detail.value });
  },

  // 添加处理记录
  addProcessingRecord() {
    const { workOrder, recordAction, recordRemark } = this.data;

    if (!recordAction) {
      wx.showToast({
        title: '请输入处理动作',
        icon: 'none'
      });
      return;
    }

    if (!recordRemark) {
      wx.showToast({
        title: '请输入处理备注',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器添加处理记录
    // workOrderManager.addProcessingRecord(workOrder.id, recordAction, recordRemark)
    //   .then(updatedOrder => {
    //     wx.hideLoading();

    //     this.setData({
    //       workOrder: updatedOrder,
    //       showAddRecordModal: false
    //     });

    //     wx.showToast({
    //       title: '处理记录已添加',
    //       icon: 'success'
    //     });
    //   })
    //   .catch(error => {
    //     wx.hideLoading();
    //     console.error('添加处理记录失败', error);

    //     wx.showToast({
    //       title: error.message || '操作失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { workOrder, apiUrl } = this.data;

    if (workOrder.imageList && workOrder.imageList.length > 0) {
      // 处理图片URL，支持完整URL和相对路径
      const fullUrls = workOrder.imageList.map(img => {
        if (img.startsWith('http')) {
          return img; // 已经是完整URL
        } else {
          return apiUrl + '/common-api/v1/file/' + img;
        }
      });

      wx.previewImage({
        current: fullUrls[index],
        urls: fullUrls
      });
    }
  },

  // 预览时间线图片
  previewTimelineImage(e) {
    const { index, images } = e.currentTarget.dataset;
    const { apiUrl } = this.data;

    if (images && images.length > 0) {
      // 处理图片URL，支持完整URL和相对路径
      const fullUrls = images.map(img => {
        if (img.startsWith('http')) {
          return img; // 已经是完整URL
        } else {
          return apiUrl + '/common-api/v1/file/' + img;
        }
      });

      wx.previewImage({
        current: fullUrls[index],
        urls: fullUrls
      });
    }
  },

  // 预览评价图片
  previewEvaluationImage(e) {
    const { index } = e.currentTarget.dataset;
    const { workOrder } = this.data;

    wx.previewImage({
      current: workOrder.evaluation.images[index],
      urls: workOrder.evaluation.images
    });
  },

  // 导航返回
  navigateBack() {
    wx.navigateBack();
  },

  // 通知上级页面刷新数据
  notifyPreviousPageRefresh() {
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2]; // 获取上一个页面

      // 如果上一个页面是工单管理页面
      if (prevPage.route.includes('workorder/workorder')) {
        if (typeof prevPage.loadWorkOrderData === 'function') {
          prevPage.loadWorkOrderData();
        }
      }

      // 如果上一个页面是工单列表页面
      if (prevPage.route.includes('workorder/list')) {
        if (typeof prevPage.loadWorkOrders === 'function') {
          prevPage.loadWorkOrders();
        }
      }
    }
  },

  // ========== 弹窗相关方法 ==========

  // 隐藏挂起弹窗
  hidePendingModal() {
    this.setData({ showPendingModal: false });
  },

  // 隐藏完成弹窗
  hideCompleteModal() {
    this.setData({ showCompleteModal: false });
  },

  // 隐藏取消弹窗
  hideCancelModal() {
    this.setData({ showCancelModal: false });
  },

  // 隐藏处理弹窗
  hideProcessModal() {
    this.setData({ showProcessModal: false });
  },

  // 输入挂起备注
  inputPendingNote(e) {
    this.setData({ pendingNote: e.detail.value });
  },

  // 输入完成备注
  inputCompleteNote(e) {
    this.setData({ completeNote: e.detail.value });
  },

  // 输入取消备注
  inputCancelNote(e) {
    this.setData({ cancelNote: e.detail.value });
  },

  // 输入处理备注
  inputProcessNote(e) {
    this.setData({ processNote: e.detail.value });
  },

  // 选择预设时间
  selectTimeOption(e) {
    const minute = e.currentTarget.dataset.minute;
    this.setData({
      processMinute: minute,
      customMinute: '' // 清空自定义输入
    });
  },

  // 输入自定义时间
  inputCustomMinute(e) {
    const value = e.detail.value;
    this.setData({
      customMinute: value,
      processMinute: value ? parseInt(value) * 60 : 0 // 转换为分钟
    });
  },

  // ========== 图片上传相关方法 ==========

  // 选择挂起图片
  choosePendingImages() {
    this.chooseImages('pending');
  },

  // 选择完成图片
  chooseCompleteImages() {
    this.chooseImages('complete');
  },

  // 选择取消图片
  chooseCancelImages() {
    this.chooseImages('cancel');
  },

  // 通用选择图片方法
  chooseImages(type) {
    const currentImages = this.data[`${type}Images`];
    const remainCount = 9 - currentImages.length;

    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImages(res.tempFiles, type);
      }
    });
  },

  // 上传图片
  uploadImages(tempFiles, type) {
    if (!tempFiles || tempFiles.length === 0) return;

    const uploadPromises = tempFiles.map(file => {
      return commApi.upLoadFile(file.tempFilePath);
    });

    Promise.all(uploadPromises)
      .then(results => {
        // 提取上传成功的图片URL
        const successUrls = results
          .map(result => result.data || '')
          .filter(url => url);

        // 更新对应类型的图片列表
        const currentImages = this.data[`${type}Images`];
        this.setData({
          [`${type}Images`]: [...currentImages, ...successUrls]
        });

        if (successUrls.length > 0) {
          wx.showToast({
            title: `${successUrls.length}张图片上传成功`,
            icon: 'success'
          });
        }
      })
      .catch(err => {
        console.error('图片上传失败:', err);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  // 删除挂起图片
  deletePendingImage(e) {
    this.deleteImage(e, 'pending');
  },

  // 删除完成图片
  deleteCompleteImage(e) {
    this.deleteImage(e, 'complete');
  },

  // 删除取消图片
  deleteCancelImage(e) {
    this.deleteImage(e, 'cancel');
  },

  // 通用删除图片方法
  deleteImage(e, type) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data[`${type}Images`]];
    images.splice(index, 1);
    this.setData({
      [`${type}Images`]: images
    });
  },

  // 预览图片
  previewModalImage(e) {
    const { index, type } = e.currentTarget.dataset;
    const images = this.data[`${type}Images`];
    const { apiUrl } = this.data;

    // 处理图片URL
    const fullUrls = images.map(img => {
      if (img.startsWith('http')) {
        return img;
      } else {
        return apiUrl + '/common-api/v1/file/' + img;
      }
    });

    wx.previewImage({
      current: fullUrls[index],
      urls: fullUrls
    });
  },

  // ========== 提交操作方法 ==========

  // 提交挂起工单
  submitPendingWorkOrder() {
    const { pendingNote, pendingImages } = this.data;
    const media = pendingImages.join(',');

    this.doPendingWorkOrder(pendingNote, media);
  },

  // 提交完成工单
  submitCompleteWorkOrder() {
    const { completeNote, completeImages } = this.data;
    const media = completeImages.join(',');

    this.doCompleteWorkOrder(completeNote, media);
  },

  // 提交取消工单
  submitCancelWorkOrder() {
    const { cancelNote, cancelImages } = this.data;
    const media = cancelImages.join(',');

    this.doCancelWorkOrder(cancelNote, media);
  },

  // 提交处理工单
  submitProcessWorkOrder() {
    const { processNote, processMinute } = this.data;

    // 验证预计处理时间
    if (!processMinute || processMinute <= 0) {
      wx.showToast({
        title: '请选择预计处理时间',
        icon: 'none'
      });
      return;
    }

    this.doProcessWorkOrder(processNote, processMinute);
  }
});
