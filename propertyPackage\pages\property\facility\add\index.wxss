/* 设施添加/编辑页样式 */

/* 基本容器 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #ff8c00;
  color: #fff;
  z-index: 100;
  padding-left: 16px;
  padding-right: 16px;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.nav-placeholder {
  width: 40px;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding-top: 44px; /* 导航栏高度 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 表单容器 */
.form-container {
  margin-top: 44px; /* 导航栏高度 */
  padding-bottom: 30rpx;
}

.form-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: #ff8c00;
  border-radius: 3rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.form-label.required::before {
  content: '*';
  color: #F44336;
  margin-right: 6rpx;
}

.form-control {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input.error {
  border: 1rpx solid #F44336;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea.error {
  border: 1rpx solid #F44336;
}

.error-message {
  font-size: 24rpx;
  color: #F44336;
  margin-top: 10rpx;
}

/* 选择器输入 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 9L12 15L18 9' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 位置输入 */
.location-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-picker-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Ccircle cx='12' cy='10' r='3' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 状态选择器 */
.status-selector {
  display: flex;
  flex-wrap: wrap;
}

.status-option {
  width: calc(33.33% - 14rpx);
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.status-option:nth-child(3n) {
  margin-right: 0;
}

.status-option.active {
  font-weight: 500;
}

/* 图片上传 */
.upload-container {
  margin-bottom: 20rpx;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6L6 18M6 6L18 18' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 24rpx 24rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.upload-button {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 5V19M5 12H19' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.upload-button text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 日期输入 */
.date-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.calendar-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 二维码绑定 */
.qrcode-bind-container {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}

.qrcode-bind-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
}

.qrcode-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 3H10V10H3V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 3H21V10H14V3Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 14H10V21H3V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 14H21V21H14V14Z' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

/* 提交按钮 */
.submit-container {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button.disabled {
  background-color: #ccc;
}

/* 日期选择器弹窗 */
.date-picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.date-picker-popup.active {
  visibility: visible;
  opacity: 1;
}

.date-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.date-picker-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.date-picker-popup.active .date-picker-container {
  transform: translateY(0);
}

.date-picker-header {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-picker-cancel {
  font-size: 28rpx;
  color: #999;
}

.date-picker-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.date-picker-confirm {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
}

.date-picker {
  height: 400rpx;
}

/* 位置选择器弹窗 */
.location-picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.location-picker-popup.active {
  visibility: visible;
  opacity: 1;
}

.location-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.location-picker-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  height: 70%;
  transform: translateY(100%);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
}

.location-picker-popup.active .location-picker-container {
  transform: translateY(0);
}

.location-picker-header {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-picker-cancel {
  font-size: 28rpx;
  color: #999;
}

.location-picker-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.location-picker-confirm {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
}

.location-picker-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.location-map {
  flex: 1;
  width: 100%;
}

.location-tip {
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
}

/* 二维码绑定弹窗 */
.qrcode-bind-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.qrcode-bind-popup.active {
  visibility: visible;
  opacity: 1;
}

.qrcode-bind-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.qrcode-bind-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.qrcode-bind-title {
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.qrcode-bind-content {
  padding: 30rpx;
}

.qrcode-bind-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.qrcode-scan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 3H5C3.89543 3 3 3.89543 3 5V7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M17 3H19C20.1046 3 21 3.89543 21 5V7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M3 17V19C3 20.1046 3.89543 21 5 21H7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 17V19C21 20.1046 20.1046 21 19 21H17' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7 12H17' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
}

.qrcode-bind-footer {
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.qrcode-bind-cancel {
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .custom-nav {
  background-color: #ff8c00;
}

.darkMode .form-section,
.darkMode .submit-container,
.darkMode .date-picker-container,
.darkMode .location-picker-container,
.darkMode .qrcode-bind-dialog {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.darkMode .section-title,
.darkMode .form-label,
.darkMode .date-picker-title,
.darkMode .location-picker-title,
.darkMode .qrcode-bind-title {
  color: #f5f5f7;
}

.darkMode .upload-tip,
.darkMode .date-picker-cancel,
.darkMode .location-picker-cancel,
.darkMode .qrcode-bind-tip,
.darkMode .location-tip {
  color: #8e8e93;
}

.darkMode .form-input,
.darkMode .form-textarea,
.darkMode .upload-button,
.darkMode .status-option,
.darkMode .qrcode-bind-cancel,
.darkMode .location-tip {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .upload-button text {
  color: #8e8e93;
}

.darkMode .date-picker-header,
.darkMode .location-picker-header,
.darkMode .qrcode-bind-title,
.darkMode .qrcode-bind-footer {
  border-color: #3a3a3c;
}
