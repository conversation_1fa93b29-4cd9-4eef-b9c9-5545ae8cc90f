/**
 * 微信小程序二维码生成工具
 * 
 * 注意：这是一个简化版的二维码生成工具，仅用于演示
 * 实际项目中应使用完整的二维码生成库
 */

// 二维码纠错级别
const QRErrorCorrectLevel = {
  L: 1,
  M: 0,
  Q: 3,
  H: 2
};

// 二维码构造函数
function QRCode(canvasId, options) {
  this.canvasId = canvasId;
  
  // 默认参数
  this.options = {
    text: '',
    width: 256,
    height: 256,
    colorDark: '#000000',
    colorLight: '#ffffff',
    correctLevel: QRErrorCorrectLevel.H
  };
  
  // 合并参数
  if (typeof options === 'object') {
    for (let key in options) {
      this.options[key] = options[key];
    }
  }
  
  // 绘制二维码
  this._draw();
}

// 绘制二维码
QRCode.prototype._draw = function() {
  const ctx = wx.createCanvasContext(this.canvasId);
  const options = this.options;
  const width = options.width;
  const height = options.height;
  
  // 绘制背景
  ctx.setFillStyle(options.colorLight);
  ctx.fillRect(0, 0, width, height);
  
  // 绘制模拟二维码（实际项目中应使用完整的二维码生成算法）
  ctx.setFillStyle(options.colorDark);
  
  // 绘制外框
  const margin = width * 0.1;
  const innerWidth = width - 2 * margin;
  const innerHeight = height - 2 * margin;
  
  ctx.strokeStyle = options.colorDark;
  ctx.lineWidth = 10;
  ctx.strokeRect(margin, margin, innerWidth, innerHeight);
  
  // 绘制定位点
  const positionSize = width * 0.15;
  
  // 左上角定位点
  ctx.fillRect(margin, margin, positionSize, positionSize);
  ctx.setFillStyle(options.colorLight);
  ctx.fillRect(margin + positionSize * 0.2, margin + positionSize * 0.2, positionSize * 0.6, positionSize * 0.6);
  ctx.setFillStyle(options.colorDark);
  ctx.fillRect(margin + positionSize * 0.3, margin + positionSize * 0.3, positionSize * 0.4, positionSize * 0.4);
  
  // 右上角定位点
  ctx.setFillStyle(options.colorDark);
  ctx.fillRect(width - margin - positionSize, margin, positionSize, positionSize);
  ctx.setFillStyle(options.colorLight);
  ctx.fillRect(width - margin - positionSize * 0.8, margin + positionSize * 0.2, positionSize * 0.6, positionSize * 0.6);
  ctx.setFillStyle(options.colorDark);
  ctx.fillRect(width - margin - positionSize * 0.7, margin + positionSize * 0.3, positionSize * 0.4, positionSize * 0.4);
  
  // 左下角定位点
  ctx.setFillStyle(options.colorDark);
  ctx.fillRect(margin, height - margin - positionSize, positionSize, positionSize);
  ctx.setFillStyle(options.colorLight);
  ctx.fillRect(margin + positionSize * 0.2, height - margin - positionSize * 0.8, positionSize * 0.6, positionSize * 0.6);
  ctx.setFillStyle(options.colorDark);
  ctx.fillRect(margin + positionSize * 0.3, height - margin - positionSize * 0.7, positionSize * 0.4, positionSize * 0.4);
  
  // 绘制数据点（模拟）
  const dataSize = width * 0.03;
  const dataGap = width * 0.02;
  const dataStartX = width * 0.25;
  const dataStartY = width * 0.25;
  const dataRows = 10;
  const dataCols = 10;
  
  for (let i = 0; i < dataRows; i++) {
    for (let j = 0; j < dataCols; j++) {
      // 随机生成数据点
      if (Math.random() > 0.5) {
        const x = dataStartX + j * (dataSize + dataGap);
        const y = dataStartY + i * (dataSize + dataGap);
        ctx.fillRect(x, y, dataSize, dataSize);
      }
    }
  }
  
  // 绘制文本（仅用于演示）
  ctx.setFontSize(12);
  ctx.setFillStyle(options.colorDark);
  ctx.fillText('扫描访问', width * 0.35, height * 0.9);
  
  // 执行绘制
  ctx.draw();
};

// 导出
module.exports = QRCode;
module.exports.correctLevel = QRErrorCorrectLevel;
