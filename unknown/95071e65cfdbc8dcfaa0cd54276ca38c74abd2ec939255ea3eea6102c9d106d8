// 请求队列管理测试页面
const houseApi = require('../../api/houseApi.js')
const communityApi = require('../../api/communityApi.js')
const userApi = require('../../api/userApi.js')

Page({
  data: {
    testResults: [],
    isTestingConcurrent: false,
    isTestingWithToken: false,
    isTestingWithoutToken: false
  },

  onLoad: function (options) {
    console.log('=== 请求队列管理测试页面加载 ===')
    this.addTestResult('页面加载完成', 'info')
    
    // 显示当前登录状态
    this.checkCurrentLoginStatus()
  },

  // 检查当前登录状态
  checkCurrentLoginStatus: function() {
    const userInfo = wx.getStorageSync('userInfo')
    const accessToken = wx.getStorageSync('access_token')
    
    if (userInfo && accessToken) {
      this.addTestResult('当前状态：已登录，有有效token', 'success')
    } else {
      this.addTestResult('当前状态：未登录或token无效', 'warning')
    }
  },

  // 添加测试结果
  addTestResult: function(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString()
    const result = {
      id: Date.now(),
      message: `[${timestamp}] ${message}`,
      type: type
    }
    
    this.setData({
      testResults: [result, ...this.data.testResults]
    })
    
    console.log(`测试结果: ${message}`)
  },

  // 清除测试结果
  clearResults: function() {
    this.setData({
      testResults: []
    })
  },

  // 测试1：有token时的并发请求
  testConcurrentWithToken: function() {
    this.setData({ isTestingConcurrent: true })
    this.addTestResult('开始测试：有token时的并发请求', 'info')
    
    const startTime = Date.now()
    
    // 同时发起多个请求
    const requests = [
      houseApi.getMyHouses().catch(e => ({ error: e, api: 'getMyHouses' })),
      communityApi.getCommunityList().catch(e => ({ error: e, api: 'getCommunityList' })),
      userApi.getUserInfo().catch(e => ({ error: e, api: 'getUserInfo' })),
      houseApi.getBuildingsByCommunity(1).catch(e => ({ error: e, api: 'getBuildingsByCommunity' })),
      houseApi.getRoomsByBuilding(1).catch(e => ({ error: e, api: 'getRoomsByBuilding' }))
    ]
    
    Promise.allSettled(requests)
      .then(results => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        let successCount = 0
        let errorCount = 0
        
        results.forEach((result, index) => {
          if (result.status === 'fulfilled' && !result.value.error) {
            successCount++
            this.addTestResult(`请求${index + 1}成功`, 'success')
          } else {
            errorCount++
            const errorMsg = result.value?.error?.message || result.reason?.message || '未知错误'
            this.addTestResult(`请求${index + 1}失败: ${errorMsg}`, 'error')
          }
        })
        
        this.addTestResult(`并发测试完成: ${successCount}成功, ${errorCount}失败, 耗时${duration}ms`, 'info')
        this.setData({ isTestingConcurrent: false })
      })
      .catch(error => {
        this.addTestResult(`并发测试异常: ${error.message}`, 'error')
        this.setData({ isTestingConcurrent: false })
      })
  },

  // 测试2：清除token后的请求（触发登录）
  testWithoutToken: function() {
    this.setData({ isTestingWithoutToken: true })
    this.addTestResult('开始测试：清除token后的请求', 'info')
    
    // 备份当前token
    const backupUserInfo = wx.getStorageSync('userInfo')
    const backupAccessToken = wx.getStorageSync('access_token')
    const backupRefreshToken = wx.getStorageSync('refresh_token')
    
    // 清除登录信息
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('access_token')
    wx.removeStorageSync('refresh_token')
    
    this.addTestResult('已清除登录信息，开始发起请求', 'warning')
    
    const startTime = Date.now()
    
    // 发起需要token的请求
    houseApi.getMyHouses()
      .then(result => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        this.addTestResult(`无token请求成功，耗时${duration}ms`, 'success')
        this.addTestResult('登录流程正常工作', 'success')
        
        // 恢复token（如果需要）
        if (backupUserInfo) wx.setStorageSync('userInfo', backupUserInfo)
        if (backupAccessToken) wx.setStorageSync('access_token', backupAccessToken)
        if (backupRefreshToken) wx.setStorageSync('refresh_token', backupRefreshToken)
        
        this.setData({ isTestingWithoutToken: false })
      })
      .catch(error => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        this.addTestResult(`无token请求失败: ${error.message}，耗时${duration}ms`, 'error')
        
        // 恢复token
        if (backupUserInfo) wx.setStorageSync('userInfo', backupUserInfo)
        if (backupAccessToken) wx.setStorageSync('access_token', backupAccessToken)
        if (backupRefreshToken) wx.setStorageSync('refresh_token', backupRefreshToken)
        
        this.setData({ isTestingWithoutToken: false })
      })
  },

  // 测试3：模拟token过期（401响应）
  testTokenExpired: function() {
    this.addTestResult('开始测试：模拟token过期', 'info')
    
    // 备份当前token
    const backupAccessToken = wx.getStorageSync('access_token')
    
    // 设置无效token
    wx.setStorageSync('access_token', 'invalid_token_for_test')
    
    this.addTestResult('已设置无效token，开始发起请求', 'warning')
    
    const startTime = Date.now()
    
    // 发起请求，应该触发token刷新或重新登录
    houseApi.getMyHouses()
      .then(result => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        this.addTestResult(`token过期处理成功，耗时${duration}ms`, 'success')
        this.addTestResult('token刷新/重新登录流程正常', 'success')
      })
      .catch(error => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        this.addTestResult(`token过期处理失败: ${error.message}，耗时${duration}ms`, 'error')
        
        // 恢复原token
        if (backupAccessToken) {
          wx.setStorageSync('access_token', backupAccessToken)
        }
      })
  },

  // 测试4：快速连续请求
  testRapidRequests: function() {
    this.addTestResult('开始测试：快速连续请求', 'info')
    
    const startTime = Date.now()
    const requestCount = 5
    const requests = []
    
    // 快速发起多个相同请求
    for (let i = 0; i < requestCount; i++) {
      requests.push(
        houseApi.getMyHouses()
          .then(result => ({ success: true, index: i }))
          .catch(error => ({ success: false, index: i, error: error.message }))
      )
    }
    
    Promise.all(requests)
      .then(results => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        const successCount = results.filter(r => r.success).length
        const failCount = results.filter(r => !r.success).length
        
        this.addTestResult(`快速连续请求完成: ${successCount}成功, ${failCount}失败, 耗时${duration}ms`, 'info')
        
        results.forEach(result => {
          if (result.success) {
            this.addTestResult(`请求${result.index + 1}成功`, 'success')
          } else {
            this.addTestResult(`请求${result.index + 1}失败: ${result.error}`, 'error')
          }
        })
      })
  },

  // 手动触发登录
  manualLogin: function() {
    this.addTestResult('开始手动登录', 'info')
    
    const app = getApp()
    app.userLogin()
      .then(result => {
        this.addTestResult('手动登录成功', 'success')
        this.checkCurrentLoginStatus()
      })
      .catch(error => {
        this.addTestResult(`手动登录失败: ${error.message}`, 'error')
      })
  },

  // 清除登录信息
  clearLogin: function() {
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('access_token')
    wx.removeStorageSync('refresh_token')
    wx.removeStorageSync('openid')
    wx.removeStorageSync('session_key')
    
    this.addTestResult('已清除所有登录信息', 'warning')
    this.checkCurrentLoginStatus()
  },

  // 查看存储信息
  viewStorage: function() {
    const userInfo = wx.getStorageSync('userInfo')
    const accessToken = wx.getStorageSync('access_token')
    const refreshToken = wx.getStorageSync('refresh_token')
    
    this.addTestResult('=== 当前存储信息 ===', 'info')
    this.addTestResult(`userInfo: ${userInfo ? '存在' : '不存在'}`, 'info')
    this.addTestResult(`access_token: ${accessToken ? '存在' : '不存在'}`, 'info')
    this.addTestResult(`refresh_token: ${refreshToken ? '存在' : '不存在'}`, 'info')
    
    if (accessToken) {
      this.addTestResult(`token前10位: ${accessToken.substring(0, 10)}...`, 'info')
    }
  }
})
