// pages/recycle/my-appointments/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    darkMode: false,
    appointments: [],
    showDetailModal: false,
    showCancelConfirm: false,
    currentAppointment: null,
    cancelAppointmentId: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息，判断是否为暗黑模式
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          darkMode: res.theme === 'dark'
        })
      }
    })

    // 加载预约数据
    this.loadAppointments()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时重新加载数据，以便获取最新预约
    this.loadAppointments()
  },

  /**
   * 加载预约数据
   */
  loadAppointments() {
    // 模拟从服务器获取预约数据
    // 实际应用中，这里应该调用API获取真实数据
    const mockAppointments = [
      {
        id: '1',
        orderNumber: 'WP38677288',
        status: '待上门',
        date: '2023-04-28',
        createTime: '2023-04-28 15:30:45',
        appointmentTime: '4月30日 周日 14点00分',
        name: '张三',
        phone: '13800138000',
        address: '北京市朝阳区建国路88号中央公园小区3号楼2单元501',
        wasteTypes: '纸类、塑料',
        weight: 10,
        remark: '纸箱较多，请带大一点的车',
        photos: [
          'https://img.yzcdn.cn/vant/cat.jpeg',
          'https://img.yzcdn.cn/vant/dog.jpeg'
        ]
      },
      {
        id: '2',
        orderNumber: 'WP38677123',
        status: '已完成',
        date: '2023-04-20',
        createTime: '2023-04-20 10:15:22',
        appointmentTime: '4月22日 周六 10点30分',
        name: '张三',
        phone: '13800138000',
        address: '北京市朝阳区建国路88号中央公园小区3号楼2单元501',
        wasteTypes: '金属、电子产品',
        weight: 5,
        remark: '',
        photos: []
      },
      {
        id: '3',
        orderNumber: 'WP38676999',
        status: '已取消',
        date: '2023-04-15',
        createTime: '2023-04-15 09:45:12',
        appointmentTime: '4月17日 周一 16点00分',
        name: '张三',
        phone: '13800138000',
        address: '北京市朝阳区建国路88号中央公园小区3号楼2单元501',
        wasteTypes: '玻璃',
        weight: 3,
        remark: '易碎物品，请小心搬运',
        photos: []
      }
    ]

    this.setData({
      appointments: mockAppointments
    })
  },

  /**
   * 查看预约详情
   */
  viewAppointmentDetail(e) {
    const id = e.currentTarget.dataset.id
    const appointment = this.data.appointments.find(item => item.id === id)

    if (appointment) {
      this.setData({
        currentAppointment: appointment,
        showDetailModal: true
      })
    }
  },

  /**
   * 隐藏详情弹窗
   */
  hideDetailModal() {
    this.setData({
      showDetailModal: false
    })
  },

  /**
   * 取消预约
   */
  cancelAppointment(e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      cancelAppointmentId: id,
      showCancelConfirm: true
    })
  },

  /**
   * 取消当前查看的预约
   */
  cancelCurrentAppointment() {
    if (this.data.currentAppointment) {
      this.setData({
        cancelAppointmentId: this.data.currentAppointment.id,
        showCancelConfirm: true,
        showDetailModal: false
      })
    }
  },

  /**
   * 隐藏取消确认弹窗
   */
  hideCancelConfirm() {
    this.setData({
      showCancelConfirm: false
    })
  },

  /**
   * 确认取消预约
   */
  confirmCancel() {
    const id = this.data.cancelAppointmentId

    if (!id) {
      return
    }

    // 显示加载提示
    wx.showLoading({
      title: '取消中...',
      mask: true
    })

    // 模拟API调用
    setTimeout(() => {
      // 更新本地数据
      const appointments = this.data.appointments.map(item => {
        if (item.id === id) {
          return { ...item, status: '已取消' }
        }
        return item
      })

      this.setData({
        appointments,
        showCancelConfirm: false
      })

      wx.hideLoading()

      wx.showToast({
        title: '预约已取消',
        icon: 'success'
      })
    }, 1000)
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url
    const urls = this.data.currentAppointment.photos

    wx.previewImage({
      current: url,
      urls
    })
  },

  /**
   * 导航到预约页面
   */
  navigateToAppointment() {
    wx.navigateTo({
      url: '/pages/recycle/appointment/index'
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    return false
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  }
})
