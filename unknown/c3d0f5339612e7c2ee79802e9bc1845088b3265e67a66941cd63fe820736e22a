// recycle.js
const util = require('../../utils/util.js')
const dateUtil = require('../../utils/dateUtil.js')

Page({
  data: {
    categories: [
      { id: 1, name: '废纸', icon: 'icon-paper', selected: false },
      { id: 2, name: '塑料', icon: 'icon-plastic', selected: false },
      { id: 3, name: '金属', icon: 'icon-metal', selected: false },
      { id: 4, name: '玻璃', icon: 'icon-glass', selected: false },
      { id: 5, name: '电器', icon: 'icon-appliance', selected: false },
      { id: 6, name: '家具', icon: 'icon-furniture', selected: false },
      { id: 7, name: '衣物', icon: 'icon-clothes', selected: false },
      { id: 8, name: '其他', icon: 'icon-other', selected: false }
    ],
    address: null,
    date: '',
    minDate: '',
    maxDate: '',
    timeIndex: 0,
    timeSlots: ['上午 9:00-12:00', '下午 14:00-18:00', '晚上 19:00-21:00'],
    remark: '',
    canSubmit: false,
    showAddressModal: false,
    addressList: [
      { id: 1, detail: '广州市天河区珠江新城星河湾小区1栋1单元101', name: '张先生', phone: '138****1234' },
      { id: 2, detail: '广州市天河区珠江新城星河湾小区1栋1单元102', name: '李女士', phone: '139****5678' }
    ],
    selectedAddressId: 0,
    showSuccessModal: false,
    orderNo: ''
  },

  onLoad: function() {
    // 设置日期范围
    const today = new Date()
    const minDate = dateUtil.formatDate(today)
    
    const maxDate = new Date()
    maxDate.setDate(today.getDate() + 30)
    
    this.setData({
      minDate,
      maxDate: dateUtil.formatDate(maxDate)
    })
    
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
    }
  },

  selectCategory: function(e) {
    const id = e.currentTarget.dataset.id
    const categories = this.data.categories.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected }
      }
      return item
    })
    
    this.setData({
      categories
    })
    
    this.checkCanSubmit()
  },

  selectAddress: function() {
    this.setData({
      showAddressModal: true,
      selectedAddressId: this.data.address ? this.data.address.id : 0
    })
  },

  closeAddressModal: function() {
    this.setData({
      showAddressModal: false
    })
  },

  selectAddressItem: function(e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      selectedAddressId: id
    })
  },

  confirmAddress: function() {
    if (this.data.selectedAddressId) {
      const address = this.data.addressList.find(item => item.id === this.data.selectedAddressId)
      this.setData({
        address,
        showAddressModal: false
      })
      
      this.checkCanSubmit()
    } else {
      wx.showToast({
        title: '请选择地址',
        icon: 'none'
      })
    }
  },

  addNewAddress: function() {
    wx.showToast({
      title: '新增地址功能开发中',
      icon: 'none'
    })
  },

  bindDateChange: function(e) {
    this.setData({
      date: e.detail.value
    })
    
    this.checkCanSubmit()
  },

  bindTimeChange: function(e) {
    this.setData({
      timeIndex: e.detail.value
    })
    
    this.checkCanSubmit()
  },

  inputRemark: function(e) {
    this.setData({
      remark: e.detail.value
    })
  },

  checkCanSubmit: function() {
    const hasSelectedCategory = this.data.categories.some(item => item.selected)
    const hasAddress = !!this.data.address
    const hasDate = !!this.data.date
    
    this.setData({
      canSubmit: hasSelectedCategory && hasAddress && hasDate
    })
  },

  submitOrder: function() {
    if (!this.data.canSubmit) return
    
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }
    
    // 生成订单号
    const orderNo = 'R' + Date.now().toString().substr(3)
    
    // 显示加载中
    wx.showLoading({
      title: '提交中...',
      mask: true
    })
    
    // 模拟提交
    setTimeout(() => {
      wx.hideLoading()
      
      this.setData({
        showSuccessModal: true,
        orderNo
      })
    }, 1500)
  },

  closeSuccessModal: function() {
    this.setData({
      showSuccessModal: false
    })
    
    // 重置表单
    this.setData({
      categories: this.data.categories.map(item => ({ ...item, selected: false })),
      date: '',
      timeIndex: 0,
      remark: '',
      canSubmit: false
    })
  },

  stopPropagation: function(e) {
    // 阻止事件冒泡
  }
})
