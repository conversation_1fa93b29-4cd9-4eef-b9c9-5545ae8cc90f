// pages/recycle/guide/detail.js
Page({
  data: {
    darkMode: false,
    typeId: '',
    typeInfo: {}
  },

  onLoad: function(options) {
    const typeId = options.type || 'recyclable'
    this.setData({
      typeId: typeId
    })
    
    // 获取分类详情
    this.getTypeInfo(typeId)
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 获取分类详情
  getTypeInfo: function(typeId) {
    // 分类详情数据
    const typeInfoMap = {
      recyclable: {
        id: 'recyclable',
        name: '可回收物',
        color: '#2196f3',
        description: '可循环利用的废弃物品',
        guidelines: [
          '投放前请将垃圾中的不可回收物分离出来',
          '纸类应尽量叠放整齐，避免揉皱',
          '饮料瓶、罐应清空内容物，并尽量压扁',
          '玻璃制品轻拿轻放，避免破碎',
          '确保物品清洁干燥，无污染'
        ],
        items: [
          '纸类（报纸、杂志、书籍、纸板箱）',
          '塑料（饮料瓶、塑料袋、塑料盒）',
          '金属（易拉罐、罐头盒、铁制品）',
          '玻璃（玻璃瓶、玻璃杯）',
          '织物（旧衣服、床单、窗帘）',
          '电子产品（废弃电器、电子设备）'
        ],
        notes: [
          '纸巾、卫生纸等已被污染的纸制品不属于可回收物',
          '塑料袋应清洁干燥后投放',
          '大型电器应联系专业回收机构处理',
          '玻璃制品破碎后应用纸包好再投放，防止割伤清洁工人'
        ]
      },
      hazardous: {
        id: 'hazardous',
        name: '有害垃圾',
        color: '#f44336',
        description: '对人体健康或自然环境造成直接或潜在危害的废弃物',
        guidelines: [
          '投放时请轻拿轻放，防止破损或泄漏',
          '废电池应放入专用回收箱',
          '过期药品应去药店或医院的专用回收箱投放',
          '灯管等易破损的有害垃圾应妥善包装后投放',
          '有害垃圾应单独投放，不要与其他垃圾混合'
        ],
        items: [
          '废电池（干电池、纽扣电池、充电电池）',
          '废灯管（日光灯管、节能灯、荧光灯）',
          '过期药品（药片、胶囊、药水、药膏）',
          '废油漆（油漆桶、调色剂）',
          '废杀虫剂（杀虫喷雾、灭蚊剂）',
          '废水银产品（温度计、血压计）',
          '废化妆品（指甲油、染发剂）'
        ],
        notes: [
          '有害垃圾投放时应确保包装完好，防止有害物质泄漏',
          '废弃的充电宝、电子烟等含电池产品属于有害垃圾',
          '家用化学品如洗涤剂、消毒剂等使用完毕后的包装也属于有害垃圾',
          '有害垃圾应尽量保持原包装，便于识别和处理'
        ]
      },
      kitchen: {
        id: 'kitchen',
        name: '厨余垃圾',
        color: '#4caf50',
        description: '易腐烂的生物质废弃物',
        guidelines: [
          '投放前尽量沥干水分，减少异味和细菌滋生',
          '去除厨余垃圾中的塑料袋、餐巾纸等杂质',
          '大块厨余垃圾应切碎后再投放',
          '有包装的过期食品应将包装去除后分类投放',
          '厨余垃圾应及时投放，不宜存放过久'
        ],
        items: [
          '食物残渣（剩菜剩饭、骨头、肉类）',
          '果皮（水果皮、果核、果肉）',
          '蔬菜（菜叶、菜根、菜梗）',
          '茶叶渣、咖啡渣',
          '宠物饲料残余',
          '过期食品（去除包装）',
          '鲜花、绿植（枯萎的花、落叶）'
        ],
        notes: [
          '厨余垃圾中不应混入塑料袋、一次性餐具等不可降解物品',
          '贝壳、蛋壳等硬质物品属于厨余垃圾，但降解较慢',
          '大量的食用油应收集后交专门机构回收，不要直接倒入下水道',
          '厨余垃圾是制作堆肥的重要原料，可用于农业生产'
        ]
      },
      other: {
        id: 'other',
        name: '其他垃圾',
        color: '#9e9e9e',
        description: '除可回收物、有害垃圾、厨余垃圾以外的其他生活废弃物',
        guidelines: [
          '投放前请确保垃圾袋口扎紧',
          '尽量减少其他垃圾的产生量',
          '有尖锐物品应包裹后投放，防止刺破垃圾袋',
          '确保投放的垃圾不含有可回收、有害或厨余成分',
          '其他垃圾应定点投放，不要随意丢弃'
        ],
        items: [
          '卫生纸、纸巾、尿不湿',
          '烟头、烟灰',
          '污染严重的塑料袋、包装袋',
          '陶瓷碎片、花盆',
          '一次性餐具（已被污染）',
          '灰尘、头发、指甲',
          '用过的纸尿裤、卫生巾'
        ],
        notes: [
          '其他垃圾是指除可回收物、有害垃圾、厨余垃圾以外的所有生活垃圾',
          '尽量减少使用一次性用品，降低其他垃圾的产生量',
          '其他垃圾填埋或焚烧处理后会对环境造成一定影响，应尽量减少产生',
          '有条件的地区可将其他垃圾进行焚烧发电，变废为宝'
        ]
      }
    }
    
    // 设置分类详情
    this.setData({
      typeInfo: typeInfoMap[typeId] || typeInfoMap.recyclable
    })
  }
})
