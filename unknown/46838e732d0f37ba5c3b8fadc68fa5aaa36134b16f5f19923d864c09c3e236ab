<!--失物招领详情页面-->
<view class="container">

  <!-- 物品基本信息 -->
  <view class="item-header">
    <view class="item-title">{{item.title}}</view>
    <view class="item-tag {{item.type === 'lost' ? 'lost-tag' : 'found-tag'}}">
      {{item.type === 'lost' ? '寻找中' : '已拾到'}}
    </view>
  </view>

  <!-- 物品图片 -->
  <view class="item-images" wx:if="{{item.images && item.images.length > 0}}">
    <swiper class="images-swiper" indicator-dots="{{item.images.length > 1}}" autoplay="{{false}}" circular="{{true}}">
      <swiper-item wx:for="{{item.images}}" wx:for-item="image" wx:key="index" bindtap="previewImage" data-url="{{image}}">
        <image class="swiper-image" src="{{image}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 物品详情卡片 -->
  <info-card title="详细信息">
    <view class="info-item">
      <view class="info-label">{{item.type === 'lost' ? '丢失时间' : '拾获时间'}}</view>
      <view class="info-value">{{item.time}}</view>
    </view>

    <view class="info-item">
      <view class="info-label">{{item.type === 'lost' ? '丢失地点' : '拾获地点'}}</view>
      <view class="info-value">{{item.location}}</view>
    </view>

    <view class="info-item">
      <view class="info-label">物品描述</view>
      <view class="info-value description">{{item.description}}</view>
    </view>

    <view class="info-item" wx:if="{{item.features && item.features.length > 0}}">
      <view class="info-label">物品特征</view>
      <view class="info-value">
        <view class="feature-item" wx:for="{{item.features}}" wx:for-item="feature" wx:key="index">
          <view class="feature-dot"></view>
          <text>{{feature}}</text>
        </view>
      </view>
    </view>
  </info-card>

  <!-- 联系方式卡片 -->
  <info-card title="联系方式">
    <view class="contact-notice">
      <image class="notice-icon" src="/images/icons/info.svg" mode="aspectFit"></image>
      <text>为保护隐私，请通过以下方式联系</text>
    </view>

    <view class="contact-options">
      <button class="contact-btn" bindtap="contactProperty">
        <image class="btn-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
        <text>联系物业</text>
      </button>

      <button class="contact-btn" open-type="contact">
        <image class="btn-icon" src="/images/icons/message.svg" mode="aspectFit"></image>
        <text>在线客服</text>
      </button>
    </view>
  </info-card>

  <!-- 温馨提示 -->
  <info-card title="温馨提示">
    <view class="tips-content">
      <view class="tips-item">1. 认领或归还物品时，请确认物品特征，避免认错。</view>
      <view class="tips-item">2. 建议在公共场所进行物品交接，注意人身安全。</view>
      <view class="tips-item">3. 本平台仅提供信息展示服务，不承担物品保管责任。</view>
    </view>
  </info-card>
</view>
