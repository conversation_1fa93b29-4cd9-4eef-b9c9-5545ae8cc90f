<!--智慧通行页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="header">
    <view class="title">智慧通行</view>
    <view class="subtitle">便捷管理您的门禁卡和访客</view>
  </view>
  
  <!-- 标签页切换 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'cards' ? 'active' : ''}}" bindtap="switchTab" data-tab="cards">
      <view class="tab-text">我的门禁卡</view>
    </view>
    <view class="tab {{activeTab === 'visitors' ? 'active' : ''}}" bindtap="switchTab" data-tab="visitors">
      <view class="tab-text">访客记录</view>
    </view>
  </view>
  
  <!-- 门禁卡列表 -->
  <view class="tab-content" wx:if="{{activeTab === 'cards'}}">
    <view class="card-list">
      <view class="card-item" wx:for="{{accessCards}}" wx:key="id" bindtap="viewCardDetail" data-id="{{item.id}}">
        <view class="card-icon">
          <view class="card-icon-inner">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
              <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
            </svg>
          </view>
        </view>
        <view class="card-info">
          <view class="card-name">{{item.name}}</view>
          <view class="card-number">卡号: {{item.cardNumber}}</view>
          <view class="card-last-used">上次使用: {{item.lastUsed}}</view>
        </view>
        <view class="card-status {{item.status}}">
          {{item.status === 'active' ? '已启用' : '已禁用'}}
        </view>
        <view class="card-actions">
          <view class="card-action-btn {{item.status === 'active' ? 'disable' : 'enable'}}" 
                catchtap="toggleCardStatus" 
                data-id="{{item.id}}">
            {{item.status === 'active' ? '禁用' : '启用'}}
          </view>
        </view>
      </view>
    </view>
    
    <view class="no-data" wx:if="{{accessCards.length === 0}}">
      <view class="no-data-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48" fill="none" stroke="#ccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
        </svg>
      </view>
      <view class="no-data-text">暂无门禁卡</view>
    </view>
    
    <view class="add-btn" bindtap="addAccessCard">
      <view class="add-icon">+</view>
      <view class="add-text">添加门禁卡</view>
    </view>
  </view>
  
  <!-- 访客记录列表 -->
  <view class="tab-content" wx:if="{{activeTab === 'visitors'}}">
    <view class="visitor-list">
      <view class="visitor-item" wx:for="{{visitors}}" wx:key="id" bindtap="viewVisitorDetail" data-id="{{item.id}}">
        <view class="visitor-icon">
          <view class="visitor-icon-inner">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </view>
        </view>
        <view class="visitor-info">
          <view class="visitor-name">{{item.name}}</view>
          <view class="visitor-phone">电话: {{item.phone}}</view>
          <view class="visitor-time">访问时间: {{item.visitTime}}</view>
        </view>
        <view class="visitor-status {{item.status}}">
          {{item.status === 'upcoming' ? '即将到访' : item.status === 'completed' ? '已完成' : '已取消'}}
        </view>
        <view class="visitor-actions" wx:if="{{item.status === 'upcoming'}}">
          <view class="visitor-action-btn qrcode" 
                catchtap="showVisitorQRCode" 
                data-id="{{item.id}}">
            查看二维码
          </view>
        </view>
      </view>
    </view>
    
    <view class="no-data" wx:if="{{visitors.length === 0}}">
      <view class="no-data-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48" fill="none" stroke="#ccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </view>
      <view class="no-data-text">暂无访客记录</view>
    </view>
    
    <view class="add-btn" bindtap="addVisitor">
      <view class="add-icon">+</view>
      <view class="add-text">添加访客</view>
    </view>
  </view>
</view>
