/* 物业员工登记页面样式 */
.container {
  padding: 0;
  min-height: 100vh;
  background-color: #f9fafb;
  padding-top: 24rpx;
}

/* 错误提示样式 */
.error-message {
  background-color: rgba(250, 62, 62, 0.1);
  color: #fa3e3e;
  padding: 24rpx;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(250, 62, 62, 0.1);
}

/* 表单标题 */
.form-title {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1b21;
}

.icon-property {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 表单样式 */
.auth-form {
  margin: 0 32rpx 48rpx;
  padding: 48rpx;
  background-color: #fff;
  border-radius: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  color: #1a1b21;
  font-weight: 500;
}

.required {
  color: #fa3e3e;
  margin-left: 8rpx;
}

.form-input {
  position: relative;
  width: 100%;
  height: 96rpx;
  background-color: #f6f6f7;
  border: 2rpx solid transparent;
  border-radius: 24rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input input {
  width: 100%;
  height: 100%;
  font-size: 30rpx;
  color: #1a1b21;
}

.form-input.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input.error {
  border-color: #fa3e3e;
  background-color: rgba(250, 62, 62, 0.05);
}

.form-input:focus-within {
  border-color: #0c8ee7;
  background-color: rgba(12, 142, 231, 0.05);
  box-shadow: 0 0 0 4rpx rgba(12, 142, 231, 0.1);
}

.check-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.form-tip {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 12rpx;
  padding-left: 8rpx;
}

/* 选择器样式 */
.form-input.picker {
  display: flex;
  align-items: center;
  position: relative;
}

.form-input.picker::after {
  content: "";
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.picker-text {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #1a1b21;
  padding-right: 40rpx;
}

.picker-text.placeholder {
  color: #8E8E93;
}

/* 上传区域样式 */
.upload-area {
  width: 100%;
  height: 240rpx;
  border: 2rpx dashed #bae0fd;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(12, 142, 231, 0.03);
  transition: all 0.3s ease;
}

.upload-area:active {
  background-color: rgba(12, 142, 231, 0.08);
  border-color: #0c8ee7;
}

.upload-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-text {
  font-size: 30rpx;
  color: #1a1b21;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.upload-desc {
  font-size: 24rpx;
  color: #8E8E93;
}

.upload-area.has-image {
  border: none;
  padding: 0;
  background-color: transparent;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.auth-btn {
  width: 100%;
  height: 108rpx;
  background: linear-gradient(135deg, #0c8ee7, #0072c6);
  color: white;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 48rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(12, 142, 231, 0.2);
  transition: all 0.3s ease;
}

.auth-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(12, 142, 231, 0.2);
}

.auth-btn[disabled] {
  background: #e0e0e0;
  color: #8E8E93;
  box-shadow: none;
}

/* 隐私政策 */
.privacy-policy {
  text-align: center;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.6;
  padding: 0 32rpx;
}

.policy-link {
  color: #0c8ee7;
  font-weight: 500;
}

/* 加载动画 */
.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .form-title {
  color: #f5f5f7;
}

.darkMode .icon-property {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230A84FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

.darkMode .auth-form {
  background-color: #2c2c2e;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.2);
}

.darkMode .form-label {
  color: #f5f5f7;
}

.darkMode .form-input {
  background-color: #3a3a3c;
  border-color: transparent;
}

.darkMode .form-input input {
  color: #f5f5f7;
}

.darkMode .form-input.valid {
  border-color: #30d158;
  background-color: rgba(48, 209, 88, 0.1);
}

.darkMode .form-input.error {
  border-color: #ff453a;
  background-color: rgba(255, 69, 58, 0.1);
}

.darkMode .form-input:focus-within {
  border-color: #0A84FF;
  background-color: rgba(10, 132, 255, 0.1);
  box-shadow: 0 0 0 4rpx rgba(10, 132, 255, 0.2);
}

.darkMode .check-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2330d158' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
}

.darkMode .form-tip {
  color: #8e8e93;
}

.darkMode .form-input.picker::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.darkMode .picker-text {
  color: #f5f5f7;
}

.darkMode .picker-text.placeholder {
  color: #8e8e93;
}

.darkMode .upload-area {
  border-color: rgba(10, 132, 255, 0.3);
  background-color: rgba(10, 132, 255, 0.05);
}

.darkMode .upload-area:active {
  background-color: rgba(10, 132, 255, 0.1);
  border-color: #0A84FF;
}

.darkMode .upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230A84FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

.darkMode .upload-text {
  color: #f5f5f7;
}

.darkMode .upload-desc {
  color: #8e8e93;
}

.darkMode .uploaded-image {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.darkMode .auth-btn {
  background: linear-gradient(135deg, #0A84FF, #0072c6);
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.3);
}

.darkMode .auth-btn:active {
  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);
}

.darkMode .auth-btn[disabled] {
  background: #3a3a3c;
  color: #8e8e93;
  box-shadow: none;
}

.darkMode .privacy-policy {
  color: #8e8e93;
}

.darkMode .policy-link {
  color: #0A84FF;
}
