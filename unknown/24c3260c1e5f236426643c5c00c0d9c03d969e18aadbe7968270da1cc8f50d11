# 访客管理API使用说明

## API接口说明

### 1. 分页查询访客列表
```javascript
const visitorsApi = require('../api/visitorsApi.js');

// 基本查询
visitorsApi.getVisitorList({
  pageNum: 1,
  pageSize: 10
}).then(res => {
  console.log('访客列表：', res);
});

// 带条件查询
visitorsApi.getVisitorList({
  pageNum: 1,
  pageSize: 10,
  visitorName: '张三',
  status: 'wait_visit'
}).then(res => {
  console.log('筛选后的访客列表：', res);
});
```

### 2. 新增访客
```javascript
const visitorData = {
  visitorName: '张三',
  phone: '13800138000',
  vehicleNumber: '京A12345', // 可选
  note: '来访目的：看房', // 可选
  stayDuration: 2, // 停留时长（小时）
  timeUnit: 'hour', // 时间单位
  visitTime: '2023-12-01T14:00:00.000Z', // ISO格式
  communityId: 1
};

visitorsApi.addVisitor(visitorData).then(res => {
  console.log('新增访客结果：', res);
});
```

### 3. 编辑访客
```javascript
const updateData = {
  id: 123,
  visitorName: '张三',
  phone: '13800138000',
  vehicleNumber: '京A12345',
  note: '更新后的备注',
  stayDuration: 3,
  timeUnit: 'hour',
  visitTime: '2023-12-01T14:00:00.000Z',
  communityId: 1
};

visitorsApi.updateVisitor(updateData).then(res => {
  console.log('编辑访客结果：', res);
});
```

### 4. 删除访客
```javascript
visitorsApi.deleteVisitor(123).then(res => {
  console.log('删除访客结果：', res);
});
```

### 5. 获取访客详情
```javascript
visitorsApi.getVisitorDetail(123).then(res => {
  console.log('访客详情：', res);
});
```

### 6. 更新访客状态
```javascript
visitorsApi.updateVisitorStatus(123, 'visited').then(res => {
  console.log('更新状态结果：', res);
});
```

## 状态字典说明

访客状态使用字典管理，通过 `util.getDictByNameEn('visitor_status')` 获取：

- `wait_visit`: 待到访
- `visited`: 已到访
- `expire`: 已过期
- `canceled`: 已取消

## 数据字段说明

### API接口字段（与后端一致）
- `id`: 访客ID
- `visitorName`: 访客姓名
- `phone`: 手机号码
- `vehicleNumber`: 车牌号（可选）
- `note`: 备注信息（可选）
- `stayDuration`: 停留时长（数字）
- `timeUnit`: 时间单位（hour/day）
- `visitTime`: 访问时间（ISO格式）
- `communityId`: 社区ID
- `status`: 访客状态
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 页面显示处理
在页面中会自动计算：
- `endTime`: 结束时间（根据visitTime + stayDuration计算）

## 注意事项

1. **时间格式**:
   - API接口使用ISO格式字符串
   - 页面显示使用YYYY-MM-DD HH:MM:SS格式
   - 统一使用`dateUtil.formatISOToDateTime()`进行格式化
2. **状态管理**: 使用字典数据进行状态判断和显示
3. **错误处理**: 所有API调用都应包含错误处理
4. **加载状态**: 在API调用期间显示加载状态
5. **数据验证**: 提交前进行必要的数据验证
6. **路径别名**: 使用`@/`路径别名进行模块导入
7. **日期工具**: 所有日期处理操作使用`utils/dateUtil.js`中的方法

## 页面修改说明

### 访客列表页面 (servicePackage/pages/visitor/list/index.js)
- 使用字典数据处理状态显示
- 调用API进行删除操作
- 去除了不存在的字段引用

### 访客注册页面 (servicePackage/pages/visitor/registration/index.js)
- 使用API创建访客
- 数据格式符合接口要求
- 添加了加载状态和错误处理

### WXML模板修改
- 使用正确的字段名称（visitorName, vehicleNumber等）
- 状态判断使用正确的状态值（wait_visit而不是pending）
- 显示备注和车牌信息
