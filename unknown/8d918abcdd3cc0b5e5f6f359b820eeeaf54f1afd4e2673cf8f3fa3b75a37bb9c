/* 实名认证页面样式 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #252f3f;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: auto;
  max-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.icon-check, .icon-user, .icon-property {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-check {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'/%3E%3Cpath d='M22 4 12 14.01l-3-3'/%3E%3C/svg%3E");
}

.icon-user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='12' cy='7' r='4'/%3E%3C/svg%3E");
}

.icon-property {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='8.5' cy='7' r='4'/%3E%3Cpath d='M20 8v6M17 11h6'/%3E%3C/svg%3E");
}

/* 进度条 */
.progress-card {
  animation: fadeIn 0.5s ease-out;
}

.progress-track {
  position: relative;
  margin-bottom: 12px;
}

.progress-track::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e5e7eb;
  z-index: 1;
}

.progress-track::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  height: 2px;
  background-color: #0c8ee7;
  z-index: 2;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 根据当前步骤设置进度条宽度 */
.step:nth-child(1).active ~ .step:nth-child(2):not(.active) ~ .step:nth-child(3):not(.active) ~ .progress-track::after {
  width: 0%;
}

.step:nth-child(1).active ~ .step:nth-child(2).active ~ .step:nth-child(3):not(.active) ~ .progress-track::after {
  width: 50%;
}

.step:nth-child(1).active ~ .step:nth-child(2).active ~ .step:nth-child(3).active ~ .progress-track::after {
  width: 100%;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 3;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
}

.step-dot {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step.active .step-dot {
  background-color: #0c8ee7;
  border-color: #0c8ee7;
  color: white;
  box-shadow: 0 0 0 4px rgba(12, 142, 231, 0.2);
}

.step.completed .step-dot {
  background-color: #3aad57;
  border-color: #3aad57;
  color: white;
}

.step-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

/* 表单区域 */
.form-card {
  animation: slideUp 0.5s ease-out;
}

/* 分段控制器 */
.segmented-control {
  display: flex;
  width: 100%;
  height: 44px;
  background-color: #f4f5f7;
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 2px;
  position: relative;
  overflow: hidden;
}

.segment {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  position: relative;
  z-index: 10;
}

.segment.active {
  color: #ffffff;
  font-weight: 600;
}

.segment-slider {
  position: absolute;
  height: calc(100% - 4px);
  width: calc(50% - 4px);
  left: 2px;
  top: 2px;
  background-color: #0c8ee7;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 表单滑动切换 */
.form-slide {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  width: 100%;
  opacity: 0;
  transform: translateX(30px);
  pointer-events: none;
  left: 0;
  display: none;
}

.form-slide.active {
  opacity: 1;
  transform: translateX(0);
  position: relative;
  pointer-events: all;
  display: block;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
}

.required {
  color: #fa3e3e;
  margin-left: 4px;
}

.optional {
  color: #6b7280;
  font-size: 12px;
  margin-left: 4px;
  font-weight: normal;
}

.form-input {
  position: relative;
  width: 100%;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
  width: 100%;
}

.form-input.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input.error {
  border-color: #fa3e3e;
}

/* 带验证码的输入框 */
.form-input-with-code {
  position: relative;
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  transition: all 0.3s;
}

/* 验证码输入框 */
.form-input-with-verify-code {
  position: relative;
  width: 100%;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input-with-verify-code input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
}

.form-input-with-verify-code .verify-code-btn {
  height: 36px;
  min-width: 100px;
  margin-right: -8px;
  border-radius: 8px;
}

.form-input-with-verify-code.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input-with-verify-code.error {
  border-color: #fa3e3e;
}

.phone-input {
  flex: 1;
  height: 100%;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.phone-input input {
  flex: 1;
  height: 100%;
  font-size: 15px;
  color: #1f2937;
}

.form-input-with-code.valid .phone-input {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input-with-code.error .phone-input {
  border-color: #fa3e3e;
}

.verify-code-btn {
  height: 100%;
  min-width: 110px;
  background-color: #0072c6;
  color: white;
  font-size: 14px;
  border-radius: 12px;
  border: none;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.verify-code-btn.sent {
  background-color: #9ca3af;
}

.verify-code-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}

/* 文本域 */
.form-textarea {
  position: relative;
  width: 100%;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-textarea textarea {
  width: 100%;
  height: 100px;
  font-size: 15px;
  color: #1f2937;
  line-height: 1.5;
  box-sizing: border-box;
}

.textarea-counter {
  align-self: flex-end;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.check-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

/* 选择器 */
.form-selector {
  width: 100%;
  min-height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-selector.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-selector.error {
  border-color: #fa3e3e;
}

.placeholder {
  color: #9fa6b2;
}

.arrow-icon {
  font-size: 12px;
  color: #9fa6b2;
}

/* 上传区域 */
.upload-area {
  width: 100%;
  height: 176px;
  border: 2px dashed #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.upload-icon {
  width: 56px;
  height: 56px;
  margin-bottom: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d2d6dc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M17 8l-5-5-5 5M12 3v12'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 4px;
}

.upload-tip {
  font-size: 12px;
  color: #6b7280;
}

.uploaded-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 54px;
  background-color: #0072c6;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  margin-top: 32px;
  box-shadow: 0 4px 10px rgba(12, 142, 231, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.submit-btn:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(12, 142, 231, 0.2);
}

/* 隐私提示 */
.privacy-tip {
  margin-top: 20px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.6;
}

.link {
  color: #0072c6;
}

/* 选择器弹窗 */
.selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s, visibility 0.4s;
}

.selector-overlay.active {
  opacity: 1;
  visibility: visible;
}

.selector-container {
  width: 100%;
  background-color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
}

.selector-overlay.active .selector-container {
  transform: translateY(0);
}

.selector-header {
  padding: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.close-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #0072c6;
  font-size: 16px;
  font-weight: 500;
}

.selector-content {
  max-height: 350px;
}

.selector-item {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-item.active {
  background-color: rgba(12, 142, 231, 0.05);
}

.selector-item-content {
  flex: 1;
}

.selector-item-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
}

.selector-item-subtitle {
  font-size: 12px;
}

.selector-item-subtitle.verified {
  color: #3aad57;
}

.selector-item-subtitle.unverified {
  color: #fa3e3e;
}

.selector-item-check {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-tip {
  padding: 32px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d2d6dc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'/%3E%3Cpath d='M9 22V12h6v10'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
}

.selector-footer {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  width: 100%;
  height: 48px;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  border: none;
}

/* 弹窗 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s, visibility 0.4s;
}

.dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.dialog-container {
  width: 320px;
  background-color: white;
  border-radius: 24px;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.dialog-overlay.active .dialog-container {
  transform: scale(1);
}

.dialog-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'/%3E%3Cpath d='M9 22V12h6v10'/%3E%3C/svg%3E");
  background-size: 36px;
  background-repeat: no-repeat;
  background-position: center;
}

.dialog-icon.success {
  background-color: #f2fbf4;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.dialog-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer.single {
  border-top: 1px solid #e5e7eb;
}

.dialog-btn {
  flex: 1;
  padding: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.dialog-btn.cancel {
  border-right: 1px solid #e5e7eb;
  color: #6b7280;
}

.dialog-btn.confirm {
  color: #0072c6;
  font-weight: 600;
}

/* 表单步骤 */
.form-step {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  width: 100%;
  display: none;
  opacity: 0;
}

.form-step.active {
  opacity: 1;
  transform: translateX(0);
  display: block;
}

.form-step.hidden {
  display: none;
  opacity: 0;
}

/* 表单错误提示 */
.form-error {
  color: #fa3e3e;
  font-size: 12px;
  margin-top: 4px;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

/* 隐私协议 */
.privacy-agreement {
  margin: 16px 0;
}

.privacy-agreement .checkbox {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.privacy-agreement checkbox {
  margin-right: 8px;
  transform: scale(0.8);
}

/* 按钮组 */
.form-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  margin-bottom: 16px;
}

.prev-step-btn {
  flex: 1;
  height: 54px;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.next-step-btn {
  width: 100%;
  height: 54px;
  background-color: #0072c6;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  margin-top: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 10px rgba(12, 142, 231, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.next-step-btn:active, .prev-step-btn:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.next-step-btn[disabled], .submit-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shake {
  10%, 90% {
    transform: translateX(-1px);
  }
  20%, 80% {
    transform: translateX(2px);
  }
  30%, 50%, 70% {
    transform: translateX(-2px);
  }
  40%, 60% {
    transform: translateX(1px);
  }
}

/* 认证方式选择器 */
.auth-method-selector {
  display: flex;
  gap: 12px;
  width: 100%;
  margin-bottom: 8px;
}

.auth-method {
  flex: 1;
  height: 90px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.auth-method.active {
  border-color: #0072c6;
  background-color: rgba(0, 114, 198, 0.05);
}

.auth-method.active::after {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: #0072c6;
  clip-path: polygon(100% 0, 0% 100%, 100% 100%);
}

.auth-method-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.online-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230072c6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 8h.01'/%3E%3Crect x='3' y='3' width='18' height='18' rx='2'/%3E%3Cpath d='M3 14l4-4a3 3 0 0 1 3 0l7 7'/%3E%3Cpath d='M13 12l2-2a3 3 0 0 1 3 0l3 3'/%3E%3C/svg%3E");
}

.offline-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230072c6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Cline x1='3' y1='9' x2='21' y2='9'/%3E%3Cline x1='9' y1='21' x2='9' y2='9'/%3E%3C/svg%3E");
}

.auth-method-text {
  display: flex;
  flex-direction: column;
}

.auth-method-title {
  font-size: 15px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.auth-method-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 日期时间选择器 */
.date-selector {
  display: flex;
  gap: 8px;
  padding: 16px;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1px solid #e5e7eb;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 80px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  padding: 8px 0;
  transition: all 0.3s;
  flex-shrink: 0;
}

.date-item.active {
  border-color: #0072c6;
  background-color: rgba(0, 114, 198, 0.05);
}

.date-weekday {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.date-day {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.date-month {
  font-size: 12px;
  color: #6b7280;
}

.time-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px;
}

.time-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  padding: 8px;
  transition: all 0.3s;
}

.time-slot.active {
  border-color: #0072c6;
  background-color: rgba(0, 114, 198, 0.05);
}

.time-slot.disabled {
  opacity: 0.5;
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.time-text {
  font-size: 15px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.time-status {
  font-size: 12px;
  color: #ef4444;
}

.confirm-btn {
  width: 100%;
  height: 48px;
  background-color: #0072c6;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.confirm-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}
