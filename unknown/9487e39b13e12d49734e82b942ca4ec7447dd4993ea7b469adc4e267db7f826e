// 失物招领组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    lostFoundFilter: 'all', // 'all', 'lost' 或 'found'
    lostFound: [
      {
        id: 1,
        type: 'lost',
        title: '丢失黑色钱包',
        description: '昨天在小区中央广场附近丢失一个黑色钱包，内有身份证、银行卡等重要证件，急需找回，酬谢500元。',
        time: '2023-06-15 18:30',
        location: '中央广场',
        contact: '张先生 (138****1234)',
        images: ['https://img.yzcdn.cn/vant/cat.jpeg']
      },
      {
        id: 2,
        type: 'found',
        title: '捡到一串钥匙',
        description: '今天早上在小区东门附近捡到一串钥匙，有房门钥匙和一个小熊钥匙扣，请失主尽快认领。',
        time: '2023-06-16 08:15',
        location: '小区东门',
        contact: '李女士 (139****5678)',
        images: ['https://img.yzcdn.cn/vant/cat.jpeg']
      },
      {
        id: 3,
        type: 'lost',
        title: '丢失儿童眼镜',
        description: '孩子在小区游乐场玩耍时丢失了一副蓝色儿童眼镜，框架是蓝色的，镜片有一定度数，对孩子学习很重要，请好心人归还。',
        time: '2023-06-14 16:45',
        location: '儿童游乐场',
        contact: '王女士 (137****9012)',
        images: []
      },
      {
        id: 4,
        type: 'found',
        title: '捡到一部手机',
        description: '在小区健身区附近捡到一部黑色手机，已锁屏，请失主提供解锁方式和手机型号等信息认领。',
        time: '2023-06-15 20:10',
        location: '健身区',
        contact: '赵先生 (135****3456)',
        images: ['https://img.yzcdn.cn/vant/cat.jpeg']
      },
      {
        id: 5,
        type: 'lost',
        title: '丢失金色手镯',
        description: '在小区游泳池更衣室丢失一个金色手镯，是家族传下来的，有很重要的纪念意义，寻回必有重谢。',
        time: '2023-06-13 14:20',
        location: '游泳池更衣室',
        contact: '刘女士 (136****7890)',
        images: []
      }
    ],
    filteredLostFound: []
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      // 初始化筛选后的失物招领列表
      this.filterLostFound()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 切换失物招领筛选
    switchLostFoundFilter: function(e) {
      const filter = e.currentTarget.dataset.filter
      this.setData({
        lostFoundFilter: filter
      })
      this.filterLostFound()
    },

    // 筛选失物招领列表
    filterLostFound: function() {
      const { lostFound, lostFoundFilter } = this.data
      let filteredList = lostFound

      if (lostFoundFilter !== 'all') {
        filteredList = lostFound.filter(item => item.type === lostFoundFilter)
      }

      this.setData({
        filteredLostFound: filteredList
      })
    },

    // 导航到失物招领详情页
    navigateToLostFoundDetail: function(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/communityPackage/pages/community/service/info/lostfound-detail?id=${id}`,
        fail: (err) => {
          console.error('导航失败:', err)
          if (err.errMsg && err.errMsg.indexOf('webviewId') > -1) {
            wx.redirectTo({
              url: `/communityPackage/pages/community/service/info/lostfound-detail?id=${id}`
            })
          }
        }
      })
    }
  }
})
