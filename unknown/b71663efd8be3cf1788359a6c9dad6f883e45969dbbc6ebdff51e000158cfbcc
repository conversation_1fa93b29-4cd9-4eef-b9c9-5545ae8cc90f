// pages/property/workorder/batch-assign/index.js
const workOrderManager = require('../../../../utils/workorder-manager');

Page({
  data: {
    darkMode: false,
    orderIds: [],
    orders: [],
    loading: true,
    submitting: false,
    
    // 员工列表
    staffList: [],
    selectedStaffId: '',
    
    // 分配备注
    assignRemark: '',
    
    // 状态栏高度
    statusBarHeight: 20
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight
    });
    
    // 获取URL参数中的工单ID列表
    if (options.ids) {
      const orderIds = options.ids.split(',');
      this.setData({ orderIds });
      this.loadWorkOrders(orderIds);
    } else {
      wx.showToast({
        title: '未选择工单',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
    
    // 加载员工列表
    this.loadStaffList();
  },
  
  // 加载工单信息
  loadWorkOrders: function(orderIds) {
    this.setData({ loading: true });
    
    const promises = orderIds.map(id => workOrderManager.getWorkOrderDetail(id));
    
    Promise.all(promises)
      .then(orders => {
        // 过滤出可以分配的工单（待处理或处理中状态）
        const validOrders = orders.filter(order => 
          order && (order.status === 'pending' || order.status === 'processing')
        );
        
        if (validOrders.length === 0) {
          wx.showToast({
            title: '没有可分配的工单',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        this.setData({
          orders: validOrders,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单信息失败', error);
        this.setData({ loading: false });
        
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 加载员工列表
  loadStaffList: function() {
    // 这里应该调用API获取员工列表
    // 暂时使用模拟数据
    const mockStaffList = [
      { id: 'staff001', name: '张工', department: '工程部', position: '维修工程师' },
      { id: 'staff002', name: '李工', department: '工程部', position: '维修工程师' },
      { id: 'staff003', name: '王工', department: '工程部', position: '维修主管' }
    ];
    
    this.setData({ staffList: mockStaffList });
  },
  
  // 选择员工
  selectStaff: function(e) {
    this.setData({ selectedStaffId: e.currentTarget.dataset.id });
  },
  
  // 输入分配备注
  inputAssignRemark: function(e) {
    this.setData({ assignRemark: e.detail.value });
  },
  
  // 提交分配
  submitAssign: function() {
    const { orderIds, selectedStaffId, assignRemark, staffList } = this.data;
    
    if (!selectedStaffId) {
      wx.showToast({
        title: '请选择员工',
        icon: 'none'
      });
      return;
    }
    
    // 获取选中的员工信息
    const selectedStaff = staffList.find(staff => staff.id === selectedStaffId);
    
    this.setData({ submitting: true });
    
    // 调用工单管理器批量分配工单
    workOrderManager.batchAssignOrders(orderIds, selectedStaff, assignRemark)
      .then(result => {
        this.setData({ submitting: false });
        
        const { success, failed } = result;
        
        if (failed.length > 0) {
          // 有失败的工单
          wx.showModal({
            title: '部分工单分配失败',
            content: `成功: ${success.length}个, 失败: ${failed.length}个`,
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        } else {
          // 全部成功
          wx.showToast({
            title: '分配成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(error => {
        console.error('批量分配工单失败', error);
        this.setData({ submitting: false });
        
        wx.showToast({
          title: error.message || '分配失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 取消分配
  cancelAssign: function() {
    wx.navigateBack();
  },
  
  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
