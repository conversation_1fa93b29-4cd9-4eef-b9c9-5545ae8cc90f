<!-- 社区群组组件 -->
<view class="groups-container">
  <!-- 社区兴趣群列表 -->
  <view class="group-list">
    <view class="group-item" wx:for="{{groups}}" wx:key="id" bindtap="navigateToGroupDetail" data-id="{{item.id}}">
      <view class="group-content">
        <view class="group-header">
          <view class="group-name">{{item.name}}</view>
          <view class="group-members">
            <image class="member-icon" src="/images/icons/user-group.svg" mode="aspectFit"></image>
            <text>{{item.memberCount}}人</text>
          </view>
        </view>
        <view class="group-desc">{{item.description}}</view>
        <view class="group-owner">
          <image class="owner-avatar" src="{{item.ownerAvatar}}" mode="aspectFill"></image>
          <text>群主: {{item.ownerName}}</text>
        </view>
      </view>
      <view class="group-action">
        <button class="detail-btn">查看详情</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{groups.length === 0}}">
    <image class="empty-image" src="/images/illustrations/empty-groups.svg" mode="aspectFit"></image>
    <view class="empty-text">暂无兴趣群</view>
    <view class="empty-subtext">社区兴趣群正在筹备中</view>
  </view>

  <!-- 底部安全区域，防止内容被底部选项卡遮挡 -->
  <view class="safe-bottom"></view>
</view>
