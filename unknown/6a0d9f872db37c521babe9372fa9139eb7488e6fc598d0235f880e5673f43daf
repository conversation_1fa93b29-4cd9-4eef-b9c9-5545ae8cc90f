/* 车辆详情页样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 40rpx;
}

/* 顶部操作栏 */
.action-bar {
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: flex-end;
  background: #FFFFFF;
}

.btn-edit, .btn-cancel, .btn-save {
  height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  border: none;
}

.btn-edit {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
}

.btn-cancel {
  background: #F2F2F7;
  color: #8E8E93;
  margin-right: 16rpx;
}

.btn-save {
  background: #FF8C00;
  color: #FFFFFF;
}

.btn-save[disabled] {
  opacity: 0.5;
}

/* 详情卡片样式 */
.detail-card {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-action {
  font-size: 28rpx;
  color: #FF8C00;
}

/* 信息列表样式 */
.info-list {
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

/* 编辑模式输入框 */
.info-input {
  flex: 1;
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #000000;
}

.picker-value {
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 车主信息样式 */
.owner-info {
  padding: 24rpx 0;
}

.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
}

.owner-details {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
  display: flex;
  justify-content: space-between;
}

.owner-id {
  color: #8E8E93;
}

.owner-date {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 空状态样式 */
.empty-state {
  padding: 48rpx 0;
  text-align: center;
}

.empty-icon {
  width: 96rpx;
  height: 96rpx;
  margin: 0 auto 16rpx;
  background: rgba(142, 142, 147, 0.1);
  border-radius: 48rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 36rpx 36rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.modal-body {
  padding: 32rpx;
}

/* 搜索框样式 */
.search-box {
  margin-bottom: 24rpx;
}

.search-input {
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
}

/* 搜索结果样式 */
.search-results {
  max-height: 400rpx;
  margin-bottom: 24rpx;
}

.result-item {
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.selected {
  background: rgba(255, 140, 0, 0.1);
}

.result-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
}

.result-info {
  font-size: 26rpx;
  color: #8E8E93;
  display: flex;
  justify-content: space-between;
}

.result-id {
  color: #8E8E93;
}

.empty-search {
  padding: 48rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #8E8E93;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  flex: 1;
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
}

.modal-footer button::after {
  border: none;
}

.modal-footer .btn-cancel {
  color: #8E8E93;
  background: transparent;
}

.modal-footer .btn-confirm {
  color: #FF8C00;
  font-weight: 600;
  background: transparent;
}

.modal-footer .btn-confirm[disabled] {
  opacity: 0.5;
}
