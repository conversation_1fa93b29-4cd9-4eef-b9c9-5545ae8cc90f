<!-- 物业服务组件 -->
<view class="property-container">
  <!-- 联系方式卡片 -->
  <view class="info-card">
    <view class="card-title">联系方式</view>
    <view class="contact-list">
      <view class="contact-item" wx:for="{{contactList}}" wx:key="id">
        <view class="contact-info">
          <view class="contact-name">{{item.name}}</view>
          <view class="contact-phone">{{item.phone}}</view>
        </view>
        <view class="contact-action" bindtap="callPhone" data-phone="{{item.phone}}">
          <view class="call-btn">
            <image class="call-icon" src="/images/icons/phone.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 物业信息卡片 -->
  <view class="info-card">
    <view class="card-title">物业信息</view>
    <view class="property-info">
      <view class="info-item">
        <image class="info-icon" src="/images/icons/location.svg" mode="aspectFit"></image>
        <text>{{propertyInfo.address}}</text>
      </view>
      <view class="info-item">
        <image class="info-icon" src="/images/icons/clock.svg" mode="aspectFit"></image>
        <text>{{propertyInfo.hours}}</text>
      </view>
    </view>
  </view>

  <!-- 在线客服和留言 -->
  <view class="info-card">
    <view class="card-title">在线服务</view>
    <view class="service-buttons">
      <button class="service-btn" open-type="contact">
        <image class="btn-icon" src="/images/icons/customer-service.svg" mode="aspectFit"></image>
        <text>在线客服</text>
      </button>

      <button class="service-btn" bindtap="showMessageForm">
        <image class="btn-icon" src="/images/icons/message.svg" mode="aspectFit"></image>
        <text>留言反馈</text>
      </button>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="info-card">
    <view class="card-title">常见问题</view>
    <view class="faq-list">
      <view class="faq-item" wx:for="{{faqList}}" wx:key="id">
        <view class="faq-question" bindtap="toggleFaq" data-id="{{item.id}}">
          <text>{{item.question}}</text>
          <image class="arrow-icon {{item.expanded ? 'expanded' : ''}}" src="/images/icons/arrow-down.svg" mode="aspectFit"></image>
        </view>
        <view class="faq-answer {{item.expanded ? 'expanded' : ''}}">
          <text>{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 留言表单弹窗 -->
  <view class="modal-mask" wx:if="{{showMessageModal}}" bindtap="hideMessageForm"></view>
  <view class="modal-dialog" wx:if="{{showMessageModal}}">
    <view class="modal-header">
      <text class="modal-title">留言反馈</text>
      <view class="modal-close" bindtap="hideMessageForm">
        <image src="/images/icons/close.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">留言类型</view>
        <picker bindchange="onTypeChange" value="{{messageTypeIndex}}" range="{{messageTypes}}">
          <view class="picker">
            {{messageTypes[messageTypeIndex]}}
            <image class="picker-arrow" src="/images/icons/arrow-down.svg" mode="aspectFit"></image>
          </view>
        </picker>
      </view>
      <view class="form-item">
        <view class="form-label">留言内容</view>
        <textarea class="form-textarea" placeholder="请输入您的留言内容" bindinput="onContentInput" value="{{messageContent}}"></textarea>
      </view>
      <view class="form-item">
        <view class="form-label">联系方式</view>
        <input class="form-input" placeholder="请输入您的联系方式" bindinput="onContactInput" value="{{messageContact}}"/>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideMessageForm">取消</button>
      <button class="submit-btn {{canSubmit ? '' : 'disabled'}}" bindtap="submitMessage">提交</button>
    </view>
  </view>

  <!-- 底部安全区域，防止内容被底部选项卡遮挡 -->
  <view class="safe-bottom"></view>
</view>
