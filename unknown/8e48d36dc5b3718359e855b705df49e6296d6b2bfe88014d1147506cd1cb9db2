// pages/property/login/login.js
Page({
  data: {
    account: '',
    password: '',
    showPassword: false,
    errorMsg: '',
    isLoading: false,
    darkMode: false
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
  },

  // 输入账号
  onAccountInput: function(e) {
    this.setData({
      account: e.detail.value,
      errorMsg: ''
    });
  },

  // 输入密码
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    });
  },

  // 清除账号
  clearAccount: function() {
    this.setData({
      account: '',
      errorMsg: ''
    });
  },

  // 切换密码可见性
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 登录
  login: function() {
    const { account, password } = this.data;
    
    // 简单验证
    if (!account.trim()) {
      this.setData({ errorMsg: '请输入账号' });
      return;
    }
    
    if (!password.trim()) {
      this.setData({ errorMsg: '请输入密码' });
      return;
    }
    
    // 显示加载状态
    this.setData({ isLoading: true });
    
    // 获取应用实例
    const app = getApp();
    
    // 调用登录函数
    app.login().then(userRole => {
      console.log('登录成功，用户角色:', userRole);
      
      // 存储登录状态
      wx.setStorageSync('isPropertyStaff', true);
      wx.setStorageSync('propertyAccount', account);
      
      // 显示登录成功提示
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            // 返回上一页或跳转到物业管理首页
            wx.navigateBack({
              fail: () => {
                // 如果返回失败，则跳转到物业管理首页
                wx.redirectTo({
                  url: '/pages/property/index/index'
                });
              }
            });
          }, 1500);
        }
      });
    }).catch(error => {
      console.error('登录失败:', error);
      this.setData({
        errorMsg: '账号或密码错误',
        isLoading: false
      });
    });
  },
  
  // 忘记密码
  forgotPassword: function() {
    wx.showToast({
      title: '请联系管理员重置密码',
      icon: 'none',
      duration: 2000
    });
  }
})
