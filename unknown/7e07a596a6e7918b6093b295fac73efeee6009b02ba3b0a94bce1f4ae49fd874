/* 添加房屋页面样式 */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* 页面标题 */
.page-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.page-subtitle {
  font-size: 14px;
  color: #8e8e93;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

/* 表单区域 */
.form-section {
  background: white;
  margin-bottom: 12px;
  padding: 20px;
}

/* 表单行 */
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  min-height: 44px;
}

.form-label {
  width: 80px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 44px;
  flex-shrink: 0;
}

.form-control {
  flex: 1;
  margin-left: 16px;
}

/* Picker样式 */
.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  transition: all 0.2s ease;
}

.picker-content:active {
  background: #e9ecef;
}

.picker-content.placeholder {
  color: #999;
}

.picker-arrow {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}



/* 角色网格布局 */
.residentType-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.residentType-tag {
  flex: 1;
  min-width: 100px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.residentType-tag:active {
  transform: scale(0.98);
}

.residentType-tag.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.05);
}

.residentType-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.residentType-desc {
  font-size: 12px;
  color: #8e8e93;
  line-height: 1.2;
}

/* 新的居住身份选择区域样式 */
.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.resident-type-container {
  padding: 0;
}

.resident-type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.resident-tag {
  flex: 1;
  min-width: 90px;
  max-width: 110px;
  padding: 8px 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.resident-tag:active {
  transform: scale(0.98);
}

.resident-tag.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.08);
}

.tag-text {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.tag-desc {
  font-size: 10px;
  color: #8e8e93;
  line-height: 1.2;
}

/* 占位提示区域 */
.placeholder-section {
  padding: 20px;
  text-align: center;
}

/* 提示文字样式 */
.placeholder-text {
  color: #999;
  font-size: 14px;
  line-height: 44px;
}

/* 加载和空状态 */
.loading-text, .empty-text {
  font-size: 14px;
  color: #8e8e93;
  line-height: 44px;
  text-align: center;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}



/* 提交按钮容器 */
.submit-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

/* 提交按钮样式 */
.submit-button {
  width: 100%;
  height: 50px;
  background: #007AFF;
  color: white;
  border-radius: 12px;
  border: none;
  font-size: 17px;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
  background: #0071eb;
}

.submit-button.disabled {
  background: rgba(0, 122, 255, 0.3);
  cursor: default;
  box-shadow: none;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 122, 255, 0.2);
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #8e8e93;
  margin-top: 4px;
}

/* 确认弹窗样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.confirm-dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.confirm-dialog {
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}

.confirm-dialog-overlay.active .confirm-dialog {
  transform: scale(1);
}

.confirm-dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.confirm-dialog-icon {
  width: 56px;
  height: 56px;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.confirm-dialog-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.confirm-dialog-message {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  line-height: 1.4;
}

.confirm-dialog-buttons {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.confirm-dialog-button {
  flex: 1;
  padding: 14px 0;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.2s;
}

.confirm-dialog-button.primary {
  color: #007AFF;
}

.confirm-dialog-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}
