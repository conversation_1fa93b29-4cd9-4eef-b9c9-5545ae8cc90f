/**
 * 工单模板管理工具类
 * 用于管理常用的处理结果和处理记录模板
 */

// 模板类型
const TEMPLATE_TYPES = {
  PROCESS: 'process', // 处理备注模板
  ASSIGN: 'assign',   // 分配备注模板
  COMPLETE: 'complete', // 完成结果模板
  RECORD: 'record'    // 处理记录模板
};

// 默认模板数据
const DEFAULT_TEMPLATES = {
  [TEMPLATE_TYPES.PROCESS]: [
    { id: 'p1', content: '已接单，将尽快安排人员处理。' },
    { id: 'p2', content: '已了解情况，将于今日安排维修人员上门处理。' },
    { id: 'p3', content: '已接单，需要采购材料，预计3天内处理完毕。' }
  ],
  [TEMPLATE_TYPES.ASSIGN]: [
    { id: 'a1', content: '请尽快处理此工单，优先级较高。' },
    { id: 'a2', content: '请在处理完成后及时更新工单状态。' },
    { id: 'a3', content: '此工单涉及多个业主，请妥善处理。' }
  ],
  [TEMPLATE_TYPES.COMPLETE]: [
    { id: 'c1', content: '已完成维修，设备恢复正常使用。' },
    { id: 'c2', content: '已处理完毕，业主对处理结果表示满意。' },
    { id: 'c3', content: '问题已解决，已向业主说明注意事项。' }
  ],
  [TEMPLATE_TYPES.RECORD]: [
    { id: 'r1', content: '现场勘查', remark: '已到现场查看情况，确认问题所在。' },
    { id: 'r2', content: '联系业主', remark: '已联系业主，约定上门时间。' },
    { id: 'r3', content: '采购材料', remark: '已采购维修所需材料，准备进行维修。' }
  ]
};

/**
 * 初始化模板数据
 * 如果本地存储中没有模板数据，则使用默认模板数据
 */
function initTemplates() {
  const types = Object.values(TEMPLATE_TYPES);
  
  types.forEach(type => {
    const key = `workorder_templates_${type}`;
    const templates = wx.getStorageSync(key);
    
    if (!templates) {
      wx.setStorageSync(key, DEFAULT_TEMPLATES[type] || []);
    }
  });
}

/**
 * 获取指定类型的模板列表
 * @param {string} type 模板类型
 * @returns {Array} 模板列表
 */
function getTemplates(type) {
  if (!Object.values(TEMPLATE_TYPES).includes(type)) {
    console.error('无效的模板类型:', type);
    return [];
  }
  
  const key = `workorder_templates_${type}`;
  return wx.getStorageSync(key) || [];
}

/**
 * 添加模板
 * @param {string} type 模板类型
 * @param {Object} template 模板对象
 * @returns {Promise<Array>} 更新后的模板列表
 */
function addTemplate(type, template) {
  return new Promise((resolve, reject) => {
    try {
      if (!Object.values(TEMPLATE_TYPES).includes(type)) {
        reject(new Error('无效的模板类型'));
        return;
      }
      
      if (!template.content) {
        reject(new Error('模板内容不能为空'));
        return;
      }
      
      const key = `workorder_templates_${type}`;
      const templates = wx.getStorageSync(key) || [];
      
      // 生成唯一ID
      const id = `${type[0]}${Date.now()}`;
      const newTemplate = { id, ...template };
      
      // 添加到列表
      templates.push(newTemplate);
      
      // 保存到本地存储
      wx.setStorageSync(key, templates);
      
      resolve(templates);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 编辑模板
 * @param {string} type 模板类型
 * @param {string} id 模板ID
 * @param {Object} template 更新的模板对象
 * @returns {Promise<Array>} 更新后的模板列表
 */
function editTemplate(type, id, template) {
  return new Promise((resolve, reject) => {
    try {
      if (!Object.values(TEMPLATE_TYPES).includes(type)) {
        reject(new Error('无效的模板类型'));
        return;
      }
      
      if (!template.content) {
        reject(new Error('模板内容不能为空'));
        return;
      }
      
      const key = `workorder_templates_${type}`;
      const templates = wx.getStorageSync(key) || [];
      
      // 查找模板
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('模板不存在'));
        return;
      }
      
      // 更新模板
      templates[index] = { ...templates[index], ...template };
      
      // 保存到本地存储
      wx.setStorageSync(key, templates);
      
      resolve(templates);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 删除模板
 * @param {string} type 模板类型
 * @param {string} id 模板ID
 * @returns {Promise<Array>} 更新后的模板列表
 */
function deleteTemplate(type, id) {
  return new Promise((resolve, reject) => {
    try {
      if (!Object.values(TEMPLATE_TYPES).includes(type)) {
        reject(new Error('无效的模板类型'));
        return;
      }
      
      const key = `workorder_templates_${type}`;
      const templates = wx.getStorageSync(key) || [];
      
      // 过滤掉要删除的模板
      const updatedTemplates = templates.filter(t => t.id !== id);
      
      // 保存到本地存储
      wx.setStorageSync(key, updatedTemplates);
      
      resolve(updatedTemplates);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 重置模板为默认值
 * @param {string} type 模板类型，如果不指定则重置所有类型
 * @returns {Promise<Object>} 重置结果
 */
function resetTemplates(type) {
  return new Promise((resolve, reject) => {
    try {
      if (type && !Object.values(TEMPLATE_TYPES).includes(type)) {
        reject(new Error('无效的模板类型'));
        return;
      }
      
      const typesToReset = type ? [type] : Object.values(TEMPLATE_TYPES);
      
      typesToReset.forEach(t => {
        const key = `workorder_templates_${t}`;
        wx.setStorageSync(key, DEFAULT_TEMPLATES[t] || []);
      });
      
      resolve({ success: true, message: '模板已重置' });
    } catch (error) {
      reject(error);
    }
  });
}

// 导出模块
module.exports = {
  TEMPLATE_TYPES,
  initTemplates,
  getTemplates,
  addTemplate,
  editTemplate,
  deleteTemplate,
  resetTemplates
};
