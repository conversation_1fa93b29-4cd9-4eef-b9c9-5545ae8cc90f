// level.js
const PointsUtil = require('../../../utils/points');
const app = getApp();

Page({
  data: {
    userPoints: 0,
    level: null,
    nextLevel: null,
    pointsToNextLevel: 0,
    progress: 0,
    privileges: null,
    allLevels: [],
    darkMode: false,
    showAllLevels: false,
    activeTab: 'current' // current, all
  },

  onLoad: function() {
    // 获取暗黑模式设置
    this.setData({
      darkMode: app.globalData.darkMode
    });

    // 加载用户等级信息
    this.loadUserLevelInfo();
  },

  onShow: function() {
    // 刷新用户等级信息
    this.loadUserLevelInfo();
  },

  onPullDownRefresh: function() {
    this.loadUserLevelInfo().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载用户等级信息
  loadUserLevelInfo: function() {
    wx.showLoading({
      title: '加载中...'
    });

    return PointsUtil.getUserLevelInfo().then(levelInfo => {
      wx.hideLoading();

      this.setData({
        userPoints: levelInfo.points,
        level: levelInfo.level,
        nextLevel: levelInfo.nextLevel,
        pointsToNextLevel: levelInfo.pointsToNextLevel,
        progress: levelInfo.progress,
        privileges: levelInfo.privileges,
        allLevels: this.getAllLevels()
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      console.error('加载用户等级信息失败:', err);
    });
  },

  // 获取所有等级信息
  getAllLevels: function() {
    const levels = PointsUtil.getAllLevels();

    // 获取每个等级的特权
    return levels.map(level => {
      const privileges = PointsUtil.getLevelPrivileges(level.id);
      return {
        ...level,
        privileges: privileges
      };
    });
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 切换显示所有等级
  toggleAllLevels: function() {
    this.setData({
      showAllLevels: !this.data.showAllLevels
    });
  },

  // 跳转到赚积分页面
  goToEarnPoints: function() {
    wx.navigateTo({
      url: '/pages/points/earn/earn'
    });
  }
})
