<!--物业管理页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="header">
    <view class="title">物业管理工作台</view>
    <view class="subtitle">一站式物业服务平台</view>
  </view>

  <!-- 服务网格 -->
  <view class="services-grid" animation="{{animationData}}">
    <view class="service-item" wx:for="{{services}}" wx:key="id" bindtap="navigateToService" data-url="{{item.url}}">
      <view class="service-icon">
        <view class="service-icon-inner" style="color: {{item.color}};" data-icon="{{item.icon}}">
          </view>
      </view>
      <view class="service-name">{{item.name}}</view>
    </view>
  </view>
</view>
