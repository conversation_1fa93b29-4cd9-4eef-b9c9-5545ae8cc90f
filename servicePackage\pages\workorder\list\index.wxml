<!-- pages/workorder/list/index.wxml -->
<view class="container">
  <!-- 筛选区域 -->
  <view class="filter-section">
    <!-- 状态标签栏 -->
    <scroll-view scroll-x class="status-tabs">
      <view
        wx:for="{{statusTabs}}"
        wx:key="index"
        class="status-tab {{currentTab === index ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="{{index}}"
      >
        {{item}}
      </view>
    </scroll-view>

    <!-- 类型标签栏 -->
    <scroll-view scroll-x class="type-tabs">
      <view
        wx:for="{{typeTabs}}"
        wx:key="index"
        class="type-tab {{currentTypeTab === index ? 'active' : ''}}"
        bindtap="switchTypeTab"
        data-tab="{{index}}"
      >
        {{item}}
      </view>
    </scroll-view>

    <!-- 搜索框 -->
    <view class="search-box">
      <image src="/images/icons/search.svg" class="search-icon" />
      <input
        type="text"
        class="search-input"
        placeholder="搜索工单号或描述"
        value="{{searchValue}}"
        bindinput="searchWorkOrders"
        bindconfirm="onSearchConfirm"
        confirm-type="search"
      />
    </view>
  </view>

  <!-- 工单列表 -->
  <scroll-view
    scroll-y
    class="order-list"
    bindscrolltolower="loadMoreOrders"
    enable-back-to-top
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-container" wx:elif="{{showEmpty}}">
      <image src="/images/icons/empty-orders.svg" class="empty-icon" />
      <text class="empty-text">暂无工单记录</text>
      <!-- <button class="create-btn" bindtap="createNewOrder">创建工单</button> -->
    </view>

    <!-- 工单列表 -->
    <block wx:else>
      <view
        wx:for="{{workOrders}}"
        wx:key="id"
        class="order-card"
        bindtap="navigateToDetail"
        data-id="{{item.id}}"
      >
        <!-- 工单卡片内容 -->
        <view class="order-header">
          <!-- 左侧类型标识区域 -->
          <view class="type-area">
            <!-- 移除图标容器，只保留文字标签 -->
            <view class="order-type-tag">{{item.typeName}}</view>
          </view>

          <!-- 中间主要信息区域 -->
          <view class="order-info">
            <view class="order-title-container">
              <view class="order-title">{{item.userDescribe}}</view>
            </view>
            <view class="order-meta">
              <text class="order-id">工单号：{{item.id}}</text>
              <text class="order-time">{{item.createTime}}</text>
            </view>
          </view>

          <!-- 右侧状态区域 -->
          <view class="order-status">
            <view class="status-tag status-{{item.status}}">{{item.statusName}}</view>
          </view>
        </view>

        <!-- 底部操作区域 -->
        <view class="order-actions">
          <!-- 待处理状态 -->
          <block wx:if="{{item.status === 'pending'}}">
            <button
              class="action-btn view-btn"
              catchtap="navigateToDetail"
              data-id="{{item.id}}"
            >
              查看详情
            </button>
            <button
              class="action-btn cancel-btn"
              catchtap="cancelOrder"
              data-id="{{item.id}}"
            >
              取消
            </button>
          </block>

          <!-- 处理中状态 -->
          <block wx:elif="{{item.status === 'processing'}}">
            <button
              class="action-btn progress-btn"
              catchtap="navigateToDetail"
              data-id="{{item.id}}"
            >
              查看进度
            </button>
          </block>

          <!-- 已完成状态 -->
          <block wx:elif="{{item.status === 'completed'}}">
            <button
              class="action-btn view-btn"
              catchtap="navigateToDetail"
              data-id="{{item.id}}"
            >
              查看详情
            </button>
            <button
              class="action-btn evaluate-btn"
              catchtap="navigateToEvaluate"
              data-id="{{item.id}}"
            >
              评价
            </button>
          </block>

          <!-- 已取消状态 -->
          <block wx:elif="{{item.status === 'cancelled'}}">
            <button
              class="action-btn view-btn"
              catchtap="navigateToDetail"
              data-id="{{item.id}}"
            >
              查看详情
            </button>
          </block>

          <!-- 其他状态默认显示查看详情 -->
          <block wx:else>
            <button
              class="action-btn view-btn"
              catchtap="navigateToDetail"
              data-id="{{item.id}}"
            >
              查看详情
            </button>
          </block>
        </view>
      </view>

      <!-- 分页指示器 -->
      <view class="pagination" wx:if="{{total > pageSize}}">
        <text class="page-info">已加载 {{workOrders.length}} 条，共 {{total}} 条</text>
      </view>
    </block>
  </scroll-view>
  
  <!-- 悬浮添加按钮 -->
  <view class="add-btn" bindtap="createNewOrder">
    <image src="/images/icons/add.svg" class="add-icon" />
  </view>
</view>
