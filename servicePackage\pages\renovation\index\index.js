// pages/renovation/index/index.js
const util = require('../../../../utils/util.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchKeyword: '',
    currentTab: 'ongoing',
    ongoingList: [],
    completedList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadApplicationList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.loadApplicationList();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadApplicationList(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载申请列表
   */
  loadApplicationList(callback) {
    // 模拟数据加载
    setTimeout(() => {
      this.setData({
        ongoingList: [
          {
            id: 'RN2023112501',
            title: '紫荆花园3栋2单元1801',
            status: 'pending',
            statusText: '等待审核',
            type: '全屋装修',
            date: '2023-11-25',
            progress: '物业初审'
          },
          {
            id: 'RN2023110302',
            title: '紫荆花园1栋1单元502',
            status: 'approved',
            statusText: '已通过',
            type: '局部改造',
            date: '2023-11-03',
            progress: '施工监督中'
          }
        ],
        completedList: [
          {
            id: 'RN2023082001',
            title: '紫荆花园2栋2单元901',
            status: 'completed',
            statusText: '已完成',
            type: '设备安装',
            date: '2023-08-20',
            completeDate: '2023-09-15'
          },
          {
            id: 'RN2023050501',
            title: '紫荆花园3栋2单元1801',
            status: 'rejected',
            statusText: '已驳回',
            type: '墙体拆改',
            date: '2023-05-05',
            rejectReason: '涉及承重墙拆除，方案调整后重新提交'
          }
        ]
      });

      if (callback) {
        callback();
      }
    }, 500);
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.searchApplications();
  },

  /**
   * 搜索确认
   */
  onSearchConfirm(e) {
    // 隐藏键盘
    wx.hideKeyboard({
      complete: () => {
        // 确保搜索关键词已设置
        this.setData({
          searchKeyword: e.detail.value
        });
        this.searchApplications();
      }
    });
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.loadApplicationList();
  },

  /**
   * 搜索申请
   */
  searchApplications() {
    const keyword = this.data.searchKeyword.toLowerCase();

    if (!keyword) {
      this.loadApplicationList();
      return;
    }

    // 过滤进行中列表
    const filteredOngoing = this.data.ongoingList.filter(item => {
      return item.title.toLowerCase().includes(keyword) ||
             item.type.toLowerCase().includes(keyword) ||
             item.progress.toLowerCase().includes(keyword);
    });

    // 过滤已完成列表
    const filteredCompleted = this.data.completedList.filter(item => {
      return item.title.toLowerCase().includes(keyword) ||
             item.type.toLowerCase().includes(keyword);
    });

    this.setData({
      ongoingList: filteredOngoing,
      completedList: filteredCompleted
    });
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/renovation/status/status?id=${id}`
    });
  },

  /**
   * 处理操作
   */
  handleAction(e) {
    const action = e.currentTarget.dataset.action;
    const id = e.currentTarget.dataset.id;

    switch (action) {
      case 'detail':
        this.viewDetail(e);
        break;
      case 'notify':
        this.notifyProperty(id);
        break;
      case 'resubmit':
        this.resubmitApplication(id);
        break;
    }
  },

  /**
   * 催办物业
   */
  notifyProperty(id) {
    wx.showToast({
      title: '已发送催办消息',
      icon: 'success'
    });
  },

  /**
   * 重新申请
   */
  resubmitApplication(id) {
    wx.showModal({
      title: '提示',
      content: '是否基于原申请内容创建新申请？',
      confirmColor: '#FF9800',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/servicePackage/pages/renovation/create/create?copy=${id}`
          });
        }
      }
    });
  },

  /**
   * 创建新申请
   */
  createNewApplication() {
    wx.navigateTo({
      url: '/servicePackage/pages/renovation/create/create'
    });
  }
})