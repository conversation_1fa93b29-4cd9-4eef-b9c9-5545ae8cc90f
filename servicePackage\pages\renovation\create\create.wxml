<!--pages/renovation/create/create.wxml-->
<view class="container">
  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="progress-bar-container">
      <view class="progress-bar-active" style="right: calc(75% + 40px);"></view>
    </view>
    <view class="step step-active">
      <view class="step-circle">1</view>
      <view class="step-label">基本信息</view>
    </view>
    <view class="step">
      <view class="step-circle">2</view>
      <view class="step-label">施工方案</view>
    </view>
    <view class="step">
      <view class="step-circle">3</view>
      <view class="step-label">材料上传</view>
    </view>
    <view class="step">
      <view class="step-circle">4</view>
      <view class="step-label">承诺签署</view>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <view class="form-section-title">房屋信息</view>
    <view class="form-group">
      <view class="form-label">
        <text>房屋地址</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <picker mode="selector" range="{{houses}}" range-key="address" value="{{houseIndex}}" bindchange="onHouseChange">
          <view class="form-picker {{formData.house ? '' : 'placeholder'}}">
            <text>{{formData.house || '请选择房屋'}}</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <view class="form-section">
    <view class="form-section-title">申请信息</view>
    <view class="form-group">
      <view class="form-label">
        <text>装修类型</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <picker mode="selector" range="{{renovationTypes}}" range-key="name" value="{{typeIndex}}" bindchange="onTypeChange">
          <view class="form-picker {{formData.renovationType ? '' : 'placeholder'}}">
            <text>{{formData.renovationType || '请选择装修类型'}}</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>预计开始日期</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <picker mode="date" value="{{formData.startDate}}" start="{{minDate}}" bindchange="onStartDateChange">
          <view class="form-picker {{formData.startDate ? '' : 'placeholder'}}">
            <text>{{formData.startDate || '请选择开始日期'}}</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>预计结束日期</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <picker mode="date" value="{{formData.endDate}}" start="{{formData.startDate || minDate}}" bindchange="onEndDateChange">
          <view class="form-picker {{formData.endDate ? '' : 'placeholder'}}">
            <text>{{formData.endDate || '请选择结束日期'}}</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <view class="form-section">
    <view class="form-section-title">联系信息</view>
    <view class="form-group">
      <view class="form-label">
        <text>联系人</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="text" placeholder="请输入联系人姓名" value="{{formData.contactName}}" bindinput="onContactNameInput" />
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>联系电话</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="number" placeholder="请输入联系电话" value="{{formData.contactPhone}}" bindinput="onContactPhoneInput" maxlength="11" />
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>施工单位</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="text" placeholder="请输入施工单位名称" value="{{formData.constructionCompany}}" bindinput="onCompanyInput" />
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>施工负责人</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="text" placeholder="请输入施工负责人姓名" value="{{formData.constructionManager}}" bindinput="onManagerInput" />
      </view>
    </view>
    <view class="form-group">
      <view class="form-label">
        <text>负责人电话</text>
        <text class="required">*</text>
      </view>
      <view class="form-field">
        <input class="form-input" type="number" placeholder="请输入施工负责人电话" value="{{formData.managerPhone}}" bindinput="onManagerPhoneInput" maxlength="11" />
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="next-button" bindtap="goToNextStep" disabled="{{!isFormValid}}">下一步</button>
  </view>
</view>