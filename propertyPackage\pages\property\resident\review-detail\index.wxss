/* 审核详情页样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 审核状态栏 */
.status-bar {
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
}

.status-bar.pending {
  background: #FF9500;
}

.status-bar.approved {
  background: #34C759;
}

.status-bar.rejected {
  background: #FF3B30;
}

.status-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.status-bar.pending .status-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-bar.approved .status-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-bar.rejected .status-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

/* 详情卡片样式 */
.detail-card {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.info-list {
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

/* 照片容器样式 */
.photo-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.photo-item {
  width: 48%;
  position: relative;
}

.id-photo, .vehicle-photo, .property-photo, .license-photo {
  width: 100%;
  height: 360rpx;
  border-radius: 16rpx;
  background: #F2F2F7;
}

.photo-label {
  position: absolute;
  bottom: 16rpx;
  left: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 关联信息样式 */
.related-house, .related-resident {
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
}

.related-title {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.related-content {
  font-size: 28rpx;
  color: #000000;
}

/* 审核意见输入框 */
.review-comment {
  width: 100%;
  height: 200rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  box-sizing: border-box;
}

/* 审核结果样式 */
.review-result {
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
}

.result-item {
  display: flex;
  margin-bottom: 16rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.result-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

.result-value.approved {
  color: #34C759;
}

.result-value.rejected {
  color: #FF3B30;
}

.reject-reason {
  color: #FF3B30;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-reject, .btn-approve {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-reject {
  background: #FFFFFF;
  color: #FF3B30;
  border: 1rpx solid rgba(255, 59, 48, 0.2);
  margin-right: 24rpx;
}

.btn-approve {
  background: #FF8C00;
  color: #FFFFFF;
}

.btn-reject[disabled], .btn-approve[disabled] {
  opacity: 0.5;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.modal-body {
  padding: 32rpx;
}

.reject-reason {
  width: 100%;
  height: 200rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  box-sizing: border-box;
}

.confirm-text {
  display: block;
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  padding: 24rpx 0;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  flex: 1;
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
}

.modal-footer button::after {
  border: none;
}

.modal-footer .btn-cancel {
  color: #8E8E93;
  background: transparent;
}

.modal-footer .btn-confirm {
  color: #FF8C00;
  font-weight: 600;
  background: transparent;
}

.modal-footer .btn-confirm[disabled] {
  opacity: 0.5;
}

/* 审核操作区域样式 */
.review-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.status-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.status-selector:active {
  background: #E5E5EA;
  border-color: #FF8C00;
}

.selector-label {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.selector-value {
  flex: 1;
  font-size: 30rpx;
  color: #000000;
  text-align: left;
}

.selector-value:empty::before {
  content: "请选择审核状态";
  color: #C7C7CC;
}

.selector-arrow {
  font-size: 28rpx;
  color: #C7C7CC;
  transform: rotate(0deg);
  transition: transform 0.3s;
}

.btn-submit {
  height: 88rpx;
  background: #FF8C00;
  color: #FFFFFF;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-submit::after {
  border: none;
}

.btn-submit[disabled] {
  background: #C7C7CC;
  color: #FFFFFF;
}

/* 状态选择列表样式 */
.status-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
  transition: background-color 0.2s;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item:active {
  background-color: #F2F2F7;
}

.status-name {
  font-size: 30rpx;
  color: #000000;
  flex: 1;
}

.status-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #C7C7CC;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s;
}

.status-check.checked {
  border-color: #FF8C00;
  background-color: #FF8C00;
}

.status-check.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 20rpx;
  border: 3rpx solid #FFFFFF;
  border-top: none;
  border-left: none;
  transform: translate(-50%, -60%) rotate(45deg);
}
