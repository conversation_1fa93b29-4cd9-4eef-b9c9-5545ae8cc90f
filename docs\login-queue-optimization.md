# 登录和请求队列管理优化

## 问题分析

### 原有问题
1. **并发请求问题**：首页在`onLoad`时同时调用登录和数据加载，导致请求竞争
2. **登录状态管理不统一**：各页面独立处理登录逻辑
3. **请求队列不完整**：只处理401错误，未处理初始登录期间的请求排队

### 时序问题示例
```
原有流程（有问题）：
onLaunch -> 设置基础配置
onLoad -> initPage() + loadBannerList() 并发执行
         ↓                    ↓
      登录请求           轮播图请求（可能失败）
```

## 优化方案

### 1. 全局登录状态管理
在`app.js`中统一管理登录状态：
- `isAuthenticated`: 是否已认证
- `isLoginInitializing`: 是否正在初始化登录
- `loginInitPromise`: 登录初始化Promise

### 2. 请求队列机制
在`utils/request.js`中实现：
- `shouldWaitForLogin()`: 检查是否需要等待登录
- `requestQueue`: 存储等待登录完成的请求
- `processRequestQueue()`: 登录完成后处理队列中的请求

### 3. 优化后的流程
```
优化后流程：
onLaunch -> initializeAppLogin() 开始登录
onLoad -> waitForLoginAndLoadData() 等待登录完成
         ↓
      登录完成 -> 处理队列中的请求
         ↓
      loadBannerList() 等数据加载
```

## 核心代码实现

### app.js - 全局登录管理
```javascript
// 初始化应用登录状态
initializeAppLogin: function () {
  this.globalData.isLoginInitializing = true;
  this.globalData.loginInitPromise = REQUEST.initializeApp()
    .then((result) => {
      this.globalData.isAuthenticated = true;
      this.globalData.isLoginInitializing = false;
      return result;
    })
    .catch((error) => {
      this.globalData.isAuthenticated = false;
      this.globalData.isLoginInitializing = false;
      return Promise.reject(error);
    });
  
  return this.globalData.loginInitPromise;
}

// 等待登录完成
waitForLogin: function () {
  if (this.globalData.isAuthenticated) {
    return Promise.resolve(true);
  }
  
  if (this.globalData.loginInitPromise) {
    return this.globalData.loginInitPromise;
  }
  
  return this.initializeAppLogin();
}
```

### utils/request.js - 请求队列管理
```javascript
// 检查是否需要等待登录
const shouldWaitForLogin = (url) => {
  return !isAppInitialized && 
         !url.includes('/auth/token') && 
         !url.includes('/oauth/login');
};

// 在baseRequest中添加队列逻辑
if (shouldWaitForLogin(url)) {
  console.log(`请求${url}需要等待登录完成，加入队列`);
  requestQueue.push({ resolve, reject, url, method, data, needToken });
  return;
}
```

### pages/index/index.js - 页面级优化
```javascript
// 等待登录完成后加载数据
waitForLoginAndLoadData: function () {
  app.waitForLogin()
    .then(() => {
      console.log('登录完成，开始加载数据');
      this.loadBannerList();
    })
    .catch((error) => {
      console.error('登录失败，使用默认数据', error);
      this.setData({
        bannerList: this.data.defaultBannerList,
        bannerLoading: false
      });
    });
}
```

## 测试场景

### 1. 新用户首次进入测试
**测试步骤：**
1. 清空所有StorageSync数据
2. 重新启动小程序
3. 观察控制台日志

**预期结果：**
```
App: 开始初始化登录状态
需要进行登录
首页: 等待登录完成后加载数据
请求/common-api/v1/imagetext/list需要等待登录完成，加入队列
wx.login成功
应用初始化登录成功
App: 登录初始化成功
首页: 登录完成，开始加载数据
处理队列中的请求: 1个
```

### 2. 已登录用户重新进入测试
**测试步骤：**
1. 确保本地有有效的登录信息
2. 重新启动小程序
3. 观察是否直接加载数据

**预期结果：**
```
App: 开始初始化登录状态
用户已登录且token有效
App: 登录初始化成功
首页: 登录完成，开始加载数据
```

### 3. Token过期自动刷新测试
**测试步骤：**
1. 模拟token过期（删除access_token但保留refresh_token）
2. 发起需要token的请求
3. 观察自动刷新流程

**预期结果：**
```
401 未授权
尝试刷新token
Token刷新成功，重新发起请求
```

## 关键优化点

### 1. 避免重复登录
- 使用`isLoginInProgress`标记防止重复登录
- 复用现有的登录Promise

### 2. 请求队列管理
- 登录期间的请求自动加入队列
- 登录完成后按顺序处理队列中的请求
- 登录失败时合理处理队列中的请求

### 3. 用户体验优化
- 登录失败时使用默认数据，避免白屏
- 提供清晰的加载状态提示
- 避免重复的错误提示

### 4. 错误处理
- 完善的错误处理机制
- 登录失败时的降级方案
- 网络异常时的重试机制

## 性能优化

### 1. 减少并发请求
- 统一登录管理，避免多个页面同时登录
- 请求队列避免重复请求

### 2. 缓存机制
- 保留原有的请求缓存机制
- 登录成功后清除缓存，确保数据新鲜

### 3. 内存管理
- 及时清理完成的Promise引用
- 避免内存泄漏

## 兼容性说明

### 向后兼容
- 保持原有API接口不变
- 现有页面无需大幅修改
- 渐进式优化，可分步实施

### 扩展性
- 易于添加新的登录状态监听
- 支持多种登录方式
- 便于添加新的请求拦截逻辑

## 监控和调试

### 日志输出
- 详细的登录流程日志
- 请求队列状态日志
- 错误处理日志

### 调试工具
- 可通过控制台查看登录状态
- 可查看请求队列中的请求数量
- 可手动触发登录重试

这个优化方案解决了原有的并发请求问题，提供了更好的用户体验和更稳定的登录机制。
