/* earn.wxss */
/* 容器样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #222;
  color: #fff;
}

.dark-mode .title,
.dark-mode .task-title {
  color: #fff;
}

.dark-mode .subtitle,
.dark-mode .task-desc,
.dark-mode .tips-content,
.dark-mode .continuous-text,
.dark-mode .continuous-next,
.dark-mode .progress-text {
  color: #aaa;
}

.dark-mode .task-card {
  background-color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.dark-mode .task-types {
  background-color: #444;
}

.dark-mode .task-type {
  color: #aaa;
}

.dark-mode .task-type.active {
  background-color: #555;
  color: #ff8c00;
}

.dark-mode .tips-header,
.dark-mode .tips {
  background-color: #333;
}

.dark-mode .task-progress .progress-bar {
  background-color: #444;
}

/* 顶部信息 */
.header {
  margin-bottom: 30rpx;
}

.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.level-tag {
  margin-left: 16rpx;
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 积分信息 */
.points-info {
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  border-radius: 16rpx;
  padding: 30rpx;
  color: white;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
}

.points-content {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 48rpx;
  font-weight: 700;
}

.level-link {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.arrow {
  margin-left: 8rpx;
}

/* 任务类型选择 */
.task-types-scroll {
  margin-bottom: 30rpx;
  white-space: nowrap;
}

.task-types {
  display: inline-flex;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 8rpx;
}

.task-type {
  min-width: 160rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 32rpx;
  margin: 0 4rpx;
}

.task-type.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 任务列表 */
.tasks-list {
  margin-bottom: 30rpx;
}

.task-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.task-card.completed {
  opacity: 0.8;
}

.task-card.limit_reached {
  opacity: 0.7;
}

/* 任务图标 */
.task-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 140, 0, 0.1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.task-icon-svg {
  width: 48rpx;
  height: 48rpx;
  background-size: 48rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 任务图标 SVG 背景 */
.calendar-check-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Cpath d='M9 16l2 2 4-4'%3E%3C/path%3E%3C/svg%3E");
}

.eye-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
}

.comment-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z'%3E%3C/path%3E%3C/svg%3E");
}

.tool-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z'%3E%3C/path%3E%3C/svg%3E");
}

.star-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
}

.credit-card-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='1' y='4' width='22' height='16' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='1' y1='10' x2='23' y2='10'%3E%3C/line%3E%3C/svg%3E");
}

.clock-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.user-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
}

.phone-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
}

.home-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

.check-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234cd964' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
}

/* 任务信息 */
.task-info {
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 16rpx;
}

.task-status {
  font-size: 22rpx;
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.task-status.limit {
  color: #faad14;
  background-color: rgba(250, 173, 20, 0.1);
}

.task-status.progress {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.task-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

/* 任务进度 */
.task-progress {
  margin-bottom: 12rpx;
}

.progress-bar {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  border-radius: 6rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 连续任务信息 */
.task-continuous {
  margin-bottom: 12rpx;
  background-color: rgba(255, 140, 0, 0.05);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.continuous-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.continuous-next {
  font-size: 24rpx;
  color: #ff8c00;
}

.task-points {
  font-size: 26rpx;
  color: #ff8c00;
  font-weight: 500;
}

/* 任务操作 */
.task-action {
  width: 160rpx;
  flex-shrink: 0;
}

.task-btn {
  width: 100%;
  height: 64rpx;
  background: linear-gradient(90deg, #ff8c00, #ff6b00);
  color: white;
  font-size: 26rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
  min-height: 0;
  box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.2);
}

.task-btn.completed {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

.task-btn.limit_reached {
  background: #f0f0f0;
  color: #999;
  box-shadow: none;
}

/* 空任务提示 */
.empty-tasks {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-size: 120rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 积分获取小贴士 */
.tips-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9f9f9;
  border-radius: 16rpx 16rpx 0 0;
  padding: 24rpx 30rpx;
  margin-top: 30rpx;
}

.tips-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.tips-toggle {
  font-size: 26rpx;
  color: #ff8c00;
}

.tips {
  background: #f9f9f9;
  border-radius: 0 0 16rpx 16rpx;
  padding: 0 30rpx 30rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 10rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}
