// earn.js
// 确保引入的模块存在
const PointsUtil = require('../../../utils/points');
const PointsService = require('../../../services/points');
const app = getApp();

Page({
  data: {
    tasks: [],
    taskTypes: [
      { id: 'all', name: '全部' },
      { id: 'basic', name: '基础任务' },
      { id: 'community', name: '社区互动' },
      { id: 'property', name: '物业服务' },
      { id: 'payment', name: '缴费任务' },
      { id: 'once', name: '一次性任务' }
    ],
    currentType: 'all',
    filteredTasks: [],
    userPoints: 0,
    userLevel: null,
    darkMode: false,
    showTips: true
  },

  onLoad: function() {
    try {
      // 获取暗黑模式设置
      this.setData({
        darkMode: app.globalData.darkMode
      });

      // 初始化积分系统
      PointsService.init();

      // 加载任务数据
      this.loadTasks();

      // 加载用户积分和等级信息
      this.loadUserInfo();
    } catch (e) {
      console.error('加载积分页面失败:', e);
      // 显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  onShow: function() {
    try {
      // 刷新任务状态
      this.loadTasks();

      // 刷新用户积分和等级信息
      this.loadUserInfo();
    } catch (e) {
      console.error('刷新积分页面失败:', e);
    }
  },

  onPullDownRefresh: function() {
    // 下拉刷新
    Promise.all([
      this.loadTasks(),
      this.loadUserInfo()
    ]).then(() => {
      wx.stopPullDownRefresh();
    }).catch(err => {
      console.error('下拉刷新失败:', err);
      wx.stopPullDownRefresh();
    });
  },

  // 加载任务数据
  loadTasks: function() {
    return new Promise((resolve, reject) => {
      try {
        wx.showLoading({
          title: '加载中...'
        });

        // 使用新的任务服务获取任务
        PointsService.getTasks().then(tasks => {
          wx.hideLoading();

          this.setData({
            tasks: tasks
          });

          // 筛选任务
          this.filterTasks(this.data.currentType);

          resolve(tasks);
        }).catch(err => {
          wx.hideLoading();
          console.error('获取任务失败:', err);

          // 设置空任务列表
          this.setData({
            tasks: [],
            filteredTasks: []
          });

          reject(err);
        });
      } catch (e) {
        wx.hideLoading();
        console.error('加载任务数据失败:', e);

        // 设置空任务列表
        this.setData({
          tasks: [],
          filteredTasks: []
        });

        reject(e);
      }
    });
  },

  // 加载用户积分和等级信息
  loadUserInfo: function() {
    return new Promise((resolve, reject) => {
      try {
        PointsUtil.getUserLevelInfo().then(levelInfo => {
          this.setData({
            userPoints: levelInfo.points,
            userLevel: levelInfo.level
          });
          resolve(levelInfo);
        }).catch(err => {
          console.error('获取用户等级信息失败:', err);
          // 设置默认值
          this.setData({
            userPoints: 0,
            userLevel: null
          });
          reject(err);
        });
      } catch (e) {
        console.error('加载用户信息失败:', e);
        // 设置默认值
        this.setData({
          userPoints: 0,
          userLevel: null
        });
        reject(e);
      }
    });
  },

  // 筛选任务
  filterTasks: function(type) {
    try {
      let filtered = this.data.tasks;
      if (type !== 'all') {
        filtered = this.data.tasks.filter(task => task.category === type);
      }

      // 按状态排序：可完成 > 进行中 > 已完成 > 已达上限
      filtered.sort((a, b) => {
        const statusOrder = { 'available': 0, 'in_progress': 1, 'completed': 2, 'limit_reached': 3 };
        return statusOrder[a.status] - statusOrder[b.status];
      });

      this.setData({
        currentType: type,
        filteredTasks: filtered
      });
    } catch (e) {
      console.error('筛选任务失败:', e);
      // 设置空任务列表
      this.setData({
        filteredTasks: []
      });
    }
  },

  // 切换任务类型
  switchTaskType: function(e) {
    try {
      const type = e.currentTarget.dataset.type;
      this.filterTasks(type);
    } catch (e) {
      console.error('切换任务类型失败:', e);
    }
  },

  // 切换显示提示
  toggleTips: function() {
    this.setData({
      showTips: !this.data.showTips
    });
  },

  // 完成任务
  completeTask: function(e) {
    try {
      const taskId = parseInt(e.currentTarget.dataset.id);
      const task = this.data.tasks.find(t => t.id === taskId);

      if (!task) {
        return;
      }

      // 检查任务状态
      if (task.status === 'completed' || task.status === 'limit_reached') {
        return;
      }

      // 根据任务类型处理
      switch (task.title) {
        case '每日签到':
          this.doSignIn(taskId);
          break;
        case '浏览社区公告':
          this.navigateTo('/pages/community/notice/notice');
          break;
        case '浏览社区活动详情':
          this.navigateTo('/pages/community/activity/activity');
          break;
        case '发布社区帖子':
          this.navigateTo('/pages/community/post/post');
          break;
        case '回复/评论社区帖子':
          this.navigateTo('/pages/community/index/index');
          break;
        case '提交报事报修申请':
          this.navigateTo('/pages/property/repair/repair');
          break;
        case '报事报修评价':
          this.navigateTo('/pages/property/repair/list');
          break;
        case '按时缴纳物业费':
        case '提前10天以上缴费':
          this.navigateTo('/pages/payment/index/index');
          break;
        case '完善个人资料':
          this.navigateTo('/pages/user/profile/profile');
          break;
        case '绑定手机号':
          this.navigateTo('/pages/user/phone/phone');
          break;
        case '绑定房屋信息':
          this.navigateTo('/pages/house/bind/bind');
          break;
        default:
          // 其他任务显示开发中提示
          wx.showToast({
            title: '功能开发中',
            icon: 'none',
            duration: 2000
          });
      }
    } catch (e) {
      console.error('完成任务失败:', e);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 页面跳转
  navigateTo: function(url) {
    wx.navigateTo({
      url: url,
      fail: function() {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 执行签到
  doSignIn: function(taskId) {
    try {
      wx.showLoading({
        title: '签到中...',
        mask: true
      });

      // 使用积分服务完成任务
      PointsService.completeTask(taskId).then(result => {
        wx.hideLoading();

        // 显示签到成功
        if (result.continuousDays) {
          // 连续签到
          if (result.extraPoints) {
            // 有额外奖励
            wx.showModal({
              title: '签到成功',
              content: `恭喜您已连续签到${result.continuousDays}天，获得${result.points}积分奖励！`,
              showCancel: false
            });
          } else {
            wx.showToast({
              title: `签到成功，已连续${result.continuousDays}天`,
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: `签到成功，获得${result.points}积分`,
            icon: 'none',
            duration: 2000
          });
        }

        // 更新任务状态和用户积分
        this.loadTasks();
        this.loadUserInfo();
      }).catch(err => {
        wx.hideLoading();
        console.error('签到失败:', err);

        // 显示错误信息
        wx.showToast({
          title: err.message || '签到失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    } catch (e) {
      wx.hideLoading();
      console.error('签到失败:', e);

      // 显示错误信息
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }
})
