# 请求队列管理优化测试指南

## 优化内容

### 🎯 核心问题解决
**问题**：当token已经存在且有效时，所有请求仍然被加入等待队列，导致不必要的延迟。

**解决方案**：
1. **应用启动时立即检查登录状态**
2. **智能判断是否需要等待登录**
3. **有效token直接执行请求**

### 🔧 优化逻辑

#### 1. 应用启动时状态检查
```javascript
// 模块加载时立即执行
const checkInitialLoginStatus = () => {
  if (checkUserLogin() && !isTokenExpired()) {
    console.log('应用启动时发现有效登录状态，直接标记为已初始化');
    isAppInitialized = true;
    return true;
  }
  return false;
};

// 立即执行检查
checkInitialLoginStatus();
```

#### 2. 智能等待判断
```javascript
const shouldWaitForLogin = (url, needToken) => {
  // 登录相关请求 - 不等待
  if (url.includes('/auth/token') || url.includes('/oauth/login')) {
    return false;
  }
  
  // 不需要token的请求 - 不等待
  if (!needToken) {
    return false;
  }
  
  // 已有有效token - 不等待
  if (checkUserLogin() && !isTokenExpired()) {
    return false;
  }
  
  // 只有在应用未初始化且正在登录时才等待
  return !isAppInitialized && isLoginInProgress;
};
```

## 测试场景

### 场景1：应用冷启动 - 有有效token
**预期行为**：
- ✅ 应用启动时立即标记为已初始化
- ✅ 所有API请求直接执行，不进入队列
- ✅ 只有在收到401响应时才触发token刷新

**测试步骤**：
1. 确保本地存储有有效的 `userInfo` 和 `access_token`
2. 重启小程序
3. 立即发起多个API请求
4. 观察控制台日志

**预期日志**：
```
检查应用启动时的登录状态
应用启动时发现有效登录状态，直接标记为已初始化
请求/users-api/v1/member/room/page直接执行，不需要等待
```

### 场景2：应用冷启动 - 无有效token
**预期行为**：
- ✅ 应用启动时检测到需要登录
- ✅ 第一个需要token的请求触发登录流程
- ✅ 后续请求加入队列等待登录完成
- ✅ 登录成功后处理队列中的所有请求

**测试步骤**：
1. 清除本地存储中的登录信息
2. 重启小程序
3. 同时发起多个需要token的API请求
4. 观察登录流程和请求队列处理

**预期日志**：
```
检查应用启动时的登录状态
应用启动时未发现有效登录状态
请求/users-api/v1/member/room/page需要等待登录完成，加入队列
开始登录流程...
登录成功，处理队列中的请求
```

### 场景3：token过期 - 401响应处理
**预期行为**：
- ✅ 收到401响应时触发token刷新
- ✅ 刷新期间的请求加入队列
- ✅ 刷新成功后重新执行原请求和队列请求
- ✅ 刷新失败时重新登录

**测试步骤**：
1. 模拟token过期（修改token值）
2. 发起需要token的API请求
3. 观察token刷新流程

### 场景4：并发请求处理
**预期行为**：
- ✅ 有效token时所有请求并发执行
- ✅ 无token时只触发一次登录，其他请求等待
- ✅ 401时只触发一次token刷新，其他请求等待

**测试步骤**：
1. 在不同登录状态下同时发起10个API请求
2. 观察请求的执行顺序和登录触发次数

## 测试代码示例

### 测试有效token场景
```javascript
// 在页面的onLoad中测试
onLoad() {
  console.log('=== 测试有效token场景 ===');
  
  // 同时发起多个请求
  const requests = [
    houseApi.getMyHouses(),
    communityApi.getCommunityList(),
    userApi.getUserInfo()
  ];
  
  Promise.all(requests)
    .then(results => {
      console.log('所有请求完成', results);
    })
    .catch(error => {
      console.error('请求失败', error);
    });
}
```

### 测试无token场景
```javascript
// 清除登录信息后测试
clearLoginAndTest() {
  console.log('=== 测试无token场景 ===');
  
  // 清除登录信息
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('refresh_token');
  
  // 重新发起请求
  setTimeout(() => {
    houseApi.getMyHouses()
      .then(result => {
        console.log('登录后请求成功', result);
      })
      .catch(error => {
        console.error('请求失败', error);
      });
  }, 100);
}
```

## 性能验证

### 1. 启动性能
**测试指标**：
- 应用启动到第一个API请求完成的时间
- 有token vs 无token的启动时间差异

**预期结果**：
- 有有效token时：< 500ms
- 无token需要登录时：< 2000ms

### 2. 并发性能
**测试指标**：
- 10个并发请求的完成时间
- 请求队列的处理效率

**预期结果**：
- 有效token时所有请求并发执行
- 无token时登录完成后快速处理队列

### 3. 内存使用
**测试指标**：
- 请求队列的内存占用
- 登录状态变量的内存使用

**预期结果**：
- 队列及时清空，无内存泄漏
- 状态变量正确重置

## 调试技巧

### 1. 控制台日志监控
关键日志点：
```javascript
// 应用初始化
"检查应用启动时的登录状态"
"应用启动时发现有效登录状态，直接标记为已初始化"

// 请求判断
"请求XXX直接执行，不需要等待"
"请求XXX需要等待登录完成，加入队列"

// 登录流程
"开始登录流程"
"登录成功，处理队列中的请求"

// Token刷新
"收到401响应，开始刷新token"
"Token刷新成功，重新发起请求"
```

### 2. 网络面板监控
观察要点：
- 请求的发起时间和顺序
- 登录请求的触发次数
- 401响应的处理情况

### 3. 存储状态检查
检查项目：
```javascript
// 在控制台执行
console.log('userInfo:', wx.getStorageSync('userInfo'));
console.log('access_token:', wx.getStorageSync('access_token'));
console.log('refresh_token:', wx.getStorageSync('refresh_token'));
```

## 常见问题排查

### 1. 请求仍然被加入队列
**可能原因**：
- `isAppInitialized` 状态未正确设置
- `checkUserLogin()` 返回false
- `isTokenExpired()` 返回true

**排查方法**：
```javascript
console.log('isAppInitialized:', isAppInitialized);
console.log('checkUserLogin():', checkUserLogin());
console.log('isTokenExpired():', isTokenExpired());
```

### 2. 登录触发多次
**可能原因**：
- `isLoginInProgress` 状态管理错误
- 并发请求未正确处理

**排查方法**：
检查登录状态变量的设置和重置时机

### 3. 队列请求未执行
**可能原因**：
- `processRequestQueue()` 未被调用
- 队列中的请求参数错误

**排查方法**：
```javascript
console.log('requestQueue length:', requestQueue.length);
console.log('requestQueue:', requestQueue);
```

## 验收标准

### ✅ 功能正确性
- [ ] 有效token时请求直接执行
- [ ] 无token时正确触发登录
- [ ] 401响应正确处理
- [ ] 队列请求正确执行

### ✅ 性能表现
- [ ] 启动时间符合预期
- [ ] 并发请求处理高效
- [ ] 内存使用合理

### ✅ 用户体验
- [ ] 无不必要的等待时间
- [ ] 错误处理友好
- [ ] 状态反馈及时

### ✅ 稳定性
- [ ] 无内存泄漏
- [ ] 状态管理正确
- [ ] 异常情况处理完善

通过以上测试确保请求队列管理优化达到预期效果，提升应用的响应速度和用户体验。
