/* 订单二维码页面样式 */
.qrcode-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 20rpx;
  box-sizing: border-box;
}

.dark {
  background-color: #1a1a1a;
  color: #fff;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.dark .card {
  background-color: #2a2a2a;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.header {
  padding: 20rpx 0 30rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
  margin-bottom: 30rpx;
}

.dark .header {
  border-bottom: 1px solid #3a3a3a;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #999;
}

.dark .subtitle {
  color: #888;
}

.qrcode-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0 40rpx;
}

.qrcode {
  width: 400rpx;
  height: 400rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
  border: 1rpx solid #eee;
}

.dark .qrcode {
  background-color: #fff; /* QR code always white for better scanning */
}

.qr-canvas {
  width: 360rpx;
  height: 360rpx;
}

.failed-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.failed-text {
  font-size: 28rpx;
  color: #ff4d4f;
}

.order-info {
  width: 100%;
}

.order-no {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.copy-btn {
  margin-left: 20rpx;
  color: #1890ff;
  font-size: 24rpx;
}

.dark .copy-btn {
  color: #4da6ff;
}

.help-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #888;
}

.help-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.goods-info {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #eee;
}

.dark .goods-info {
  border-top: 1px solid #3a3a3a;
}

.order-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.goods-card {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
}

.dark .goods-card {
  background-color: #333;
}

.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px dashed #eee;
}

.dark .goods-header {
  border-bottom: 1px dashed #444;
}

.goods-name {
  font-size: 28rpx;
  font-weight: 500;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.dark .goods-price {
  color: #ff9d4d;
}

.goods-detail {
  font-size: 26rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.label {
  color: #888;
}

.dark .label {
  color: #aaa;
}

.status-pending {
  color: #ff6b00;
}

.dark .status-pending {
  color: #ff9d4d;
}

.action-btns {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #1890ff;
  color: #fff;
}

.dark .btn-primary {
  background-color: #096dd9;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.dark .btn-secondary {
  background-color: #444;
  color: #eee;
}

.tips-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.tips-card {
  width: 85%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.dark .tips-card {
  background-color: #2a2a2a;
}

.tips-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.dark .tips-header {
  border-bottom: 1px solid #3a3a3a;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
}

.dark .close-icon {
  color: #888;
}

.tips-content {
  padding: 30rpx;
}

.tip-item {
  display: flex;
  margin-bottom: 25rpx;
  align-items: center;
}

.tip-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.dark .tip-number {
  background-color: #096dd9;
}

.tip-text {
  font-size: 28rpx;
}

.tip-warning {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #fff7e6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fa8c16;
}

.dark .tip-warning {
  background-color: #3b3022;
  color: #ffc069;
}
