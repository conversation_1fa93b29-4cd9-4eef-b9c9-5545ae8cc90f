// components/line-icon/line-icon.js
Component({
  properties: {
    name: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: '#007AFF'
    },
    size: {
      type: Number,
      value: 24
    },
    bgColor: {
      type: String,
      value: '#f0f7ff'
    }
  },
  data: {
    ctx: null
  },
  lifetimes: {
    ready: function() {
      const query = this.createSelectorQuery();
      query.select('#myCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置画布大小
          const dpr = wx.getSystemInfoSync().pixelRatio;
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          ctx.scale(dpr, dpr);

          this.ctx = ctx;
          this.canvas = canvas;
          this.drawIcon();
        });
    }
  },
  observers: {
    'name, color, size': function() {
      // 当属性变化时重新绘制图标
      if (this.ctx) {
        this.drawIcon();
      }
    }
  },
  methods: {
    drawIcon: function() {
      if (!this.ctx) return;

      const name = this.data.name;
      const color = this.data.color;
      const size = this.data.size;
      const ctx = this.ctx;

      // 清空画布
      ctx.clearRect(0, 0, size, size);

      // 设置线条样式
      ctx.lineWidth = 1.5;
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      // 根据图标名称绘制不同的图形
      switch(name) {
        // 消息图标
        case 'message-repair':
          this.drawEditIcon(ctx, size);
          break;
        case 'message-water':
          this.drawLocationIcon(ctx, size);
          break;
        case 'message-facility':
          this.drawInfoIcon(ctx, size);
          break;

        // 快捷服务图标
        case 'payment':
          this.drawCardIcon(ctx, size);
          break;
        case 'repair':
          this.drawWrenchIcon(ctx, size);
          break;
        case 'vote':
          this.drawEditIcon(ctx, size);
          break;
        case 'goods':
          this.drawShirtIcon(ctx, size);
          break;
        case 'visitor':
          this.drawPersonIcon(ctx, size);
          break;
        case 'garbage':
          this.drawTrashIcon(ctx, size);
          break;
        case 'service':
          this.drawServiceIcon(ctx, size);
          break;
        case 'renovation':
          this.drawBookIcon(ctx, size);
          break;
        default:
          // 默认绘制一个圆形
          ctx.beginPath();
          ctx.arc(size/2, size/2, size/3, 0, 2 * Math.PI);
          ctx.stroke();
      }
    },

    // 编辑/笔图标
    drawEditIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      ctx.beginPath();
      // 绘制笔的主体
      ctx.moveTo(padding + width * 0.2, padding + height * 0.8);
      ctx.lineTo(padding + width * 0.2, padding + height * 0.6);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.1);
      ctx.lineTo(padding + width * 0.9, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.4, padding + height * 0.8);
      ctx.closePath();
      ctx.stroke();

      // 绘制笔尖
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.7, padding + height * 0.1);
      ctx.lineTo(padding + width * 0.8, padding);
      ctx.lineTo(padding + width, padding + height * 0.2);
      ctx.lineTo(padding + width * 0.9, padding + height * 0.3);
      ctx.stroke();
    },

    // 位置/水滴图标
    drawLocationIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      ctx.beginPath();
      // 绘制水滴形状
      ctx.moveTo(padding + width/2, padding);
      ctx.bezierCurveTo(
        padding, padding + height * 0.4,
        padding, padding + height * 0.8,
        padding + width/2, padding + height
      );
      ctx.bezierCurveTo(
        padding + width, padding + height * 0.8,
        padding + width, padding + height * 0.4,
        padding + width/2, padding
      );
      ctx.stroke();
    },

    // 信息图标
    drawInfoIcon: function(ctx, size) {
      const padding = size * 0.2;
      const radius = (size - 2 * padding) / 2;
      const centerX = size / 2;
      const centerY = size / 2;

      // 绘制圆形
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.stroke();

      // 绘制感叹号的点
      ctx.beginPath();
      ctx.arc(centerX, centerY + radius * 0.5, size * 0.03, 0, 2 * Math.PI);
      ctx.fill();

      // 绘制感叹号的线
      ctx.beginPath();
      ctx.moveTo(centerX, centerY - radius * 0.4);
      ctx.lineTo(centerX, centerY + radius * 0.2);
      ctx.stroke();
    },

    // 卡片/支付图标
    drawCardIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width * 0.7;

      // 绘制卡片轮廓
      ctx.beginPath();
      ctx.rect(padding, padding + (size - 2 * padding - height) / 2, width, height);
      ctx.stroke();

      // 绘制卡片上的横线
      ctx.beginPath();
      ctx.moveTo(padding, padding + (size - 2 * padding - height) / 2 + height * 0.3);
      ctx.lineTo(padding + width, padding + (size - 2 * padding - height) / 2 + height * 0.3);
      ctx.stroke();
    },

    // 扳手/维修图标
    drawWrenchIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制扳手
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.7);
      ctx.lineTo(padding + width * 0.9, padding + height * 0.5);
      ctx.lineTo(padding + width * 0.5, padding + height * 0.1);
      ctx.lineTo(padding + width * 0.3, padding + height * 0.3);
      ctx.stroke();

      // 绘制扳手的手柄
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.1, padding + height * 0.9);
      ctx.stroke();
    },

    // 衣物图标
    drawShirtIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制T恤轮廓
      ctx.beginPath();
      ctx.moveTo(padding, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.3, padding);
      ctx.lineTo(padding + width * 0.7, padding);
      ctx.lineTo(padding + width, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.8, padding + height);
      ctx.lineTo(padding + width * 0.2, padding + height);
      ctx.lineTo(padding + width * 0.2, padding + height * 0.3);
      ctx.closePath();
      ctx.stroke();

      // 绘制领口
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.4, padding);
      ctx.lineTo(padding + width * 0.4, padding + height * 0.2);
      ctx.lineTo(padding + width * 0.6, padding + height * 0.2);
      ctx.lineTo(padding + width * 0.6, padding);
      ctx.stroke();
    },

    // 人物/访客图标
    drawPersonIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制头部
      ctx.beginPath();
      ctx.arc(padding + width/2, padding + height * 0.3, width * 0.2, 0, 2 * Math.PI);
      ctx.stroke();

      // 绘制身体
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.9);
      ctx.lineTo(padding + width * 0.3, padding + height * 0.6);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.6);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.9);
      ctx.stroke();

      // 绘制手臂
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.2, padding + height * 0.5);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.5);
      ctx.stroke();
    },

    // 垃圾桶图标
    drawTrashIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制垃圾桶盖
      ctx.beginPath();
      ctx.moveTo(padding, padding + height * 0.2);
      ctx.lineTo(padding + width, padding + height * 0.2);
      ctx.stroke();

      // 绘制垃圾桶提手
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.4, padding);
      ctx.lineTo(padding + width * 0.4, padding + height * 0.2);
      ctx.moveTo(padding + width * 0.6, padding);
      ctx.lineTo(padding + width * 0.6, padding + height * 0.2);
      ctx.stroke();

      // 绘制垃圾桶主体
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.2, padding + height * 0.2);
      ctx.lineTo(padding + width * 0.2, padding + height);
      ctx.lineTo(padding + width * 0.8, padding + height);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.2);
      ctx.stroke();
    },

    // 服务图标
    drawServiceIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制第一个人
      ctx.beginPath();
      ctx.arc(padding + width * 0.3, padding + height * 0.3, width * 0.15, 0, 2 * Math.PI);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.45);
      ctx.lineTo(padding + width * 0.3, padding + height * 0.7);
      ctx.stroke();

      // 绘制第二个人
      ctx.beginPath();
      ctx.arc(padding + width * 0.7, padding + height * 0.3, width * 0.15, 0, 2 * Math.PI);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(padding + width * 0.7, padding + height * 0.45);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.7);
      ctx.stroke();

      // 绘制连接线
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.6);
      ctx.lineTo(padding + width * 0.7, padding + height * 0.6);
      ctx.stroke();
    },

    // 书本/装修图标
    drawBookIcon: function(ctx, size) {
      const padding = size * 0.2;
      const width = size - 2 * padding;
      const height = width;

      // 绘制书本外形
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding);
      ctx.lineTo(padding + width * 0.3, padding + height);
      ctx.lineTo(padding + width, padding + height);
      ctx.lineTo(padding + width, padding);
      ctx.stroke();

      // 绘制书脊
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding);
      ctx.bezierCurveTo(
        padding, padding + height * 0.2,
        padding, padding + height * 0.8,
        padding + width * 0.3, padding + height
      );
      ctx.stroke();

      // 绘制书页
      ctx.beginPath();
      ctx.moveTo(padding + width * 0.3, padding + height * 0.3);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.3);
      ctx.moveTo(padding + width * 0.3, padding + height * 0.5);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.5);
      ctx.moveTo(padding + width * 0.3, padding + height * 0.7);
      ctx.lineTo(padding + width * 0.8, padding + height * 0.7);
      ctx.stroke();
    }
  }
})
