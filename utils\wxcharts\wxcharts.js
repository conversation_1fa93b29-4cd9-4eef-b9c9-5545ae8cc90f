/*
 * wx-charts.js v1.0
 * https://github.com/xiaolin3303/wx-charts
 * 2016-11-28
 * Designed and built with all the love of Web
 */

'use strict';

var config = {
    yAxisWidth: 15,
    yAxisSplit: 5,
    xAxisHeight: 15,
    xAxisLineHeight: 15,
    legendHeight: 15,
    yAxisTitleWidth: 15,
    padding: 12,
    columePadding: 3,
    fontSize: 10,
    dataPointShape: ['circle', 'diamond', 'triangle', 'rect'],
    colors: ['#7cb5ec', '#f7a35c', '#434348', '#90ed7d', '#f15c80', '#8085e9'],
    pieChartLinePadding: 25,
    pieChartTextPadding: 15,
    xAxisTextPadding: 3,
    titleColor: '#333333',
    titleFontSize: 20,
    subtitleColor: '#999999',
    subtitleFontSize: 15,
    toolTipPadding: 3,
    toolTipBackground: '#000000',
    toolTipOpacity: 0.7,
    toolTipLineHeight: 14,
    radarGridCount: 3,
    radarLabelTextMargin: 15
};

// 兼容H5环境
function getH5Context() {
    return {
        setStrokeStyle: function setStrokeStyle(e) {
            this.strokeStyle = e;
        },
        setLineWidth: function setLineWidth(e) {
            this.lineWidth = e;
        },
        setFillStyle: function setFillStyle(e) {
            this.fillStyle = e;
        }
    };
}

// 获取微信小程序上下文
function getContext(canvas) {
    if (!canvas) return null;

    // 在微信小程序中，canvas 参数实际上已经是 context 对象
    var ctx = canvas;

    // 适配不同环境
    if (typeof ctx.setStrokeStyle !== 'function') {
        Object.assign(ctx, getH5Context());
    }

    return ctx;
}

// 创建图表
function createChart(config) {
    var chartType = config.type || 'line';
    var ctx = getContext(config.canvas);
    if (!ctx) return null;

    // 根据类型创建不同的图表
    switch (chartType) {
        case 'pie':
            return drawPieChart(ctx, config);
        case 'ring':
            return drawRingChart(ctx, config);
        case 'line':
            return drawLineChart(ctx, config);
        case 'column':
            return drawColumnChart(ctx, config);
        case 'area':
            return drawAreaChart(ctx, config);
        case 'radar':
            return drawRadarChart(ctx, config);
        default:
            return drawLineChart(ctx, config);
    }
}

// 绘制饼图
function drawPieChart(ctx, config) {
    var series = config.series || [];
    var width = config.width || 300;
    var height = config.height || 200;
    var center = {
        x: width / 2,
        y: height / 2
    };
    var radius = Math.min(width, height) / 2 - (config.padding || 10);

    // 计算总和
    var total = 0;
    series.forEach(function(item) {
        total += item.data;
    });

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制饼图
    var startAngle = 0;
    series.forEach(function(item, index) {
        var percent = item.data / total;
        var endAngle = startAngle + 2 * Math.PI * percent;

        // 绘制扇形
        ctx.beginPath();
        ctx.moveTo(center.x, center.y);
        ctx.arc(center.x, center.y, radius, startAngle, endAngle);
        ctx.setFillStyle(config.colors[index % config.colors.length]);
        ctx.fill();

        // 绘制分割线
        ctx.beginPath();
        ctx.setStrokeStyle('#ffffff');
        ctx.setLineWidth(1);
        ctx.moveTo(center.x, center.y);
        ctx.lineTo(
            center.x + Math.cos(endAngle) * radius,
            center.y + Math.sin(endAngle) * radius
        );
        ctx.stroke();

        startAngle = endAngle;
    });

    // 绘制图例
    if (config.showLegend) {
        drawLegend(ctx, config, series);
    }

    return {
        type: 'pie',
        canvas: config.canvas,
        update: function(newSeries) {
            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 更新数据
            config.series = newSeries || series;

            // 重新绘制
            drawPieChart(ctx, config);
        }
    };
}

// 绘制环形图
function drawRingChart(ctx, config) {
    var pieChart = drawPieChart(ctx, config);
    var width = config.width || 300;
    var height = config.height || 200;
    var center = {
        x: width / 2,
        y: height / 2
    };
    var innerRadius = (Math.min(width, height) / 2 - config.padding) * 0.6;

    // 绘制内圆
    ctx.beginPath();
    ctx.arc(center.x, center.y, innerRadius, 0, 2 * Math.PI);
    ctx.setFillStyle('#FFFFFF');
    ctx.fill();

    return {
        type: 'ring',
        canvas: config.canvas,
        update: pieChart.update
    };
}

// 绘制折线图
function drawLineChart(ctx, config) {
    var series = config.series || [];
    var categories = config.categories || [];
    var width = config.width || 300;
    var height = config.height || 200;
    var padding = config.padding || 10;
    var yAxisHeight = height - 2 * padding - config.xAxisHeight;
    var xAxisWidth = width - 2 * padding - config.yAxisWidth;

    // 计算Y轴范围
    var maxValue = 0;
    var minValue = 0;
    series.forEach(function(item) {
        item.data.forEach(function(value) {
            if (value > maxValue) maxValue = value;
            if (value < minValue) minValue = value;
        });
    });

    // 绘制Y轴
    drawYAxis(ctx, config, minValue, maxValue, yAxisHeight, padding);

    // 绘制X轴
    drawXAxis(ctx, config, categories, xAxisWidth, padding);

    // 绘制折线
    series.forEach(function(item, index) {
        drawLine(ctx, config, item.data, index, minValue, maxValue, xAxisWidth, yAxisHeight, padding);
    });

    // 绘制图例
    if (config.showLegend) {
        drawLegend(ctx, config, series);
    }

    return {
        type: 'line',
        canvas: config.canvas,
        update: function(newSeries) {
            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 更新数据
            config.series = newSeries || series;

            // 重新绘制
            drawLineChart(ctx, config);
        }
    };
}

// 绘制柱状图
function drawColumnChart(ctx, config) {
    var series = config.series || [];
    var categories = config.categories || [];
    var width = config.width || 300;
    var height = config.height || 200;
    var padding = config.padding || 10;
    var yAxisHeight = height - 2 * padding - config.xAxisHeight;
    var xAxisWidth = width - 2 * padding - config.yAxisWidth;

    // 计算Y轴范围
    var maxValue = 0;
    var minValue = 0;
    series.forEach(function(item) {
        item.data.forEach(function(value) {
            if (value > maxValue) maxValue = value;
            if (value < minValue) minValue = value;
        });
    });

    // 绘制Y轴
    drawYAxis(ctx, config, minValue, maxValue, yAxisHeight, padding);

    // 绘制X轴
    drawXAxis(ctx, config, categories, xAxisWidth, padding);

    // 绘制柱状
    drawColumn(ctx, config, series, categories, minValue, maxValue, xAxisWidth, yAxisHeight, padding);

    // 绘制图例
    if (config.showLegend) {
        drawLegend(ctx, config, series);
    }

    return {
        type: 'column',
        canvas: config.canvas,
        update: function(newSeries) {
            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 更新数据
            config.series = newSeries || series;

            // 重新绘制
            drawColumnChart(ctx, config);
        }
    };
}

// 绘制面积图
function drawAreaChart(ctx, config) {
    var lineChart = drawLineChart(ctx, config);

    // 绘制面积
    var series = config.series || [];
    var width = config.width || 300;
    var height = config.height || 200;
    var padding = config.padding || 10;
    var yAxisHeight = height - 2 * padding - config.xAxisHeight;
    var xAxisWidth = width - 2 * padding - config.yAxisWidth;

    // 计算Y轴范围
    var maxValue = 0;
    var minValue = 0;
    series.forEach(function(item) {
        item.data.forEach(function(value) {
            if (value > maxValue) maxValue = value;
            if (value < minValue) minValue = value;
        });
    });

    // 绘制面积
    series.forEach(function(item, index) {
        drawArea(ctx, config, item.data, index, minValue, maxValue, xAxisWidth, yAxisHeight, padding);
    });

    return {
        type: 'area',
        canvas: config.canvas,
        update: lineChart.update
    };
}

// 简化版的辅助函数
function drawYAxis(ctx, config, minValue, maxValue, yAxisHeight, padding) {
    // 绘制Y轴
    ctx.beginPath();
    ctx.setStrokeStyle('#cccccc');
    ctx.setLineWidth(1);
    ctx.moveTo(padding + config.yAxisWidth, padding);
    ctx.lineTo(padding + config.yAxisWidth, padding + yAxisHeight);
    ctx.stroke();

    // 绘制Y轴刻度
    var step = (maxValue - minValue) / config.yAxisSplit;
    for (var i = 0; i <= config.yAxisSplit; i++) {
        var y = padding + yAxisHeight - i * yAxisHeight / config.yAxisSplit;
        var value = minValue + i * step;

        // 绘制刻度线
        ctx.beginPath();
        ctx.setStrokeStyle('#cccccc');
        ctx.setLineWidth(1);
        ctx.moveTo(padding + config.yAxisWidth, y);
        ctx.lineTo(padding + config.yAxisWidth - 5, y);
        ctx.stroke();

        // 绘制刻度值
        ctx.setFillStyle('#666666');
        ctx.setFontSize(config.fontSize);
        ctx.fillText(value.toFixed(0), padding, y + 3);
    }
}

function drawXAxis(ctx, config, categories, xAxisWidth, padding) {
    // 绘制X轴
    ctx.beginPath();
    ctx.setStrokeStyle('#cccccc');
    ctx.setLineWidth(1);
    ctx.moveTo(padding + config.yAxisWidth, padding + config.xAxisHeight);
    ctx.lineTo(padding + config.yAxisWidth + xAxisWidth, padding + config.xAxisHeight);
    ctx.stroke();

    // 绘制X轴刻度
    var step = xAxisWidth / categories.length;
    for (var i = 0; i < categories.length; i++) {
        var x = padding + config.yAxisWidth + i * step + step / 2;

        // 绘制刻度线
        ctx.beginPath();
        ctx.setStrokeStyle('#cccccc');
        ctx.setLineWidth(1);
        ctx.moveTo(x, padding + config.xAxisHeight);
        ctx.lineTo(x, padding + config.xAxisHeight + 5);
        ctx.stroke();

        // 绘制刻度值
        ctx.setFillStyle('#666666');
        ctx.setFontSize(config.fontSize);
        ctx.fillText(categories[i], x - 10, padding + config.xAxisHeight + 15);
    }
}

function drawLine(ctx, config, data, index, minValue, maxValue, xAxisWidth, yAxisHeight, padding) {
    var step = xAxisWidth / data.length;
    var color = config.colors[index % config.colors.length];

    // 绘制折线
    ctx.beginPath();
    ctx.setStrokeStyle(color);
    ctx.setLineWidth(2);

    for (var i = 0; i < data.length; i++) {
        var x = padding + config.yAxisWidth + i * step + step / 2;
        var y = padding + yAxisHeight - (data[i] - minValue) / (maxValue - minValue) * yAxisHeight;

        if (i === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }

    ctx.stroke();

    // 绘制数据点
    for (var i = 0; i < data.length; i++) {
        var x = padding + config.yAxisWidth + i * step + step / 2;
        var y = padding + yAxisHeight - (data[i] - minValue) / (maxValue - minValue) * yAxisHeight;

        ctx.beginPath();
        ctx.setFillStyle(color);
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
    }
}

function drawColumn(ctx, config, series, categories, minValue, maxValue, xAxisWidth, yAxisHeight, padding) {
    var step = xAxisWidth / categories.length;
    var columnWidth = step / (series.length + 1) - config.columePadding;

    for (var i = 0; i < series.length; i++) {
        var color = config.colors[i % config.colors.length];

        for (var j = 0; j < series[i].data.length; j++) {
            var value = series[i].data[j];
            var x = padding + config.yAxisWidth + j * step + (i + 1) * columnWidth + i * config.columePadding;
            var height = (value - minValue) / (maxValue - minValue) * yAxisHeight;
            var y = padding + yAxisHeight - height;

            // 绘制柱状
            ctx.beginPath();
            ctx.setFillStyle(color);
            ctx.rect(x, y, columnWidth, height);
            ctx.fill();
        }
    }
}

function drawArea(ctx, config, data, index, minValue, maxValue, xAxisWidth, yAxisHeight, padding) {
    var step = xAxisWidth / data.length;
    var color = config.colors[index % config.colors.length];

    // 绘制面积
    ctx.beginPath();
    ctx.setFillStyle(color);
    ctx.globalAlpha = 0.2;

    // 起点
    ctx.moveTo(padding + config.yAxisWidth, padding + yAxisHeight);

    for (var i = 0; i < data.length; i++) {
        var x = padding + config.yAxisWidth + i * step + step / 2;
        var y = padding + yAxisHeight - (data[i] - minValue) / (maxValue - minValue) * yAxisHeight;

        ctx.lineTo(x, y);
    }

    // 终点
    ctx.lineTo(padding + config.yAxisWidth + xAxisWidth, padding + yAxisHeight);
    ctx.closePath();
    ctx.fill();
    ctx.globalAlpha = 1;

    // 绘制折线
    drawLine(ctx, config, data, index, minValue, maxValue, xAxisWidth, yAxisHeight, padding);
}

function drawLegend(ctx, config, series) {
    var legendHeight = config.legendHeight;
    var legendWidth = 0;
    var legendMargin = 10;
    var legendPadding = 5;
    var legendItemWidth = 0;

    // 计算图例宽度（简化版，不依赖 measureText）
    for (var i = 0; i < series.length; i++) {
        // 估算文本宽度：每个字符约 10 像素
        var textWidth = series[i].name.length * 10;
        legendItemWidth = 20 + textWidth + legendPadding;
        legendWidth += legendItemWidth + legendMargin;
    }

    // 绘制图例
    var startX = (config.width - legendWidth) / 2;
    var startY = config.height - legendHeight - 10;

    for (var i = 0; i < series.length; i++) {
        var color = config.colors[i % config.colors.length];

        // 绘制图例颜色块
        ctx.beginPath();
        ctx.setFillStyle(color);
        ctx.rect(startX, startY, 15, 15);
        ctx.fill();

        // 绘制图例文字
        ctx.setFillStyle('#666666');
        ctx.setFontSize(config.fontSize);
        ctx.fillText(series[i].name, startX + 20, startY + 12);

        startX += legendItemWidth + legendMargin;
    }
}

function drawRadarChart(ctx, config) {
    // 简化版雷达图实现
    return {
        type: 'radar',
        canvas: config.canvas,
        update: function() {}
    };
}

// 导出图表创建函数
module.exports = {
    create: createChart
};
