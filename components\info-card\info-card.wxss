/* 信息卡片组件样式 */
.info-card {
  margin: 16px 0;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
}

.info-card.with-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}
