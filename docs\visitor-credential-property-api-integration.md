# 访客凭证页面物业员工API接口集成

## 概述

当物业员工扫描访客二维码进入访客凭证页面时，使用专门的物业接口`visitorsApi.properGetVisitorDetail`来获取访客详情，而不是普通用户的接口。

## 修改内容

### 1. 问题分析

#### 原有逻辑
```javascript
// 原代码 - 所有用户都使用相同接口
getVisitorData: function () {
  visitorsApi.getVisitorDetail(this.data.visitorId).then(res => {
    // 处理访客数据
  });
}
```

**问题**：
- 物业员工和普通用户使用相同的接口
- 无法区分不同身份的访问权限
- 可能导致数据权限问题

### 2. 解决方案

#### 身份判断逻辑
```javascript
// 判断是否是物业员工身份扫码进入
const isPropertyStaff = this.data.fromScan && util.checkPropertyAuthenticated().isPropertyAuthenticated;

// 根据身份选择不同的API接口
const apiCall = isPropertyStaff ? 
  visitorsApi.properGetVisitorDetail(this.data.visitorId) : 
  visitorsApi.getVisitorDetail(this.data.visitorId);
```

#### 判断条件
1. **`this.data.fromScan`**: 确保是从扫码进入的
2. **`util.checkPropertyAuthenticated().isPropertyAuthenticated`**: 确保是已认证的物业员工

### 3. API接口对比

#### 普通用户接口
```javascript
// 接口路径: /users-api/v1/community/visitor?id={id}
visitorsApi.getVisitorDetail(id)
```

#### 物业员工接口  
```javascript
// 接口路径: /manage-api/v1/community/visitor?id={id}
visitorsApi.properGetVisitorDetail(id)
```

**区别**：
- **路径前缀不同**: `users-api` vs `manage-api`
- **权限级别不同**: 物业接口可能有更高的访问权限
- **数据范围不同**: 物业接口可能返回更详细的信息

### 4. 修改的方法

#### 4.1 获取访客数据方法
```javascript
getVisitorData: function () {
  var that = this
  
  // 判断是否是物业员工身份扫码进入
  const isPropertyStaff = this.data.fromScan && util.checkPropertyAuthenticated().isPropertyAuthenticated;
  
  // 根据身份选择不同的API接口
  const apiCall = isPropertyStaff ? 
    visitorsApi.properGetVisitorDetail(this.data.visitorId) : 
    visitorsApi.getVisitorDetail(this.data.visitorId);
  
  console.log('获取访客详情，使用接口：', isPropertyStaff ? 'properGetVisitorDetail' : 'getVisitorDetail');
  
  apiCall.then(res => {
    // 处理访客数据...
  });
}
```

#### 4.2 刷新访客数据方法
```javascript
refreshVisitorData: function () {
  console.log('核销成功后刷新访客数据...');

  // 判断是否是物业员工身份扫码进入
  const isPropertyStaff = this.data.fromScan && util.checkPropertyAuthenticated().isPropertyAuthenticated;
  
  // 根据身份选择不同的API接口
  const apiCall = isPropertyStaff ? 
    visitorsApi.properGetVisitorDetail(this.data.visitorId) : 
    visitorsApi.getVisitorDetail(this.data.visitorId);

  // 重新获取访客详情
  apiCall.then(res => {
    // 处理刷新后的数据...
  });
}
```

## 使用场景

### 1. 普通用户场景
- **进入方式**: 直接点击访客列表项、分享链接等
- **身份检查**: `fromScan = false` 或 `isPropertyAuthenticated = false`
- **使用接口**: `visitorsApi.getVisitorDetail`
- **权限范围**: 只能查看自己创建的访客

### 2. 物业员工扫码场景
- **进入方式**: 扫描访客二维码（`fromScan = true`）
- **身份检查**: `isPropertyAuthenticated = true`
- **使用接口**: `visitorsApi.properGetVisitorDetail`
- **权限范围**: 可以查看所有访客信息，进行核销等操作

## 数据流程

### 普通用户流程
```
用户点击访客 → fromScan=false → 使用getVisitorDetail → 获取自己的访客数据
```

### 物业员工扫码流程
```
物业员工扫码 → fromScan=true → 检查物业认证 → 使用properGetVisitorDetail → 获取完整访客数据
```

## 技术实现

### 1. 身份检查方法
```javascript
// 检查物业员工认证状态
const propertyResult = util.checkPropertyAuthenticated();
const isPropertyStaff = propertyResult.isPropertyAuthenticated;
```

### 2. 接口选择逻辑
```javascript
// 动态选择API接口
const apiCall = isPropertyStaff ? 
  visitorsApi.properGetVisitorDetail(this.data.visitorId) : 
  visitorsApi.getVisitorDetail(this.data.visitorId);
```

### 3. 调试日志
```javascript
console.log('获取访客详情，使用接口：', isPropertyStaff ? 'properGetVisitorDetail' : 'getVisitorDetail');
```

## 安全考虑

### 1. 权限验证
- **前端验证**: 检查用户的物业认证状态
- **后端验证**: API接口会进一步验证用户权限
- **双重保障**: 前后端都进行权限检查

### 2. 数据隔离
- **普通用户**: 只能访问自己创建的访客数据
- **物业员工**: 可以访问小区内所有访客数据
- **接口分离**: 使用不同的API路径确保数据隔离

### 3. 操作权限
- **普通用户**: 查看、编辑、删除自己的访客
- **物业员工**: 查看所有访客、进行核销操作

## 测试验证

### 1. 普通用户测试
1. 以普通用户身份登录
2. 点击访客列表中的访客
3. 验证使用`getVisitorDetail`接口
4. 确认只能看到自己的访客

### 2. 物业员工测试
1. 以物业员工身份登录并认证
2. 扫描访客二维码进入凭证页面
3. 验证使用`properGetVisitorDetail`接口
4. 确认可以看到访客详情和核销按钮

### 3. 权限边界测试
1. 未认证物业员工扫码 → 使用普通接口
2. 普通用户直接进入 → 使用普通接口
3. 已认证物业员工扫码 → 使用物业接口

## 相关文件

### 修改的文件
- `servicePackage/pages/visitor/credential/index.js` - 访客凭证页面逻辑

### 依赖的文件
- `api/visitorsApi.js` - 访客API接口定义
- `utils/util.js` - 工具方法（物业认证检查）

### API接口
- `visitorsApi.getVisitorDetail(id)` - 普通用户访客详情接口
- `visitorsApi.properGetVisitorDetail(id)` - 物业员工访客详情接口

## 总结

这个修改实现了：

1. **身份区分**: 根据用户身份和进入方式选择不同的API接口
2. **权限控制**: 物业员工使用专门的管理接口，获得更高权限
3. **安全保障**: 通过身份验证确保接口使用的安全性
4. **功能完整**: 支持普通用户和物业员工的不同使用场景

通过这个改进，访客凭证页面能够根据用户身份智能选择合适的API接口，确保数据安全和功能完整性。
