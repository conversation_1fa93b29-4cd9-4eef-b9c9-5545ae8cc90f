/* 社区群组组件样式 */
.groups-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: #f9fafb;
  padding: 16px;
  padding-bottom: 70px; /* 为底部选项卡预留空间 */
}

/* 群组列表样式 */
.group-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.group-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.group-content {
  flex: 1;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.group-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.group-members {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.member-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.group-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.group-owner {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  margin-bottom: 12px;
}

.owner-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.group-action {
  display: flex;
  justify-content: flex-end;
}

.detail-btn {
  background-color: #ff8c00;
  color: #fff;
  font-size: 14px;
  padding: 6px 16px;
  border-radius: 20px;
  line-height: 1.5;
  border: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 底部安全区域 */
.safe-bottom {
  height: 20px;
}
