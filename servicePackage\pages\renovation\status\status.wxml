<!--pages/renovation/status/status.wxml-->
<view class="container">
  <!-- 申请状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <view class="status-title">{{application.title}}</view>
      <view class="status-badge status-{{application.status}}">{{application.statusText}}</view>
    </view>
    <view class="status-info">
      <view class="info-row">
        <view class="info-label">申请编号</view>
        <view class="info-value">{{application.id}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">申请类型</view>
        <view class="info-value">{{application.type}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">申请日期</view>
        <view class="info-value">{{application.date}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">预计工期</view>
        <view class="info-value">{{application.startDate}} 至 {{application.endDate}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">当前进度</view>
        <view class="info-value progress-{{application.status}}">{{application.progress}}</view>
      </view>
    </view>
  </view>

  <!-- 进度时间线 -->
  <view class="timeline-section">
    <view class="section-title">申请进度</view>

    <view class="timeline">
      <view class="timeline-item {{item.status}}" wx:for="{{timeline}}" wx:key="step">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">{{item.title}}</view>
          <view class="timeline-time" wx:if="{{item.time}}">{{item.time}}</view>
          <view class="timeline-desc" wx:if="{{item.description}}">{{item.description}}</view>
          <view class="timeline-action" wx:if="{{item.action}}">
            <button class="action-button" bindtap="handleAction" data-action="{{item.action}}">{{item.actionText}}</button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系信息 -->
  <view class="contact-section">
    <view class="section-title">联系信息</view>

    <view class="contact-card">
      <view class="contact-row">
        <view class="contact-label">联系人</view>
        <view class="contact-value">{{application.contactName}}</view>
      </view>
      <view class="contact-row">
        <view class="contact-label">联系电话</view>
        <view class="contact-value">
          <text>{{application.contactPhone}}</text>
          <button class="call-button" bindtap="makeCall" data-phone="{{application.contactPhone}}">拨打</button>
        </view>
      </view>
      <view class="contact-row">
        <view class="contact-label">施工单位</view>
        <view class="contact-value">{{application.constructionCompany}}</view>
      </view>
      <view class="contact-row">
        <view class="contact-label">施工负责人</view>
        <view class="contact-value">{{application.constructionManager}}</view>
      </view>
      <view class="contact-row">
        <view class="contact-label">负责人电话</view>
        <view class="contact-value">
          <text>{{application.managerPhone}}</text>
          <button class="call-button" bindtap="makeCall" data-phone="{{application.managerPhone}}">拨打</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 物业反馈 -->
  <view class="feedback-section" wx:if="{{application.feedback}}">
    <view class="section-title">物业反馈</view>

    <view class="feedback-card">
      <view class="feedback-content">{{application.feedback}}</view>
      <view class="feedback-time">{{application.feedbackTime}}</view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions" wx:if="{{application.status === 'rejected'}}">
    <button class="reapply-button" bindtap="reapplyRenovation">重新申请</button>
  </view>

  <view class="bottom-actions" wx:if="{{application.status === 'pending'}}">
    <button class="notify-button" bindtap="notifyProperty">催办物业</button>
  </view>

  <view class="bottom-actions" wx:if="{{application.status === 'completed'}}">
    <button class="back-button" bindtap="goToList">返回列表</button>
  </view>
</view>