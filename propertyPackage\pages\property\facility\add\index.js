// pages/property/facility/add/index.js
const dateUtil = require('../../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: false,

    // 编辑模式
    isEditMode: false,
    facilityId: '',

    // 表单数据
    formData: {
      name: '',
      code: '',
      category: 'monitor',
      location: '',
      status: 'normal',
      brand: '',
      model: '',
      supplier: '',
      contactPhone: '',
      installDate: '',
      warrantyEndDate: '',
      responsiblePerson: '',
      description: ''
    },

    // 分类选项
    categoryOptions: [
      { value: 'monitor', label: '监控设施' },
      { value: 'door', label: '门禁设施' },
      { value: 'fire', label: '消防设施' },
      { value: 'light', label: '照明设施' },
      { value: 'elevator', label: '电梯设施' },
      { value: 'water', label: '给排水设施' },
      { value: 'hvac', label: '暖通设施' },
      { value: 'other', label: '其他设施' }
    ],

    // 状态选项
    statusOptions: [
      { value: 'normal', label: '正常', color: '#4CAF50' },
      { value: 'warning', label: '警告', color: '#FF9800' },
      { value: 'fault', label: '故障', color: '#F44336' },
      { value: 'offline', label: '离线', color: '#9E9E9E' },
      { value: 'maintenance', label: '维护中', color: '#2196F3' }
    ],

    // 图片上传
    uploadedImages: [],

    // 日期选择
    currentDateType: '', // installDate, warrantyEndDate
    currentDate: new Date().getTime(),
    showDatePicker: false,

    // 位置选择
    showLocationPicker: false,

    // 表单验证
    formErrors: {
      name: '',
      code: '',
      location: '',
      responsiblePerson: ''
    },

    // 提交状态
    isSubmitting: false,

    // 二维码/NFC绑定
    showQRCodeBindDialog: false,
    qrCodeValue: '',

    // 位置选择相关
    latitude: 23.099994,
    longitude: 113.324520,
    markers: []
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 检查是否是编辑模式
    if (options.id) {
      this.setData({
        isEditMode: true,
        facilityId: options.id,
        isLoading: true
      });

      // 加载设施详情
      this.loadFacilityDetail(options.id);
    } else {
      // 设置默认日期
      const formData = this.data.formData;
      const now = new Date();
      formData.installDate = dateUtil.formatDate(now);

      // 设置默认保修期为3年
      const warrantyDate = new Date(now);
      warrantyDate.setFullYear(warrantyDate.getFullYear() + 3);
      formData.warrantyEndDate = dateUtil.formatDate(warrantyDate);

      this.setData({
        formData: formData
      });
    }
  },

  // 加载设施详情（编辑模式）
  loadFacilityDetail: function(facilityId) {
    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const facility = this.getMockFacilityDetail(facilityId);

      this.setData({
        formData: facility,
        uploadedImages: facility.images || [],
        isLoading: false
      });

      // 如果有位置信息，设置地图标记
      if (facility.latitude && facility.longitude) {
        this.setData({
          latitude: facility.latitude,
          longitude: facility.longitude,
          markers: [{
            id: 1,
            latitude: facility.latitude,
            longitude: facility.longitude,
            title: facility.name
          }]
        });
      }
    }, 500);
  },

  // 获取模拟设施详情
  getMockFacilityDetail: function(facilityId) {
    const facilities = {
      '1': {
        id: '1',
        name: '小区正门监控',
        code: 'CAM-001',
        category: 'monitor',
        location: '小区正门',
        status: 'normal',
        brand: '海康威视',
        model: 'DS-2CD2T85G1-I8',
        supplier: '广州安防科技有限公司',
        contactPhone: '020-12345678',
        installDate: '2022-05-15',
        warrantyEndDate: '2025-05-15',
        responsiblePerson: '张工',
        description: '小区正门入口监控摄像头，用于监控进出人员和车辆',
        latitude: 23.099994,
        longitude: 113.324520,
        images: [
          '/images/facility/camera1.png',
          '/images/facility/camera2.png'
        ]
      }
    };

    return facilities[facilityId] || facilities['1'];
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 表单输入处理
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    // 更新表单数据
    const formData = this.data.formData;
    formData[field] = value;

    this.setData({
      formData: formData
    });

    // 清除对应的错误提示
    if (this.data.formErrors[field]) {
      const formErrors = this.data.formErrors;
      formErrors[field] = '';
      this.setData({
        formErrors: formErrors
      });
    }
  },

  // 选择分类
  onCategoryChange: function(e) {
    const { value } = e.detail;

    const formData = this.data.formData;
    // 根据选择的索引获取对应的分类值
    const categoryValue = this.data.categoryOptions[value].value;
    formData.category = categoryValue;

    this.setData({
      formData: formData
    });
  },

  // 选择状态
  onStatusChange: function(e) {
    const { value } = e.currentTarget.dataset;

    const formData = this.data.formData;
    formData.status = value;

    this.setData({
      formData: formData
    });
  },

  // 显示日期选择器
  showDatePicker: function(e) {
    const { type } = e.currentTarget.dataset;

    this.setData({
      currentDateType: type,
      showDatePicker: true,
      currentDate: [0, 0, 0] // 使用默认值
    });
  },

  // 隐藏日期选择器
  hideDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },

  // 确认日期选择
  confirmDatePicker: function() {
    const { currentDateType } = this.data;

    // 这里应该根据picker-view的选择生成日期
    // 简化处理，使用当前日期
    const today = new Date();
    const formattedDate = today.getFullYear() + '-' +
                         String(today.getMonth() + 1).padStart(2, '0') + '-' +
                         String(today.getDate()).padStart(2, '0');

    const formData = this.data.formData;
    formData[currentDateType] = formattedDate;

    this.setData({
      formData: formData,
      showDatePicker: false
    });
  },

  // 日期变更
  onDateChange: function(e) {
    // 记录选择的日期值
    this.setData({
      selectedDateValue: e.detail.value
    });
  },

  // 显示位置选择器
  showLocationPicker: function() {
    this.setData({
      showLocationPicker: true
    });
  },

  // 隐藏位置选择器
  hideLocationPicker: function() {
    this.setData({
      showLocationPicker: false
    });
  },

  // 确认位置选择
  confirmLocationPicker: function() {
    const { latitude, longitude, markers } = this.data;

    // 获取位置名称（这里应该是通过逆地理编码获取，目前简化处理）
    const locationName = markers.length > 0 ? markers[0].title : '未知位置';

    const formData = this.data.formData;
    formData.location = locationName;
    formData.latitude = latitude;
    formData.longitude = longitude;

    this.setData({
      formData: formData,
      showLocationPicker: false
    });
  },

  // 地图标记点击
  markerTap: function(e) {
    console.log('marker tap', e);
  },

  // 地图点击
  mapTap: function(e) {
    const { latitude, longitude } = e.detail;

    this.setData({
      latitude: latitude,
      longitude: longitude,
      markers: [{
        id: 1,
        latitude: latitude,
        longitude: longitude,
        title: '设施位置'
      }]
    });
  },

  // 上传图片
  uploadImage: function() {
    const { uploadedImages } = this.data;

    // 最多上传9张图片
    const remainCount = 9 - uploadedImages.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 这里应该是上传图片到服务器
        // 目前只是添加到本地数据
        const tempFilePaths = res.tempFilePaths;

        this.setData({
          uploadedImages: [...uploadedImages, ...tempFilePaths]
        });
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;

    wx.previewImage({
      current: uploadedImages[index],
      urls: uploadedImages
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;

    uploadedImages.splice(index, 1);

    this.setData({
      uploadedImages: uploadedImages
    });
  },

  // 显示二维码绑定对话框
  showQRCodeBind: function() {
    this.setData({
      showQRCodeBindDialog: true
    });
  },

  // 隐藏二维码绑定对话框
  hideQRCodeBind: function() {
    this.setData({
      showQRCodeBindDialog: false
    });
  },

  // 扫描二维码
  scanQRCode: function() {
    wx.scanCode({
      success: (res) => {
        this.setData({
          qrCodeValue: res.result,
          showQRCodeBindDialog: false
        });

        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 验证表单
  validateForm: function() {
    const { formData } = this.data;
    const formErrors = {
      name: '',
      code: '',
      location: '',
      responsiblePerson: ''
    };

    let isValid = true;

    // 验证设施名称
    if (!formData.name.trim()) {
      formErrors.name = '请填写设施名称';
      isValid = false;
    }

    // 验证设施编号
    if (!formData.code.trim()) {
      formErrors.code = '请填写设施编号';
      isValid = false;
    }

    // 验证位置
    if (!formData.location.trim()) {
      formErrors.location = '请填写设施位置';
      isValid = false;
    }

    // 验证负责人
    if (!formData.responsiblePerson.trim()) {
      formErrors.responsiblePerson = '请填写负责人';
      isValid = false;
    }

    this.setData({
      formErrors: formErrors
    });

    return isValid;
  },

  // 提交表单
  submitForm: function() {
    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    // 设置提交状态
    this.setData({
      isSubmitting: true
    });

    // 这里应该是提交表单到服务器
    // 目前只是模拟提交
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });

      wx.showToast({
        title: this.data.isEditMode ? '更新成功' : '添加成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
})
