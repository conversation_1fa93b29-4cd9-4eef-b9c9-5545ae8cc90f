<!--record.wxml-->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 顶部信息 -->
  <view class="header">
    <view class="title-row">
      <view class="title">积分明细</view>
      <view class="level-tag" wx:if="{{userLevel}}">{{userLevel.name}}</view>
    </view>
  </view>

  <!-- 积分信息 -->
  <view class="points-info">
    <view class="points-content">
      <view class="points-label">当前积分</view>
      <view class="points-value">{{userPoints}}</view>
    </view>
    <navigator url="/pages/points/level/level" class="level-link">
      <text>等级特权</text>
      <text class="arrow">></text>
    </navigator>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{showSearch}}">
    <view class="search-input-wrap">
      <input class="search-input"
             placeholder="搜索积分记录"
             value="{{searchValue}}"
             bindinput="inputSearch"
             confirm-type="search"
             bindconfirm="doSearch" />
      <view class="search-clear" bindtap="clearSearch" wx:if="{{searchValue}}">×</view>
    </view>
    <view class="search-btn" bindtap="doSearch">搜索</view>
    <view class="search-cancel" bindtap="toggleSearch">取消</view>
  </view>

  <!-- 工具栏 -->
  <view class="toolbar" wx:else>
    <view class="record-types">
      <view class="record-type {{currentType === item.id ? 'active' : ''}}"
            wx:for="{{recordTypes}}"
            wx:key="id"
            bindtap="switchRecordType"
            data-type="{{item.id}}">
        {{item.name}}
      </view>
    </view>

    <view class="toolbar-actions">
      <view class="action-btn search-icon" bindtap="toggleSearch">
        <image mode="aspectFit"></image>
      </view>
      <view class="action-btn filter-icon" bindtap="toggleFilter">
        <image mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <view class="filter-header">
      <view class="filter-title">时间筛选</view>
      <view class="filter-close" bindtap="toggleFilter">×</view>
    </view>

    <!-- 预设时间范围 -->
    <view class="time-ranges">
      <view class="time-range {{currentTimeRange === item.id ? 'active' : ''}}"
            wx:for="{{timeRanges}}"
            wx:key="id"
            bindtap="switchTimeRange"
            data-range="{{item.id}}">
        {{item.name}}
      </view>
    </view>

    <!-- 自定义时间范围 -->
    <view class="custom-date-range">
      <view class="date-range-title">自定义时间范围</view>
      <view class="date-range-inputs">
        <picker mode="date" value="{{startDate}}" bindchange="bindStartDateChange">
          <view class="date-picker {{startDate ? 'has-value' : ''}}">
            {{startDate || '开始日期'}}
          </view>
        </picker>
        <view class="date-separator">至</view>
        <picker mode="date" value="{{endDate}}" bindchange="bindEndDateChange">
          <view class="date-picker {{endDate ? 'has-value' : ''}}">
            {{endDate || '结束日期'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 筛选操作按钮 -->
    <view class="filter-actions">
      <button class="filter-btn reset-btn" bindtap="resetFilter">重置</button>
      <button class="filter-btn apply-btn" bindtap="applyCustomDateRange">应用</button>
    </view>
  </view>

  <!-- 当前筛选条件 -->
  <view class="current-filter" wx:if="{{currentTimeRange !== 'all' || searchValue}}">
    <view class="filter-tags">
      <view class="filter-tag" wx:if="{{currentTimeRange !== 'all'}}">
        {{currentTimeRangeName}}
        <text class="tag-close" bindtap="resetFilter">×</text>
      </view>
      <view class="filter-tag" wx:if="{{searchValue}}">
        搜索: {{searchValue}}
        <text class="tag-close" bindtap="clearSearch">×</text>
      </view>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-list">
    <block wx:if="{{filteredRecords.length > 0}}">
      <view class="record-item" wx:for="{{filteredRecords}}" wx:key="index">
        <view class="record-icon {{item.type === 'earn' ? 'earn-icon' : 'use-icon'}}">
          <text>{{item.type === 'earn' ? '+' : '-'}}</text>
        </view>
        <view class="record-info">
          <view class="record-desc">{{item.description}}</view>
          <view class="record-time">{{item.formattedDate}}</view>
        </view>
        <view class="record-points {{item.type === 'earn' ? 'earn-points' : 'use-points'}}">
          {{item.type === 'earn' ? '+' : '-'}}{{item.points}}
        </view>
      </view>

      <view class="loading-more" wx:if="{{loading}}">加载中...</view>
      <view class="no-more" wx:if="{{!hasMore && !loading && filteredRecords.length > 0}}">没有更多记录了</view>
    </block>

    <view class="empty-records" wx:else>
      <view class="empty-icon">📋</view>
      <view class="empty-text">暂无积分记录</view>
    </view>
  </view>
</view>
