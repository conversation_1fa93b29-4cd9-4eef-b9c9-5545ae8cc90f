# WebSocket优化版使用指南

## 概述

WebSocket管理器已经过优化，专注于连接生命周期管理，与现有的登录和token管理系统完全解耦。新版本具有更强的稳定性和更清晰的职责分离。

## 核心优化特性

### 1. 职责分离
- **WebSocket管理器**：只负责socket连接的生命周期管理
- **现有request.js**：继续负责用户登录和token管理
- **清晰边界**：通过`wx.getStorageSync('access_token')`获取token状态

### 2. 智能状态管理
- **无token等待**：检测到无token时，每10秒检查一次，避免无效连接尝试
- **token过期处理**：依赖现有token刷新机制，等待新token后自动重连
- **连接状态同步**：实时监控连接状态变化

### 3. 增强的重连机制
- **条件重连**：仅在有有效token且连接异常时启动重连
- **状态防护**：使用标志位防止并发重连
- **智能退避**：支持指数退避算法，避免频繁重连

### 4. 优化的心跳机制
- **条件启动**：仅在连接成功且token有效时启动心跳
- **实时检查**：心跳过程中持续检查token和连接状态
- **异常处理**：心跳超时时智能判断是否重连

## 使用方法

### 基本使用（无变化）
```javascript
// 在页面中使用
const app = getApp();

// 发送消息
app.sendPrivateMessage('text', 'Hello!', 'receiverId');

// 查询消息
app.queryPrivateMessage('receiverId');

// 检查连接状态
if (app.isWebSocketConnected()) {
  console.log('WebSocket已连接');
}
```

### 新增功能

#### 1. Token状态监控
```javascript
const app = getApp();
const wsManager = app.getWebSocketManager();

// 监听token状态变化
wsManager.on('tokenChange', (event) => {
  console.log('Token状态变化:', event.hasToken);
});

// 获取当前token状态
const tokenStatus = wsManager.getTokenStatus();
console.log('Token状态:', tokenStatus);
```

#### 2. 增强的连接统计
```javascript
const wsManager = app.getWebSocketManager();
const stats = wsManager.getStats();

console.log('详细统计:', {
  连接状态: stats.readyState,
  是否连接: stats.isConnected,
  有无Token: stats.hasToken,
  等待Token: stats.isWaitingForToken,
  重连次数: stats.reconnectAttempts,
  心跳状态: stats.heartbeatActive,
  消息队列: stats.messageQueueLength
});
```

#### 3. 手动控制
```javascript
const wsManager = app.getWebSocketManager();

// 手动触发token检查
wsManager.forceTokenCheck();

// 重置重连计数器
wsManager.resetReconnectCounter();
```

## 状态流转图

```
启动 → 初始化管理器 → 启动Token监控
                    ↓
              检查Token状态
                    ↓
        ┌─────────────────────────┐
        ↓                         ↓
    无Token                   有Token
        ↓                         ↓
    等待模式                  尝试连接
   (10秒检查)                     ↓
        ↓                   连接成功/失败
    检测到Token                   ↓
        ↓                   启动心跳/重连
    尝试连接 ←─────────────────────┘
```

## 关键改进点

### 1. Token状态处理
- **无token时**：进入等待模式，停止所有连接尝试
- **token变化时**：自动检测并响应token状态变化
- **token过期时**：依赖现有刷新机制，不重复实现登录逻辑

### 2. 连接生命周期
- **建立连接**：仅在有有效token时尝试
- **维持连接**：通过心跳机制保持活跃
- **异常处理**：智能判断异常原因，决定重连策略

### 3. 错误处理
- **网络错误**：检查token后决定重连
- **认证错误**：进入token等待状态
- **心跳超时**：关闭连接并重连

### 4. 资源管理
- **定时器管理**：统一管理所有定时器，避免泄漏
- **状态清理**：销毁时完整清理所有状态
- **内存优化**：及时清理无用的监听器和队列

## 配置说明

### Token检查间隔
```javascript
// 在websocket-config.js中可以调整
tokenState: {
  checkInterval: 10000  // 10秒检查一次token状态
}
```

### 重连策略
```javascript
// 重连配置
reconnect: {
  maxAttempts: 5,              // 最大重连次数
  baseDelay: 1000,             // 基础延迟
  maxDelay: 30000,             // 最大延迟
  useExponentialBackoff: true  // 指数退避
}
```

### 心跳配置
```javascript
// 心跳配置
heartbeat: {
  interval: 30000,    // 30秒间隔
  timeout: 5000,      // 5秒超时
  maxMissed: 3        // 最大丢失次数
}
```

## 与现有系统的协同

### 1. 与request.js协同
- WebSocket管理器不处理登录逻辑
- 依赖request.js的token管理和刷新机制
- 通过Storage监控token状态变化

### 2. 与app.js协同
- app.js负责初始化WebSocket管理器
- 提供便捷的消息发送接口
- 处理全局的消息分发

### 3. 错误处理一致性
- 使用相同的日志格式和级别
- 保持与现有错误处理的一致性
- 统一的用户提示和反馈

## 故障排查

### 常见问题
1. **连接无法建立**
   - 检查token是否存在：`wsManager.getTokenStatus()`
   - 检查是否在等待状态：`stats.isWaitingForToken`

2. **频繁重连**
   - 检查token是否频繁变化
   - 查看重连次数：`stats.reconnectAttempts`

3. **心跳异常**
   - 检查心跳状态：`stats.heartbeatActive`
   - 查看丢失次数：`stats.heartbeatMissedCount`

### 调试方法
```javascript
// 获取完整状态信息
const wsManager = app.getWebSocketManager();
console.log('WebSocket状态:', wsManager.getStats());

// 手动触发检查
wsManager.forceTokenCheck();

// 监听所有事件
wsManager.on('tokenChange', console.log);
wsManager.on('reconnect', console.log);
```

## 总结

优化后的WebSocket管理器具有以下优势：

1. **更清晰的职责分离**：专注于连接管理，不处理登录逻辑
2. **更智能的状态管理**：自动处理token状态变化
3. **更稳定的连接机制**：避免无效连接尝试，减少资源浪费
4. **更完善的错误处理**：智能判断错误原因，采取合适的处理策略
5. **更好的系统集成**：与现有系统无缝协同工作

这个优化版本可以直接替换原有实现，无需修改现有的使用代码，同时提供了更强的稳定性和可维护性。
