# 我的页面房产检测功能

## 功能概述

在我的页面中，当用户点击进入"我的工单"、"我的缴费"、"我的好物"、"我的活动"时，系统会检测用户是否已添加房产信息。如果没有房产，会弹出提示引导用户添加房产。

## 实现逻辑

### 1. 需要房产检测的页面

以下页面在跳转前需要进行房产检测：
- `/servicePackage/pages/workorder/list/index` - 我的工单
- `/servicePackage/pages/payment/history/history` - 我的缴费  
- `/profilePackage/pages/goods/my/my` - 我的好物
- `/profilePackage/pages/profile/my-activities/my-activities` - 我的活动

### 2. 检测流程

#### 基本条件检测
1. **登录状态检测** - 用户必须已登录
2. **社区选择检测** - 用户必须已选择社区
3. **实名认证检测** - 用户必须已完成实名认证

#### 房产检测
4. **房产数量检测** - 检查用户的房产数量（`houseCount`）
   - 如果 `houseCount > 0`：有房产，直接跳转到目标页面
   - 如果 `houseCount = 0`：无房产，显示添加房产提示

## 代码实现

### 1. 修改navigateWithAuth方法

```javascript
// 需要认证的页面跳转（服务记录相关）
navigateWithAuth: function (e) {
  const url = e.currentTarget.dataset.url;

  // 检查是否是需要房产验证的页面
  const needsPropertyCheck = [
    '/servicePackage/pages/workorder/list/index', // 我的工单
    '/servicePackage/pages/payment/history/history', // 我的缴费
    '/profilePackage/pages/goods/my/my', // 我的好物
    '/profilePackage/pages/profile/my-activities/my-activities' // 我的活动
  ];

  if (needsPropertyCheck.includes(url)) {
    // 需要房产验证的页面，先检查房产
    this.checkPropertyAndNavigate(url);
  } else {
    // 其他服务记录功能需要检测：已登录 + 已选择小区
    util.checkStatusAndNavigate(url, {
      requireAuth: true,
      requireCommunity: true
    });
  }
}
```

### 2. 新增checkPropertyAndNavigate方法

```javascript
// 检查房产并跳转
checkPropertyAndNavigate: function(url) {
  // 首先检查基本条件：已登录 + 已选择小区 + 已认证
  const authResult = util.checkUserAuthenticated();
  const communityResult = util.checkCommunitySelected();

  // 基本条件检测逻辑...
  
  // 基本条件满足，检查房产
  this.checkHouseProperty(url);
}
```

### 3. 新增checkHouseProperty方法

```javascript
// 检查房产
checkHouseProperty: function(url) {
  // 检查当前房产数量
  if (this.data.houseCount > 0) {
    // 有房产，直接跳转
    wx.navigateTo({
      url: url
    });
  } else {
    // 没有房产，显示添加房产提示
    wx.showModal({
      title: '添加房产',
      content: '使用此功能需要先添加房产信息，是否立即添加？',
      confirmText: '去添加',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 保存目标URL，添加房产后跳转
          wx.setStorageSync('redirectAfterAddHouse', url);
          wx.navigateTo({
            url: '/profilePackage/pages/profile/house/add/add'
          });
        }
      }
    });
  }
}
```

### 4. 修改房产添加页面的成功回调

在 `profilePackage/pages/profile/house/add/add.js` 中修改 `confirmSuccess` 方法：

```javascript
// 确认成功
confirmSuccess: function () {
  this.setData({
    showSuccessDialog: false
  });

  // 检查是否有待跳转的URL（从我的页面添加房产后跳转）
  const redirectAfterAddHouse = wx.getStorageSync('redirectAfterAddHouse');
  if (redirectAfterAddHouse) {
    // 清除存储的跳转URL
    wx.removeStorageSync('redirectAfterAddHouse');
    // 跳转到目标页面
    wx.navigateTo({
      url: redirectAfterAddHouse
    });
    return;
  }

  // 原有的跳转逻辑...
}
```

## 用户体验流程

### 场景1：有房产的用户
1. 用户点击"我的工单"
2. 系统检测：已登录 ✓ 已选择社区 ✓ 已认证 ✓ 有房产 ✓
3. 直接跳转到工单列表页面

### 场景2：无房产的用户
1. 用户点击"我的工单"
2. 系统检测：已登录 ✓ 已选择社区 ✓ 已认证 ✓ 无房产 ✗
3. 弹出提示："使用此功能需要先添加房产信息，是否立即添加？"
4. 用户点击"去添加"
5. 跳转到房产添加页面
6. 用户添加房产成功
7. 自动跳转到工单列表页面

### 场景3：基本条件不满足
1. 用户点击"我的工单"
2. 系统检测到未登录/未选择社区/未认证
3. 显示相应的提示弹窗
4. 引导用户完成相应的操作

## 数据依赖

### houseCount字段
- **来源**：`loadHouseData()` 方法从 `houseApi.getHouseList()` 获取
- **更新时机**：页面onShow时调用 `loadAssetCounts()`
- **存储位置**：页面data中的 `houseCount` 字段

### 存储键值
- `redirectAfterAddHouse`：存储添加房产后需要跳转的目标URL

## 相关文件

- `pages/profile/profile.js` - 我的页面主逻辑
- `pages/profile/profile.wxml` - 我的页面模板
- `profilePackage/pages/profile/house/add/add.js` - 房产添加页面
- `api/houseApi.js` - 房产相关API接口

## 注意事项

1. **数据实时性**：房产数量基于页面加载时的数据，如果用户在其他地方添加了房产，需要重新进入我的页面才能更新
2. **跳转逻辑**：添加房产成功后会自动跳转到目标页面，无需用户再次点击
3. **错误处理**：如果房产添加失败，用户需要手动重新尝试
4. **兼容性**：保持与现有认证检测逻辑的兼容性
