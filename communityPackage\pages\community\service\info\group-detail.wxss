/* 社区兴趣群详情页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
  padding-bottom: 24px;
}

/* 页面顶部样式 */

/* 群组基本信息 */
.group-header {
  padding: 24px 24px 16px;
  background: linear-gradient(to bottom, #ff6b00, transparent);
}

.group-name {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.group-stats {
  display: flex;
  gap: 16px;
}

.group-members, .group-created {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.stats-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
  opacity: 0.9;
}

/* 卡片通用样式 */
.detail-card, .owner-card, .join-card, .notice-card {
  margin: 16px 24px 0;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

/* 群组详情卡片样式 */
.group-description {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.group-activities {
  margin-top: 16px;
}

.activities-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.activity-item {
  display: flex;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.activity-date {
  width: 80px;
  font-size: 14px;
  color: #ff8c00;
  font-weight: 500;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 群主信息卡片样式 */
.owner-info {
  display: flex;
  align-items: center;
}

.owner-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 16px;
}

.owner-details {
  flex: 1;
}

.owner-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.owner-intro {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 入群方式卡片样式 */
.join-description {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.contact-info {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.contact-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.contact-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
}

.copy-btn {
  width: 100%;
  height: 40px;
  background-color: #ff8c00;
  color: #fff;
  font-size: 15px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.copy-btn::after {
  border: none;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  margin-bottom: 8px;
}

.qrcode-tip {
  font-size: 12px;
  color: #999;
}

/* 注意事项卡片样式 */
.notice-card {
  background-color: #fff9f0;
  border-left: 4px solid #ff8c00;
}

.notice-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #ff8c00;
  margin-bottom: 8px;
}

.notice-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.notice-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
