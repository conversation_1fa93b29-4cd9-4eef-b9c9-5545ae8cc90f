// pages/points/mall/detail.js
const app = getApp();
const PointsUtil = require('../../../utils/points');
const GoodsService = require('../../../services/goods');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    product: null,
    userPoints: 0,
    quantity: 1,
    showBuyModal: false,
    cashAmount: 0,
    totalPoints: 0,
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('积分商城商品详情页 onLoad:', options);

    if (options.id) {
      this.setData({
        id: parseInt(options.id)
      });
      this.loadProductDetail(options.id);
      this.loadUserPoints();
    } else {
      wx.showToast({
        title: '商品ID不存在',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新用户积分
    this.loadUserPoints();
  },

  // 加载商品详情
  loadProductDetail: function(productId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    console.log('加载商品详情, productId:', productId);

    GoodsService.getGoodsDetail(productId).then(product => {
      console.log('获取商品详情成功:', product);
      this.setData({
        product: product,
        loading: false
      });
      this.calculateTotal();
      wx.hideLoading();
    }).catch(err => {
      console.error('获取商品详情失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '获取商品详情失败',
        icon: 'none',
        duration: 2000
      });
      this.setData({
        loading: false
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    });
  },

  // 加载用户积分
  loadUserPoints: function() {
    PointsUtil.getUserPoints().then(points => {
      this.setData({
        userPoints: points
      });
    }).catch(err => {
      console.error('获取用户积分失败:', err);
      this.setData({
        userPoints: 0
      });
    });
  },

  // 计算总积分和现金
  calculateTotal: function() {
    const { product, quantity } = this.data;
    if (!product) return;

    let totalPoints = product.points * quantity;
    let cashAmount = 0;

    // 如果是积分+现金商品
    if (product.cashPrice && product.cashPrice > 0) {
      cashAmount = product.cashPrice * quantity;
    }

    this.setData({
      totalPoints: totalPoints,
      cashAmount: cashAmount.toFixed(2)
    });
  },

  // 增加数量
  increaseQuantity: function() {
    const { quantity, product } = this.data;
    if (product && quantity < product.stock) {
      this.setData({
        quantity: quantity + 1
      });
      this.calculateTotal();
    } else {
      wx.showToast({
        title: '已达到最大库存',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 减少数量
  decreaseQuantity: function() {
    const { quantity } = this.data;
    if (quantity > 1) {
      this.setData({
        quantity: quantity - 1
      });
      this.calculateTotal();
    }
  },

  // 显示兑换弹窗
  showExchangeModal: function() {
    const { userPoints, totalPoints, product } = this.data;

    // 检查积分是否足够
    if (userPoints < totalPoints) {
      wx.showToast({
        title: '积分不足',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查库存
    if (product.stock <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({
      showBuyModal: true
    });
  },

  // 关闭兑换弹窗
  closeBuyModal: function() {
    this.setData({
      showBuyModal: false
    });
  },

  // 确认兑换
  confirmExchange: function() {
    const { id, quantity, product } = this.data;

    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    // 如果是纯积分商品
    if (!product.cashPrice || product.cashPrice <= 0) {
      GoodsService.exchangeGoods(id, quantity).then(result => {
        wx.hideLoading();
        this.setData({
          showBuyModal: false
        });

        wx.showToast({
          title: '兑换成功',
          icon: 'success',
          duration: 2000
        });

        // 跳转到订单详情页
        setTimeout(() => {
          wx.navigateTo({
            url: `/pages/goods/order/order?id=${result.order.id}`
          });
        }, 1500);
      }).catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.message || '兑换失败',
          icon: 'none',
          duration: 2000
        });
      });
    }
    // 如果是积分+现金商品
    else {
      // 调用商品服务的积分+现金兑换方法
      GoodsService.exchangeGoodsWithCash(id, quantity).then(result => {
        wx.hideLoading();
        this.setData({
          showBuyModal: false
        });

        wx.showToast({
          title: '兑换成功',
          icon: 'success',
          duration: 2000
        });

        // 跳转到订单详情页
        setTimeout(() => {
          wx.navigateTo({
            url: `/pages/goods/order/order?id=${result.order.id}`
          });
        }, 1500);
      }).catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.message || '兑换失败',
          icon: 'none',
          duration: 2000
        });
      });
    }
  },

  // 预览图片
  previewImage: function(e) {
    const { product } = this.data;
    const index = e.currentTarget.dataset.index;

    if (product && product.images && product.images.length > 0) {
      wx.previewImage({
        current: product.images[index],
        urls: product.images
      });
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { product } = this.data;
    if (product) {
      return {
        title: product.title,
        path: `/pages/points/mall/detail?id=${product.id}`,
        imageUrl: product.images && product.images.length > 0 ? product.images[0] : ''
      };
    }
    return {};
  }
})