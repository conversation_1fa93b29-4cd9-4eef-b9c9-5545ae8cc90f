# 房屋添加页面Picker组件测试指南

## 🎯 测试目标

验证房屋添加页面从卡片布局改为picker组件后的功能完整性和用户体验。

## 📋 测试前准备

### 1. 环境检查
- ✅ 确保用户已登录
- ✅ 确保已选择小区（`selectedCommunity`存在）
- ✅ 确保后端接口可访问
- ✅ 确保楼栋和房间数据存在

### 2. 测试路径
```
房屋列表页 → 点击悬浮添加按钮 → 添加房屋页面
/profilePackage/pages/profile/house/add/add?mode=add
```

## 🧪 功能测试

### 1. 页面加载测试

#### 测试步骤
1. 进入添加房屋页面
2. 观察页面布局和组件显示

#### 预期结果
- ✅ 页面标题显示"添加房屋"
- ✅ 显示三个步骤：选择楼栋、选择房间、选择身份
- ✅ 楼栋picker正常显示，默认显示"请选择楼栋"
- ✅ 房间和角色选择区域暂时隐藏
- ✅ 底部提交按钮隐藏

#### 界面检查
```
┌─────────────────────────┐
│ 添加房屋                │
├─────────────────────────┤
│ ① 选择楼栋              │
│ 楼栋: [请选择楼栋 ▼]    │
│                         │
│ (房间选择区域隐藏)      │
│ (角色选择区域隐藏)      │
│ (提交按钮隐藏)          │
└─────────────────────────┘
```

### 2. 楼栋选择测试

#### 测试步骤
1. 点击楼栋picker
2. 在弹出的选择器中选择一个楼栋
3. 观察页面变化

#### 预期结果
- ✅ picker弹出滚动选择器
- ✅ 显示所有可用楼栋列表
- ✅ 选择楼栋后picker显示选中的楼栋名称
- ✅ 自动显示"选择房间"区域
- ✅ 开始加载该楼栋的房间数据
- ✅ 房间picker显示"正在加载房间信息..."

#### 数据验证
```javascript
// 在控制台检查数据状态
console.log('选中楼栋ID:', this.data.selectedBuildingId);
console.log('选中楼栋索引:', this.data.selectedBuildingIndex);
console.log('楼栋数据:', this.data.buildingPickerRange);
```

### 3. 房间选择测试

#### 测试步骤
1. 等待房间数据加载完成
2. 点击房间picker
3. 选择一个房间
4. 观察页面变化

#### 预期结果
- ✅ 房间picker显示"请选择房间"
- ✅ 点击后弹出房间选择器
- ✅ 显示该楼栋下的所有房间
- ✅ 选择房间后picker显示选中的房间号
- ✅ 自动显示"选择身份"区域
- ✅ 显示三种角色选项

#### 界面检查
```
┌─────────────────────────┐
│ ① 选择楼栋              │
│ 楼栋: [1栋 ▼]          │
│                         │
│ ② 选择房间              │
│ 房间: [101 ▼]          │
│                         │
│ ③ 选择身份              │
│ [业主] [租户] [家庭成员] │
└─────────────────────────┘
```

### 4. 角色选择测试

#### 测试步骤
1. 点击任一角色选项
2. 观察选中状态和按钮变化

#### 预期结果
- ✅ 点击的角色显示选中状态（蓝色边框和背景）
- ✅ 显示选中指示器（✓）
- ✅ 其他角色取消选中状态
- ✅ 底部显示"添加房屋"按钮
- ✅ 按钮为可点击状态

### 5. 提交功能测试

#### 测试步骤
1. 完成所有选择后点击"添加房屋"按钮
2. 观察提交过程和结果

#### 预期结果
- ✅ 显示加载指示器
- ✅ 按钮文字变为"添加中..."
- ✅ 成功后显示成功弹窗
- ✅ 点击确定返回房屋列表
- ✅ 新房屋出现在列表中

## 🔄 编辑模式测试

### 1. 编辑页面加载

#### 测试步骤
1. 从房屋列表点击编辑按钮
2. 进入编辑页面

#### 预期结果
- ✅ 页面标题显示"编辑房屋"
- ✅ 楼栋picker显示当前房屋的楼栋
- ✅ 房间picker显示当前房屋的房间
- ✅ 角色显示当前选中状态
- ✅ 按钮文字显示"保存修改"

### 2. 编辑功能验证

#### 测试步骤
1. 修改楼栋选择
2. 修改房间选择
3. 修改角色选择
4. 保存修改

#### 预期结果
- ✅ 修改楼栋后房间列表重新加载
- ✅ 可以正常选择新的房间
- ✅ 可以修改角色
- ✅ 保存成功后返回列表
- ✅ 修改内容正确更新

## ⚠️ 异常情况测试

### 1. 网络异常测试

#### 测试场景
- 楼栋数据加载失败
- 房间数据加载失败
- 提交请求失败

#### 预期行为
- ✅ 显示友好的错误提示
- ✅ 提供重试按钮
- ✅ 不会出现白屏或崩溃

### 2. 数据异常测试

#### 测试场景
- 楼栋列表为空
- 房间列表为空
- 接口返回格式错误

#### 预期行为
- ✅ 显示"暂无可选楼栋/房间"提示
- ✅ 提供重新加载功能
- ✅ 错误信息清晰明确

### 3. 用户操作异常

#### 测试场景
- 快速连续点击picker
- 在数据加载中切换选择
- 页面切换时的状态保持

#### 预期行为
- ✅ 防止重复操作
- ✅ 加载中禁用picker
- ✅ 状态正确保持

## 🎨 界面体验测试

### 1. Picker组件体验

#### 检查要点
- ✅ picker点击响应及时
- ✅ 滚动选择流畅
- ✅ 选中状态显示清晰
- ✅ 占位文字合适

### 2. 视觉设计验证

#### 检查要点
- ✅ 无红色边框显示
- ✅ 表单布局整洁
- ✅ 间距合理
- ✅ 字体大小适中

### 3. 交互反馈

#### 检查要点
- ✅ 步骤指示器清晰
- ✅ 加载状态明确
- ✅ 错误提示友好
- ✅ 成功反馈及时

## 📱 兼容性测试

### 1. 设备适配
- ✅ iPhone各尺寸正常显示
- ✅ Android各尺寸正常显示
- ✅ 横竖屏切换正常

### 2. 系统兼容
- ✅ iOS 12+ 正常工作
- ✅ Android 7+ 正常工作
- ✅ 微信版本兼容

## 🔧 调试技巧

### 1. 数据状态检查
```javascript
// 在页面中添加调试信息
console.log('当前状态:', {
  selectedBuildingId: this.data.selectedBuildingId,
  selectedBuildingIndex: this.data.selectedBuildingIndex,
  selectedRoomId: this.data.selectedRoomId,
  selectedRoomIndex: this.data.selectedRoomIndex,
  selectedRole: this.data.selectedRole
});
```

### 2. 接口调用监控
```javascript
// 监控API调用
console.log('楼栋数据:', this.data.buildingPickerRange);
console.log('房间数据:', this.data.roomPickerRange);
```

### 3. 事件处理验证
```javascript
// 在picker事件中添加日志
onBuildingPickerChange: function(e) {
  console.log('楼栋选择变化:', e.detail.value);
  // ... 原有逻辑
}
```

## ✅ 验收标准

### 功能完整性
- [ ] 楼栋选择正常工作
- [ ] 房间选择正常工作
- [ ] 角色选择正常工作
- [ ] 新增功能正常
- [ ] 编辑功能正常

### 用户体验
- [ ] 界面简洁美观
- [ ] 操作流程顺畅
- [ ] 反馈及时清晰
- [ ] 错误处理友好

### 技术质量
- [ ] 无控制台错误
- [ ] 性能表现良好
- [ ] 兼容性符合要求
- [ ] 代码结构清晰

### 对比验证
- [ ] 相比卡片布局更简洁
- [ ] 操作效率有提升
- [ ] 空间利用更合理
- [ ] 系统一致性更好

## 🚀 发布检查

在发布前确保：
1. 所有测试用例通过
2. 无明显的用户体验问题
3. 与产品设计要求一致
4. 代码质量符合标准

通过以上测试确保picker组件实现达到预期效果，为用户提供更好的房屋添加体验。
