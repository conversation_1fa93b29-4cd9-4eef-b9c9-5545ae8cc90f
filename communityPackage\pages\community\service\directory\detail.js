// 便民信息黄页详情页面
const app = getApp()

Page({
  data: {
    serviceId: null,
    service: {}
  },

  onLoad: function(options) {
    const { id } = options

    this.setData({
      serviceId: parseInt(id)
    })

    // 加载服务详情
    this.loadServiceDetail()
  },



  // 加载服务详情
  loadServiceDetail: function() {
    const { serviceId } = this.data

    // 模拟从服务器获取数据
    // 实际应该调用API获取数据
    const allServices = this.getAllServices()
    const service = allServices.find(s => s.id === serviceId)

    if (service) {
      // 获取分类名称
      const categories = this.getCategories()
      const category = categories.find(c => c.id === service.category)

      service.categoryName = category ? category.name : '未分类'

      this.setData({
        service
      })
    } else {
      wx.showToast({
        title: '服务不存在',
        icon: 'none'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取所有服务数据
  getAllServices: function() {
    return [
      {
        id: 101,
        name: '阿姨家政服务',
        description: '我们提供专业的家政服务，包括日常保洁、深度清洁、开荒保洁等。我们的阿姨经过专业培训，服务态度好，工作认真负责，价格合理透明。',
        address: '广州市天河区珠江新城冼村路11号',
        phone: '020-12345678',
        hours: '周一至周日 08:00-20:00',
        category: 1,
        services: [
          '日常家庭保洁',
          '深度清洁',
          '开荒保洁',
          '月嫂服务',
          '育儿嫂服务',
          '老人陪护'
        ]
      },
      {
        id: 102,
        name: '全能家电维修',
        description: '专业提供各品牌家电维修、安装、清洗服务。我们拥有多年维修经验的技师团队，能够快速解决各类家电问题，提供上门服务，收费透明合理。',
        address: '广州市天河区天河路385号',
        phone: '020-87654321',
        hours: '周一至周日 09:00-21:00',
        category: 2,
        services: [
          '空调维修/安装/清洗',
          '冰箱维修',
          '洗衣机维修',
          '热水器维修/安装',
          '电视维修',
          '小家电维修'
        ]
      },
      {
        id: 103,
        name: '优质桶装水配送',
        description: '提供多品牌桶装水配送服务，包括农夫山泉、怡宝、景田等知名品牌。我们保证水源纯净，配送及时，价格实惠，还提供饮水机销售和维护服务。',
        address: '广州市天河区天河东路123号',
        phone: '020-66668888',
        hours: '周一至周日 08:00-18:00',
        category: 3,
        services: [
          '桶装水配送',
          '饮水机销售',
          '饮水机维护/清洗',
          '矿泉水批发'
        ]
      },
      {
        id: 104,
        name: '洁净洗衣店',
        description: '提供专业的衣物洗涤、熨烫和皮具护理服务。我们使用环保洗涤剂，采用先进的洗涤设备，确保衣物洗涤干净、不损伤面料，并提供取送服务。',
        address: '广州市天河区体育西路123号',
        phone: '020-55556666',
        hours: '周一至周日 08:30-20:30',
        category: 4,
        services: [
          '衣物洗涤',
          '衣物熨烫',
          '皮具护理',
          '窗帘清洗',
          '床上用品清洗',
          '取送服务'
        ]
      },
      {
        id: 105,
        name: '24小时开锁服务',
        description: '提供24小时紧急开锁、换锁、保险柜开启等服务。我们的技师经验丰富，工具齐全，能够快速解决各类锁具问题，价格公道，服务周到。',
        address: '广州市天河区天河北路123号',
        phone: '020-12312345',
        hours: '24小时服务',
        category: 5,
        services: [
          '紧急开锁',
          '换锁',
          '保险柜开启',
          '汽车开锁',
          '锁具销售'
        ]
      }
    ]
  },

  // 获取分类数据
  getCategories: function() {
    return [
      { id: 1, name: '家政保洁' },
      { id: 2, name: '家电维修' },
      { id: 3, name: '送水/送奶' },
      { id: 4, name: '洗衣服务' },
      { id: 5, name: '开锁换锁' },
      { id: 6, name: '搬家服务' },
      { id: 7, name: '餐饮外卖' },
      { id: 8, name: '超市便利' },
      { id: 9, name: '更多服务' }
    ]
  },

  // 拨打电话
  callPhone: function() {
    const { service } = this.data

    if (service.phone) {
      wx.makePhoneCall({
        phoneNumber: service.phone
      })
    }
  },

  // 打开地图
  openLocation: function() {
    const { service } = this.data

    if (service.address) {
      // 实际应用中应该有经纬度信息
      // 这里模拟一个位置
      wx.openLocation({
        latitude: 23.12911,
        longitude: 113.26627,
        name: service.name,
        address: service.address,
        scale: 18
      })
    }
  },

  // 收藏服务
  saveContact: function() {
    wx.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  }
})
