# 弹窗样式优化说明

## 优化概述

对添加居民页面的两个弹窗进行了样式优化：
1. 房屋选择弹窗 - 完全复制我的房屋页面弹窗样式
2. 人员标签弹窗 - 改为居中显示，使用checkbox多选

## 房屋选择弹窗优化

### 样式特点
- **底部弹出**: 从屏幕底部向上滑出
- **圆角设计**: 顶部圆角16rpx，现代化外观
- **渐变动画**: 使用cubic-bezier缓动函数，流畅自然
- **遮罩层**: 半透明黑色背景，点击可关闭
- **响应式高度**: 最大高度80vh，内容自适应

### 布局结构
```
.add-house-modal
├── .modal-mask (遮罩层)
└── .modal-content (内容容器)
    ├── .modal-header (头部)
    ├── .model-selected-view (已选信息)
    ├── .modal-body (主体内容)
    │   ├── .search-container (搜索框)
    │   └── .content-scroll (滚动区域)
    │       └── .selection-grid (选择网格)
    └── .modal-footer (底部按钮)
```

### 关键样式
```css
/* 底部弹出动画 */
.modal-content {
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.show .modal-content {
  transform: translateY(0);
}

/* 4列网格布局 */
.selection-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

/* 选中状态样式 */
.selection-item.selected {
  background: rgba(0, 122, 255, 0.1);
  border-color: #007AFF;
  color: #007AFF;
}
```

## 人员标签弹窗优化

### 样式特点
- **居中显示**: 在屏幕中央弹出
- **固定尺寸**: 宽度600rpx，高度自适应
- **缩放动画**: 从0.8倍缩放到1倍，带弹性效果
- **阴影效果**: 深度阴影增强层次感
- **checkbox选择**: 使用原生checkbox组件

### 布局结构
```
.tag-modal
├── .modal-mask (遮罩层)
└── .tag-content (内容容器)
    ├── .modal-header (头部)
    ├── .modal-body (主体内容)
    │   └── .tag-scroll (滚动区域)
    │       └── .tag-list (标签列表)
    │           └── .tag-item (标签项)
    └── .modal-footer (底部按钮)
```

### 关键样式
```css
/* 居中定位 */
.tag-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
}

/* 缩放动画 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* checkbox样式 */
.tag-checkbox {
  margin-right: 16rpx;
  transform: scale(1.2);
}
```

## 按钮样式优化

### 设计特点
- **渐变背景**: 确定按钮使用蓝色渐变
- **阴影效果**: 增加深度感
- **按压反馈**: 点击时缩放效果
- **禁用状态**: 灰色背景，不可点击

### 按钮样式
```css
.btn-confirm {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.btn-confirm:active {
  transform: scale(0.98);
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}
```

## 交互优化

### 房屋选择交互
1. **搜索功能**: 实时筛选楼栋/房间
2. **分步选择**: 楼栋→房间的清晰流程
3. **状态显示**: 已选信息实时更新
4. **重新选择**: 支持返回上一步

### 标签选择交互
1. **checkbox多选**: 原生组件，体验一致
2. **实时反馈**: 选中状态立即显示
3. **滚动支持**: 标签过多时可滚动
4. **批量操作**: 一次性选择多个标签

## 响应式设计

### 适配特点
- **弹性布局**: 使用flex和grid布局
- **相对单位**: 使用rpx适配不同屏幕
- **最大高度**: 防止内容超出屏幕
- **滚动支持**: 内容过多时自动滚动

## 性能优化

### 动画性能
- **硬件加速**: 使用transform属性
- **合理时长**: 0.3s动画时长，流畅不拖沓
- **缓动函数**: cubic-bezier提供自然的动画效果

### 交互性能
- **防抖处理**: 搜索输入实时响应
- **虚拟滚动**: 大量数据时的性能保障
- **状态管理**: 最小化数据更新范围
