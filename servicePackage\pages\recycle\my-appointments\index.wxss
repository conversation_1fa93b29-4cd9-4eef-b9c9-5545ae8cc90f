/* 我的预约页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}



.banner-content {
  text-align: center;
  padding: 20rpx 30rpx 40rpx;
  background: linear-gradient(135deg, #4caf50, #2e7d32);
  color: white;
  margin-bottom: 30rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 预约记录列表 */
.appointment-list {
  padding: 0 30rpx;
}

.appointment-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.appointment-status {
  font-size: 28rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.appointment-status.pending {
  background-color: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

.appointment-status.completed {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27AE60;
}

.appointment-status.canceled {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.appointment-date {
  font-size: 26rpx;
  color: #999;
}

.appointment-content {
  padding: 24rpx;
}

.appointment-info {
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  margin-bottom: 12rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.info-value.address {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.appointment-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
}

.detail-btn {
  background-color: #f0f0f0;
  color: #666;
}

.cancel-btn {
  background-color: #ffebee;
  color: #f44336;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Cpath d='M8 14h.01'%3E%3C/path%3E%3Cpath d='M12 14h.01'%3E%3C/path%3E%3Cpath d='M16 14h.01'%3E%3C/path%3E%3Cpath d='M8 18h.01'%3E%3C/path%3E%3Cpath d='M12 18h.01'%3E%3C/path%3E%3Cpath d='M16 18h.01'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.5;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.empty-action {
  padding: 16rpx 40rpx;
  background-color: #27AE60;
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 预约详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.detail-modal.visible {
  opacity: 1;
  visibility: visible;
}

.detail-content {
  width: 90%;
  max-width: 650rpx;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detail-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.detail-body {
  padding: 24rpx;
  overflow-y: auto;
}

.detail-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.status-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 30rpx;
}

.status-icon.pending {
  background-color: rgba(25, 118, 210, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%231976d2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-icon.completed {
  background-color: rgba(39, 174, 96, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%2327AE60' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.status-icon.canceled {
  background-color: rgba(158, 158, 158, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%239e9e9e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
}

.status-text.pending {
  color: #1976d2;
}

.status-text.completed {
  color: #27AE60;
}

.status-text.canceled {
  color: #9e9e9e;
}

.detail-section {
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 12rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.detail-photos {
  margin-bottom: 24rpx;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.photo-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.detail-actions {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.detail-action-btn {
  width: 80%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.detail-action-btn.cancel-btn {
  background-color: #ffebee;
  color: #f44336;
}

/* 取消预约确认弹窗 */
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.confirm-modal.visible {
  opacity: 1;
  visibility: visible;
}

.confirm-content {
  width: 80%;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.confirm-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.confirm-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.confirm-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.confirm-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.confirm-btn.cancel {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn.confirm {
  background-color: #f44336;
  color: white;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .banner {
  background: linear-gradient(135deg, #388e3c, #2e7d32);
}

.darkMode .appointment-item {
  background-color: #2c2c2e;
}

.darkMode .appointment-header {
  border-bottom-color: #3a3a3c;
}

.darkMode .info-label {
  color: #8e8e93;
}

.darkMode .info-value {
  color: #f5f5f7;
}

.darkMode .detail-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
}

.darkMode .cancel-btn {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.darkMode .empty-text {
  color: #8e8e93;
}

.darkMode .empty-action {
  background-color: #388e3c;
}

.darkMode .detail-content {
  background-color: #2c2c2e;
}

.darkMode .detail-header {
  border-bottom-color: #3a3a3c;
}

.darkMode .detail-title {
  color: #f5f5f7;
}

.darkMode .section-title {
  color: #f5f5f7;
}

.darkMode .detail-label {
  color: #8e8e93;
}

.darkMode .detail-value {
  color: #f5f5f7;
}

.darkMode .detail-section {
  border-bottom-color: #3a3a3c;
}

.darkMode .confirm-content {
  background-color: #2c2c2e;
}

.darkMode .confirm-title {
  color: #f5f5f7;
}

.darkMode .confirm-message {
  color: #8e8e93;
}

.darkMode .confirm-btn.cancel {
  background-color: #3a3a3c;
  color: #f5f5f7;
}
