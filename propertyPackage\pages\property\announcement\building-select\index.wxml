<!-- 楼栋单元选择页 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 搜索框 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索楼栋名称" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
    </view>
  </view>

  <!-- 已选楼栋单元提示 -->
  <view class="selected-tip" wx:if="{{selectedBuildingIds.length > 0}}">
    <text>已选择 {{selectedBuildingIds.length}} 个楼栋单元</text>
  </view>

  <!-- 楼栋单元列表 -->
  <view class="building-list" wx:if="{{!isLoading && filteredBuildings.length > 0}}">
    <view class="building-item {{selectedBuildingIds.includes(item.id) ? 'selected' : ''}}"
          wx:for="{{filteredBuildings}}"
          wx:key="id"
          bindtap="toggleBuildingSelection"
          data-id="{{item.id}}">
      <view class="building-info">
        <view class="building-name">{{item.name}}</view>
        <view class="building-address" wx:if="{{item.address}}">{{item.address}}</view>
      </view>
      <view class="checkbox {{selectedBuildingIds.includes(item.id) ? 'checked' : ''}}"></view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && filteredBuildings.length === 0}}">
    <view class="empty-icon"></view>
    <view class="empty-text">{{searchValue ? '未找到相关楼栋单元，试试其他关键词吧！' : '暂无楼栋单元数据'}}</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-actions">
    <view class="action-btn cancel" bindtap="cancelSelection">取消</view>
    <view class="action-btn confirm" bindtap="confirmSelection">确认</view>
  </view>
</view>
