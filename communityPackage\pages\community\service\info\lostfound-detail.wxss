/* 失物招领详情页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
  padding-bottom: 24px;
}

/* 页面顶部样式 */

/* 物品基本信息 */
.item-header {
  padding: 24px 24px 16px;
  background: linear-gradient(to bottom, #ff6b00, transparent);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-title {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  flex: 1;
  margin-right: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.item-tag {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 16px;
  color: #fff;
  font-weight: 500;
}

.lost-tag {
  background-color: #ff8c00;
}

.found-tag {
  background-color: #3498db;
}

/* 物品图片 */
.item-images {
  margin: 0 24px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.images-swiper {
  height: 200px;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 卡片通用样式 */
.detail-card, .contact-card, .tips-card {
  margin: 16px 24px 0;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

/* 物品详情卡片样式 */
.info-item {
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #999;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #333;
}

.info-value.description {
  line-height: 1.6;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ff8c00;
  margin-top: 8px;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 联系方式卡片样式 */
.contact-notice {
  display: flex;
  align-items: center;
  background-color: #fff9f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.notice-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.contact-options {
  display: flex;
  gap: 16px;
}

.contact-btn {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.contact-btn:first-child {
  background-color: #ff8c00;
  color: #fff;
}

.contact-btn:first-child .btn-icon {
  filter: brightness(0) invert(1);
}

.contact-btn:last-child {
  background-color: #fff;
  color: #ff8c00;
  border: 1px solid #ff8c00;
}

.contact-btn::after {
  border: none;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 温馨提示卡片样式 */
.tips-card {
  background-color: #f9fafb;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.tips-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.tips-item {
  margin-bottom: 8px;
}

.tips-item:last-child {
  margin-bottom: 0;
}
