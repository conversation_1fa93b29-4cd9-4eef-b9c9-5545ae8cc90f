/* pages/renovation/materials/materials.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
  background-color: #fff;
  position: relative;
}

.progress-bar-container {
  position: absolute;
  top: 60rpx;
  left: 60rpx;
  right: 60rpx;
  height: 4rpx;
  background-color: #E5E5EA;
  z-index: 1;
}

.progress-bar-active {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: #FF9800;
  transition: right 0.3s ease;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-circle {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #E5E5EA;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.step-label {
  font-size: 24rpx;
  color: #8E8E93;
  white-space: nowrap;
}

.step-active .step-circle {
  background-color: #FF9800;
  color: white;
}

.step-active .step-label {
  color: #FF9800;
  font-weight: 500;
}

.step-complete .step-circle {
  background-color: #FF9800;
  color: white;
}

.step-complete .step-label {
  color: #333;
  font-weight: 500;
}

/* 提示信息 */
.hint-section {
  margin: 24rpx 32rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
}

.hint-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #FF9800;
}

.hint-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #FF9800;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 12rpx;
}

.hint-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 上传区域 */
.upload-section {
  margin: 24rpx 0;
  background-color: white;
  padding: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.upload-description {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 24rpx;
}

.file-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
}

.file-item {
  width: calc(33.33% - 24rpx);
  margin: 12rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  height: 180rpx;
}

.file-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 2;
}

.upload-button {
  width: calc(33.33% - 24rpx);
  margin: 12rpx;
  height: 180rpx;
  border: 2rpx dashed #C7C7CC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F9F9F9;
}

.upload-icon {
  font-size: 48rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
  line-height: 1;
}

.upload-text {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: white;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: space-between;
}

.back-button {
  width: 30%;
  height: 88rpx;
  background-color: #F2F2F7;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.next-button {
  width: 65%;
  height: 88rpx;
  background-color: #FF9800;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.next-button[disabled] {
  background-color: #E5E5EA;
  color: #8E8E93;
}