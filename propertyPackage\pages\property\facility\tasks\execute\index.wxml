<!--巡检执行页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">执行任务</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 任务执行内容 -->
  <block wx:if="{{!isLoading && task && currentPoint}}">
    <!-- 任务信息 -->
    <view class="task-info">
      <view class="task-title">{{task.title}}</view>
      <view class="task-meta">
        <view class="task-type {{task.type}}">{{task.typeText}}</view>
        <view class="task-status {{task.status}}">{{task.statusText}}</view>
      </view>
      <view class="task-progress-bar">
        <view class="progress-inner" style="width: {{task.progress || 0}}%;"></view>
      </view>
      <view class="task-progress-text">
        <text>进度: {{task.progress || 0}}%</text>
        <text>{{currentPointIndex + 1}}/{{inspectionPoints.length}}</text>
      </view>
    </view>
    
    <!-- 当前巡检点信息 -->
    <view class="point-info">
      <view class="point-header">
        <view class="point-title">当前巡检点</view>
        <view class="point-scan {{scanSuccess ? 'success' : ''}}" bindtap="scanQRCode">
          <view class="scan-icon"></view>
          <text>{{scanSuccess ? '已扫码确认' : '扫码确认'}}</text>
        </view>
      </view>
      <view class="point-card">
        <view class="point-name">{{currentPoint.name}}</view>
        <view class="point-code">编号: {{currentPoint.code}}</view>
        <view class="point-location">
          <view class="location-icon"></view>
          <text>{{currentPoint.location}}</text>
        </view>
      </view>
    </view>
    
    <!-- 检查项列表 -->
    <view class="check-list">
      <view class="section-title">检查项</view>
      <view 
        class="check-item" 
        wx:for="{{currentPoint.checkItems}}" 
        wx:key="id" 
        wx:for-index="itemIndex"
      >
        <view class="check-header">
          <view class="check-name">{{item.name}}</view>
          <view class="check-number">{{itemIndex + 1}}/{{currentPoint.checkItems.length}}</view>
        </view>
        <view class="check-description">{{item.description}}</view>
        
        <!-- 检查结果选择 -->
        <view class="check-result">
          <view 
            class="result-option {{checkResults[currentPointIndex + '_' + itemIndex].result === 'normal' ? 'active' : ''}}" 
            bindtap="selectCheckResult" 
            data-point-index="{{currentPointIndex}}" 
            data-item-index="{{itemIndex}}" 
            data-result="normal"
          >
            <view class="option-icon normal"></view>
            <text>正常</text>
          </view>
          <view 
            class="result-option {{checkResults[currentPointIndex + '_' + itemIndex].result === 'abnormal' ? 'active' : ''}}" 
            bindtap="selectCheckResult" 
            data-point-index="{{currentPointIndex}}" 
            data-item-index="{{itemIndex}}" 
            data-result="abnormal"
          >
            <view class="option-icon abnormal"></view>
            <text>异常</text>
          </view>
        </view>
        
        <!-- 异常信息展示 -->
        <view class="abnormal-info" wx:if="{{checkResults[currentPointIndex + '_' + itemIndex].result === 'abnormal' && checkResults[currentPointIndex + '_' + itemIndex].description}}">
          <view class="abnormal-description">{{checkResults[currentPointIndex + '_' + itemIndex].description}}</view>
          <view class="abnormal-images" wx:if="{{checkResults[currentPointIndex + '_' + itemIndex].images.length > 0}}">
            <image 
              class="abnormal-image" 
              wx:for="{{checkResults[currentPointIndex + '_' + itemIndex].images}}" 
              wx:key="index" 
              wx:for-item="image" 
              src="{{image}}" 
              mode="aspectFill"
            ></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view 
        class="action-button next-button" 
        bindtap="showConfirm" 
        data-type="next" 
        wx:if="{{currentPointIndex < inspectionPoints.length - 1}}"
      >
        下一个巡检点
      </view>
      <view 
        class="action-button complete-button" 
        bindtap="showConfirm" 
        data-type="complete" 
        wx:if="{{currentPointIndex === inspectionPoints.length - 1}}"
      >
        完成任务
      </view>
    </view>
  </block>
  
  <!-- 异常表单弹窗 -->
  <view class="abnormal-form {{showAbnormalForm ? 'active' : ''}}">
    <view class="abnormal-form-mask" bindtap="closeAbnormalForm"></view>
    <view class="abnormal-form-container">
      <view class="abnormal-form-header">
        <view class="abnormal-form-title">异常情况描述</view>
        <view class="close-icon" bindtap="closeAbnormalForm"></view>
      </view>
      <view class="abnormal-form-content">
        <textarea 
          class="abnormal-textarea" 
          placeholder="请详细描述异常情况" 
          value="{{abnormalDescription}}" 
          bindinput="onAbnormalInput"
          maxlength="500"
        ></textarea>
        
        <!-- 图片上传 -->
        <view class="upload-container">
          <view class="upload-title">上传图片</view>
          <view class="upload-list">
            <view class="upload-item" wx:for="{{uploadedImages}}" wx:key="index">
              <image class="upload-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
              <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}"></view>
            </view>
            <view class="upload-button" bindtap="uploadImage" wx:if="{{uploadedImages.length < 9}}">
              <view class="upload-icon"></view>
              <text>上传图片</text>
            </view>
          </view>
          <view class="upload-tip">最多上传9张图片，可拍照或从相册选择</view>
        </view>
      </view>
      <view class="abnormal-form-footer">
        <view class="form-button cancel-button" bindtap="closeAbnormalForm">取消</view>
        <view class="form-button submit-button" bindtap="submitAbnormalForm">确认</view>
      </view>
    </view>
  </view>
  
  <!-- 确认对话框 -->
  <view class="confirm-dialog {{showConfirmDialog ? 'active' : ''}}">
    <view class="confirm-dialog-mask" bindtap="closeConfirmDialog"></view>
    <view class="confirm-dialog-container">
      <view class="confirm-dialog-title">
        {{confirmDialogType === 'next' ? '确认进入下一个巡检点？' : '确认完成任务？'}}
      </view>
      <view class="confirm-dialog-content">
        {{confirmDialogType === 'next' ? '当前巡检点的检查结果将被保存，无法返回修改。' : '所有巡检点的检查结果将被提交，任务将标记为已完成。'}}
      </view>
      <view class="confirm-dialog-footer">
        <view class="dialog-button cancel-button" bindtap="closeConfirmDialog">取消</view>
        <view class="dialog-button confirm-button" bindtap="confirmAction">确认</view>
      </view>
    </view>
  </view>
</view>
