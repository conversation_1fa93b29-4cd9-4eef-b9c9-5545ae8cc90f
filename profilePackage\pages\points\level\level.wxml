<!--level.wxml-->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 顶部卡片 -->
  <view class="level-card">
    <!-- 用户等级信息 -->
    <view class="level-header">
      <view class="level-icon">
        <image class="level-icon-img" src="/images/icons/{{level.icon}}.png" mode="aspectFit"></image>
      </view>
      <view class="level-info">
        <view class="level-name">{{level.name}}</view>
        <view class="level-points">当前积分: {{userPoints}}</view>
      </view>
    </view>

    <!-- 等级进度 -->
    <view class="level-progress" wx:if="{{nextLevel}}">
      <view class="progress-text">
        <text>距离升级还需 {{pointsToNextLevel}} 积分</text>
        <text>{{progress}}%</text>
      </view>
      <view class="progress-bar">
        <view class="progress-inner" style="width: {{progress}}%"></view>
      </view>
      <view class="next-level-info">
        <view class="next-level-text">下一等级: {{nextLevel.name}}</view>
        <button class="earn-points-btn" bindtap="goToEarnPoints">去赚积分</button>
      </view>
    </view>

    <!-- 最高等级提示 -->
    <view class="highest-level" wx:else>
      <view class="crown-icon">👑</view>
      <view class="highest-text">恭喜您已达到最高等级</view>
    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'current' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="current">当前特权</view>
    <view class="tab {{activeTab === 'all' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="all">等级说明</view>
  </view>

  <!-- 当前特权 -->
  <view class="tab-content" wx:if="{{activeTab === 'current'}}">
    <view class="privileges-list">
      <view class="privilege-item" wx:for="{{privileges.privileges}}" wx:key="id">
        <view class="privilege-icon">
          <text class="privilege-emoji">🎁</text>
        </view>
        <view class="privilege-content">
          <view class="privilege-title">{{item.name}}</view>
          <view class="privilege-desc">{{item.description}}</view>
        </view>
      </view>

      <!-- 积分兑换比例 -->
      <view class="privilege-item">
        <view class="privilege-icon">
          <text class="privilege-emoji">💰</text>
        </view>
        <view class="privilege-content">
          <view class="privilege-title">积分抵扣比例</view>
          <view class="privilege-desc">{{privileges.pointsRatio}}积分 = 1元，最高可抵扣订单金额的{{privileges.maxDiscountRatio * 100}}%</view>
        </view>
      </view>

      <!-- 积分兑换权益 -->
      <view class="privilege-item">
        <view class="privilege-icon">
          <text class="privilege-emoji">🛒</text>
        </view>
        <view class="privilege-content">
          <view class="privilege-title">积分兑换权益</view>
          <view class="privilege-desc">{{privileges.exchangeRights}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 等级说明 -->
  <view class="tab-content" wx:if="{{activeTab === 'all'}}">
    <view class="all-levels">
      <!-- 等级列表 -->
      <view class="level-item {{level.id === item.id ? 'current-level' : ''}}"
            wx:for="{{allLevels}}"
            wx:key="id">
        <view class="level-item-header">
          <view class="level-item-icon">
            <image class="level-item-icon-img" src="/images/icons/{{item.icon}}.png" mode="aspectFit"></image>
          </view>
          <view class="level-item-info">
            <view class="level-item-name">{{item.name}}</view>
            <view class="level-item-range">{{item.minPoints}} - {{item.maxPoints === Infinity ? '∞' : item.maxPoints}} 积分</view>
          </view>
          <view class="level-item-tag" wx:if="{{level.id === item.id}}">当前等级</view>
        </view>

        <!-- 等级特权 -->
        <view class="level-item-privileges">
          <view class="level-privilege-item" wx:for="{{item.privileges.privileges}}" wx:for-item="privilege" wx:key="id">
            <view class="level-privilege-dot"></view>
            <view class="level-privilege-text">{{privilege.name}}: {{privilege.description}}</view>
          </view>

          <!-- 积分兑换比例 -->
          <view class="level-privilege-item">
            <view class="level-privilege-dot"></view>
            <view class="level-privilege-text">积分抵扣比例: {{item.privileges.pointsRatio}}积分 = 1元，最高可抵扣订单金额的{{item.privileges.maxDiscountRatio * 100}}%</view>
          </view>

          <!-- 积分兑换权益 -->
          <view class="level-privilege-item">
            <view class="level-privilege-dot"></view>
            <view class="level-privilege-text">积分兑换权益: {{item.privileges.exchangeRights}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="bottom-tips">
    <view class="tip-text">积分有效期为获取日起1年，请及时使用</view>
    <view class="tip-text">如有疑问，请联系客服</view>
  </view>
</view>
