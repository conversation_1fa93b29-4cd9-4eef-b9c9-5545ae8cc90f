/* pages/payment/settings/settings.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}



/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 140, 0, 0.2);
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 设置卡片 */
.settings-card {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #ff8c00;
  border-radius: 4rpx;
}

/* 设置项 */
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: #333;
}

.item-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 18L15 12L9 6' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 复选框 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.checkbox-item:last-child {
  margin-right: 0;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-right: 8rpx;
  position: relative;
}

.checkbox.checked {
  border-color: #ff8c00;
  background-color: #ff8c00;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 3rpx solid white;
  border-bottom: 3rpx solid white;
  top: 8rpx;
  left: 6rpx;
  transform: rotate(-45deg);
}

/* 支付方式 */
.payment-methods {
  display: flex;
  flex-wrap: wrap;
}

.payment-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  padding: 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  width: 120rpx;
}

.payment-method:last-child {
  margin-right: 0;
}

.payment-method.checked {
  border-color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.05);
}

.payment-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.payment-icon.wechat {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.5 8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5zm6.5 1.5c.83 0 1.5-.67 1.5-1.5S16.83 7 16 7s-1.5.67-1.5 1.5.67 1.5 1.5 1.5zm1.5 3c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5zm-9 0c0 .83-.67 1.5-1.5 1.5S5 13.83 5 13s.67-1.5 1.5-1.5 1.5.67 1.5 1.5z' fill='%2307C160'/%3E%3C/svg%3E");
}

.payment-icon.alipay {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.422 13.707c-0.378-0.075-1.806-0.556-3.075-0.881-1.269-0.325-2.3-0.506-2.3-0.506s-1.031 1.519-2.131 2.9c-1.1 1.381-2.35 2.194-4.181 2.194s-3.494-1.381-3.494-3.244c0-1.863 1.663-3.244 3.494-3.244 1.831 0 3.075 0.506 4.344 1.519 0 0 0.994-2.025 1.372-3.244 0.378-1.219 0.5-2.194 0.5-2.194h-6.675v-0.881h3.494v-1.519h-4.344v-0.881h4.344v-2.025h1.831v2.025h4.344v0.881h-4.344v1.519h3.494v0.881h-5.675c0 0-0.169 0.881-0.378 1.519h6.053c0 0-0.169 1.381-0.831 3.244 0 0 3.913 1.381 6.053 2.025 2.131 0.644 2.3 1.863 2.131 2.194-0.169 0.325-0.831 0.556-2.131 0.556-1.3 0-3.075-0.881-3.075-0.881v1.519c0 0 1.663 0.881 3.913 0.881s3.913-1.219 4.181-2.025c0.269-0.806-0.169-2.025-2.131-2.338z' fill='%231677FF'/%3E%3C/svg%3E");
}

.payment-icon.bank {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 10v7h16v-7M2 7h20M12 7V4M6 7l6-3 6 3M6 21h12' stroke='%********' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.payment-method text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 银行卡信息 */
.bank-card-info {
  display: flex;
  align-items: center;
}

.bank-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.bank-icon.icbc {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='24' height='24' rx='4' fill='%23C50F1C'/%3E%3Cpath d='M12 6L18 12L12 18L6 12L12 6Z' fill='white'/%3E%3C/svg%3E");
}

.bank-icon.cmb {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='24' height='24' rx='4' fill='%23E60012'/%3E%3Cpath d='M7 12L12 7L17 12L12 17L7 12Z' fill='white'/%3E%3C/svg%3E");
}

.bank-details {
  display: flex;
  flex-direction: column;
}

.bank-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.card-number {
  font-size: 24rpx;
  color: #999;
}

.select-card {
  display: flex;
  align-items: center;
  color: #999;
}

/* 提示信息 */
.settings-tips {
  display: flex;
  align-items: flex-start;
  margin-top: 24rpx;
  padding: 16rpx;
  background-color: #fff9f0;
  border-radius: 8rpx;
}

.tip-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z' fill='%23FFCC80' stroke='%23FF8C00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 8V12' stroke='%23FF8C00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M12 16H12.01' stroke='%23FF8C00' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.tip-text .highlight {
  color: #ff8c00;
}

/* 选择器弹窗 */
.selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.selector-content {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.selector-modal.show .selector-content {
  transform: translateY(0);
}

.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.selector-list {
  margin-bottom: 40rpx;
}

.selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.selector-item:last-child {
  border-bottom: none;
}

.selector-item.selected {
  color: #ff8c00;
}

.check-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z' fill='%23FF8C00'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.selector-cancel {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background-color: #f2f2f7;
  color: #333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 银行卡列表 */
.bank-card-list {
  margin-bottom: 40rpx;
}

.bank-card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.bank-card-left {
  display: flex;
  align-items: center;
}

.bank-card-item.selected {
  background-color: rgba(255, 140, 0, 0.05);
}

.card-type {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.add-bank-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  color: #ff8c00;
}

.add-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 5V19M5 12H19' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 暗黑模式 */
.darkMode {
  background-color: #1c1c1e;
  color: #fff;
}

.darkMode .settings-card,
.darkMode .selector-content {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .card-title,
.darkMode .item-label,
.darkMode .payment-method text,
.darkMode .bank-name,
.darkMode .selector-title {
  color: #fff;
}

.darkMode .item-value,
.darkMode .tip-text,
.darkMode .card-number,
.darkMode .card-type {
  color: #8e8e93;
}

.darkMode .settings-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .selector-item,
.darkMode .bank-card-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .payment-method {
  border-color: #3a3a3c;
}

.darkMode .payment-method.checked {
  border-color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.1);
}

.darkMode .settings-tips {
  background-color: rgba(255, 140, 0, 0.1);
}

.darkMode .selector-cancel {
  background-color: #3a3a3c;
  color: #fff;
}
