/* 我的贡献页面样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 贡献统计卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stats-header {
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-subtitle {
  font-size: 26rpx;
  color: #999;
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #4caf50;
  margin-bottom: 10rpx;
}

.unit {
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 4rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 环保进度卡片样式 */
.progress-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.progress-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.progress-level {
  font-size: 30rpx;
  font-weight: 600;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

.tree-animation {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.tree-container {
  width: 200rpx;
  height: 200rpx;
  position: relative;
}

.tree {
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.tree-1 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='200' height='200' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-8m0 0l-5-5m5 5l5-5M12 14V6m0 0L7 1m5 5l5-5'%3E%3C/path%3E%3C/svg%3E");
}

.tree-2 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='200' height='200' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-4m0 0c-3 0-6-3-6-6 0-3 2-5 5-5 0-2 2-4 5-4 4 0 7 3 7 7 0 2-1 3-2 4 1 1 1 2 1 3 0 3-2 5-5 5h-5z'%3E%3C/path%3E%3C/svg%3E");
}

.tree-3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='200' height='200' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-4m0 0c-4 0-8-4-8-8 0-4 3-7 7-7 0-2 2-3 4-3 5 0 9 4 9 9 0 3-1 5-3 6 1 1 2 2 2 4 0 3-2 5-5 5h-6z'%3E%3C/path%3E%3C/svg%3E");
}

.tree-4 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='200' height='200' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-4m0 0c-5 0-10-5-10-10 0-5 4-8 9-8 0-0 3 0 5 2 2-2 5-2 7-1 2 1 3 3 3 5 0 2-1 4-3 5 2 1 3 3 3 5 0 3-2 6-6 6h-8z'%3E%3C/path%3E%3C/svg%3E");
}

.tree-5 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='200' height='200' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22v-4m0 0c-6 0-12-6-12-12 0-1 0-2 1-3 1-1 2-1 3-1 0-1 1-2 3-2 2 0 3 1 4 2 1-1 2-2 4-2 2 0 4 1 5 3 1-1 2-1 3-1 1 0 2 1 3 2 0 1 0 2 0 3 0 2-1 4-3 5 2 1 3 3 3 5 0 3-2 6-6 6h-8z'%3E%3C/path%3E%3C/svg%3E");
}

.progress-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-track {
  flex: 1;
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #4caf50;
  border-radius: 8rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  width: 60rpx;
  text-align: right;
}

.level-info {
  text-align: center;
}

.level-text {
  font-size: 26rpx;
  color: #666;
}

/* 环保成就卡片样式 */
.achievements-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.achievement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.achievement-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  margin-bottom: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 60%;
}

.achievement-icon.locked {
  opacity: 0.5;
  filter: grayscale(1);
}

.beginner-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='7'%3E%3C/circle%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

.recycler-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.expert-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9800' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'%3E%3C/path%3E%3C/svg%3E");
}

.carbon-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8h1a4 4 0 0 1 0 8h-1'%3E%3C/path%3E%3Cpath d='M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z'%3E%3C/path%3E%3Cline x1='6' y1='1' x2='6' y2='4'%3E%3C/line%3E%3Cline x1='10' y1='1' x2='10' y2='4'%3E%3C/line%3E%3Cline x1='14' y1='1' x2='14' y2='4'%3E%3C/line%3E%3C/svg%3E");
}

.master-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239c27b0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.leader-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
}

.achievement-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  text-align: center;
}

.achievement-status {
  font-size: 22rpx;
  color: #999;
}

.achievement-item.unlocked .achievement-status {
  color: #4caf50;
}

/* 环保记录卡片样式 */
.records-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.records-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 40rpx;
  flex-shrink: 0;
}

.recycle-icon {
  background-color: rgba(33, 150, 243, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='16 3 21 3 21 8'%3E%3C/polyline%3E%3Cline x1='4' y1='20' x2='21' y2='3'%3E%3C/line%3E%3Cpolyline points='21 16 21 21 16 21'%3E%3C/polyline%3E%3Cline x1='15' y1='15' x2='21' y2='21'%3E%3C/line%3E%3Cline x1='4' y1='4' x2='9' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.quiz-icon {
  background-color: rgba(255, 152, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%23ff9800' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'%3E%3C/path%3E%3C/svg%3E");
}

.achievement-icon {
  background-color: rgba(76, 175, 80, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%234caf50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-value {
  font-size: 30rpx;
  font-weight: 600;
}

.record-value.positive {
  color: #4caf50;
}

.record-value.negative {
  color: #f44336;
}

/* 成就详情弹窗样式 */
.achievement-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-card {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #f5f5f5;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 60%;
}

.detail-icon.locked {
  opacity: 0.5;
  filter: grayscale(1);
}

.detail-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.detail-content {
  text-align: center;
}

.detail-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.detail-status {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.detail-status.unlocked {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.detail-status.locked {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.detail-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.detail-progress {
  margin-bottom: 30rpx;
}

.detail-progress-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.detail-progress-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.detail-progress-track {
  width: 100%;
  height: 100%;
}

.detail-progress-fill {
  height: 100%;
  background-color: #4caf50;
  border-radius: 8rpx;
}

.detail-reward {
  background-color: rgba(255, 152, 0, 0.1);
  padding: 20rpx;
  border-radius: 12rpx;
}

.reward-title {
  font-size: 28rpx;
  color: #ff9800;
  margin-bottom: 10rpx;
}

.reward-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff9800;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .title {
  color: #f5f5f7;
}

.darkMode .subtitle {
  color: #8e8e93;
}

.darkMode .stats-card,
.darkMode .progress-card,
.darkMode .achievements-card,
.darkMode .records-card,
.darkMode .detail-card {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .stats-title,
.darkMode .progress-title,
.darkMode .card-title,
.darkMode .record-title,
.darkMode .detail-name {
  color: #f5f5f7;
}

.darkMode .stats-subtitle,
.darkMode .stat-label,
.darkMode .progress-text,
.darkMode .level-text,
.darkMode .achievement-status,
.darkMode .record-time,
.darkMode .detail-desc,
.darkMode .detail-progress-text {
  color: #8e8e93;
}

.darkMode .achievement-name {
  color: #f5f5f7;
}

.darkMode .achievement-icon,
.darkMode .detail-icon,
.darkMode .detail-close {
  background-color: #3a3a3c;
}

.darkMode .progress-track,
.darkMode .detail-progress-bar {
  background-color: #3a3a3c;
}

.darkMode .record-item {
  border-bottom-color: #3a3a3c;
}
