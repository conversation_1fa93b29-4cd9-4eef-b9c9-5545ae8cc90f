
页面完善前提项:
删除页面模拟数据
使用真实的接口数据字段,不要自定义字段
注意使用字典值匹配
顶部统计账单金额先忽略,不要动,等待后续统计接口完善后再修改
筛选条件:
时间:本月 本季度 本年 对应入参timeLevel:2 3 4
类型筛选去掉
状态使用字典物业缴费账单状态
列表添加翻页功能,下拉刷新,上拉加载更多.

servicePackage/pages/payment/history/history我的缴费列表
1.用到的字典
1-1.物业缴费账单状态util.getDictByNameEn('property_payment_bill_status')[0].children 
待付款 wait_pay
已付款 settle
1-2.物业缴费明细状态util.getDictByNameEn('property_payment_detail_status')[0].children 
未生效 not_effective
已生效 effective
已到期 become_due
1-3.支付类型util.getDictByNameEn('pay_type')[0].children 
微信支付 wechat_pay
支付宝 alipay
1-4.支付周期,固定值
月度 monthly
季度 quarterly
年度 yearly

2.获取我的缴费列表
接口地址payment.queryMyPaymentList(params)
入参params=
{
pageNum:this.data.pageNum,
pageSize:this.data.pageSize,
communityId:wx.getStorageSync('selectedCommunity').id,
}
接口返回
{
    "total": 3,
    "list": [
      {
        "id": "5",
        "paymentObjectId": "2",
        "unitPrice": 122,
        "unit": "121",
        "quantity": 3.55,
        "status": "wait_pay",
        "communityId": "2",
        "openid": null,
        "billNo": null,
        "payNo": null,
        "refundNo": null,
        "payTime": null,
        "refundTime": null,
        "payType": null,
        "paymentItemSnapshot": "{\"id\":\"1\",\"paymentItemName\":\"23\",\"paymentItemDescribe\":\"232323\",\"unitPrice\":122,\"unit\":\"121\",
		\"billingCycle\":\"monthly\",\"isActive\":true,\"note\":\"23\"}",
        "billDate": "2025-07-10 16:52:37",
        "paymentDetailId": null,
        "lastQuantity": null,
        "createTime": "2025-07-10 16:52:37",
        "updateTime": null,
        "payAmount": 303.17,
        "discountAmount": 129.93,
        "totalAmount": 433.1
      },
      {
        "id": "2",
        "paymentObjectId": "2",
        "unitPrice": 122,
        "unit": "121",
        "quantity": 1,
        "status": "wait_pay",
        "communityId": "2",
        "openid": null,
        "billNo": null,
        "payNo": null,
        "refundNo": null,
        "payTime": null,
        "refundTime": null,
        "payType": null,
        "paymentItemSnapshot": "{\"id\":\"1\",\"paymentItemName\":\"23\",\"paymentItemDescribe\":\"232323\",\"unitPrice\":122,\"unit\":\"121\",\"billingCycle\":\"monthly\",\"isActive\":true,\"note\":\"23\"}",
        "billDate": "2025-06-06 00:00:00",
        "paymentDetailId": "2",
        "lastQuantity": 1,
        "createTime": "2025-07-09 19:42:10",
        "updateTime": null,
        "payAmount": 0.01,
        "discountAmount": 121.99,
        "totalAmount": 122
      },
      {
        "id": "1",
        "paymentObjectId": "2",
        "unitPrice": 1,
        "unit": "个",
        "quantity": 1,
        "status": "wait_pay",
        "communityId": null,
        "openid": null,
        "billNo": null,
        "payNo": null,
        "refundNo": null,
        "payTime": null,
        "refundTime": null,
        "payType": null,
        "paymentItemSnapshot": null,
        "billDate": null,
        "paymentDetailId": null,
        "lastQuantity": null,
        "createTime": null,
        "updateTime": null,
        "payAmount": 0.01,
        "discountAmount": 0.99,
        "totalAmount": 1
      }
    ],
    "pageNum": 1,
    "pageSize": 500,
    "size": 3,
    "startRow": 1,
    "endRow": 3,
    "pages": 1,
    "prePage": 0,
    "nextPage": 0,
    "isFirstPage": true,
    "isLastPage": true,
    "hasPreviousPage": false,
    "hasNextPage": false,
    "navigatePages": 8,
    "navigatepageNums": [
      1
    ],
    "navigateFirstPage": 1,
    "navigateLastPage": 1
  }

列表item参数说明
id Collapse allintegerint64
缴费账单ID

paymentObjectId Collapse allintegerint64
缴费对象ID

unitPrice Collapse allnumberdouble
单价

unit Collapse allstring
单位

quantity Collapse allnumberdouble
数量

status Collapse allstring
状态

communityId Collapse allintegerint64
小区ID

openid Collapse allstring
openid

billNo Collapse allstring
账单编号

payNo Collapse allstring
支付单号

refundNo Collapse allstring
退款单号

payTime Collapse allstringdate-time
支付时间

refundTime Collapse allstringdate-time
退款时间

payType Collapse allstring
支付类型

paymentItemSnapshot Collapse allstring
缴费项目快照

billDate Collapse allstringdate-time
账单日期

paymentDetailId Collapse allintegerint64
缴费明细ID（合同ID）

lastQuantity Collapse allintegerint32
上期示数

createTime Collapse allstringdate-time
创建时间

updateTime Collapse allstringdate-time
修改时间

payAmount Collapse allnumberdouble
支付金额

discountAmount Collapse allnumberdouble
优惠金额

totalAmount Collapse allnumberdouble
总金额

3.列表item需要显示缴费名称,支付状态,账单时间,费用,支付的周期
