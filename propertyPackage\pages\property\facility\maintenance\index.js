// pages/property/facility/maintenance/index.js
const dateUtil = require('../../../../../utils/dateUtil.js')

Page({
  data: {
    statusBarHeight: 20,
    darkMode: false,
    isLoading: true,
    
    // 设施ID
    facilityId: '',
    
    // 设施信息
    facility: null,
    
    // 表单数据
    formData: {
      workType: 'maintenance', // maintenance-保养, repair-维修
      description: '',
      materialsUsed: '',
      cost: '',
      operatorName: '',
      operatorPhone: '',
      completionDate: '',
      completionTime: '',
      statusAfter: 'normal' // normal-正常, warning-警告, fault-故障
    },
    
    // 工作类型选项
    workTypeOptions: [
      { value: 'maintenance', label: '保养' },
      { value: 'repair', label: '维修' }
    ],
    
    // 状态选项
    statusOptions: [
      { value: 'normal', label: '正常', color: '#4CAF50' },
      { value: 'warning', label: '警告', color: '#FF9800' },
      { value: 'fault', label: '故障', color: '#F44336' }
    ],
    
    // 图片上传
    uploadedImages: [],
    
    // 日期选择
    currentDate: new Date().getTime(),
    showDatePicker: false,
    
    // 时间选择
    currentTime: '09:00',
    showTimePicker: false,
    
    // 表单验证
    formErrors: {
      description: '',
      operatorName: '',
      operatorPhone: ''
    },
    
    // 提交状态
    isSubmitting: false
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 获取设施ID
    if (options.id) {
      this.setData({
        facilityId: options.id
      });
      this.loadFacilityDetail(options.id);
    } else {
      wx.showToast({
        title: '设施ID无效',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载设施详情
  loadFacilityDetail: function(facilityId) {
    this.setData({ isLoading: true });
    
    // 这里应该是从服务器获取数据
    // 目前使用模拟数据
    setTimeout(() => {
      const facility = this.getMockFacilityDetail(facilityId);
      
      // 设置表单默认值
      const formData = this.data.formData;
      
      // 获取当前用户信息（模拟）
      const userInfo = this.getMockUserInfo();
      formData.operatorName = userInfo.name;
      formData.operatorPhone = userInfo.phone;
      
      // 设置当前日期和时间
      const now = new Date();
      formData.completionDate = dateUtil.formatDate(now);
      formData.completionTime = dateUtil.formatTime(now).substring(0, 5); // 只取小时和分钟
      
      this.setData({
        facility: facility,
        formData: formData,
        currentDate: now.getTime(),
        currentTime: formData.completionTime,
        isLoading: false
      });
    }, 500);
  },
  
  // 获取模拟设施详情
  getMockFacilityDetail: function(facilityId) {
    const facilities = {
      '1': {
        id: '1',
        name: '小区正门监控',
        code: 'CAM-001',
        category: 'monitor',
        categoryText: '监控设施',
        location: '小区正门',
        status: 'normal',
        statusText: '正常'
      },
      '8': {
        id: '8',
        name: '1号楼单元门',
        code: 'ACC-003',
        category: 'door',
        categoryText: '门禁设施',
        location: '1号楼',
        status: 'fault',
        statusText: '故障'
      }
    };
    
    return facilities[facilityId] || facilities['1'];
  },
  
  // 获取模拟用户信息
  getMockUserInfo: function() {
    return {
      name: '张工',
      phone: '13800138000'
    };
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 表单输入处理
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新表单数据
    const formData = this.data.formData;
    formData[field] = value;
    
    this.setData({
      formData: formData
    });
    
    // 清除对应的错误提示
    if (this.data.formErrors[field]) {
      const formErrors = this.data.formErrors;
      formErrors[field] = '';
      this.setData({
        formErrors: formErrors
      });
    }
  },
  
  // 选择工作类型
  onWorkTypeChange: function(e) {
    const { value } = e.currentTarget.dataset;
    
    const formData = this.data.formData;
    formData.workType = value;
    
    this.setData({
      formData: formData
    });
  },
  
  // 选择状态
  onStatusChange: function(e) {
    const { value } = e.currentTarget.dataset;
    
    const formData = this.data.formData;
    formData.statusAfter = value;
    
    this.setData({
      formData: formData
    });
  },
  
  // 显示日期选择器
  showDatePicker: function() {
    this.setData({
      showDatePicker: true
    });
  },
  
  // 隐藏日期选择器
  hideDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },
  
  // 确认日期选择
  confirmDatePicker: function(e) {
    const { value } = e.detail;
    
    const formData = this.data.formData;
    formData.completionDate = dateUtil.formatDate(new Date(value));
    
    this.setData({
      currentDate: value,
      formData: formData,
      showDatePicker: false
    });
  },
  
  // 显示时间选择器
  showTimePicker: function() {
    this.setData({
      showTimePicker: true
    });
  },
  
  // 隐藏时间选择器
  hideTimePicker: function() {
    this.setData({
      showTimePicker: false
    });
  },
  
  // 确认时间选择
  confirmTimePicker: function(e) {
    const { value } = e.detail;
    
    const formData = this.data.formData;
    formData.completionTime = value;
    
    this.setData({
      currentTime: value,
      formData: formData,
      showTimePicker: false
    });
  },
  
  // 上传图片
  uploadImage: function() {
    const { uploadedImages } = this.data;
    
    // 最多上传9张图片
    const remainCount = 9 - uploadedImages.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 这里应该是上传图片到服务器
        // 目前只是添加到本地数据
        const tempFilePaths = res.tempFilePaths;
        
        this.setData({
          uploadedImages: [...uploadedImages, ...tempFilePaths]
        });
      }
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    wx.previewImage({
      current: uploadedImages[index],
      urls: uploadedImages
    });
  },
  
  // 删除图片
  deleteImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadedImages } = this.data;
    
    uploadedImages.splice(index, 1);
    
    this.setData({
      uploadedImages: uploadedImages
    });
  },
  
  // 验证表单
  validateForm: function() {
    const { formData } = this.data;
    const formErrors = {
      description: '',
      operatorName: '',
      operatorPhone: ''
    };
    
    let isValid = true;
    
    // 验证处理描述
    if (!formData.description.trim()) {
      formErrors.description = '请填写处理描述';
      isValid = false;
    }
    
    // 验证操作人
    if (!formData.operatorName.trim()) {
      formErrors.operatorName = '请填写操作人';
      isValid = false;
    }
    
    // 验证联系电话
    if (!formData.operatorPhone.trim()) {
      formErrors.operatorPhone = '请填写联系电话';
      isValid = false;
    } else if (!/^1\d{10}$/.test(formData.operatorPhone)) {
      formErrors.operatorPhone = '请填写正确的手机号码';
      isValid = false;
    }
    
    this.setData({
      formErrors: formErrors
    });
    
    return isValid;
  },
  
  // 提交表单
  submitForm: function() {
    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }
    
    // 设置提交状态
    this.setData({
      isSubmitting: true
    });
    
    // 这里应该是提交表单到服务器
    // 目前只是模拟提交
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
})
