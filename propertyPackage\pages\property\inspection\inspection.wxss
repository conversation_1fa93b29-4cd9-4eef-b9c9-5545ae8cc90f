/* 巡检记录页面样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部添加按钮预留空间 */
}

/* 搜索框 */
.search-bar {
  padding: 24rpx 32rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input-wrap {
  display: flex;
  align-items: center;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 统计卡片 */
.stats-card {
  margin: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  opacity: 0;
}

.stats-row {
  display: flex;
  padding: 24rpx 0;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stats-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 0 24rpx;
}

.stats-footer {
  padding: 20rpx 24rpx;
  font-size: 24rpx;
  color: #666;
}

.today-stat {
  text-align: center;
}

.highlight {
  color: #ff8c00;
  font-weight: 600;
}

/* 日期筛选 */
.date-filter {
  margin: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.date-range {
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}

.date-value {
  color: #ff8c00;
  margin-left: 8rpx;
}

.date-separator {
  margin: 0 16rpx;
  color: #999;
  font-size: 26rpx;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  padding: 16rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow-x: auto;
  white-space: nowrap;
}

.tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  padding: 0 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
  transition: all 0.3s;
}

.tab.active {
  background-color: #ff8c00;
  color: #fff;
}

/* 巡检列表 */
.inspection-list {
  padding: 24rpx 32rpx;
}

.inspection-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.inspection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.inspection-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.inspection-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  padding: 0 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.inspection-status.pending {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500;
}

.inspection-status.completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.inspection-status.abnormal {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.inspection-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
}

.info-label {
  width: 140rpx;
  font-size: 26rpx;
  color: #999;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.abnormal-text {
  color: #ff3b30;
}

.inspection-images {
  display: flex;
  margin-top: 16rpx;
}

.inspection-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 11l3 3L22 4'%3E%3C/path%3E%3Cpath d='M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 24rpx 0;
  color: #999;
  font-size: 26rpx;
}

.loading-indicator {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 140, 0, 0.1);
  border-top: 3rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 创建按钮 */
.create-btn {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #ff8c00;
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.create-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='5' x2='12' y2='19'%3E%3C/line%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #1c1c1e;
  color: #fff;
}

.dark-mode .search-bar,
.dark-mode .stats-card,
.dark-mode .date-filter,
.dark-mode .category-tabs,
.dark-mode .inspection-card {
  background-color: #2c2c2e;
}

.dark-mode .search-input-wrap {
  background-color: #3a3a3c;
}

.dark-mode .search-input {
  color: #fff;
}

.dark-mode .stat-value,
.dark-mode .inspection-title {
  color: #fff;
}

.dark-mode .stat-label,
.dark-mode .info-value {
  color: #d1d1d6;
}

.dark-mode .info-label {
  color: #8e8e93;
}

.dark-mode .stats-divider {
  background-color: #3a3a3c;
}

.dark-mode .category-tabs {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .tab {
  color: #d1d1d6;
}

.dark-mode .empty-text,
.dark-mode .loading-text,
.dark-mode .load-more {
  color: #8e8e93;
}
