// 社区兴趣群详情页面
const app = getApp()

Page({
  data: {
    groupId: null,
    group: {}
  },

  onLoad: function(options) {
    const { id } = options

    this.setData({
      groupId: parseInt(id)
    })

    // 加载群组详情
    this.loadGroupDetail()
  },



  // 加载群组详情
  loadGroupDetail: function() {
    const { groupId } = this.data

    // 模拟从服务器获取数据
    // 实际应该调用API获取数据
    const allGroups = this.getAllGroups()
    const group = allGroups.find(g => g.id === groupId)

    if (group) {
      this.setData({
        group
      })
    } else {
      wx.showToast({
        title: '群组不存在',
        icon: 'none'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取所有群组数据
  getAllGroups: function() {
    return [
      {
        id: 1,
        name: '晨跑健身小组',
        description: '每天早上6:30在小区中央广场集合，一起晨跑健身，享受健康生活。我们会根据成员的体能水平安排不同的运动强度，适合各个年龄段的居民参与。除了晨跑，我们还会组织其他健身活动，如瑜伽、太极等。',
        memberCount: 28,
        createdTime: '2023年3月创建',
        ownerName: '张先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        ownerIntro: '健身爱好者，持有国家健身教练证书，有5年的健身指导经验。',
        joinMethod: 'contact',
        contactInfo: 'fitness_zhang',
        joinDescription: '欢迎小区内热爱运动的居民加入我们的晨跑健身小组。我们不收取任何费用，只需要您有持之以恒的决心和热情。',
        activities: [
          {
            date: '6月20日',
            title: '小区环湖跑',
            description: '绕小区湖泊跑步3圈，约5公里。'
          },
          {
            date: '6月25日',
            title: '瑜伽课程',
            description: '在中央广场进行初级瑜伽教学。'
          },
          {
            date: '7月1日',
            title: '健身知识分享',
            description: '分享科学健身的方法和注意事项。'
          }
        ]
      },
      {
        id: 2,
        name: '读书分享会',
        description: '每周日下午3点在社区活动室举行读书分享会，交流读书心得，共同成长。我们会选择各类优质书籍进行阅读和讨论，包括文学、历史、科学、心理学等多个领域。欢迎热爱阅读的居民加入我们，一起在书海中徜徉。',
        memberCount: 15,
        createdTime: '2023年1月创建',
        ownerName: '李女士',
        ownerAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        ownerIntro: '文学爱好者，曾在出版社工作，热爱阅读和写作。',
        joinMethod: 'qrcode',
        qrcode: 'https://example.com/qrcode.jpg', // 实际应该是真实的二维码图片
        joinDescription: '我们的读书会欢迎所有热爱阅读的居民参加。每月会推荐1-2本书籍，成员可以根据自己的兴趣选择阅读，然后在分享会上交流读书心得。',
        activities: [
          {
            date: '6月18日',
            title: '《活着》读书分享',
            description: '讨论余华的经典作品《活着》。'
          },
          {
            date: '7月2日',
            title: '《人类简史》导读',
            description: '介绍尤瓦尔·赫拉利的《人类简史》。'
          }
        ]
      },
      {
        id: 3,
        name: '棋牌娱乐群',
        description: '喜欢棋牌游戏的邻居们快来加入，周末一起娱乐放松。我们主要进行象棋、围棋、五子棋、扑克等棋牌活动，氛围轻松愉快，适合各个年龄段的居民参与。我们相信，棋牌活动不仅能够娱乐身心，还能锻炼思维能力。',
        memberCount: 32,
        createdTime: '2022年12月创建',
        ownerName: '王先生',
        ownerAvatar: 'https://randomuser.me/api/portraits/men/68.jpg',
        ownerIntro: '退休教师，棋牌爱好者，擅长象棋和围棋。',
        joinMethod: 'contact',
        contactInfo: 'chess_wang',
        joinDescription: '我们的棋牌群欢迎所有喜欢棋牌活动的居民加入。无论您是新手还是高手，都可以在这里找到适合自己的对手。我们会定期在社区活动室组织线下棋牌活动。',
        activities: [
          {
            date: '6月24日',
            title: '象棋比赛',
            description: '社区象棋友谊赛，设置少量奖品。'
          },
          {
            date: '7月8日',
            title: '扑克牌活动',
            description: '组织各类扑克牌游戏，增进邻里感情。'
          }
        ]
      }
    ]
  },

  // 复制微信号
  copyContact: function() {
    const { group } = this.data

    if (group.contactInfo) {
      wx.setClipboardData({
        data: group.contactInfo,
        success: function() {
          wx.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 预览二维码
  previewQrcode: function() {
    const { group } = this.data

    if (group.qrcode) {
      wx.previewImage({
        urls: [group.qrcode],
        current: group.qrcode
      })
    }
  }
})
