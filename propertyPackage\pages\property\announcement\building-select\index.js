/**
 * 楼栋选择页
 * 用于选择特定楼栋
 */

const announcementApi = require('../../../../../utils/announcement-api');
const util = require('../../../../../utils/util');

Page({
  data: {
    darkMode: false,
    buildings: [], // 所有楼栋单元列表
    filteredBuildings: [], // 过滤后的楼栋单元列表
    selectedBuildingIds: [], // 已选楼栋单元ID列表
    searchValue: '', // 搜索关键词
    isLoading: true, // 是否正在加载
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '选择楼栋单元'
    });

    // 获取已选楼栋单元ID列表
    if (options.selected) {
      try {
        const selectedBuildingIds = JSON.parse(options.selected);
        this.setData({ selectedBuildingIds });
      } catch (e) {
        console.error('解析已选楼栋单元失败', e);
      }
    }

    // 加载楼栋单元列表
    this.loadBuildings();
  },

  // 加载楼栋列表
  loadBuildings: function() {
    this.setData({ isLoading: true });

    // 调用API获取楼栋列表
    announcementApi.getBuildingList()
      .then(res => {
        const buildings = res.data || this.getMockBuildings();

        this.setData({
          buildings,
          filteredBuildings: buildings,
          isLoading: false
        });
      })
      .catch(err => {
        console.error('加载楼栋列表失败', err);

        // 使用模拟数据
        const mockBuildings = this.getMockBuildings();

        this.setData({
          buildings: mockBuildings,
          filteredBuildings: mockBuildings,
          isLoading: false
        });

        wx.showToast({
          title: '加载失败，使用模拟数据',
          icon: 'none'
        });
      });
  },

  // 获取模拟楼栋数据
  getMockBuildings: function() {
    // 生成模拟楼栋单元数据
    const buildings = [];

    // A栋1-5单元
    for (let i = 1; i <= 5; i++) {
      buildings.push({
        id: `A-${i}`,
        name: `A栋${i}单元`,
        address: `小区1区`
      });
    }

    // B栋1-5单元
    for (let i = 1; i <= 5; i++) {
      buildings.push({
        id: `B-${i}`,
        name: `B栋${i}单元`,
        address: `小区1区`
      });
    }

    // C栋1-5单元
    for (let i = 1; i <= 5; i++) {
      buildings.push({
        id: `C-${i}`,
        name: `C栋${i}单元`,
        address: `小区2区`
      });
    }

    return buildings;
  },

  // 搜索框输入
  onSearchInput: function(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value });
    this.filterBuildings(value);
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({
      searchValue: '',
      filteredBuildings: this.data.buildings
    });
  },

  // 过滤楼栋
  filterBuildings: function(keyword) {
    if (!keyword) {
      this.setData({
        filteredBuildings: this.data.buildings
      });
      return;
    }

    const filteredBuildings = this.data.buildings.filter(building => {
      return building.name.includes(keyword) || (building.address && building.address.includes(keyword));
    });

    this.setData({ filteredBuildings });
  },

  // 选择/取消选择楼栋单元
  toggleBuildingSelection: function(e) {
    const id = e.currentTarget.dataset.id;
    const selectedBuildingIds = [...this.data.selectedBuildingIds];

    const index = selectedBuildingIds.indexOf(id);
    if (index === -1) {
      // 选择楼栋单元
      selectedBuildingIds.push(id);
    } else {
      // 取消选择楼栋单元
      selectedBuildingIds.splice(index, 1);
    }

    this.setData({ selectedBuildingIds });
  },

  // 获取已选楼栋单元列表
  getSelectedBuildings: function() {
    const { buildings, selectedBuildingIds } = this.data;

    return buildings.filter(building => selectedBuildingIds.includes(building.id));
  },

  // 确认选择
  confirmSelection: function() {
    const selectedBuildings = this.getSelectedBuildings();

    if (selectedBuildings.length === 0) {
      wx.showToast({
        title: '请至少选择一个楼栋单元',
        icon: 'none'
      });
      return;
    }

    // 将选择结果返回给上一页
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 上一页

    // 调用上一页的方法，传递选择结果
    prevPage.onBuildingsSelected({
      detail: {
        buildings: selectedBuildings,
        buildingIds: this.data.selectedBuildingIds
      }
    });

    // 返回上一页
    wx.navigateBack();
  },

  // 取消选择
  cancelSelection: function() {
    wx.navigateBack();
  }
});
