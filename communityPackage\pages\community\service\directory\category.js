// 便民信息黄页分类页面
const app = getApp()
const navigator = require('../../../../../utils/navigator')

Page({
  data: {
    categoryId: null,
    categoryName: '',
    searchKeyword: '',
    services: [],
    filteredServices: []
  },

  onLoad: function(options) {
    const { id, name } = options

    this.setData({
      categoryId: parseInt(id),
      categoryName: name || '服务'
    })

    // 加载该分类下的服务
    this.loadCategoryServices()
  },

  onShow: function() {
    // 页面显示时的逻辑
    // 清除所有导航节流标记
    navigator.clearThrottles();
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    this.filterServices()
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    })

    this.filterServices()
  },

  // 根据关键词筛选服务
  filterServices: function() {
    const { services, searchKeyword } = this.data

    if (!searchKeyword) {
      this.setData({
        filteredServices: services
      })
      return
    }

    const filtered = services.filter(service =>
      service.name.includes(searchKeyword) ||
      service.description.includes(searchKeyword) ||
      (service.address && service.address.includes(searchKeyword))
    )

    this.setData({
      filteredServices: filtered
    })
  },

  // 加载分类服务数据
  loadCategoryServices: function() {
    const { categoryId } = this.data

    // 模拟从服务器获取数据
    // 实际应该调用API获取数据
    const mockServices = this.getMockServices()
    const categoryServices = mockServices.filter(service => service.category === categoryId)

    this.setData({
      services: categoryServices,
      filteredServices: categoryServices
    })
  },

  // 获取模拟服务数据
  getMockServices: function() {
    return [
      {
        id: 101,
        name: '阿姨家政服务',
        description: '专业保洁、月嫂、育儿嫂服务',
        address: '天河区珠江新城',
        phone: '020-12345678',
        category: 1
      },
      {
        id: 102,
        name: '全能家电维修',
        description: '各品牌家电维修、安装、清洗',
        address: '天河区天河路',
        phone: '020-87654321',
        category: 2
      },
      {
        id: 103,
        name: '优质桶装水配送',
        description: '品牌桶装水配送、饮水机销售维护',
        address: '天河区天河东路',
        phone: '020-66668888',
        category: 3
      },
      {
        id: 104,
        name: '洁净洗衣店',
        description: '专业洗衣、熨烫、皮具护理',
        address: '天河区体育西路',
        phone: '020-55556666',
        category: 4
      },
      {
        id: 105,
        name: '24小时开锁服务',
        description: '开锁、换锁、保险柜开启',
        address: '天河区天河北路',
        phone: '020-12312345',
        category: 5
      },
      {
        id: 106,
        name: '专业搬家公司',
        description: '居民搬家、公司搬迁、长途搬运',
        address: '天河区林和西路',
        phone: '020-87878787',
        category: 6
      },
      {
        id: 107,
        name: '快餐外卖',
        description: '中式快餐、西式简餐、各类小吃',
        address: '天河区天河路',
        phone: '020-66667777',
        category: 7
      },
      {
        id: 108,
        name: '24小时便利店',
        description: '日常用品、零食饮料、生活必需品',
        address: '天河区天河路',
        phone: '020-88889999',
        category: 8
      },
      {
        id: 109,
        name: '家庭保洁服务',
        description: '日常保洁、深度清洁、开荒保洁',
        address: '天河区珠江新城',
        phone: '020-55554444',
        category: 1
      },
      {
        id: 110,
        name: '空调维修服务',
        description: '各品牌空调维修、清洗、安装',
        address: '天河区天河路',
        phone: '020-33332222',
        category: 2
      }
    ]
  },

  // 导航到详情页面
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id
    navigator.navigateTo(`/communityPackage/pages/community/service/directory/detail?id=${id}`);
  }
})
