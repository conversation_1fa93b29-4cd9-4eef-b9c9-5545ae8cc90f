/* 待核销订单列表页样式 */
.container {
  padding: 30rpx;
  padding-bottom: 160rpx; /* 为底部按钮留出空间 */
}

/* 商品信息区域 */
.goods-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.goods-card {
  display: flex;
  align-items: center;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.goods-info {
  flex: 1;
}

.goods-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 10rpx;
}

.goods-stats {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 订单列表区域 */
.orders-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: #ff8c00;
  border-radius: 4rpx;
}

.scan-btn {
  font-size: 28rpx;
  color: #ff8c00;
  display: flex;
  align-items: center;
}

.scan-btn::after {
  content: '';
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M7 21H3a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpath d='M17 21h4a2 2 0 0 0 2-2v-4'%3E%3C/path%3E%3Cpath d='M7 3H3a2 2 0 0 0-2 2v4'%3E%3C/path%3E%3Cpath d='M17 3h4a2 2 0 0 1 2 2v4'%3E%3C/path%3E%3Cline x1='12' y1='7' x2='12' y2='17'%3E%3C/line%3E%3Cline x1='7' y1='12' x2='17' y2='12'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 24rpx;
  color: #666;
}

.order-content {
  display: flex;
  padding: 20rpx;
  align-items: center;
}

.buyer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.order-info {
  flex: 1;
}

.buyer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.order-quantity,
.order-amount {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.order-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.action-btn.contact {
  background-color: #e3f2fd;
  color: #2196f3;
}

/* 无订单提示 */
.no-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.no-orders-icon {
  margin-bottom: 20rpx;
}

.no-orders-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部扫码按钮 */
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.footer-btn {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.footer-btn.scan {
  background: #ff8c00;
  color: white;
  display: flex;
  align-items: center;
}

.scan-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M7 21H3a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpath d='M17 21h4a2 2 0 0 0 2-2v-4'%3E%3C/path%3E%3Cpath d='M7 3H3a2 2 0 0 0-2 2v4'%3E%3C/path%3E%3Cpath d='M17 3h4a2 2 0 0 1 2 2v4'%3E%3C/path%3E%3Cline x1='12' y1='7' x2='12' y2='17'%3E%3C/line%3E%3Cline x1='7' y1='12' x2='17' y2='12'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗黑模式样式 */
.darkMode .goods-section,
.darkMode .orders-section,
.darkMode .footer {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .section-title {
  color: #f5f5f7;
}

.darkMode .goods-title {
  color: #f5f5f7;
}

.darkMode .goods-stats {
  color: #8e8e93;
}

.darkMode .order-item {
  background: #3a3a3c;
}

.darkMode .order-header {
  border-bottom: 1rpx solid #2c2c2e;
  color: #8e8e93;
}

.darkMode .buyer-name {
  color: #f5f5f7;
}

.darkMode .order-quantity,
.darkMode .order-amount {
  color: #8e8e93;
}

.darkMode .no-orders-text {
  color: #8e8e93;
}

.darkMode .loading-icon {
  border: 6rpx solid #3a3a3c;
  border-top: 6rpx solid #ff8c00;
}

.darkMode .loading-text {
  color: #8e8e93;
}
