# 部门选择展开问题调试和修复

## 问题分析

根据您提供的数据结构，发现了几个可能导致展开失败的问题：

### 1. 数据结构特点

```json
{
  "id": "7091961583521234963",
  "orgName": "智慧物业发展规划公司",
  "type": "company",
  "children": [
    {
      "id": "7091961583521234964",
      "orgName": "市场调查部",
      "type": "dept",
      "children": null  // ← 注意这里是 null，不是空数组 []
    }
  ]
}
```

### 2. 修复的问题

#### 2.1 条件判断问题
**原来的代码：**
```javascript
// WXML中
wx:if="{{type === 'company' && children && children.length > 0}}"

// JavaScript中  
if (org.children && org.children.length > 0)
```

**修复后：**
```javascript
// WXML中
wx:if="{{type === 'company' && children && children.length}}"

// JavaScript中
if (org.children && org.children.length)
```

**原因：** `children.length > 0` 在 `children` 为 `null` 时会报错，而 `children.length` 在 `children` 为 `null` 时返回 `undefined`，在布尔上下文中为 `false`。

#### 2.2 添加调试日志

为了更好地诊断问题，添加了详细的调试日志：

```javascript
selectDepartment: function (e) {
  const id = e.currentTarget.dataset.id;
  const name = e.currentTarget.dataset.name;
  const type = e.currentTarget.dataset.type;
  const level = e.currentTarget.dataset.level;

  console.log('点击了组织:', { id, name, type, level });
  console.log('当前filteredDepartments:', this.data.filteredDepartments);
  
  if (type === 'company') {
    console.log(`准备切换公司 ${name}(${id}) 的展开状态`);
    this.toggleCompanyExpanded(id);
  }
}

toggleCompanyExpanded: function(companyId) {
  console.log(`开始切换公司 ${companyId} 的展开状态`);
  
  const updateExpanded = (orgList, depth = 0) => {
    console.log(`处理层级 ${depth}:`, orgList.map(org => ({ 
      id: org.id, 
      name: org.orgName, 
      expanded: org.expanded, 
      hasChildren: !!(org.children && org.children.length) 
    })));
    
    return orgList.map(org => {
      if (org.id === companyId && org.type === 'company') {
        const newExpanded = !org.expanded;
        console.log(`找到目标公司 ${org.orgName}(${org.id}), 展开状态: ${org.expanded} -> ${newExpanded}`);
        console.log(`该公司的children:`, org.children);
        return { ...org, expanded: newExpanded };
      }
      
      if (org.children && org.children.length) {
        return { ...org, children: updateExpanded(org.children, depth + 1) };
      }
      
      return org;
    });
  };

  const updatedDepartments = updateExpanded(this.data.filteredDepartments);
  console.log('更新后的数据:', updatedDepartments);
  
  this.setData({ filteredDepartments: updatedDepartments });
}
```

## 调试步骤

### 1. 检查数据加载
打开开发者工具控制台，查看：
```javascript
console.log('部门数据加载完成:', processedData);
```

### 2. 检查点击事件
点击公司时，查看控制台输出：
```
点击了组织: {id: "7091961583521234963", name: "智慧物业发展规划公司", type: "company", level: "1"}
当前filteredDepartments: [...]
准备切换公司 智慧物业发展规划公司(7091961583521234963) 的展开状态
```

### 3. 检查展开逻辑
查看递归查找过程：
```
开始切换公司 7091961583521234963 的展开状态
处理层级 0: [{id: "7091961583521234952", name: "网信科技公司", expanded: false, hasChildren: true}]
处理层级 1: [{id: "7091961583521234954", name: "研发部", expanded: false, hasChildren: false}, ...]
找到目标公司 智慧物业发展规划公司(7091961583521234963), 展开状态: false -> true
该公司的children: [{id: "7091961583521234964", name: "市场调查部", ...}]
```

### 4. 检查数据更新
查看更新后的数据结构：
```
更新后的数据: [...]
```

## 可能的其他问题

### 1. 模板数据传递问题
确保递归模板中的数据传递正确：
```xml
<template is="org-item" data="{{...childItem, level: level + 1, selectedDepartmentId: selectedDepartmentId}}" />
```

### 2. CSS样式问题
检查展开图标的CSS动画：
```css
.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}
```

### 3. 事件绑定问题
确保递归模板中的事件绑定正确：
```xml
<view class="selector-item" bindtap="selectDepartment" 
      data-id="{{id}}" data-name="{{orgName}}" data-type="{{type}}">
```

## 验证方法

### 1. 手动验证
1. 打开部门选择器
2. 点击"网信科技公司"，应该能展开
3. 点击"智慧物业发展规划公司"，应该能展开
4. 查看控制台日志，确认逻辑执行正确

### 2. 数据验证
在控制台中执行：
```javascript
// 检查当前数据结构
console.log('当前部门数据:', this.data.filteredDepartments);

// 检查特定公司的展开状态
const company = this.data.filteredDepartments[0].children.find(c => c.id === '7091961583521234963');
console.log('智慧物业发展规划公司状态:', company);
```

## 预期结果

修复后应该能够：
1. ✅ 点击"网信科技公司"正常展开/收起
2. ✅ 点击"智慧物业发展规划公司"正常展开/收起  
3. ✅ 展开图标正确显示和旋转
4. ✅ 子级部门正确显示
5. ✅ 只有部门可以被选中
6. ✅ 控制台日志清晰显示执行过程

如果问题仍然存在，请查看控制台日志，确定具体在哪一步出现了问题。
