# WebSocket Token刷新功能测试文档

## 改进内容

### 1. 增强的Token状态检测
- 不仅检测token是否存在，还检测token是否过期
- 使用与request.js相同的过期检测逻辑（提前5分钟判断过期）

### 2. Token过期处理流程
```
Token过期检测 → 关闭当前连接 → 调用request.js刷新token → token刷新成功 → 自动重连
```

### 3. 新增方法

#### `handleTokenExpired()`
- 检测到token过期时调用
- 关闭现有连接，停止重连和心跳
- 调用`refreshTokenAndReconnect()`

#### `refreshTokenAndReconnect()`
- 调用request.js的`refreshToken()`方法
- 刷新成功后重新检查token状态并连接
- 刷新失败则进入等待状态

#### `setupRequestTokenListener()`
- 监听request.js的token变化通知
- 当token刷新成功时立即检查连接状态

### 4. 改进的连接错误处理
- `handleClose()` 和 `handleError()` 方法现在都会检测token过期状态
- 根据token状态选择合适的处理策略：
  - 无token → 等待状态
  - token过期 → 刷新token
  - token有效 → 重连

## 测试场景

### 场景1：Token过期时的自动刷新
1. 用户正常使用WebSocket连接
2. Token到期时间临近（提前5分钟）
3. WebSocket管理器检测到token过期
4. 自动调用request.js刷新token
5. 刷新成功后自动重连

### 场景2：连接断开时的Token检测
1. WebSocket连接因网络问题断开
2. 重连前检测token状态
3. 如果token过期，先刷新token再重连
4. 如果token有效，直接重连

### 场景3：Token刷新失败的处理
1. Token过期需要刷新
2. 刷新token失败（如refresh_token也过期）
3. 进入等待状态，等待用户重新登录

## 关键改进点

1. **协同工作**：WebSocket管理器与request.js的token管理机制协同工作
2. **主动刷新**：检测到token过期时主动刷新，而不是等待API请求失败
3. **状态同步**：监听request.js的token变化，确保状态同步
4. **错误恢复**：token刷新成功后自动恢复连接，用户无感知

## 使用方式

WebSocket管理器的使用方式保持不变，所有token管理都在后台自动处理：

```javascript
// 初始化（在app.js中）
const websocketManager = require('./utils/websocket-manager');
websocketManager.init();

// 使用（在页面中）
websocketManager.on('open', () => {
  console.log('连接已建立');
});

websocketManager.sendPrivateMessage('text', 'Hello', 'receiverId');
```

用户无需关心token过期和刷新的细节，WebSocket管理器会自动处理这些情况。
