<!--员工列表页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索员工姓名或编号" value="{{searchText}}" bindinput="onSearchInput" confirm-type="search" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchText}}"></view>
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <view class="filter-icon"></view>
    </view>
  </view>
  
  <!-- 筛选面板 -->
  <view class="filter-panel {{filterVisible ? 'show' : ''}}">
    <view class="filter-mask" bindtap="hideFilter"></view>
    <view class="filter-content">
      <view class="filter-header">
        <text>筛选条件</text>
        <view class="close-btn" bindtap="hideFilter"></view>
      </view>
      
      <!-- 组织筛选 -->
      <view class="filter-section">
        <view class="filter-title">组织</view>
        <view class="filter-options">
          <view class="filter-option {{selectedOrganization === item ? 'active' : ''}}"
                wx:for="{{organizations}}"
                wx:key="*this"
                data-index="{{index}}"
                bindtap="selectOrganization">{{item}}</view>
        </view>
      </view>
      
      <!-- 职位筛选 -->
      <view class="filter-section">
        <view class="filter-title">职位</view>
        <view class="filter-options">
          <view class="filter-option {{selectedPosition === item ? 'active' : ''}}" 
                wx:for="{{positions}}" 
                wx:key="*this" 
                data-index="{{index}}" 
                bindtap="selectPosition">{{item}}</view>
        </view>
      </view>
      
      <!-- 性别筛选 -->
      <view class="filter-section">
        <view class="filter-title">性别</view>
        <view class="filter-options">
          <view class="filter-option {{selectedGender === item ? 'active' : ''}}" 
                wx:for="{{genders}}" 
                wx:key="*this" 
                data-index="{{index}}" 
                bindtap="selectGender">{{item}}</view>
        </view>
      </view>
      
      <!-- 年龄筛选 -->
      <view class="filter-section">
        <view class="filter-title">年龄</view>
        <view class="filter-options">
          <view class="filter-option {{selectedAgeRange === item ? 'active' : ''}}" 
                wx:for="{{ageRanges}}" 
                wx:key="*this" 
                data-index="{{index}}" 
                bindtap="selectAgeRange">{{item}}</view>
        </view>
      </view>
      
      <!-- 筛选按钮 -->
      <view class="filter-actions">
        <button class="reset-btn" bindtap="resetFilter">重置</button>
        <button class="apply-btn" bindtap="applyFilter">应用</button>
      </view>
    </view>
  </view>
  
  <!-- 排序选项 -->
  <view class="sort-bar">
    <view class="sort-title">排序方式：</view>
    <view class="sort-options">
      <view class="sort-option {{selectedSortOption === item ? 'active' : ''}}" 
            wx:for="{{sortOptions}}" 
            wx:key="*this" 
            data-index="{{index}}" 
            bindtap="selectSortOption">
        {{item}}
        <view class="sort-direction" wx:if="{{selectedSortOption === item}}">
          <view class="{{isAscending ? 'sort-asc' : 'sort-desc'}}"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 员工列表 -->
  <view class="staff-list" wx:if="{{!isLoading && !isEmpty}}">
    <view class="staff-card" wx:for="{{filteredStaffList}}" wx:key="id" bindtap="viewStaffDetail" data-id="{{item.id}}">
      <view class="staff-info">
        <view class="staff-name-id">
          <text class="staff-name">{{item.name}}</text>
          <text class="staff-id">{{item.employeeId}}</text>
        </view>
        <view class="staff-position">{{item.organization}} | {{item.position}}</view>
        <view class="staff-details">
          <text class="staff-gender-age">{{item.gender}} | {{item.age}}岁</text>
          <text class="staff-entry">入职: {{item.entryDate}}</text>
        </view>
      </view>
      <view class="staff-arrow"></view>
    </view>

    <!-- 加载更多提示 -->
    <view class="load-more-tip" wx:if="{{!hasMore && filteredStaffList.length > 0}}">
      <text>已显示全部 {{total}} 条数据</text>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoading && isEmpty}}">
    <view class="empty-icon"></view>
    <text class="empty-text">暂无员工数据</text>
    <text class="empty-subtext">点击下方按钮添加员工</text>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="stats-btn" bindtap="viewStaffStats">
      <view class="stats-icon"></view>
      <text>员工统计</text>
    </button>
    <button class="add-btn" bindtap="addNewStaff">
      <view class="add-icon"></view>
      <text>添加员工</text>
    </button>
  </view>
</view>
