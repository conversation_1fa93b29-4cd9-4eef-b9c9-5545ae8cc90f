<!--garbage.wxml-->
<view class="container">
  <view class="page-header">
    <view class="page-title">垃圾分类指南</view>
    <view class="page-subtitle">保护环境，从分类做起</view>
  </view>
  
  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="14"></icon>
      <input class="search-input" placeholder="搜索垃圾名称" bindinput="onSearchInput" value="{{searchKeyword}}"/>
      <icon wx:if="{{searchKeyword}}" class="clear-icon" type="clear" size="14" bindtap="clearSearch"></icon>
    </view>
  </view>
  
  <view class="category-tabs">
    <view class="category-tab {{currentCategory === 'all' ? 'active' : ''}}" 
          bindtap="switchCategory" data-category="all">
      <view class="tab-icon">
        <icon class="iconfont icon-all"></icon>
      </view>
      <view class="tab-name">全部</view>
    </view>
    <view class="category-tab {{currentCategory === 'recyclable' ? 'active' : ''}}" 
          bindtap="switchCategory" data-category="recyclable">
      <view class="tab-icon recyclable">
        <icon class="iconfont icon-recyclable"></icon>
      </view>
      <view class="tab-name">可回收物</view>
    </view>
    <view class="category-tab {{currentCategory === 'harmful' ? 'active' : ''}}" 
          bindtap="switchCategory" data-category="harmful">
      <view class="tab-icon harmful">
        <icon class="iconfont icon-harmful"></icon>
      </view>
      <view class="tab-name">有害垃圾</view>
    </view>
    <view class="category-tab {{currentCategory === 'kitchen' ? 'active' : ''}}" 
          bindtap="switchCategory" data-category="kitchen">
      <view class="tab-icon kitchen">
        <icon class="iconfont icon-kitchen"></icon>
      </view>
      <view class="tab-name">厨余垃圾</view>
    </view>
    <view class="category-tab {{currentCategory === 'other' ? 'active' : ''}}" 
          bindtap="switchCategory" data-category="other">
      <view class="tab-icon other">
        <icon class="iconfont icon-other"></icon>
      </view>
      <view class="tab-name">其他垃圾</view>
    </view>
  </view>
  
  <view class="garbage-list">
    <view class="garbage-item" wx:for="{{filteredItems}}" wx:key="id" bindtap="showDetail" data-item="{{item}}">
      <view class="garbage-icon {{item.category}}">
        <icon class="iconfont {{getCategoryIcon(item.category)}}"></icon>
      </view>
      <view class="garbage-info">
        <view class="garbage-name">{{item.name}}</view>
        <view class="garbage-category">{{getCategoryName(item.category)}}</view>
      </view>
      <view class="garbage-arrow">></view>
    </view>
  </view>
  
  <view class="no-result" wx:if="{{filteredItems.length === 0}}">
    <icon class="no-result-icon" type="info" size="40" color="#ccc"></icon>
    <view class="no-result-text">未找到相关垃圾信息</view>
    <view class="no-result-tip">您可以联系客服添加此垃圾分类信息</view>
  </view>
  
  <view class="category-guide" wx:if="{{showGuide}}">
    <view class="guide-header">
      <view class="guide-title">{{getCategoryName(guideCategory)}}指南</view>
      <view class="guide-close" bindtap="closeGuide">×</view>
    </view>
    <view class="guide-content">
      <view class="guide-icon {{guideCategory}}">
        <icon class="iconfont {{getCategoryIcon(guideCategory)}}"></icon>
      </view>
      <view class="guide-description">{{getCategoryDescription(guideCategory)}}</view>
      <view class="guide-examples">
        <view class="examples-title">常见物品：</view>
        <view class="examples-list">{{getCategoryExamples(guideCategory)}}</view>
      </view>
      <view class="guide-tips">
        <view class="tips-title">投放提示：</view>
        <view class="tips-list">{{getCategoryTips(guideCategory)}}</view>
      </view>
    </view>
  </view>
</view>
