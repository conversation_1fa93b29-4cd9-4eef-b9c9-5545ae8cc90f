// utils/statistics-manager.js
const dateUtil = require('./dateUtil.js')

/**
 * 数据统计管理工具类
 * 用于获取、处理和缓存数据统计相关的数据
 */
class StatisticsManager {
  constructor() {
    // 缓存数据
    this.cache = {
      dashboard: {
        data: null,
        timestamp: 0
      },
      categories: {
        resident: { data: null, timestamp: 0 },
        house: { data: null, timestamp: 0 },
        parking: { data: null, timestamp: 0 },
        facility: { data: null, timestamp: 0 },
        workorder: { data: null, timestamp: 0 },
        visitor: { data: null, timestamp: 0 }
      }
    };
    
    // 缓存过期时间（毫秒）
    this.cacheExpiration = 5 * 60 * 1000; // 5分钟
  }
  
  /**
   * 获取仪表盘数据
   * @param {boolean} forceRefresh 是否强制刷新缓存
   * @returns {Promise} 返回仪表盘数据的Promise
   */
  getDashboardData(forceRefresh = false) {
    return new Promise((resolve, reject) => {
      // 检查缓存是否有效
      const now = Date.now();
      if (!forceRefresh && 
          this.cache.dashboard.data && 
          (now - this.cache.dashboard.timestamp < this.cacheExpiration)) {
        // 返回缓存数据
        resolve(this.cache.dashboard.data);
        return;
      }
      
      // 这里应该调用API获取数据，这里使用模拟数据
      setTimeout(() => {
        const data = {
          workOrderCount: this._getRandomInt(5, 20),
          newWorkOrderCount: this._getRandomInt(1, 8),
          visitorCount: this._getRandomInt(15, 40),
          currentVisitorCount: this._getRandomInt(5, 15),
          parkingRate: this._getRandomInt(60, 95) + '%',
          usedParkingCount: this._getRandomInt(120, 180),
          totalParkingCount: 200,
          abnormalFacilityCount: this._getRandomInt(0, 5),
          monitorOnlineRate: this._getRandomInt(95, 100) + '%',
          residentCount: this._getRandomInt(240, 270),
          residentVerifyRate: this._getRandomInt(80, 95) + '%'
        };
        
        // 更新缓存
        this.cache.dashboard.data = data;
        this.cache.dashboard.timestamp = now;
        
        resolve(data);
      }, 500);
    });
  }
  
  /**
   * 获取分类数据
   * @param {string} category 分类名称
   * @param {string} timeRange 时间范围
   * @param {boolean} forceRefresh 是否强制刷新缓存
   * @returns {Promise} 返回分类数据的Promise
   */
  getCategoryData(category, timeRange = 'week', forceRefresh = false) {
    return new Promise((resolve, reject) => {
      // 检查分类是否有效
      if (!this.cache.categories[category]) {
        reject(new Error(`无效的分类: ${category}`));
        return;
      }
      
      // 检查缓存是否有效
      const now = Date.now();
      if (!forceRefresh && 
          this.cache.categories[category].data && 
          (now - this.cache.categories[category].timestamp < this.cacheExpiration)) {
        // 返回缓存数据
        resolve(this.cache.categories[category].data);
        return;
      }
      
      // 这里应该调用API获取数据，这里使用模拟数据
      setTimeout(() => {
        let data;
        
        switch (category) {
          case 'resident':
            data = this._getResidentData(timeRange);
            break;
          case 'house':
            data = this._getHouseData(timeRange);
            break;
          case 'parking':
            data = this._getParkingData(timeRange);
            break;
          case 'facility':
            data = this._getFacilityData(timeRange);
            break;
          case 'workorder':
            data = this._getWorkOrderData(timeRange);
            break;
          case 'visitor':
            data = this._getVisitorData(timeRange);
            break;
          default:
            reject(new Error(`无效的分类: ${category}`));
            return;
        }
        
        // 更新缓存
        this.cache.categories[category].data = data;
        this.cache.categories[category].timestamp = now;
        
        resolve(data);
      }, 500);
    });
  }
  
  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cache.dashboard.data = null;
    this.cache.dashboard.timestamp = 0;
    
    Object.keys(this.cache.categories).forEach(category => {
      this.cache.categories[category].data = null;
      this.cache.categories[category].timestamp = 0;
    });
  }
  
  /**
   * 格式化百分比
   * @param {number} value 百分比值（0-100）
   * @returns {string} 格式化后的百分比
   */
  formatPercentage(value) {
    return `${Math.round(value)}%`;
  }
  
  /**
   * 生成随机整数
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {number} 随机整数
   * @private
   */
  _getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
  
  /**
   * 获取居民分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 居民分类数据
   * @private
   */
  _getResidentData(timeRange) {
    const totalCount = this._getRandomInt(240, 270);
    const ownerCount = this._getRandomInt(140, 160);
    const tenantCount = this._getRandomInt(60, 80);
    const familyCount = totalCount - ownerCount - tenantCount;
    const maleCount = this._getRandomInt(130, 150);
    const femaleCount = totalCount - maleCount;
    
    return {
      totalCount,
      verifiedRate: this._getRandomInt(80, 95) + '%',
      ownerCount,
      tenantCount,
      familyCount,
      maleCount,
      femaleCount,
      ageDistribution: [
        { range: '18岁以下', count: this._getRandomInt(10, 20) },
        { range: '18-30岁', count: this._getRandomInt(60, 80) },
        { range: '31-45岁', count: this._getRandomInt(80, 100) },
        { range: '46-60岁', count: this._getRandomInt(50, 70) },
        { range: '60岁以上', count: this._getRandomInt(15, 25) }
      ]
    };
  }
  
  /**
   * 获取房屋分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 房屋分类数据
   * @private
   */
  _getHouseData(timeRange) {
    const totalCount = this._getRandomInt(180, 200);
    const occupiedCount = this._getRandomInt(150, 180);
    
    return {
      totalCount,
      occupiedCount,
      occupancyRate: Math.round(occupiedCount / totalCount * 100) + '%',
      typeDistribution: [
        { type: '两室一厅', count: this._getRandomInt(80, 100) },
        { type: '三室一厅', count: this._getRandomInt(60, 80) },
        { type: '四室两厅', count: this._getRandomInt(20, 40) },
        { type: '其他', count: this._getRandomInt(5, 15) }
      ],
      buildingDistribution: [
        { building: 'A栋', count: this._getRandomInt(40, 60) },
        { building: 'B栋', count: this._getRandomInt(40, 60) },
        { building: 'C栋', count: this._getRandomInt(40, 60) },
        { building: 'D栋', count: this._getRandomInt(40, 60) }
      ]
    };
  }
  
  /**
   * 获取车位分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 车位分类数据
   * @private
   */
  _getParkingData(timeRange) {
    const totalCount = 200;
    const usedCount = this._getRandomInt(120, 180);
    
    return {
      totalCount,
      usedCount,
      usageRate: Math.round(usedCount / totalCount * 100) + '%',
      typeDistribution: [
        { type: '地上车位', count: this._getRandomInt(60, 80) },
        { type: '地下车位', count: this._getRandomInt(100, 120) }
      ],
      vehicleTypeDistribution: [
        { type: '小型车', count: this._getRandomInt(100, 140) },
        { type: 'SUV', count: this._getRandomInt(30, 50) },
        { type: '其他', count: this._getRandomInt(5, 15) }
      ]
    };
  }
  
  /**
   * 获取设施分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 设施分类数据
   * @private
   */
  _getFacilityData(timeRange) {
    const totalCount = this._getRandomInt(50, 70);
    const normalCount = this._getRandomInt(45, 65);
    const abnormalCount = totalCount - normalCount;
    
    return {
      totalCount,
      normalCount,
      abnormalCount,
      statusRate: Math.round(normalCount / totalCount * 100) + '%',
      typeDistribution: [
        { type: '电梯', count: this._getRandomInt(8, 12) },
        { type: '监控', count: this._getRandomInt(20, 30) },
        { type: '门禁', count: this._getRandomInt(10, 15) },
        { type: '消防', count: this._getRandomInt(8, 12) },
        { type: '其他', count: this._getRandomInt(5, 10) }
      ],
      maintenanceRecords: [
        { date: dateUtil.formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), count: this._getRandomInt(1, 3) },
        { date: dateUtil.formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000)), count: this._getRandomInt(1, 3) },
        { date: dateUtil.formatDate(new Date()), count: this._getRandomInt(0, 2) }
      ]
    };
  }
  
  /**
   * 获取工单分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 工单分类数据
   * @private
   */
  _getWorkOrderData(timeRange) {
    const totalCount = this._getRandomInt(100, 150);
    const completedCount = this._getRandomInt(80, 120);
    const pendingCount = this._getRandomInt(10, 20);
    const processingCount = totalCount - completedCount - pendingCount;
    
    return {
      totalCount,
      completedCount,
      pendingCount,
      processingCount,
      completionRate: Math.round(completedCount / totalCount * 100) + '%',
      typeDistribution: [
        { type: '维修', count: this._getRandomInt(60, 80) },
        { type: '投诉', count: this._getRandomInt(20, 40) },
        { type: '建议', count: this._getRandomInt(10, 20) },
        { type: '其他', count: this._getRandomInt(5, 15) }
      ],
      statusDistribution: [
        { status: '待处理', count: pendingCount },
        { status: '处理中', count: processingCount },
        { status: '已完成', count: completedCount }
      ]
    };
  }
  
  /**
   * 获取访客分类数据
   * @param {string} timeRange 时间范围
   * @returns {object} 访客分类数据
   * @private
   */
  _getVisitorData(timeRange) {
    const totalCount = this._getRandomInt(100, 150);
    const currentCount = this._getRandomInt(10, 20);
    
    return {
      totalCount,
      currentCount,
      purposeDistribution: [
        { purpose: '探亲访友', count: this._getRandomInt(50, 70) },
        { purpose: '快递外卖', count: this._getRandomInt(30, 50) },
        { purpose: '维修服务', count: this._getRandomInt(10, 20) },
        { purpose: '其他', count: this._getRandomInt(5, 15) }
      ],
      timeDistribution: [
        { time: '上午', count: this._getRandomInt(30, 50) },
        { time: '下午', count: this._getRandomInt(40, 60) },
        { time: '晚上', count: this._getRandomInt(20, 40) }
      ],
      stayDurationDistribution: [
        { duration: '1小时内', count: this._getRandomInt(40, 60) },
        { duration: '1-3小时', count: this._getRandomInt(30, 50) },
        { duration: '3小时以上', count: this._getRandomInt(10, 30) }
      ]
    };
  }
}

// 创建单例
const statisticsManager = new StatisticsManager();

module.exports = statisticsManager;
