/* 物业/服务咨询页面样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9fafb;
  position: relative;
  padding-bottom: 24px;
}

/* 页面顶部样式 */

/* 卡片内容样式 */
info-card {
  margin: 16px 24px;
}

/* 联系方式卡片样式 */
.contact-list {
  margin-bottom: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.contact-phone {
  font-size: 15px;
  color: #666;
}

.contact-action {
  margin-left: 16px;
}

.call-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 140, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.call-btn:active {
  background-color: rgba(255, 140, 0, 0.2);
  transform: scale(0.95);
}

.call-icon {
  width: 20px;
  height: 20px;
}

.property-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  opacity: 0.5;
}

/* 常见问题FAQ样式 */
.faq-list {
  display: flex;
  flex-direction: column;
}

.faq-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  cursor: pointer;
}

.faq-question {
  font-size: 16px;
  color: #333;
  flex: 1;
}

.faq-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.faq-arrow.expanded {
  transform: rotate(180deg);
}

.faq-arrow image {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

.faq-answer {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  padding: 0 0 16px;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.faq-answer.expanded {
  max-height: 500px;
  opacity: 1;
}

/* 在线服务样式 */
.service-buttons {
  display: flex;
  gap: 16px;
}

.service-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px;
  line-height: normal;
  font-size: 16px;
  color: #333;
  transition: all 0.3s ease;
}

.service-btn::after {
  border: none;
}

.service-btn:first-child {
  background-color: #ff8c00;
  color: #fff;
  border: none;
}

.service-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.service-btn:first-child .btn-icon {
  filter: brightness(0) invert(1);
}

/* 留言表单弹窗样式 */
.message-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  align-items: flex-end;
  visibility: hidden;
}

.message-modal.show {
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-modal.show .modal-mask {
  opacity: 1;
}

.modal-content {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-radius: 24px 24px 0 0;
  padding: 24px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 101;
}

.message-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close image {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

.modal-body {
  margin-bottom: 24px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f7;
  border-radius: 12px;
  padding: 0 16px;
  height: 48px;
  font-size: 15px;
  color: #333;
}

.picker-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

.form-textarea {
  width: 100%;
  height: 120px;
  background-color: #f5f5f7;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
}

.form-input {
  width: 100%;
  height: 48px;
  background-color: #f5f5f7;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.modal-footer {
  display: flex;
  gap: 16px;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: normal;
}

.cancel-btn {
  background-color: #f5f5f7;
  color: #333;
}

.submit-btn {
  background-color: #ff8c00;
  color: #fff;
}

.submit-btn[disabled] {
  background-color: rgba(255, 140, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
}

.cancel-btn::after, .submit-btn::after {
  border: none;
}
