/* 私信消息处理测试页面样式 */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 8rpx solid #007aff;
  padding-left: 20rpx;
}

.test-item {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.test-item:last-child {
  margin-bottom: 0;
}

.test-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.test-btn.primary {
  background-color: #28a745;
}

.test-btn:active {
  opacity: 0.8;
}

.info-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  position: relative;
}

.info-text:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #007aff;
  font-weight: bold;
}

.current-message {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.message-preview {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
}

.json-text {
  font-size: 24rpx;
  color: #495057;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
