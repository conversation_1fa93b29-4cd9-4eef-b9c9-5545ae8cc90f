/* 工单管理主页面样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 卡片通用样式 */
.overview-card, .action-card, .recent-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 28rpx;
  color: #ff8c00;
}

/* 工单概览卡片样式 */
.status-grid {
  display: flex;
  justify-content: space-between;
}

.status-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 16rpx;
  background-color: #f8f8f8;
  margin: 0 10rpx;
}

.status-item:first-child {
  margin-left: 0;
}

.status-item:last-child {
  margin-right: 0;
}

.status-count {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.status-label {
  font-size: 24rpx;
  color: #666;
}

/* 状态颜色 */
.status-item.pending .status-count {
  color: #ff9800;
}

.status-item.processing .status-count {
  color: #2196f3;
}

.status-item.completed .status-count {
  color: #4caf50;
}

.status-item.cancelled .status-count {
  color: #9e9e9e;
}

/* 快捷操作卡片样式 */
.action-grid {
  display: flex;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.action-label {
  font-size: 28rpx;
  color: #333;
}

/* 图标样式 */
.list-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='8' y1='6' x2='21' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='12' x2='21' y2='12'%3E%3C/line%3E%3Cline x1='8' y1='18' x2='21' y2='18'%3E%3C/line%3E%3Cline x1='3' y1='6' x2='3.01' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='12' x2='3.01' y2='12'%3E%3C/line%3E%3Cline x1='3' y1='18' x2='3.01' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.create-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='5' x2='12' y2='19'%3E%3C/line%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.stats-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23ff8c00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='20' x2='18' y2='10'%3E%3C/line%3E%3Cline x1='12' y1='20' x2='12' y2='4'%3E%3C/line%3E%3Cline x1='6' y1='20' x2='6' y2='14'%3E%3C/line%3E%3C/svg%3E");
}

/* 最近工单列表样式 */
.order-list {
  margin-top: 20rpx;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-type-tag {
  min-width: 60rpx;
  height: 48rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #fff;
  padding: 0 12rpx;
}

/* 不同类型工单的标签颜色 */
.order-type-tag.repair {
  background-color: #ff8c00; /* 维修工单 - 橙色 */
}

.order-type-tag.complaint {
  background-color: #f44336; /* 投诉工单 - 红色 */
}

.order-type-tag.suggestion {
  background-color: #2196f3; /* 建议工单 - 蓝色 */
}

.order-type-tag.other {
  background-color: #9e9e9e; /* 其他工单 - 灰色 */
}

.order-info {
  flex: 1;
  margin-right: 20rpx;
}

.order-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-meta {
  font-size: 24rpx;
  color: #999;
}

.order-id {
  margin-right: 16rpx;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  background-color: #f5f5f5;
}

/* 工单状态样式 */
.status-pending .order-status {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-processing .order-status {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-completed .order-status {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-cancelled .order-status {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* 空列表样式 */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'%3E%3C/path%3E%3Cpolyline points='13 2 13 9 20 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .title,
.darkMode .card-title {
  color: #fff;
}

.darkMode .subtitle,
.darkMode .status-label,
.darkMode .action-label,
.darkMode .order-title {
  color: #ccc;
}

.darkMode .overview-card,
.darkMode .action-card,
.darkMode .recent-card {
  background-color: #2a2a2a;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.darkMode .status-item {
  background-color: #333;
}

.darkMode .action-icon {
  background-color: #333;
}

.darkMode .order-item {
  border-bottom-color: #333;
}

.darkMode .order-meta,
.darkMode .empty-text {
  color: #888;
}

.darkMode .order-type-tag {
  opacity: 0.9;
}
