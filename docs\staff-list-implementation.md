# 员工管理列表页面实现文档

## 功能概述

员工管理列表页面已完全重构，删除了所有模拟数据，改为使用 `propertyApi.getPersonList` 接口获取真实数据。

## 主要功能

### 1. 数据获取
- **API接口**：使用 `propertyApi.getPersonList` 获取员工列表数据
- **分页加载**：支持分页获取数据，默认每页10条
- **搜索功能**：支持按员工姓名搜索，通过API参数 `personName` 实现
- **下拉刷新**：支持下拉刷新获取最新数据
- **上拉加载**：支持上拉加载更多数据

### 2. 数据处理
- **格式转换**：将API返回的原始数据转换为页面所需格式
- **字典映射**：使用字典数据转换状态码和性别码为中文显示
- **部门职位**：动态获取组织架构和职位信息用于显示和筛选

### 3. 筛选排序
- **部门筛选**：从组织架构动态获取部门列表
- **职位筛选**：从职位接口动态获取职位列表
- **性别筛选**：使用性别字典数据
- **年龄筛选**：支持年龄范围筛选
- **排序功能**：支持按姓名、入职时间、部门排序

## 数据结构

### API返回的原始数据格式
```javascript
{
  id: "20",
  personName: "卜凡傲",
  personNumber: "3131321",
  phone: "13685156020",
  idCard: "320323199006060298",
  gender: "man",
  age: 35,
  orgId: "7091961583521234970",
  positionId: "14",
  status: "active",
  entryTime: "2025-07-15 00:00:00",
  media: "assets/bae3cdae07c547bcaf6871752ef61bcf.jpg",
  salary: 6546,
  email: "<EMAIL>",
  address: "对话"
}
```

### 转换后的页面数据格式
```javascript
{
  id: "20",
  name: "卜凡傲",
  employeeId: "3131321",
  phone: "13685156020",
  idCard: "320323199006060298",
  gender: "男",
  age: 35,
  department: "董事部",
  position: "高级工程师",
  status: "在职",
  entryDate: "2025-07-15",
  facePhoto: "https://api.example.com/common-api/v1/file/assets/xxx.jpg",
  statusCode: "active",
  genderCode: "man",
  orgId: "7091961583521234970",
  positionId: "14"
}
```

## 关键方法

### `loadStaffData(isLoadMore)`
- 加载员工数据的主要方法
- 支持分页加载和搜索
- 处理加载状态和错误处理

### `formatStaffData(rawData)`
- 格式化API返回的原始数据
- 转换字典值为中文显示
- 处理图片路径和日期格式

### `initializeData()`
- 初始化页面数据
- 并行加载组织架构和职位数据
- 获取字典数据

### `loadOrgTree()` 和 `loadPositions()`
- 获取组织架构和职位数据
- 用于筛选选项和数据显示

## 字典数据使用

### 员工状态字典 (person_status)
- `active` → "在职"
- `inactive` → "离职"
- `trial` → "试用"
- `pending` → "待审核"

### 性别字典 (gender)
- `man` → "男"
- `woman` → "女"

## 分页逻辑

1. **初始加载**：pageNum=1, pageSize=10
2. **搜索重置**：搜索时重置为第一页
3. **下拉刷新**：重置为第一页并清空现有数据
4. **上拉加载**：pageNum递增，数据追加到现有列表
5. **加载完成**：根据 `total` 判断是否还有更多数据

## 搜索功能

- **实时搜索**：输入时立即触发搜索
- **API搜索**：通过 `personName` 参数在服务端搜索
- **重置分页**：搜索时重置到第一页

## 筛选功能

- **前端筛选**：除搜索外的其他筛选在前端进行
- **动态选项**：部门和职位选项从API动态获取
- **状态保持**：筛选条件在页面生命周期内保持

## 错误处理

1. **网络错误**：显示错误提示，保持现有数据
2. **数据为空**：显示空状态页面
3. **加载失败**：停止加载状态，允许重试

## 性能优化

1. **并行加载**：基础数据（组织、职位、字典）并行获取
2. **分页加载**：避免一次性加载大量数据
3. **前端筛选**：减少API请求次数
4. **图片懒加载**：头像图片按需加载

## 相关文件

- `propertyPackage/pages/property/staff/staff-list.js` - 主要逻辑
- `propertyPackage/pages/property/staff/staff-list.wxml` - 页面结构
- `propertyPackage/pages/property/staff/staff-list.wxss` - 样式文件
- `propertyPackage/pages/property/staff/staff-list.json` - 页面配置
- `api/propertyApi.js` - 员工相关API
- `api/commApi.js` - 通用API（职位等）
