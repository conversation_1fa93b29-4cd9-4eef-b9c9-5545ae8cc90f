// pages/property/workorder/batch-complete/index.js
const workOrderManager = require('../../../../utils/workorder-manager');

Page({
  data: {
    darkMode: false,
    orderIds: [],
    orders: [],
    loading: true,
    submitting: false,
    
    // 完成信息
    completeResult: '',
    completeRemark: '',
    
    // 状态栏高度
    statusBarHeight: 20
  },

  onLoad: function(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }
    
    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight
    });
    
    // 获取URL参数中的工单ID列表
    if (options.ids) {
      const orderIds = options.ids.split(',');
      this.setData({ orderIds });
      this.loadWorkOrders(orderIds);
    } else {
      wx.showToast({
        title: '未选择工单',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载工单信息
  loadWorkOrders: function(orderIds) {
    this.setData({ loading: true });
    
    const promises = orderIds.map(id => workOrderManager.getWorkOrderDetail(id));
    
    Promise.all(promises)
      .then(orders => {
        // 过滤出可以完成的工单（处理中状态）
        const validOrders = orders.filter(order => 
          order && order.status === 'processing'
        );
        
        if (validOrders.length === 0) {
          wx.showToast({
            title: '没有可完成的工单',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        this.setData({
          orders: validOrders,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单信息失败', error);
        this.setData({ loading: false });
        
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 输入完成结果
  inputCompleteResult: function(e) {
    this.setData({ completeResult: e.detail.value });
  },
  
  // 输入完成备注
  inputCompleteRemark: function(e) {
    this.setData({ completeRemark: e.detail.value });
  },
  
  // 提交完成
  submitComplete: function() {
    const { orderIds, completeResult, completeRemark } = this.data;
    
    if (!completeResult) {
      wx.showToast({
        title: '请输入处理结果',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    // 调用工单管理器批量完成工单
    workOrderManager.batchCompleteOrders(orderIds, completeResult, completeRemark)
      .then(result => {
        this.setData({ submitting: false });
        
        const { success, failed } = result;
        
        if (failed.length > 0) {
          // 有失败的工单
          wx.showModal({
            title: '部分工单完成失败',
            content: `成功: ${success.length}个, 失败: ${failed.length}个`,
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        } else {
          // 全部成功
          wx.showToast({
            title: '完成成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(error => {
        console.error('批量完成工单失败', error);
        this.setData({ submitting: false });
        
        wx.showToast({
          title: error.message || '完成失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 取消完成
  cancelComplete: function() {
    wx.navigateBack();
  },
  
  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
