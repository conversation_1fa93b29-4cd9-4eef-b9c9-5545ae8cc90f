/* pages/workorder/evaluate/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #FF8C00; /* 主品牌色，橙色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 工单基本信息 */
.order-info-card {
  margin-top: 44px;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.order-meta {
  display: flex;
  align-items: center;
}

.order-type {
  font-size: 12px;
  color: #666;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.order-id {
  font-size: 12px;
  color: #999;
}

/* 评价表单 */
.evaluate-form {
  flex: 1;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

/* 星级评分 */
.rating-section {
  margin-bottom: 24px;
}

.rating-stars {
  display: flex;
  margin-bottom: 8px;
}

.star-item {
  margin-right: 16px;
}

.star-icon {
  width: 32px;
  height: 32px;
  /* 确保星星图标使用橙色 */
  filter: sepia(100%) saturate(300%) brightness(100%) hue-rotate(350deg);
}

.rating-text {
  font-size: 14px;
  color: #666;
}

/* 评价内容 */
.comment-section {
  margin-bottom: 24px;
}

.textarea-wrapper {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}

.comment-textarea {
  width: 100%;
  height: 120px;
  font-size: 14px;
  line-height: 1.5;
}

.textarea-counter {
  position: absolute;
  right: 12px;
  bottom: 12px;
  font-size: 12px;
  color: #999;
}

/* 图片上传 */
.upload-section {
  margin-bottom: 24px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-button {
  width: 80px;
  height: 80px;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-bottom: 8px;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.delete-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 匿名评价 */
.anonymous-section {
  margin-bottom: 24px;
}

.anonymous-switch {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.anonymous-text {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}

.anonymous-tip {
  font-size: 12px;
  color: #999;
}

/* 提交按钮 */
.submit-btn-wrapper {
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.submit-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  background-color: #FF8C00; /* 主品牌色，橙色 */
  color: #fff;
  border-radius: 8px;
  text-align: center; /* 确保文字居中 */
  display: flex;
  align-items: center;
  justify-content: center; /* 确保文字水平居中 */
}
