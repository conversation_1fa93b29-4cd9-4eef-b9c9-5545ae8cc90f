<!--payment.wxml-->
<view class="container">
  <!-- 固定导航栏 -->
  <view class="fixed-nav">
    <view class="payment-header">
      <view class="payment-title">缴费确认</view>
      <view class="header-actions" bindtap="showMoreMenu">
        <view class="iconfont icon-more"></view>
      </view>
    </view>
  </view>

  <!-- 内容容器 -->
  <view class="content-container">

  <!-- 费用摘要卡片 -->
  <view class="fee-summary-card">
    <view class="total-amount">
      <view class="total-amount-label">待缴费用总计</view>
      <view class="total-amount-value">¥1,258.50</view>
    </view>
    <view class="fee-tags">
      <view class="fee-tag">物业费</view>
      <view class="fee-tag">停车费</view>
      <view class="fee-tag">水电费</view>
    </view>
    <view class="due-date {{daysRemaining <= 3 ? 'urgent' : ''}}">
      <view class="due-date-badge" wx:if="{{daysRemaining <= 3}}">临近截止</view>
      缴费期限：2023年12月31日（剩余{{daysRemaining}}天）
    </view>
    <view class="data-source">数据来自物业管理系统 · 最近同步：{{lastSyncTime}}</view>
    <view class="analysis-entry" bindtap="navigateToPaymentAnalysis">
      <view class="analysis-icon"></view>
      <text>查看缴费分析</text>
      <view class="arrow-icon"></view>
    </view>
  </view>

  <!-- 缴费项目列表 -->
  <view class="fee-items-card">
    <view class="card-header">
      <view class="card-title">缴费项目</view>
      <view class="select-all">
        <checkbox checked="{{allSelected}}" bindtap="toggleSelectAll"></checkbox>
        <text>全选</text>
      </view>
    </view>
    <view class="fee-item" wx:for="{{feeItems}}" wx:key="id" bindtap="toggleSelectItem" data-id="{{item.id}}">
      <view class="fee-item-checkbox">
        <checkbox checked="{{item.selected}}"></checkbox>
      </view>
      <view class="fee-item-left">
        <view class="fee-item-info">
          <view class="fee-item-name">{{item.name}}</view>
          <view class="fee-item-period">{{item.period}}</view>
        </view>
      </view>
      <view class="fee-item-amount">{{item.amount}}</view>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-methods-card">
    <view class="card-header">
      <view class="card-title">支付方式</view>
      <view class="change-method" bindtap="togglePaymentMethodsExpand">
        <text>{{paymentMethodsExpanded ? '收起' : '更换'}}</text>
        <view class="iconfont {{paymentMethodsExpanded ? 'icon-up' : 'icon-down'}}"></view>
      </view>
    </view>

    <!-- 当前选中的支付方式 -->
    <view class="current-payment-method" wx:if="{{!paymentMethodsExpanded}}">
      <view class="payment-method-left">
        <view class="payment-method-icon {{currentPaymentMethod.iconClass}}"></view>
        <view class="payment-method-name">{{currentPaymentMethod.name}}</view>
      </view>
    </view>

    <!-- 展开的支付方式列表 -->
    <view class="payment-methods-list" wx:if="{{paymentMethodsExpanded}}">
      <view class="payment-method" wx:for="{{paymentMethods}}" wx:key="id" bindtap="selectPaymentMethod" data-method="{{item.id}}">
        <view class="payment-method-left">
          <view class="payment-method-icon {{item.iconClass}}"></view>
          <view class="payment-method-name">{{item.name}}</view>
        </view>
        <view class="payment-method-radio {{paymentMethod === item.id ? 'selected' : ''}}"></view>
      </view>
    </view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-info-card">
    <view class="card-title">缴费信息</view>
    <view class="info-row">
      <view class="info-label">业主姓名</view>
      <view class="info-value">{{userName || '未登录'}}</view>
    </view>
    <view class="info-row">
      <view class="info-label">房屋信息</view>
      <view class="info-value">{{selectedCommunity || '请先选择小区'}} {{houseInfo}}</view>
    </view>
    <view class="info-row">
      <view class="info-label">联系电话</view>
      <view class="info-value">{{userPhone || '未绑定'}}</view>
    </view>
  </view>

  <!-- 支付摘要 -->
  <view class="payment-summary">
    <view class="payment-summary-left">
      <view class="payment-summary-method">
        <view class="iconfont {{currentPaymentMethod.iconClass}}"></view>
        <text>{{currentPaymentMethod.name}}</text>
      </view>
      <view class="payment-summary-amount">合计：<text class="highlight">¥{{totalAmount}}</text></view>
    </view>
    <view class="payment-summary-right">
      <view class="payment-security-badges">
        <image class="security-badge" src="/images/icons/pci-dss.png" mode="aspectFit"></image>
        <image class="security-badge" src="/images/icons/wechat-pay.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 安全支付提示 -->
  <view class="security-tip">
    <view class="iconfont icon-security"></view>
    支付信息已加密，请放心支付
  </view>
  </view>

  <!-- 底部支付按钮 -->
  <view class="payment-button-container">
    <button class="payment-button" bindtap="confirmPayment" disabled="{{isProcessing || selectedItems.length === 0}}">
      {{isProcessing ? '支付处理中...' : '确认支付 ¥' + totalAmount}}
    </button>
  </view>
</view>

<!-- 支付确认弹窗 -->
<view class="payment-confirm-modal {{showConfirmModal ? 'show' : ''}}" bindtap="closeConfirmModal">
  <view class="payment-confirm-content" catchtap="stopPropagation">
    <view class="payment-confirm-title">确认支付</view>
    <view class="payment-confirm-desc">此费用将从{{currentPaymentMethod.name}}扣款，确定吗？</view>
    <view class="payment-confirm-amount">¥{{totalAmount}}</view>
    <view class="payment-confirm-footer">
      <button class="payment-confirm-btn cancel" bindtap="closeConfirmModal">取消</button>
      <button class="payment-confirm-btn confirm" bindtap="processPayment">确定</button>
    </view>
  </view>
</view>

<!-- 支付成功弹窗 -->
<view class="payment-result-modal {{showSuccessModal ? 'show' : ''}}" bindtap="closeSuccessModal">
  <view class="payment-result-content success-content" catchtap="stopPropagation">
    <view class="payment-result-icon success"></view>
    <view class="payment-result-title">支付成功</view>
    <view class="payment-result-desc">您已成功支付物业费用 <text class="highlight">¥{{totalAmount}}</text></view>

    <!-- 缴费明细 -->
    <view class="payment-details">
      <view class="payment-detail-item" wx:for="{{selectedItems}}" wx:key="id">
        <text>{{item.name}}</text>
        <text>{{item.amount}}</text>
      </view>
    </view>

    <view class="payment-result-actions">
      <button class="action-btn" bindtap="downloadInvoice">
        <view class="iconfont icon-download"></view>
        <text>查看电子发票</text>
      </button>
      <button class="action-btn" bindtap="shareInvoice">
        <view class="iconfont icon-share"></view>
        <text>分享</text>
      </button>
      <button class="action-btn" bindtap="viewPaymentDetail" data-id="1001">
        <view class="iconfont icon-detail"></view>
        <text>查看详情</text>
      </button>
    </view>

    <view class="payment-result-footer">
      <button class="payment-result-btn secondary" bindtap="navigateToPaymentHistory">缴费记录</button>
      <button class="payment-result-btn" bindtap="closeSuccessModal">完成</button>
    </view>
  </view>
</view>

<!-- 已缴费提示弹窗 -->
<view class="payment-result-modal {{showAlreadyPaidModal ? 'show' : ''}}" bindtap="closeAlreadyPaidModal">
  <view class="payment-result-content" catchtap="stopPropagation">
    <view class="payment-result-icon success"></view>
    <view class="payment-result-title">您的物业费已缴清</view>
    <view class="payment-result-desc">感谢您按时缴纳物业费，您是小区的榜样！</view>
    <view class="payment-history-info">
      <text class="payment-date">{{lastPaymentDate}}</text> 缴纳 <text class="highlight">¥1,258.50</text>
    </view>
    <view class="payment-result-footer">
      <button class="payment-result-btn secondary" bindtap="navigateToPaymentHistory">缴费记录</button>
      <button class="payment-result-btn" bindtap="closeAlreadyPaidModal">知道了</button>
    </view>
  </view>
</view>

<!-- 更多菜单弹窗 -->
<view class="more-menu-modal {{showMoreMenu ? 'show' : ''}}" bindtap="closeMoreMenu">
  <view class="more-menu-content" catchtap="stopPropagation">
    <view class="more-menu-item" bindtap="navigateToPaymentHistory">
      <view class="iconfont icon-history"></view>
      <text>缴费记录</text>
    </view>
    <view class="more-menu-item" bindtap="navigateToPaymentAnalysis">
      <view class="iconfont icon-analysis"></view>
      <text>缴费分析</text>
    </view>
    <view class="more-menu-item" bindtap="navigateToPaymentSettings">
      <view class="iconfont icon-settings"></view>
      <text>缴费设置</text>
    </view>
    <view class="more-menu-item" bindtap="toggleAutoPayment">
      <view class="iconfont {{autoPayment ? 'icon-check' : 'icon-uncheck'}}"></view>
      <text>下次自动扣款</text>
    </view>
    <view class="more-menu-item" bindtap="resetPaymentStatus">
      <view class="iconfont icon-reset"></view>
      <text>重置支付状态</text>
    </view>
  </view>
</view>
