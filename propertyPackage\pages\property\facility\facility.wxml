<!--设施管理主页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 顶部区域 -->
  <view class="header">
    <view class="title-area">
      <view class="page-title">设施管理</view>
      <view class="scan-icon" bindtap="scanCode">
        <view class="icon-scan"></view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrap">
        <view class="search-icon"></view>
        <input class="search-input" type="text" placeholder="搜索设施名称、编号、位置" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" />
        <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchValue}}"></view>
      </view>
    </view>
  </view>

  <!-- 分类标签栏 -->
  <scroll-view class="category-tabs" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view
      class="category-tab {{item.active ? 'active' : ''}}"
      wx:for="{{categories}}"
      wx:key="id"
      bindtap="switchCategory"
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 快捷统计/入口 -->
  <view class="quick-stats" animation="{{animationData}}">
    <view class="stat-item" bindtap="navigateToPendingRepairs">
      <view class="stat-value">{{statistics.pendingRepairs}}</view>
      <view class="stat-label">待处理报修</view>
    </view>
    <view class="stat-item" bindtap="navigateToPendingMaintenance">
      <view class="stat-value">{{statistics.pendingMaintenance}}</view>
      <view class="stat-label">待执行保养</view>
    </view>
    <view class="stat-item" bindtap="navigateToTodayInspection">
      <view class="stat-value">{{statistics.todayInspection}}</view>
      <view class="stat-label">今日巡检任务</view>
    </view>
  </view>

  <!-- 设施列表 -->
  <view class="facility-list" wx:if="{{!isLoading && facilities.length > 0}}">
    <view
      class="facility-card"
      wx:for="{{facilities}}"
      wx:key="id"
      bindtap="navigateToDetail"
      data-id="{{item.id}}"
    >
      <view class="facility-icon {{item.category}}-icon" lazy-load="true"></view>
      <view class="facility-info">
        <view class="facility-header">
          <view class="facility-name">{{item.name}}</view>
          <view class="facility-status {{item.status}}">{{item.statusText}}</view>
        </view>
        <view class="facility-code">编号: {{item.code}}</view>
        <view class="facility-location">
          <view class="location-icon"></view>
          <text>{{item.location}}</text>
        </view>
        <view class="facility-maintenance" wx:if="{{item.nextMaintenance}}">
          <view class="maintenance-icon"></view>
          <text>下次保养: {{item.nextMaintenance}}</text>
        </view>
      </view>
      <view class="facility-actions">
        <view class="action-btn repair-btn" catchtap="navigateToRepair" data-id="{{item.id}}">报修</view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading-more" wx:if="{{isLoadingMore}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view class="no-more-data" wx:if="{{!hasMoreData && facilities.length > 0}}">
      <text>没有更多数据了</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <empty-state
    wx:if="{{!isLoading && facilities.length === 0}}"
    icon="empty-search"
    title="未找到设施"
    description="尝试更换筛选条件或清除搜索"
  />

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <view class="action-button add-button" bindtap="navigateToAdd" wx:if="{{canAddFacility}}">
      <view class="button-icon add-icon"></view>
      <text>添加设施</text>
    </view>
    <view class="action-button task-button" bindtap="navigateToMyTasks">
      <view class="button-icon task-icon"></view>
      <text>我的任务</text>
    </view>
  </view>
</view>
