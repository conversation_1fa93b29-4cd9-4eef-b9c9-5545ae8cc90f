/* points.wxss */
.container {
  padding: 30rpx;
  padding-bottom: 200rpx;
}

.points-header {
  margin-bottom: 40rpx;
}

.points-card {
  background: linear-gradient(to right, #ff8c00, #ff6b00);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
}

.points-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.points-value {
  font-size: 64rpx;
  font-weight: 700;
  margin-bottom: 40rpx;
}

.points-actions {
  display: flex;
  justify-content: space-between;
}

.points-action {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
}

.mall-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: baseline;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 16rpx;
}

.section-more {
  font-size: 28rpx;
  color: #FF8C00;
  display: flex;
  align-items: center;
}

.section-more text {
  margin-right: 4rpx;
}

.category-tabs {
  display: flex;
  margin-bottom: 30rpx;
  background: #f5f5f5;
  border-radius: 40rpx;
  padding: 8rpx;
}

.category-tab {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border-radius: 32rpx;
}

.category-tab.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.product-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.product-image {
  width: 100%;
  height: 240rpx;
  background-color: #f5f5f5;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-points {
  font-size: 24rpx;
  color: #ff8c00;
  font-weight: 500;
}

/* 商品详情弹窗 */
.product-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.product-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.product-detail-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.product-detail-modal.show .product-detail-content {
  transform: scale(1);
}

.product-detail-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  z-index: 10;
}

.product-detail-image {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
}

.product-detail-info {
  padding: 30rpx;
}

.product-detail-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.product-detail-points {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
  margin-bottom: 24rpx;
}

.product-detail-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.product-detail-footer {
  padding: 0 30rpx 30rpx;
}

.product-detail-btn {
  width: 100%;
  height: 80rpx;
  background: #ff8c00;
  color: white;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-detail-btn.disabled {
  background: #ccc;
  color: #666;
}
