// pages/payment/invoice/invoice.js
const util = require('@/utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,

    // 发票数据
    invoiceId: '',
    invoiceDetail: null,

    // 画布相关
    canvasWidth: 750,
    canvasHeight: 1000,
    pixelRatio: 2,
    canvasContext: null,

    // 分享选项是否显示
    showShareOptions: false,

    // 保存图片状态
    isSaving: false,
    saveSuccess: false
  },

  onLoad: function (options) {
    // 获取发票ID
    if (options.id) {
      this.setData({
        invoiceId: options.id
      })
      this.loadInvoiceDetail(options.id)
    } else {
      wx.showToast({
        title: '发票ID无效',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onReady: function () {
    // 获取画布上下文
    const query = wx.createSelectorQuery()
    query.select('#invoiceCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')

          // 设置画布大小
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvas.width = this.data.canvasWidth * dpr
          canvas.height = this.data.canvasHeight * dpr
          ctx.scale(dpr, dpr)

          this.setData({
            canvasContext: ctx,
            pixelRatio: dpr
          })

          // 如果发票数据已加载，则绘制发票
          if (this.data.invoiceDetail) {
            this.drawInvoice()
          }
        }
      })
  },



  // 加载发票详情
  loadInvoiceDetail: function (id) {
    this.setData({
      isLoading: true
    })

    // 模拟加载数据
    setTimeout(() => {
      // 根据ID获取不同的发票数据
      let detail = this.getMockInvoiceDetail(id)

      this.setData({
        invoiceDetail: detail,
        isLoading: false
      })

      // 如果画布上下文已准备好，则绘制发票
      if (this.data.canvasContext) {
        this.drawInvoice()
      }
    }, 1000)
  },

  // 获取模拟的发票详情数据
  getMockInvoiceDetail: function (id) {
    // 基础数据
    const details = {
      // 物业费发票
      '1001': {
        id: '1001',
        title: '物业管理费',
        invoiceCode: '************',
        invoiceNo: '********',
        date: '2023-12-26',
        amount: '720.00',
        taxAmount: '38.30',
        totalAmount: '758.30',
        buyer: {
          name: '张先生',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX'
        },
        seller: {
          name: '星河湾物业管理有限公司',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX',
          bank: '中国工商银行上海XX支行',
          account: '1001XXXXXXXX'
        },
        items: [
          {
            name: '物业管理服务',
            spec: '基础物业服务',
            unit: '月',
            quantity: '3',
            unitPrice: '240.00',
            amount: '720.00',
            taxRate: '6%',
            taxAmount: '38.30'
          }
        ],
        remark: '2023年10月-12月物业费',
        payee: '王小明',
        checker: '李小红',
        drawer: '系统'
      },

      // 停车费发票
      '1002': {
        id: '1002',
        title: '停车费',
        invoiceCode: '************',
        invoiceNo: '********',
        date: '2023-12-26',
        amount: '450.00',
        taxAmount: '23.94',
        totalAmount: '473.94',
        buyer: {
          name: '张先生',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX'
        },
        seller: {
          name: '星河湾物业管理有限公司',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX',
          bank: '中国工商银行上海XX支行',
          account: '1001XXXXXXXX'
        },
        items: [
          {
            name: '停车服务',
            spec: '地下停车位租赁',
            unit: '月',
            quantity: '3',
            unitPrice: '150.00',
            amount: '450.00',
            taxRate: '6%',
            taxAmount: '23.94'
          }
        ],
        remark: '2023年第四季度停车费',
        payee: '王小明',
        checker: '李小红',
        drawer: '系统'
      },

      // 水电费发票
      '1003': {
        id: '1003',
        title: '水电费',
        invoiceCode: '************',
        invoiceNo: '********',
        date: '2023-12-26',
        amount: '88.50',
        taxAmount: '4.71',
        totalAmount: '93.21',
        buyer: {
          name: '张先生',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX'
        },
        seller: {
          name: '星河湾物业管理有限公司',
          taxId: '91310000XXXXXXXX',
          address: '上海市浦东新区XX路XX号',
          phone: '021-XXXXXXXX',
          bank: '中国工商银行上海XX支行',
          account: '1001XXXXXXXX'
        },
        items: [
          {
            name: '水费',
            spec: '居民用水',
            unit: '吨',
            quantity: '8',
            unitPrice: '3.50',
            amount: '28.00',
            taxRate: '6%',
            taxAmount: '1.49'
          },
          {
            name: '电费',
            spec: '居民用电',
            unit: '度',
            quantity: '110',
            unitPrice: '0.55',
            amount: '60.50',
            taxRate: '6%',
            taxAmount: '3.22'
          }
        ],
        remark: '2023年11月水电费',
        payee: '王小明',
        checker: '李小红',
        drawer: '系统'
      }
    }

    // 返回对应ID的详情，如果没有则返回默认数据
    return details[id] || details['1001']
  },

  // 绘制发票
  drawInvoice: function () {
    const ctx = this.data.canvasContext
    const invoice = this.data.invoiceDetail

    if (!ctx || !invoice) return

    // 清空画布
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)

    // 绘制背景
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)

    // 绘制发票标题
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 36px sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText('电子发票（普通发票）', this.data.canvasWidth / 2, 60)

    // 绘制发票代码和号码
    ctx.font = '24px sans-serif'
    ctx.textAlign = 'left'
    ctx.fillText(`发票代码：${invoice.invoiceCode}`, 50, 100)
    ctx.fillText(`发票号码：${invoice.invoiceNo}`, 400, 100)

    // 绘制日期
    ctx.fillText(`开票日期：${invoice.date}`, 50, 140)

    // 绘制分隔线
    ctx.strokeStyle = '#dddddd'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(50, 160)
    ctx.lineTo(700, 160)
    ctx.stroke()

    // 绘制购买方信息
    ctx.fillText('购买方', 50, 190)
    ctx.font = '22px sans-serif'
    ctx.fillText(`名称：${invoice.buyer.name}`, 50, 220)
    ctx.fillText(`纳税人识别号：${invoice.buyer.taxId}`, 50, 250)
    ctx.fillText(`地址、电话：${invoice.buyer.address} ${invoice.buyer.phone}`, 50, 280)

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, 300)
    ctx.lineTo(700, 300)
    ctx.stroke()

    // 绘制销售方信息
    ctx.font = '24px sans-serif'
    ctx.fillText('销售方', 50, 330)
    ctx.font = '22px sans-serif'
    ctx.fillText(`名称：${invoice.seller.name}`, 50, 360)
    ctx.fillText(`纳税人识别号：${invoice.seller.taxId}`, 50, 390)
    ctx.fillText(`地址、电话：${invoice.seller.address} ${invoice.seller.phone}`, 50, 420)
    ctx.fillText(`开户行及账号：${invoice.seller.bank} ${invoice.seller.account}`, 50, 450)

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, 470)
    ctx.lineTo(700, 470)
    ctx.stroke()

    // 绘制商品信息表头
    ctx.font = '22px sans-serif'
    ctx.fillText('货物或应税劳务、服务名称', 50, 500)
    ctx.fillText('规格型号', 280, 500)
    ctx.fillText('单位', 380, 500)
    ctx.fillText('数量', 430, 500)
    ctx.fillText('单价', 480, 500)
    ctx.fillText('金额', 550, 500)
    ctx.fillText('税率', 620, 500)
    ctx.fillText('税额', 670, 500)

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, 510)
    ctx.lineTo(700, 510)
    ctx.stroke()

    // 绘制商品信息
    let y = 540
    invoice.items.forEach(item => {
      ctx.fillText(item.name, 50, y)
      ctx.fillText(item.spec, 280, y)
      ctx.fillText(item.unit, 380, y)
      ctx.fillText(item.quantity, 430, y)
      ctx.fillText(item.unitPrice, 480, y)
      ctx.fillText(item.amount, 550, y)
      ctx.fillText(item.taxRate, 620, y)
      ctx.fillText(item.taxAmount, 670, y)
      y += 40
    })

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, y)
    ctx.lineTo(700, y)
    ctx.stroke()

    // 绘制合计
    y += 40
    ctx.font = 'bold 24px sans-serif'
    ctx.fillText('合计', 50, y)
    ctx.fillText(`¥${invoice.amount}`, 550, y)
    ctx.fillText(`¥${invoice.taxAmount}`, 670, y)

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, y + 20)
    ctx.lineTo(700, y + 20)
    ctx.stroke()

    // 绘制价税合计
    y += 60
    ctx.fillText(`价税合计：¥${invoice.totalAmount}（人民币 ${this.convertNumberToChinese(invoice.totalAmount)}）`, 50, y)

    // 绘制备注
    y += 40
    ctx.font = '22px sans-serif'
    ctx.fillText(`备注：${invoice.remark}`, 50, y)

    // 绘制分隔线
    ctx.beginPath()
    ctx.moveTo(50, y + 20)
    ctx.lineTo(700, y + 20)
    ctx.stroke()

    // 绘制开票信息
    y += 60
    ctx.fillText(`收款人：${invoice.payee}`, 50, y)
    ctx.fillText(`复核：${invoice.checker}`, 300, y)
    ctx.fillText(`开票人：${invoice.drawer}`, 550, y)

    // 绘制二维码（模拟）
    ctx.strokeStyle = '#333333'
    ctx.strokeRect(600, 800, 100, 100)
    ctx.font = '16px sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText('扫码验真', 650, 920)

    // 绘制完成
    wx.hideLoading()
  },

  // 数字转中文大写
  convertNumberToChinese: function (num) {
    const chineseNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    const chineseUnits = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
    const chineseMoney = ['元', '角', '分']

    // 处理小数点
    let [integer, decimal] = num.toString().split('.')
    if (!decimal) {
      decimal = '00'
    } else if (decimal.length === 1) {
      decimal = decimal + '0'
    }

    // 处理整数部分
    let chineseStr = ''
    const integerLength = integer.length
    for (let i = 0; i < integerLength; i++) {
      const digit = parseInt(integer[i])
      const unit = chineseUnits[integerLength - 1 - i]
      if (digit !== 0) {
        chineseStr += chineseNums[digit] + unit
      } else {
        if (i === integerLength - 1 || integer[i + 1] !== '0') {
          chineseStr += chineseNums[digit]
        }
      }
    }

    // 添加"元"
    chineseStr += chineseMoney[0]

    // 处理小数部分
    if (decimal[0] !== '0') {
      chineseStr += chineseNums[parseInt(decimal[0])] + chineseMoney[1]
    }
    if (decimal[1] !== '0') {
      chineseStr += chineseNums[parseInt(decimal[1])] + chineseMoney[2]
    }

    // 如果没有小数部分，添加"整"
    if (decimal === '00') {
      chineseStr += '整'
    }

    return chineseStr
  },

  // 保存发票到相册
  saveInvoiceToAlbum: function () {
    this.setData({
      isSaving: true
    })

    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    // 将画布转换为临时文件路径
    const query = wx.createSelectorQuery()
    query.select('#invoiceCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node

          wx.canvasToTempFilePath({
            canvas: canvas,
            success: (res) => {
              // 保存图片到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  wx.hideLoading()
                  this.setData({
                    isSaving: false,
                    saveSuccess: true
                  })
                  wx.showToast({
                    title: '保存成功',
                    icon: 'success'
                  })
                },
                fail: (err) => {
                  wx.hideLoading()
                  this.setData({
                    isSaving: false
                  })

                  // 如果是用户拒绝授权导致的失败
                  if (err.errMsg.indexOf('auth deny') >= 0) {
                    wx.showModal({
                      title: '提示',
                      content: '需要您授权保存图片到相册',
                      confirmText: '去授权',
                      success: (res) => {
                        if (res.confirm) {
                          wx.openSetting()
                        }
                      }
                    })
                  } else {
                    wx.showToast({
                      title: '保存失败',
                      icon: 'none'
                    })
                  }
                }
              })
            },
            fail: () => {
              wx.hideLoading()
              this.setData({
                isSaving: false
              })
              wx.showToast({
                title: '生成图片失败',
                icon: 'none'
              })
            }
          }, this)
        }
      })
  },

  // 显示分享选项
  showShareOptions: function () {
    this.setData({
      showShareOptions: true
    })
  },

  // 隐藏分享选项
  hideShareOptions: function () {
    this.setData({
      showShareOptions: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    return
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  },

  // 分享给好友
  onShareAppMessage: function () {
    const invoice = this.data.invoiceDetail
    return {
      title: `${invoice.title}电子发票`,
      path: `/pages/payment/invoice/invoice?id=${invoice.id}`,
      imageUrl: '/images/share-invoice.png' // 默认分享图片
    }
  }
})
