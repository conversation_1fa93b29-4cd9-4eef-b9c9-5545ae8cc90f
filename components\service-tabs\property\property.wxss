/* 物业服务组件样式 */
.property-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: #f9fafb;
  padding: 16px;
  padding-bottom: 70px; /* 为底部选项卡预留空间 */
}

/* 信息卡片样式 */
.info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #ff8c00;
  border-radius: 2px;
}

/* 联系方式列表样式 */
.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
}

.contact-phone {
  font-size: 14px;
  color: #666;
}

.contact-action {
  margin-left: 16px;
}

.call-btn {
  width: 36px;
  height: 36px;
  background-color: #ff8c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-icon {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1);
}

/* 物业信息样式 */
.property-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.info-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* 在线服务按钮样式 */
.service-buttons {
  display: flex;
  justify-content: space-around;
}

.service-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 16px;
  border-radius: 8px;
  width: 45%;
  box-shadow: none;
  border: 1px solid #f0f0f0;
}

.service-btn::after {
  border: none;
}

.btn-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

/* 常见问题样式 */
.faq-list {
  display: flex;
  flex-direction: column;
}

.faq-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  line-height: 1.5;
  display: none;
}

.faq-answer.expanded {
  display: block;
}

/* 留言表单弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close image {
  width: 16px;
  height: 16px;
}

.modal-content {
  padding: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.picker {
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #333;
}

.picker-arrow {
  width: 16px;
  height: 16px;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #333;
}

.form-input {
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 16px;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.submit-btn {
  background-color: #ff8c00;
  color: #fff;
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #fff;
}

/* 底部安全区域 */
.safe-bottom {
  height: 20px;
}
