// my-activities.js
const app = getApp()

Page({
  data: {
    enrolledActivities: [], // 用户已报名的活动列表
    showCancelModal: false, // 是否显示取消报名确认弹窗
    currentActivity: {}, // 当前操作的活动
    isLoading: true // 是否正在加载数据
  },

  onLoad: function() {

    // 加载用户已报名的活动数据
    this.loadEnrolledActivities()
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.loadEnrolledActivities()
  },

  onPullDownRefresh: function() {
    // 下拉刷新
    this.loadEnrolledActivities(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载用户已报名的活动数据
  loadEnrolledActivities: function(callback) {
    this.setData({ isLoading: true })

    // 获取用户信息
    const userInfo = app.globalData.userInfo

    // 从本地存储获取已报名活动ID
    const enrolledIds = wx.getStorageSync('userEnrolledEvents') || []

    // 如果没有报名活动，直接返回空数组
    if (!enrolledIds.length) {
      this.setData({
        enrolledActivities: [],
        isLoading: false
      })
      if (callback) callback()
      return
    }

    // 模拟从服务器获取活动数据
    // 实际项目中应该调用API获取数据
    setTimeout(() => {
      // 模拟活动数据
      const allActivities = [
        {
          id: 1,
          title: '中秋团圆晚会',
          description: '共度中秋佳节，邻里同欢。活动包括猜灯谜、品月饼、文艺表演等多种形式，欢迎社区居民踊跃参与。',
          date: '2023-09-29',
          time: '19:00-21:00',
          location: '小区中央广场',
          status: 'upcoming',
          statusText: '即将开始',
          image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 2,
          title: '社区健康讲座',
          description: '邀请三甲医院专家主讲，内容包括常见疾病预防和健康生活方式，适合所有年龄段居民参加。',
          date: '2023-10-05',
          time: '14:00-16:00',
          location: '社区活动中心',
          status: 'upcoming',
          statusText: '即将开始',
          image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 3,
          title: '亲子运动会',
          description: '为增进亲子关系，特举办此次运动会。活动包括亲子接力、趣味游戏等多个环节，适合3-12岁儿童及其家长参加。',
          date: '2023-10-12',
          time: '09:00-12:00',
          location: '小区运动场',
          status: 'upcoming',
          statusText: '即将开始',
          image: 'https://images.unsplash.com/photo-1527529482837-4698179dc6ce?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 4,
          title: '社区读书会',
          description: '每月一次的读书分享活动，本月主题为经典文学作品赏析，欢迎文学爱好者参加。',
          date: '2023-10-08',
          time: '19:30-21:30',
          location: '小区图书室',
          status: 'upcoming',
          statusText: '即将开始',
          image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 5,
          title: '垃圾分类宣传活动',
          description: '通过互动游戏和实践演示，帮助居民了解垃圾分类知识，提高环保意识。',
          date: '2023-09-20',
          time: '10:00-11:30',
          location: '小区垃圾分类站',
          status: 'ongoing',
          statusText: '进行中',
          image: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 6,
          title: '社区义务植树',
          description: '为美化社区环境，组织居民参与植树活动，共同建设绿色家园。',
          date: '2023-09-15',
          time: '09:00-11:00',
          location: '小区公园',
          status: 'ended',
          statusText: '已结束',
          image: 'https://images.unsplash.com/photo-1503785640985-f62e3aeee448?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        },
        {
          id: 7,
          title: '老年人健康体检',
          description: '为社区60岁以上老年人提供免费体检服务，包括血压、血糖测量和常见疾病筛查。',
          date: '2023-09-25',
          time: '08:00-12:00',
          location: '社区卫生服务中心',
          status: 'ongoing',
          statusText: '进行中',
          image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        }
      ]

      // 筛选出用户已报名的活动
      // 使用 some 方法进行比较，避免类型不匹配问题
      const enrolledActivities = allActivities.filter(activity =>
        enrolledIds.some(id => id == activity.id)
      )

      this.setData({
        enrolledActivities,
        isLoading: false
      })

      if (callback) callback()
    }, 500)
  },

  // 导航到活动详情页
  navigateToActivityDetail: function(e) {
    const activity = e.currentTarget.dataset.activity
    wx.navigateTo({
      url: `/pages/community/event-detail/event-detail?id=${activity.id}`
    })
  },

  // 显示取消报名确认弹窗
  showCancelConfirm: function(e) {
    // 注意：使用catchtap已经阻止了事件冒泡，不需要再调用e.stopPropagation()

    const id = e.currentTarget.dataset.id
    // 使用双等号而不是三等号，以避免类型不匹配问题
    const activity = this.data.enrolledActivities.find(item => item.id == id)

    if (activity) {
      this.setData({
        currentActivity: activity,
        showCancelModal: true
      })
    } else {
      console.error('未找到活动ID:', id, '当前活动列表:', this.data.enrolledActivities)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  // 隐藏取消报名确认弹窗
  hideCancelModal: function() {
    this.setData({
      showCancelModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡
  },

  // 取消报名
  cancelEnrollment: function() {
    const activityId = this.data.currentActivity.id

    // 获取已报名活动ID列表
    let enrolledIds = wx.getStorageSync('userEnrolledEvents') || []

    // 从列表中移除当前活动ID（使用双等号进行比较，避免类型不匹配问题）
    enrolledIds = enrolledIds.filter(id => id != activityId)

    // 更新本地存储
    wx.setStorageSync('userEnrolledEvents', enrolledIds)

    // 更新页面数据（使用双等号进行比较，避免类型不匹配问题）
    const enrolledActivities = this.data.enrolledActivities.filter(activity => activity.id != activityId)

    this.setData({
      enrolledActivities,
      showCancelModal: false
    })

    // 显示取消成功提示
    wx.showToast({
      title: '取消报名成功',
      icon: 'success'
    })
  },

  // 导航到社区活动页面
  navigateToCommunity: function() {
    // 使用reLaunch而不是navigateTo，避免导航栈问题
    wx.reLaunch({
      url: '/pages/community/community'
    })
  },

  // 不再需要自定义返回函数，使用系统导航栏的返回按钮
})
