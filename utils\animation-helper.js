/**
 * 动画工具类
 * 提供常用的动画效果
 */

/**
 * 创建淡入动画
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createFadeInAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.opacity(0).step({ duration: 0 });
  animation.opacity(1).step({ duration });
  
  return animation;
}

/**
 * 创建淡出动画
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createFadeOutAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.opacity(1).step({ duration: 0 });
  animation.opacity(0).step({ duration });
  
  return animation;
}

/**
 * 创建滑入动画（从右侧滑入）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideInRightAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateX('100%').step({ duration: 0 });
  animation.translateX(0).step({ duration });
  
  return animation;
}

/**
 * 创建滑出动画（向右侧滑出）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideOutRightAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateX(0).step({ duration: 0 });
  animation.translateX('100%').step({ duration });
  
  return animation;
}

/**
 * 创建滑入动画（从左侧滑入）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideInLeftAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateX('-100%').step({ duration: 0 });
  animation.translateX(0).step({ duration });
  
  return animation;
}

/**
 * 创建滑出动画（向左侧滑出）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideOutLeftAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateX(0).step({ duration: 0 });
  animation.translateX('-100%').step({ duration });
  
  return animation;
}

/**
 * 创建滑入动画（从底部滑入）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideInBottomAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateY('100%').step({ duration: 0 });
  animation.translateY(0).step({ duration });
  
  return animation;
}

/**
 * 创建滑出动画（向底部滑出）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createSlideOutBottomAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateY(0).step({ duration: 0 });
  animation.translateY('100%').step({ duration });
  
  return animation;
}

/**
 * 创建缩放动画（从小到大）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createScaleInAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.scale(0.5).opacity(0).step({ duration: 0 });
  animation.scale(1).opacity(1).step({ duration });
  
  return animation;
}

/**
 * 创建缩放动画（从大到小）
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createScaleOutAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.scale(1).opacity(1).step({ duration: 0 });
  animation.scale(0.5).opacity(0).step({ duration });
  
  return animation;
}

/**
 * 创建弹跳动画
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createBounceAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.scale(1).step({ duration: 0 });
  animation.scale(1.2).step({ duration: duration / 3 });
  animation.scale(0.9).step({ duration: duration / 3 });
  animation.scale(1).step({ duration: duration / 3 });
  
  return animation;
}

/**
 * 创建摇晃动画
 * @param {number} duration 动画持续时间（毫秒）
 * @returns {Object} 动画对象
 */
function createShakeAnimation(duration = 300) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'ease'
  });
  
  animation.translateX(0).step({ duration: 0 });
  animation.translateX(-10).step({ duration: duration / 5 });
  animation.translateX(10).step({ duration: duration / 5 });
  animation.translateX(-10).step({ duration: duration / 5 });
  animation.translateX(10).step({ duration: duration / 5 });
  animation.translateX(0).step({ duration: duration / 5 });
  
  return animation;
}

/**
 * 创建旋转动画
 * @param {number} duration 动画持续时间（毫秒）
 * @param {number} angle 旋转角度
 * @returns {Object} 动画对象
 */
function createRotateAnimation(duration = 300, angle = 360) {
  const animation = wx.createAnimation({
    duration,
    timingFunction: 'linear'
  });
  
  animation.rotate(0).step({ duration: 0 });
  animation.rotate(angle).step({ duration });
  
  return animation;
}

// 导出模块
module.exports = {
  createFadeInAnimation,
  createFadeOutAnimation,
  createSlideInRightAnimation,
  createSlideOutRightAnimation,
  createSlideInLeftAnimation,
  createSlideOutLeftAnimation,
  createSlideInBottomAnimation,
  createSlideOutBottomAnimation,
  createScaleInAnimation,
  createScaleOutAnimation,
  createBounceAnimation,
  createShakeAnimation,
  createRotateAnimation
};
