# WebSocket快速开始指南

## 概述

WebSocket管理器已经集成到小程序中，会在app.js启动时自动初始化。您只需要在页面中调用相应的方法即可使用WebSocket功能。

## 服务器配置

WebSocket服务器地址：`ws://**********:8080/websocket/` + access_token

## 基本使用

### 1. 发送私聊消息

```javascript
Page({
  sendMessage: function() {
    const app = getApp();
    
    // 发送文本消息
    app.sendPrivateMessage('text', 'Hello!', 'receiverId');
    
    // 发送图片消息
    app.sendPrivateMessage('image', '/path/to/image.jpg', 'receiverId');
  }
});
```

### 2. 查询消息历史

```javascript
Page({
  queryHistory: function() {
    const app = getApp();
    app.queryPrivateMessage('receiverId');
  }
});
```

### 3. 检查连接状态

```javascript
Page({
  checkConnection: function() {
    const app = getApp();
    
    if (app.isWebSocketConnected()) {
      console.log('WebSocket已连接');
    } else {
      console.log('WebSocket未连接');
    }
  }
});
```

### 4. 处理接收到的消息

在页面中添加`onWebSocketMessage`方法来处理接收到的消息：

```javascript
Page({
  // 处理WebSocket消息
  onWebSocketMessage: function(topic, data) {
    switch (topic) {
      case 'send_private_message':
        // 处理接收到的私聊消息
        console.log('收到新消息:', data);
        this.handleNewMessage(data);
        break;
        
      case 'query_private_message':
        // 处理消息历史查询结果
        console.log('消息历史:', data);
        this.handleMessageHistory(data);
        break;
    }
  },
  
  handleNewMessage: function(message) {
    // 处理新消息的逻辑
    wx.showToast({
      title: '收到新消息',
      icon: 'none'
    });
  },
  
  handleMessageHistory: function(messages) {
    // 处理消息历史的逻辑
    console.log('查询到', messages.length, '条消息');
  }
});
```

## 消息格式

### 发送消息格式
```javascript
{
  "topic": "send_private_message",
  "data": {
    "type": "text",           // 消息类型：text 或 image
    "content": "Hello!",      // 消息内容或图片路径
    "senderId": "1000001",    // 发送者ID（自动获取）
    "receiverId": "100000"    // 接收者ID
  }
}
```

### 查询消息格式
```javascript
{
  "topic": "query_private_message",
  "data": {
    "senderId": "1000001",    // 发送者ID（自动获取）
    "receiverId": "100000"    // 对方用户ID
  }
}
```

## 测试页面

项目包含了一个测试页面 `pages/websocket-test/`，您可以：

1. 在app.json中添加页面路径：
```json
{
  "pages": [
    "pages/websocket-test/websocket-test"
  ]
}
```

2. 访问测试页面进行功能验证

## 常见问题

### Q: WebSocket连接失败怎么办？
A: 检查以下几点：
- 确保在微信小程序后台配置了WebSocket域名：`ws://**********:8080`
- 确保用户已登录（有access_token）
- 检查网络连接

### Q: 消息发送失败怎么办？
A: 检查以下几点：
- 确保WebSocket连接正常
- 确保消息格式正确
- 检查receiverId是否有效

### Q: 如何修改WebSocket服务器地址？
A: 修改 `utils/websocket-config.js` 文件中的 `server.baseUrl` 配置

### Q: 如何查看连接状态和统计信息？
A: 使用以下代码：
```javascript
const app = getApp();
const wsManager = app.getWebSocketManager();
const stats = wsManager.getStats();
console.log('连接统计:', stats);
```

## 注意事项

1. **自动初始化**：WebSocket会在小程序启动时自动初始化，无需手动调用
2. **登录依赖**：WebSocket需要access_token，会等待登录完成后自动连接
3. **断线重连**：连接断开时会自动重连，最多重连5次
4. **消息队列**：连接断开时消息会缓存，连接恢复后自动发送
5. **页面切换**：WebSocket连接在全局维护，页面切换时保持连接

## 高级用法

如果需要更高级的功能，可以直接使用WebSocket管理器：

```javascript
const app = getApp();
const wsManager = app.getWebSocketManager();

// 监听连接事件
wsManager.on('open', () => {
  console.log('连接已建立');
});

wsManager.on('close', () => {
  console.log('连接已关闭');
});

// 发送自定义消息
wsManager.sendMessage({
  topic: 'custom_topic',
  data: { custom: 'data' }
});
```

这样就可以开始使用WebSocket功能了！
