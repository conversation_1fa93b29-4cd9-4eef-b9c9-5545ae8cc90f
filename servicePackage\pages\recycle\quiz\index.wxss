/* 每日5题闯关页面样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 未开始状态样式 */
.start-container {
  margin-bottom: 40rpx;
}

.start-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.start-icon {
  width: 120rpx;
  height: 120rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='120' height='120' fill='none' stroke='%23ff9800' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 30rpx;
}

.start-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.start-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.start-info {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.reward-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
}

.info-text {
  font-size: 26rpx;
  color: #666;
}

.start-btn {
  width: 100%;
  height: 80rpx;
  background-color: #ff9800;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.3);
}

/* 历史记录样式 */
.history-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.history-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.history-score {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.history-reward {
  font-size: 26rpx;
  color: #ff9800;
}

.no-history {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}

.no-history-text {
  font-size: 28rpx;
  color: #999;
}

/* 答题状态样式 */
.quiz-container {
  margin-bottom: 40rpx;
}

.progress-bar {
  margin-bottom: 30rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.progress-track {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #ff9800;
  border-radius: 8rpx;
  transition: width 0.3s;
}

.question-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.question-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  transition: all 0.2s;
}

.option-letter {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
  margin-right: 20rpx;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.option-item.selected {
  background-color: rgba(255, 152, 0, 0.1);
  border: 2rpx solid #ff9800;
}

.option-item.selected .option-letter {
  background-color: #ff9800;
  color: white;
}

.option-item.correct {
  background-color: rgba(76, 175, 80, 0.1);
  border: 2rpx solid #4caf50;
}

.option-item.correct .option-letter {
  background-color: #4caf50;
  color: white;
}

.option-item.wrong {
  background-color: rgba(244, 67, 54, 0.1);
  border: 2rpx solid #f44336;
}

.option-item.wrong .option-letter {
  background-color: #f44336;
  color: white;
}

.explanation {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.explanation-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.explanation-title {
  color: #4caf50;
}

.explanation-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.quiz-controls {
  display: flex;
  justify-content: center;
}

.control-btn {
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.next-btn {
  background-color: #ff9800;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.3);
}

/* 完成状态样式 */
.result-container {
  margin-bottom: 40rpx;
}

.result-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 20rpx;
}

.result-icon.perfect {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='120' height='120' fill='none' stroke='%234caf50' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

.result-icon.good {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='120' height='120' fill='none' stroke='%23ff9800' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M8 14s1.5 2 4 2 4-2 4-2'%3E%3C/path%3E%3Cline x1='9' y1='9' x2='9.01' y2='9'%3E%3C/line%3E%3Cline x1='15' y1='9' x2='15.01' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.result-icon.normal {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='120' height='120' fill='none' stroke='%23f44336' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='8' y1='15' x2='16' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='9.01' y2='9'%3E%3C/line%3E%3Cline x1='15' y1='9' x2='15.01' y2='9'%3E%3C/line%3E%3C/svg%3E");
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.result-score {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff9800;
}

.result-reward {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.reward-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='40' height='40' fill='none' stroke='%23ff9800' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='8' r='7'%3E%3C/circle%3E%3Cpolyline points='8.21 13.89 7 23 12 20 17 23 15.79 13.88'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-right: 10rpx;
}

.reward-text {
  font-size: 28rpx;
  color: #ff9800;
  font-weight: 600;
}

.result-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 600;
}

.summary-value.correct {
  color: #4caf50;
}

.summary-value.wrong {
  color: #f44336;
}

.result-controls {
  display: flex;
  gap: 20rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.review-btn {
  background-color: #f5f5f5;
  color: #666;
}

.home-btn {
  background-color: #ff9800;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.3);
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .title {
  color: #f5f5f7;
}

.darkMode .subtitle {
  color: #8e8e93;
}

.darkMode .start-card,
.darkMode .history-section,
.darkMode .question-card,
.darkMode .explanation,
.darkMode .result-card {
  background-color: #2c2c2e;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.darkMode .start-title,
.darkMode .title-text,
.darkMode .history-date,
.darkMode .history-score,
.darkMode .question-text,
.darkMode .option-text,
.darkMode .result-title {
  color: #f5f5f7;
}

.darkMode .start-desc,
.darkMode .info-text,
.darkMode .progress-text,
.darkMode .explanation-text {
  color: #8e8e93;
}

.darkMode .progress-track {
  background-color: #3a3a3c;
}

.darkMode .option-item {
  background-color: #3a3a3c;
}

.darkMode .option-letter {
  background-color: #2c2c2e;
  color: #8e8e93;
}

.darkMode .history-item {
  border-bottom-color: #3a3a3c;
}

.darkMode .no-history-text {
  color: #8e8e93;
}

.darkMode .review-btn {
  background-color: #3a3a3c;
  color: #f5f5f7;
}
