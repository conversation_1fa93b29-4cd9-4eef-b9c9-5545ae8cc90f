// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#ff8c00",
    darkMode: false,
    // 总未读消息数量
    totalUnreadCount: 0,
    list: [{
      pagePath: "/pages/index/index",
      text: "首页",
      iconName: "home"
    }, {
      pagePath: "/pages/goods/goods",
      text: "好物",
      iconName: "goods"
    }, {
      pagePath: "/pages/points/points",
      text: "积分",
      iconName: "points"
    }, {
      pagePath: "/pages/messages/messages",
      text: "消息",
      iconName: "message"
    },
    {
      pagePath: "/pages/profile/profile",
      text: "我的",
      iconName: "profile"
    }]
  },
  attached() {
    // 检查当前暗黑模式状态
    const app = getApp();
    if (app && app.globalData) {
      this.setData({
        darkMode: app.globalData.darkMode || false
      });

      // 监听暗黑模式状态变化
      const that = this;
      Object.defineProperty(app.globalData, 'darkModeChangeEvent', {
        configurable: true,
        enumerable: true,
        set: function(value) {
          that.setData({
            darkMode: value.darkMode
          });
          this._darkModeChangeEvent = value;
        },
        get: function() {
          return this._darkModeChangeEvent;
        }
      });
    }
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      const index = data.index;

      // 先设置选中状态，再跳转，提供更好的视觉反馈
      this.setData({
        selected: index
      });

      wx.switchTab({
        url
      });
    }
  }
})
