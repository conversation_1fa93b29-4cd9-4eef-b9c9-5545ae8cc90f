// pages/recycle/appointment/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    darkMode: false,
    currentStep: 1, // 当前步骤，1-基本信息，2-废品信息，3-确认提交
    addressInputMode: 'map', // 地址输入模式：map-地图选择，manual-手动输入
    formData: {
      name: '',
      phone: '',
      address: '',
      addressDetail: '',
      appointmentTime: '',
      weight: 10,
      remark: '',
      photos: [],
      agreed: false
    },
    wasteTypes: [
      { id: 1, name: '纸类', icon: 'paper-icon', selected: false },
      { id: 2, name: '塑料', icon: 'plastic-icon', selected: false },
      { id: 3, name: '金属', icon: 'metal-icon', selected: false },
      { id: 4, name: '玻璃', icon: 'glass-icon', selected: false },
      { id: 5, name: '电子产品', icon: 'electronic-icon', selected: false },
      { id: 6, name: '家具', icon: 'furniture-icon', selected: false }
    ],
    dateTimeArray: [[], [], []],
    dateTimeIndex: [0, 0, 0],
    showSuccessModal: false,
    orderNumber: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息，判断是否为暗黑模式
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          darkMode: res.theme === 'dark'
        })
      }
    })

    // 初始化日期时间选择器数据
    this.initDateTimePicker()
  },

  /**
   * 初始化日期时间选择器
   */
  initDateTimePicker() {
    const date = new Date()
    const days = []
    const hours = []
    const minutes = []

    // 未来7天
    for (let i = 0; i < 7; i++) {
      const futureDate = new Date(date.getTime() + i * 24 * 60 * 60 * 1000)
      const month = futureDate.getMonth() + 1
      const day = futureDate.getDate()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][futureDate.getDay()]
      days.push(`${month}月${day}日 ${weekday}`)
    }

    // 小时，9:00-18:00
    for (let i = 9; i <= 18; i++) {
      hours.push(`${i}点`)
    }

    // 分钟，每30分钟
    minutes.push('00分', '30分')

    this.setData({
      dateTimeArray: [days, hours, minutes],
      dateTimeIndex: [0, 0, 0]
    })
  },

  /**
   * 日期时间选择器列变化事件
   */
  bindDateTimeColumnChange(e) {
    const column = e.detail.column
    const value = e.detail.value
    const dateTimeIndex = this.data.dateTimeIndex
    dateTimeIndex[column] = value
    this.setData({
      dateTimeIndex
    })
  },

  /**
   * 日期时间选择器值变化事件
   */
  bindDateTimeChange(e) {
    const dateTimeIndex = e.detail.value
    const dateTimeArray = this.data.dateTimeArray
    const appointmentTime = `${dateTimeArray[0][dateTimeIndex[0]]} ${dateTimeArray[1][dateTimeIndex[1]]}${dateTimeArray[2][dateTimeIndex[2]]}`

    this.setData({
      dateTimeIndex,
      'formData.appointmentTime': appointmentTime
    })
  },

  /**
   * 输入联系人姓名
   */
  inputName(e) {
    this.setData({
      'formData.name': e.detail.value
    })
  },

  /**
   * 输入联系电话
   */
  inputPhone(e) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  /**
   * 切换地址输入模式
   */
  switchAddressMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      addressInputMode: mode
    })
  },

  /**
   * 选择地址
   */
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'formData.address': res.address
        })
      },
      fail: (err) => {
        if (err.errMsg !== 'chooseLocation:fail cancel') {
          wx.showToast({
            title: '选择地址失败',
            icon: 'none'
          })
          // 自动切换到手动输入模式
          this.setData({
            addressInputMode: 'manual'
          })
        }
      }
    })
  },

  /**
   * 手动输入地址
   */
  inputAddress(e) {
    this.setData({
      'formData.address': e.detail.value
    })
  },

  /**
   * 输入详细地址
   */
  inputAddressDetail(e) {
    this.setData({
      'formData.addressDetail': e.detail.value
    })
  },

  /**
   * 选择废品类型
   */
  selectWasteType(e) {
    const id = e.currentTarget.dataset.id
    const wasteTypes = this.data.wasteTypes.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected }
      }
      return item
    })
    this.setData({
      wasteTypes
    })
  },

  /**
   * 重量滑块变化
   */
  weightSliderChange(e) {
    this.setData({
      'formData.weight': e.detail.value
    })
  },

  /**
   * 输入备注信息
   */
  inputRemark(e) {
    this.setData({
      'formData.remark': e.detail.value
    })
  },

  /**
   * 选择照片
   */
  chooseImage() {
    wx.chooseImage({
      count: 3 - this.data.formData.photos.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const photos = [...this.data.formData.photos, ...res.tempFilePaths]
        this.setData({
          'formData.photos': photos
        })
      }
    })
  },

  /**
   * 删除照片
   */
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index
    const photos = this.data.formData.photos.filter((_, i) => i !== index)
    this.setData({
      'formData.photos': photos
    })
  },

  /**
   * 协议勾选变化
   */
  agreementChange(e) {
    this.setData({
      'formData.agreed': e.detail.value.length > 0
    })
  },

  /**
   * 显示服务协议
   */
  showAgreement() {
    wx.showModal({
      title: '废品回收服务协议',
      content: '本协议是您与我们之间关于废品回收服务的约定。通过预约我们的废品回收服务，您同意遵守本协议的全部条款。\n\n1. 服务内容：我们提供上门回收废品的服务，包括但不限于纸类、塑料、金属、玻璃、电子产品等可回收物品。\n\n2. 服务时间：我们将在您预约的时间上门提供服务，如有特殊情况需要变更，我们会提前与您联系。\n\n3. 服务费用：根据回收物品的种类和重量，我们可能会支付相应的回收费用给您，具体金额以现场评估为准。\n\n4. 隐私保护：我们会妥善保管您的个人信息，不会泄露给无关第三方。',
      showCancel: false,
      confirmText: '我已阅读'
    })
  },

  /**
   * 下一步
   */
  nextStep() {
    if (this.data.currentStep === 1) {
      // 验证基本信息
      if (!this.validateStep1()) {
        return
      }
    } else if (this.data.currentStep === 2) {
      // 验证废品信息
      if (!this.validateStep2()) {
        return
      }
    }

    const nextStep = this.data.currentStep + 1
    this.setData({
      currentStep: nextStep
    })
  },

  /**
   * 上一步
   */
  prevStep() {
    const prevStep = this.data.currentStep - 1
    this.setData({
      currentStep: prevStep
    })
  },

  /**
   * 验证步骤1
   */
  validateStep1() {
    const { name, phone, address, addressDetail, appointmentTime } = this.data.formData

    if (!name) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      })
      return false
    }

    if (!phone) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      })
      return false
    }

    if (!/^1\d{10}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      })
      return false
    }

    if (!address) {
      wx.showToast({
        title: '请选择回收地址',
        icon: 'none'
      })
      return false
    }

    if (!addressDetail) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
      return false
    }

    if (!appointmentTime) {
      wx.showToast({
        title: '请选择预约时间',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 验证步骤2
   */
  validateStep2() {
    const selectedTypes = this.data.wasteTypes.filter(item => item.selected)

    if (selectedTypes.length === 0) {
      wx.showToast({
        title: '请选择至少一种废品类型',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 获取已选择的废品类型文本
   */
  get selectedWasteTypesText() {
    const selectedTypes = this.data.wasteTypes.filter(item => item.selected)
    return selectedTypes.map(item => item.name).join('、')
  },

  /**
   * 提交表单
   */
  submitForm() {
    if (!this.data.formData.agreed) {
      wx.showToast({
        title: '请阅读并同意服务协议',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '提交中...',
      mask: true
    })

    // 模拟提交
    setTimeout(() => {
      wx.hideLoading()

      // 生成订单号
      const orderNumber = 'WP' + new Date().getTime().toString().slice(-8)

      this.setData({
        showSuccessModal: true,
        orderNumber
      })
    }, 1500)
  },

  /**
   * 返回首页
   */
  backToHome() {
    // 直接跳转到小程序首页
    wx.switchTab({
      url: '/pages/index/index',
      fail: (err) => {
        console.error('返回首页失败:', err)
        // 如果switchTab失败，尝试使用reLaunch
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }
    })
  },

  /**
   * 查看我的预约
   */
  viewMyAppointments() {
    wx.navigateTo({
      url: '/pages/recycle/my-appointments/index'
    })
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  }
})