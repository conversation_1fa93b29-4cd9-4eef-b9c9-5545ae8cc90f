/**
 * 关怀版样式
 * 适合老年人使用的界面样式
 */

/* 全局字体大小 */
page {
  font-size: 18px !important;
}

/* 标题字体 */
.title, .header-title, .section-title, .card-title {
  font-size: 22px !important;
  font-weight: bold !important;
}

/* 按钮样式 */
button {
  font-size: 20px !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  border-radius: 12rpx !important;
}

/* 输入框样式 */
input {
  font-size: 18px !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
}

/* 列表项样式 */
.list-item, .settings-item, .menu-item {
  padding: 24rpx 32rpx !important;
  min-height: 96rpx !important;
}

/* 卡片样式 */
.card {
  border-radius: 16rpx !important;
  margin-bottom: 32rpx !important;
  padding: 32rpx !important;
}

/* 图标放大 */
.icon, .svg-icon, image {
  transform: scale(1.2) !important;
}

/* 增加对比度 */
text, view {
  color: #000000 !important;
}

/* 高亮重要信息 */
.important, .price, .status, .highlight {
  font-weight: bold !important;
  color: #ff6600 !important;
}

/* 增加点击区域 */
.clickable, .tappable, navigator, button {
  padding: 24rpx !important;
}

/* 简化界面，隐藏非必要元素 */
.simplified-hidden {
  display: none !important;
}

/* 增加间距 */
.container {
  padding: 32rpx !important;
}

/* 增加表单元素间距 */
form .form-item {
  margin-bottom: 32rpx !important;
}

/* 增加列表项间距 */
.list-item + .list-item,
.settings-item + .settings-item,
.menu-item + .menu-item {
  margin-top: 16rpx !important;
}

/* 增加按钮文字间距 */
button text {
  letter-spacing: 2rpx !important;
}

/* 增加标签对比度 */
.label, .tag {
  background-color: #f0f0f0 !important;
  color: #000000 !important;
  font-weight: bold !important;
  padding: 8rpx 16rpx !important;
  border-radius: 8rpx !important;
}

/* 增加开关大小 */
switch {
  transform: scale(1.3) !important;
}

/* 增加滑块大小 */
slider {
  height: 60rpx !important;
}

/* 增加单选框和复选框大小 */
radio, checkbox {
  transform: scale(1.3) !important;
}

/* 增加导航栏高度 */
.tab-bar {
  height: 120rpx !important;
}

/* 增加导航栏图标大小 */
.tab-bar-item image {
  width: 56rpx !important;
  height: 56rpx !important;
}

/* 增加导航栏文字大小 */
.tab-bar-item text {
  font-size: 28rpx !important;
  margin-top: 8rpx !important;
}
