# 批量邀请页面常用访客选择功能

## 功能概述

在批量邀请访客页面中，新增了"常用访客"选择功能，用户可以从常用访客列表中批量选择访客，快速添加到当前的访客列表中，提高批量邀请的效率。

## 功能特点

### 1. 快速选择
- 在添加访客按钮旁边增加"常用访客"按钮
- 点击后弹出常用访客选择弹窗
- 支持多选和全选功能

### 2. 智能去重
- 自动检测已存在的访客（根据手机号判断）
- 只添加不重复的访客到列表中
- 显示添加成功和重复访客的数量提示

### 3. 数据同步
- 使用与访客登记页面相同的常用访客数据源
- 调用visitorsApi.getVisitorList接口获取常用访客
- 支持实时数据更新

## 实现细节

### 1. 页面结构修改

#### WXML文件修改
```xml
<!-- 原来的单个添加访客按钮 -->
<view class="add-visitor-btn" bindtap="showAddVisitorModal">
  <image src="/images/icons/add.svg" class="add-visitor-icon" />
  <text>添加访客</text>
</view>

<!-- 修改后的双按钮布局 -->
<view class="add-visitor-buttons">
  <!-- 手动添加访客按钮 -->
  <view class="add-visitor-btn" bindtap="showAddVisitorModal">
    <image src="/images/icons/add.svg" class="add-visitor-icon" />
    <text>添加访客</text>
  </view>
  
  <!-- 选择常用访客按钮 -->
  <view class="add-visitor-btn frequent-visitor-btn" bindtap="showFrequentVisitorsModal">
    <image src="/images/icons/star-fill.svg" class="add-visitor-icon" />
    <text>常用访客</text>
  </view>
</view>
```

#### 常用访客选择弹窗
```xml
<!-- 常用访客选择弹窗 -->
<view class="visitor-picker-modal {{showFrequentVisitorsModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideFrequentVisitorsModal"></view>
  <view class="visitor-picker-content frequent-visitors-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择常用访客</text>
      <view class="visitor-picker-close" bindtap="hideFrequentVisitorsModal">取消</view>
    </view>
    <view class="visitor-picker-body">
      <!-- 全选/取消全选 -->
      <view class="frequent-visitors-select-all">
        <view class="frequent-visitors-checkbox {{selectAllFrequentVisitors ? 'checked' : ''}}" 
              bindtap="toggleSelectAllFrequentVisitors">
          <image src="/images/icons/check.svg" class="frequent-visitors-check-icon" wx:if="{{selectAllFrequentVisitors}}" />
        </view>
        <text class="frequent-visitors-select-all-text">全选</text>
        <text class="frequent-visitors-count">(已选{{selectedFrequentVisitors.length}}位)</text>
      </view>
      
      <!-- 常用访客列表 -->
      <view class="frequent-visitors-list">
        <view wx:for="{{frequentVisitors}}" wx:key="id"
              class="frequent-visitor-item {{selectedFrequentVisitors.indexOf(item.id) !== -1 ? 'selected' : ''}}"
              bindtap="toggleFrequentVisitor" data-id="{{item.id}}">
          <view class="frequent-visitor-checkbox {{selectedFrequentVisitors.indexOf(item.id) !== -1 ? 'checked' : ''}}">
            <image src="/images/icons/check.svg" class="frequent-visitor-check-icon" 
                   wx:if="{{selectedFrequentVisitors.indexOf(item.id) !== -1}}" />
          </view>
          <view class="frequent-visitor-avatar">{{item.visitorName[0]}}</view>
          <view class="frequent-visitor-info">
            <view class="frequent-visitor-name">{{item.visitorName}}</view>
            <view class="frequent-visitor-phone">{{item.phone}}</view>
            <view class="frequent-visitor-car" wx:if="{{item.vehicleNumber}}">
              <text>车牌: {{item.vehicleNumber}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-cancel-btn" bindtap="hideFrequentVisitorsModal">取消</button>
      <button class="visitor-picker-confirm-btn" bindtap="confirmSelectFrequentVisitors" 
              disabled="{{selectedFrequentVisitors.length === 0}}">
        确定({{selectedFrequentVisitors.length}})
      </button>
    </view>
  </view>
</view>
```

### 2. 数据结构

#### 新增数据字段
```javascript
data: {
  // 常用访客选择弹窗
  showFrequentVisitorsModal: false,
  frequentVisitors: [], // 常用访客列表
  selectedFrequentVisitors: [], // 已选择的常用访客ID数组
  selectAllFrequentVisitors: false, // 是否全选常用访客
}
```

### 3. 核心方法

#### 显示常用访客弹窗
```javascript
showFrequentVisitorsModal: function() {
  this.getFrequentVisitors(); // 获取常用访客列表
  this.setData({
    showFrequentVisitorsModal: true,
    selectedFrequentVisitors: [],
    selectAllFrequentVisitors: false
  });
}
```

#### 获取常用访客列表
```javascript
getFrequentVisitors: function() {
  const selectedCommunity = wx.getStorageSync('selectedCommunity') || {};
  const communityId = selectedCommunity.id;

  const params = {
    pageNum: 1,
    pageSize: 500,
    communityId: communityId,
    isUsual: true
  };

  visitorsApi.getVisitorList(params)
    .then(res => {
      if (res && res.list) {
        const frequentVisitors = res.list.map(visitor => ({
          id: visitor.id,
          visitorName: visitor.visitorName,
          phone: visitor.phone,
          vehicleNumber: visitor.vehicleNumber || '',
          note: visitor.note || ''
        }));

        this.setData({
          frequentVisitors: frequentVisitors
        });
      }
    })
    .catch(err => {
      console.error('获取常用访客失败：', err);
    });
}
```

#### 切换访客选择状态
```javascript
toggleFrequentVisitor: function(e) {
  const id = e.currentTarget.dataset.id;
  const selectedFrequentVisitors = [...this.data.selectedFrequentVisitors];
  const index = selectedFrequentVisitors.indexOf(id);

  if (index === -1) {
    selectedFrequentVisitors.push(id);
  } else {
    selectedFrequentVisitors.splice(index, 1);
  }

  const selectAllFrequentVisitors = selectedFrequentVisitors.length === this.data.frequentVisitors.length;

  this.setData({
    selectedFrequentVisitors: selectedFrequentVisitors,
    selectAllFrequentVisitors: selectAllFrequentVisitors
  });
}
```

#### 确认选择常用访客
```javascript
confirmSelectFrequentVisitors: function() {
  const { selectedFrequentVisitors, frequentVisitors, visitors } = this.data;
  
  // 获取选中的常用访客数据
  const selectedVisitors = frequentVisitors.filter(visitor => 
    selectedFrequentVisitors.includes(visitor.id)
  );

  // 转换为批量邀请页面的访客格式
  const newVisitors = selectedVisitors.map(visitor => ({
    name: visitor.visitorName,
    phone: visitor.phone,
    carNumber: visitor.vehicleNumber || ''
  }));

  // 检查是否有重复的访客（根据手机号判断）
  const existingPhones = visitors.map(v => v.phone);
  const uniqueNewVisitors = newVisitors.filter(visitor => 
    !existingPhones.includes(visitor.phone)
  );

  // 添加到访客列表
  const updatedVisitors = [...visitors, ...uniqueNewVisitors];

  this.setData({
    visitors: updatedVisitors,
    showFrequentVisitorsModal: false
  });

  // 显示成功提示
  const addedCount = uniqueNewVisitors.length;
  const duplicateCount = newVisitors.length - uniqueNewVisitors.length;
  
  let message = `已添加${addedCount}位访客`;
  if (duplicateCount > 0) {
    message += `，${duplicateCount}位访客已存在`;
  }

  wx.showToast({
    title: message,
    icon: 'success'
  });
}
```

## 使用流程

### 1. 进入批量邀请页面
- 用户从访客登记页面点击"批量邀请"进入批量邀请页面

### 2. 选择常用访客
- 点击"常用访客"按钮
- 弹出常用访客选择弹窗
- 显示当前用户的所有常用访客

### 3. 多选访客
- 点击访客项进行单选/取消选择
- 点击"全选"进行全选/取消全选
- 实时显示已选择的访客数量

### 4. 确认添加
- 点击"确定"按钮
- 系统自动去重，只添加不重复的访客
- 显示添加结果提示

### 5. 继续操作
- 添加的访客显示在访客列表中
- 可以继续手动添加其他访客
- 填写公共信息后提交批量邀请

## 注意事项

1. **数据去重**：系统会根据手机号自动去重，避免重复添加相同访客
2. **权限检查**：需要用户已登录且选择了社区
3. **网络异常**：获取常用访客失败时会显示空列表
4. **用户体验**：支持全选/取消全选，实时显示选择数量
5. **样式适配**：弹窗支持滚动，适配不同屏幕尺寸

## 相关文件

- `servicePackage/pages/visitor/batch-invite/index.js` - 主要逻辑实现
- `servicePackage/pages/visitor/batch-invite/index.wxml` - 页面结构
- `servicePackage/pages/visitor/batch-invite/index.wxss` - 样式定义
- `api/visitorsApi.js` - 访客API接口
- `servicePackage/pages/visitor/registration/index.js` - 参考的常用访客实现
