/* about.wxss */
.container {
  padding: 40rpx;
}

.about-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 28rpx;
  color: #999;
}

.about-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #ff8c00;
}

.section-content {
  padding: 0 10rpx;
}

.section-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.contact-item {
  display: flex;
  margin-bottom: 16rpx;
}

.contact-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 20rpx 0;
}

.rich-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 富文本内容样式 */
.rich-content :global(p) {
  margin-bottom: 16rpx;
}

.rich-content :global(img) {
  max-width: 100%;
  height: auto;
  margin: 16rpx 0;
}

.rich-content :global(a) {
  color: #ff8c00;
  text-decoration: none;
}
