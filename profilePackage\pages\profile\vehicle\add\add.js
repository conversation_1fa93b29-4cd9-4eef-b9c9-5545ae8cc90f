// 添加车辆页面逻辑
const app = getApp()
const vehicleApi = require('../../../../../api/vehicleApi.js')
const propertyApi = require('../../../../../api/propertyApi.js')
const commApi = require('../../../../../api/commApi.js')

Page({
  data: {
    // 页面模式
    mode: 'add', // 'add' 或 'edit'
    vehicleId: '', // 编辑模式下的车辆ID
    source: 'profile', // 'profile' 个人车辆 或 'property' 物业管理
    residentId: '', // 物业编辑模式下的居民ID
    pageTitle: '添加车辆',
    submitButtonText: '添加车辆',

    // 车牌号码
    plateChars: ['苏', 'A', '', '', '', '', '', ''],
    currentPlateIndex: 2,

    // 车辆颜色
    selectedColor: '',
    colorValue: '',
    availableColors: [], // 可选颜色列表

    // 停车信息
    parkingType: '固定车位', // 车位类型
    parkingTypeIndex: 0, // 车位类型选择器索引
    parkingTypeOptions: ['固定车位', '公共车位', '临时车位'], // 车位类型选项
    parkingNumber: '',

    // 主要用车
    mainUse: true,

    // 行驶证照片
    drivingLicenseUrl: '',
    uploadedImagePath: '',

    // 车牌选择器
    showPlateSelector: false,
    currentStep: 1, // 默认为省份选择步骤
    plateSelectorTitle: '选择省份简称',
    selectedProvince: '苏',
    selectedCity: 'A',

    // 颜色选择器
    showColorSelector: false,

    // 表单状态
    canSubmit: false,

    currentTime: '9:22',
    batteryLevel: '97%'
  },

  onLoad: function (options) {
    // 获取URL参数
    this.fromPage = options.from || '';
    const mode = options.mode || 'add';
    const vehicleId = options.id || '';
    const source = options.source || 'profile';
    const residentId = options.residentId || '';
    debugger
    // 设置页面模式和来源
    this.setData({
      mode: mode,
      vehicleId: vehicleId,
      source: source,
      residentId: residentId,
      pageTitle: mode === 'edit' ? '编辑车辆' : '添加车辆',
      submitButtonText: mode === 'edit' ? '保存修改' : '添加车辆'
    });

    // 初始化颜色选项
    this.initializeColors();

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: mode === 'edit' ? '编辑车辆' : '添加车辆'
    });

    // 如果是编辑模式，加载车辆数据
    if (mode === 'edit' && vehicleId) {
      this.loadVehicleData(vehicleId);
    } else {
      // 检查表单状态
      this.checkFormStatus();
    }
  },

  // 初始化颜色选项
  initializeColors: function () {
    const colors = vehicleApi.getVehicleColors();
    this.setData({
      availableColors: colors
    });
  },

  // 加载车辆数据（编辑模式）
  loadVehicleData: function (vehicleId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 根据来源选择不同的API
    let apiPromise;
    if (this.data.source === 'property') {
      // 物业管理模式，使用物业API
      apiPromise = propertyApi.propertyGetResidentVehicle(vehicleId);
    } else {
      // 个人车辆模式，使用个人API
      apiPromise = vehicleApi.getVehicleById(vehicleId);
    }

    apiPromise
      .then(res => {
        var vehicle = res;
        vehicle = vehicleApi.formatVehicleData(vehicle);
        console.log('加载车辆数据成功', vehicle);
         
        // 解析车牌号码
        const plateNumber = vehicle.plateNumber || '';
        const plateChars = ['', '', '', '', '', '', '', ''];
        for (let i = 0; i < plateNumber.length && i < 8; i++) {
          plateChars[i] = plateNumber[i];
        }
         
        // 找到车位类型索引
        let parkingTypeIndex = 0;
        const parkingType = vehicle.parkingType || '固定车位';
        const typeIndex = this.data.parkingTypeOptions.indexOf(parkingType);
        if (typeIndex !== -1) {
          parkingTypeIndex = typeIndex;
        }
         
    
         
        this.setData({
          plateChars: plateChars,
          selectedColor: vehicle.vehicleColor || '',
          colorValue: vehicle.colorValue || '',
          parkingType: parkingType,
          parkingTypeIndex: parkingTypeIndex,
          parkingNumber: vehicle.parkingNumber || '',
          mainUse: vehicle.mainUse || false,
          drivingLicenseUrl: vehicle.drivingLicenseUrl || '',
          uploadedImagePath: vehicle.drivingLicenseUrl || ''
        });
         
        this.checkFormStatus();
        wx.hideLoading();
      })
      .catch(error => {
        console.error('加载车辆数据失败', error);
        wx.hideLoading();
        wx.showToast({
          title: '加载失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 车位类型选择
  onParkingTypeChange: function (e) {
    const index = e.detail.value;
    this.setData({
      parkingTypeIndex: index,
      parkingType: this.data.parkingTypeOptions[index]
    });
  },

  // 打开车牌选择器
  openPlateSelector: function (e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('点击的位置：', index); // 添加日志，方便调试

    // 设置当前编辑的车牌位置
    this.setData({
      currentPlateIndex: index,
      showPlateSelector: true
    });

    // 根据点击的位置决定打开哪个选择界面
    if (index === 0) {
      // 点击第一位（省份简称），打开省份选择器
      console.log('打开省份选择器');
      this.setData({
        currentStep: 1,
        plateSelectorTitle: '选择省份简称'
      });
    } else if (index === 1) {
      // 点击第二位（城市代码），打开城市代码选择器
      console.log('打开城市代码选择器');
      this.setData({
        currentStep: 2,
        plateSelectorTitle: '选择城市代码'
      });
    } else {
      // 点击其他位置，打开数字字母混合键盘
      console.log('打开数字字母混合键盘');
      this.setData({
        currentStep: 3,
        plateSelectorTitle: '输入车牌号码'
      });
    }
  },

  // 关闭车牌选择器
  closePlateSelector: function () {
    this.setData({
      showPlateSelector: false
    });
  },



  // 选择省份
  selectProvince: function (e) {
    const province = e.currentTarget.dataset.province;

    // 更新省份
    const plateChars = this.data.plateChars;
    plateChars[0] = province;

    this.setData({
      plateChars,
      selectedProvince: province
    });

    this.checkFormStatus();
  },

  // 选择城市
  selectCity: function (e) {
    const city = e.currentTarget.dataset.city;

    // 更新城市
    const plateChars = this.data.plateChars;
    plateChars[1] = city;

    this.setData({
      plateChars,
      selectedCity: city
    });

    this.checkFormStatus();
  },

  // 输入车牌字符
  inputPlateChar: function (e) {
    const char = e.currentTarget.dataset.char;
    const { currentPlateIndex, plateChars, currentStep } = this.data;

    // 在任何位置输入字符
    if (currentPlateIndex >= 0 && currentPlateIndex < 8) {
      // 更新当前位置的字符
      plateChars[currentPlateIndex] = char;

      // 根据当前步骤决定下一步操作
      if (currentStep === 1) {
        // 选择省份后，自动进入城市代码选择
        this.setData({
          plateChars,
          currentStep: 2,
          currentPlateIndex: 1,
          plateSelectorTitle: '选择城市代码'
        });
      } else if (currentStep === 2) {
        // 选择城市代码后，自动进入车牌号码输入
        this.setData({
          plateChars,
          currentStep: 3,
          currentPlateIndex: 2,
          plateSelectorTitle: '输入车牌号码'
        });
      } else {
        // 输入车牌号码时，自动移动到下一位
        let nextIndex = currentPlateIndex + 1;
        if (nextIndex >= 8) {
          nextIndex = 7; // 停留在最后一位
        }

        this.setData({
          plateChars,
          currentPlateIndex: nextIndex
        });
      }

      // 检查表单状态
      this.checkFormStatus();
    }
  },



  // 删除车牌字符
  backspacePlateChar: function () {
    const { currentPlateIndex, plateChars, currentStep } = this.data;

    // 根据当前步骤和位置进行删除处理
    if (currentStep === 3) {
      // 在车牌号码输入步骤中
      // 如果当前位置有字符，则删除当前位置的字符
      if (plateChars[currentPlateIndex] && currentPlateIndex >= 2) {
        plateChars[currentPlateIndex] = '';
      }
      // 否则删除前一个位置的字符并移动到前一个位置
      else {
        let prevIndex = currentPlateIndex - 1;
        if (prevIndex < 2) {
          // 如果要删除到城市代码位置，则返回到城市代码选择界面
          this.setData({
            currentStep: 2,
            currentPlateIndex: 1,
            plateSelectorTitle: '选择城市代码'
          });
          return;
        }

        plateChars[prevIndex] = '';

        this.setData({
          currentPlateIndex: prevIndex
        });
      }

      this.setData({
        plateChars
      });
    } else if (currentStep === 2) {
      // 在城市代码选择步骤中，返回到省份选择
      this.setData({
        currentStep: 1,
        currentPlateIndex: 0,
        plateSelectorTitle: '选择省份简称'
      });
    }
    // 在省份选择步骤中不执行删除操作

    // 检查表单状态
    this.checkFormStatus();
  },

  // 清空车牌号码
  clearPlateChar: function () {
    const { plateChars, currentStep } = this.data;

    // 根据当前步骤决定清空行为
    if (currentStep === 3) {
      // 在车牌号码输入步骤中，只清空第2位之后的字符
      for (let i = 2; i < 8; i++) {
        plateChars[i] = '';
      }

      this.setData({
        plateChars,
        currentPlateIndex: 2
      });
    } else if (currentStep === 2) {
      // 在城市代码选择步骤中，返回到省份选择
      this.setData({
        currentStep: 1,
        currentPlateIndex: 0,
        plateSelectorTitle: '选择省份简称'
      });
    } else if (currentStep === 1) {
      // 在省份选择步骤中，不执行清空操作
    }

    // 检查表单状态
    this.checkFormStatus();
  },

  // 确认车牌号码
  confirmPlateNumber: function () {
    this.setData({
      showPlateSelector: false
    });
  },

  // 打开颜色选择器
  openColorSelector: function () {
    this.setData({
      showColorSelector: true
    });
  },

  // 关闭颜色选择器
  closeColorSelector: function () {
    this.setData({
      showColorSelector: false
    });
  },

  // 选择颜色
  selectColor: function (e) {
    const color = e.currentTarget.dataset.color;
    const value = e.currentTarget.dataset.value;

    this.setData({
      selectedColor: color,
      colorValue: value,
      showColorSelector: false
    });

    this.checkFormStatus();
  },

  // 停车位输入
  onParkingSpaceInput: function (e) {
    this.setData({
      parkingNumber: e.detail.value
    });
  },

  // 主要用车开关变化
  onPrimaryVehicleChange: function (e) {
    this.setData({
      mainUse: e.detail.value
    });
  },

  // 从相册选择行驶证照片
  chooseFromAlbum: function () {
    console.log('调用从相册选择方法');
    wx.showLoading({
      title: '准备相册...',
      mask: true
    });

    // 优先使用旧API，兼容性更好
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功(旧API)', res);
        // 检查文件大小，限制为5MB
        wx.getFileInfo({
          filePath: res.tempFilePaths[0],
          success: (fileInfo) => {
            const fileSizeInMB = fileInfo.size / (1024 * 1024);
            if (fileSizeInMB > 5) {
              wx.hideLoading();
              wx.showToast({
                title: '图片大小不能超过5MB',
                icon: 'none',
                duration: 2000
              });
              return;
            }

            this.setData({ drivingLicenseUrl: res.tempFilePaths[0] });
            // 上传图片
            this.uploadPhoto(res.tempFilePaths[0]);
            wx.hideLoading();
          },
          fail: () => {
            // 如果获取文件信息失败，直接使用图片
            this.setData({ drivingLicenseUrl: res.tempFilePaths[0] });
            this.uploadPhoto(res.tempFilePaths[0]);
            wx.hideLoading();
          }
        });
      },
      fail: (err) => {
        console.log('选择图片失败(旧API)', err);
        // 如果旧API失败，尝试使用新API
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album'],
          sizeType: ['compressed'],
          success: (res) => {
            console.log('选择图片成功(新API)', res);
            // 检查文件大小，限制为5MB
            const tempFile = res.tempFiles[0];
            const fileSizeInMB = tempFile.size / (1024 * 1024);

            if (fileSizeInMB > 5) {
              wx.hideLoading();
              wx.showToast({
                title: '图片大小不能超过5MB',
                icon: 'none',
                duration: 2000
              });
              return;
            }

            this.setData({ drivingLicenseUrl: tempFile.tempFilePath });
            this.uploadPhoto(tempFile.tempFilePath);
            wx.hideLoading();
          },
          fail: (err2) => {
            console.log('选择图片失败(新API)', err2);
            // 用户取消选择不显示错误提示
            if (err2.errMsg !== "chooseMedia:fail cancel") {
              wx.showToast({
                title: '选择图片失败',
                icon: 'none',
                duration: 2000
              });
            }
            wx.hideLoading();
          }
        });
      }
    });
  },

  // 拍照上传行驶证照片
  takePhoto: function () {
    console.log('调用拍照上传方法');
    wx.showLoading({
      title: '准备相机...',
      mask: true
    });

    // 直接使用wx.chooseImage，这是最基础的API，兼容性最好
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        console.log('拍照成功', res);
        // 设置照片路径
        this.setData({ drivingLicenseUrl: res.tempFilePaths[0] });

        // 上传图片
        this.uploadPhoto(res.tempFilePaths[0]);

        wx.hideLoading();
      },
      fail: (err) => {
        console.log('拍照失败', err);
        // 用户取消拍照不显示错误提示
        if (err.errMsg !== "chooseImage:fail cancel") {
          wx.showToast({
            title: '拍照失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
        wx.hideLoading();
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 上传图片
  uploadPhoto: function (src) {
    if (!src) {
      console.log('本地测试环境，图片上传已跳过');
      this.checkFormStatus();
      return Promise.resolve({ data: '', message: '本地测试环境，图片上传已跳过' });
    }

    return commApi.upLoadFile(src)
      .then((result) => {
        console.log('图片上传成功', result);

        this.setData({
          uploadedImagePath: result.data || ''
        });

        wx.showToast({
          title: '图片上传成功',
          icon: 'success',
          duration: 1500
        });

        this.checkFormStatus();
        return result;
      })
      .catch((errorMsg) => {
        console.log('图片上传失败', errorMsg);

        // 本地测试环境，不显示错误提示
        console.log('本地测试环境，图片上传失败已忽略');

        this.checkFormStatus();
        return { data: '', message: '本地测试环境，图片上传失败已忽略' };
      });
  },

  // 上传行驶证照片（保留兼容性）
  uploadDrivingLicense: function () {
    this.chooseFromAlbum();
  },

  // 移除行驶证照片
  removeDrivingLicense: function () {
    this.setData({
      drivingLicenseUrl: '',
      uploadedImagePath: ''
    });

    this.checkFormStatus();
  },

  // 检查表单状态
  checkFormStatus: function () {
    const { plateChars, selectedColor  } = this.data;
    // 检查车牌号码是否完整
    let isPlateComplete = true;
    for (let i = 0; i < 7; i++) {
      if (!plateChars[i]) {
        isPlateComplete = false;
        break;
      }
    }

    // 检查是否可以提交
    const canSubmit = isPlateComplete && selectedColor;

    this.setData({
      canSubmit
    });
  },

  // 提交表单
  submitForm: function () {
    if (!this.data.canSubmit) {
      return;
    }

    const isEditMode = this.data.mode === 'edit';

    wx.showLoading({
      title: isEditMode ? '保存中...' : '提交中...',
      mask: true
    });

    // 构建车牌号码
    const plateNumber = this.data.plateChars.join('');

    // 构建车辆信息
    const vehicleData = {
      plateNumber: plateNumber,
      vehicleColor: this.data.selectedColor,
      parkingType: this.data.parkingType,
      parkingNumber: this.data.parkingNumber,
      mainUse: this.data.mainUse,
      status: isEditMode ? undefined : 'pending', // 编辑模式不修改状态
      media: this.data.uploadedImagePath || this.data.drivingLicenseUrl || ''
    };

    // 编辑模式需要添加ID
    if (isEditMode) {
      vehicleData.id = this.data.vehicleId;
    }

    // 物业编辑模式需要添加居民ID
    if (this.data.source === 'property' && this.data.residentId) {
      vehicleData.residentId = this.data.residentId;
    }

    // 根据来源和模式选择不同的API
    let apiCall;
    if (this.data.source === 'property') {
      // 物业管理模式
      if (isEditMode) {
        apiCall = propertyApi.propertyEditResidentVehicle(vehicleData);
      } else {
        apiCall = propertyApi.propertyAddResidentVehicle(vehicleData);
      }
    } else {
      // 个人车辆模式
      apiCall = isEditMode ? vehicleApi.updateVehicle(vehicleData) : vehicleApi.addVehicle(vehicleData);
    }

    apiCall
      .then(res => {
        console.log(isEditMode ? '编辑车辆成功' : '添加车辆成功', res);
        wx.hideLoading();

        // 显示成功提示
        wx.showToast({
          title: isEditMode ? '保存成功' : '添加成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回上一页
            setTimeout(() => {
              this.goBack();
            }, 2000);
          }
        });
      })
      .catch(err => {
        console.log(isEditMode ? '编辑车辆失败' : '添加车辆失败', err);
        wx.hideLoading();

        // 如果API调用失败，使用本地存储作为备用（仅新增模式）
        if (!isEditMode) {
          this.saveVehicleToStorage(vehicleData, plateNumber);
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
  },

  // 保存车辆信息到本地存储（备用方案）
  saveVehicleToStorage: function (vehicleData, plateNumber) {
    // 构建本地存储格式的车辆信息
    const vehicleInfo = {
      id: 'vehicle_' + Date.now(),
      plateNumber: plateNumber,
      color: vehicleData.vehicleColor,
      colorValue: this.data.colorValue,
      parkingType: vehicleData.parkingType,
      parkingNumber: vehicleData.parkingNumber,
      mainUse: vehicleData.mainUse,
      status: '审核中',
      statusClass: 'pending',
      addDate: new Date().toISOString(),
      formattedDate: new Date().toISOString().split('T')[0],
      drivingLicenseUrl: vehicleData.media
    };

    // 从本地存储获取现有车辆列表
    let vehicles = wx.getStorageSync('my_vehicles') || [];

    // 如果设置为主要车辆，则其他车辆设为非主要
    if (vehicleInfo.mainUse) {
      vehicles = vehicles.map(v => {
        return { ...v, mainUse: false };
      });
    }

    // 添加新车辆
    vehicles.push(vehicleInfo);

    // 保存到本地存储
    wx.setStorageSync('my_vehicles', vehicles);

    // 显示成功提示
    wx.showToast({
      title: '添加成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟返回上一页
        setTimeout(() => {
          this.goBack();
        }, 2000);
      }
    });
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，则跳转到车辆列表页面
        wx.navigateTo({
          url: '/profilePackage/pages/profile/vehicle/vehicle'
        });
      }
    });
  },

  // 阻止滚动穿透
  preventTouchMove: function () {
    return false;
  }
})
